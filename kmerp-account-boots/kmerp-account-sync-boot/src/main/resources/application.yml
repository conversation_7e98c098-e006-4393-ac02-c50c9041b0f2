spring:
  servlet:
    multipart:
      max-file-size: 500MB  # 单个文件大小上限
      max-request-size: 1000MB  # 总上传数据大小上限
  profiles:
    active: @boot-env@
  main:
    # webflux 启动必填，尤其是同时存在web和webflux依赖
    web-application-type: servlet
  application:
    name: @boot-name@
  # spring batch的配置，默认按照这个配置，不要动
  batch:
    job:
      enabled: false
    jdbc:
      initialize-schema: always
      platform: mysql
      schema: classpath:org/springframework/batch/core/schema-mysql.sql
# 配置这个是为了解决发布系统启动时的校验，如果不关闭jsp,请求project_auto_check_monitor.jsp会404
server:
  servlet:
    jsp:
      registered: false

file-path:
    tmp: /tmp/

dubbo:
  protocols:
    dubbo-protocol:
      name: dubbo
      port: 20880
    tri-protocol:
      name: tri
      port: 50051
  protocol:
    serialization: hessian2
    prefer-serialization: hessian2,fastjson2
  consumer:
    timeout: 10000
    retries: 3
    check: false
  application:
    name: @boot-name@
    logger: slf4j
  registries:
    erpZk:
      address: zookeeper://erp-zook.superboss.cc:30002
      register-mode: all
      default: true
    commonZk:
      address: zookeeper://zoo1.superboss.cc:30002,zookeeper://zoo2.superboss.cc:30002,zookeeper://zoo3.superboss.cc:30002
      register-mode: all
      default: false
    middleZk:
      address: zookeeper://dzzk.superboss.cc:30002
      register-mode: all
      default: false
  version:
    kmbi:
      core: 1.0.0


session:
  app:
    key: 1000
  diamond:
    config: ocs.m-k2j72fdb74e92294

cache:
  app:
    key: 1030
  diamond:
    config:
      new: ocs.m-k2j72fdb74e92294

kmerp:
  redis:
    connector-id: NACOS:KMERP:REDIS:RP-MAIN
    enable: true
  mq:
    rocket:
      producer:
        enable: true
        name-server: @mq-name-server@
        producer-group-name: kmerp-account-task-producer-group-@boot-env@
        unit-name: kmerp-account-task-producer-@boot-env@
      consumer:
        enable: true
        name-server: @mq-name-server@
        consumer-group-name: kmerp-account-task-producer-group-@boot-env@
        unit-name: kmerp-account-task-producer-@boot-env@
  dubbo:
    version:
      base: erp-@boot-env@-0.0.1
      trade: erp-trade-@boot-env@-0.0.1
      item: erp-item-@boot-env@-0.0.1
      item-search: erp-item-search-@boot-env@-0.0.1
      pt: erp-pt-@boot-env@-0.0.1
      pda: erp-pda-@boot-env@-0.0.1
      purchase: erp-caigou-@boot-env@-0.0.1
      aftersale: erp-aftersale-@boot-env@-0.0.1
      dms: erp-dms-@boot-env@-0.0.1
      fms: erp-fms-@boot-env@-0.0.1
      wms: erp-wms-@boot-env@-0.0.1
      logistics: erp-logistics-warning-@boot-env@-0.0.1
      chessboard: 1.0.0
  env: @boot-env@
  chessboard-service-name: kmerp-account

#com.raycloud.dmj.data.chessboard.config.ChessboardProperties
chessboard:
  conf:
    consumer: open
    nacos:
      serverAddr: erp-nacos.superboss.cc:30004
      namespace: 36906f14-f7d5-44fd-b2e1-04f43dbca568
    redis:
      addr: redis://r-k2j47mq0q8sdm3a4n2.redis.zhangbei.rds.aliyuncs.com:6379
      passwordCode: redis.r-k2j47mq0q8sdm3a4n2
    application:
      env: @boot-env@
      serviceName: kmerp-account

web:
  dynamic:
    path: erpaccount