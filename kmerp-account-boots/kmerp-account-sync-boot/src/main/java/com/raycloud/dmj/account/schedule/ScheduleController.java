package com.raycloud.dmj.account.schedule;

import com.google.common.collect.Maps;
import com.raycloud.dmj.account.core.scheduled.IScheduledService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("schedule")
public class ScheduleController {

    @Resource
    private List<IScheduledService> scheduleList;

    @PostMapping("do-task/{scheduleType}")
    public String schedule(@PathVariable("scheduleType") String scheduleType, @RequestBody(required = false) Map<String, Object> params) {
        scheduleList.stream()
                .filter(scheduledService -> scheduledService.getScheduledType().getValue().equals(scheduleType))
                .findFirst()
                .ifPresent(scheduledService -> scheduledService.doTask(params == null ? Maps.newHashMap() : params));
        return "success";
    }

}
