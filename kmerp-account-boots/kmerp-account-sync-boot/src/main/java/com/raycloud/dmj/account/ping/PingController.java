package com.raycloud.dmj.account.ping;


import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
public class PingController {

    @RequestMapping("project_auto_check_monitor.jsp")
    public Object health() {
        Map<String, Object> res = new HashMap<>();
        res.put("status", "ok");
        return res;
    }

    @RequestMapping("ping")
    public Object ping() {
        return "ping:" + System.currentTimeMillis();
    }

}

