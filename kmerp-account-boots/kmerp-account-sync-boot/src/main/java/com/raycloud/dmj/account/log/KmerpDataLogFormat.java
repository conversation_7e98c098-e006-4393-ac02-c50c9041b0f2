package com.raycloud.dmj.account.log;

import com.raycloud.log4j.jest.MessageFormatSkeleton;
import org.apache.log4j.spi.LoggingEvent;

import java.util.Map;

public class KmerpDataLogFormat extends MessageFormatSkeleton {
    public static String index = "kmas";

    public KmerpDataLogFormat() {
    }

    protected String writeBasic(Map<String, Object> logData, LoggingEvent event) {
        Object eventMessage = event.getMessage();
        if (eventMessage == null) {
            logData.put("message", "");
        } else {
            logData.put("message", eventMessage);
        }

        return index;
    }
}
