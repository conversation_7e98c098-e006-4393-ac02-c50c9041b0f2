<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE log4j:configuration SYSTEM "log4j.dtd">
<log4j:configuration xmlns:log4j="http://jakarta.apache.org/log4j/">

    <appender name="CONSOLE" class="org.apache.log4j.ConsoleAppender">
        <layout class="org.apache.log4j.PatternLayout">
            <param name="ConversionPattern" value="%d %5p --- [%15t] %c:%L - %m%n" />
        </layout>
    </appender>

    <appender name="ons" class="com.raycloud.log4j.rocketmq.appender.RocketMqAppender">
        <param name="messageFormatClass" value="com.raycloud.dmj.account.log.KmerpDataLogFormat" />
        <param name="applicationName" value="kmerp-account-export-boot" />
        <param name="topic" value="LOG_FLASH"/>
        <param name="tag" value="erp.logging"/>
        <param name="producerGroup" value="PID_ERP_LOG_FLASH"/>
        <param name="namesrvAddr" value="erp-mq-nameserver.superboss.cc:9876"/>
        <param name="forwardInterval" value="5000" />
        <param name="batchSize" value="10" />
        <param name="useClue" value="true" />
        <param name="rejectStrategy" value="1"/>
        <filter class="com.raycloud.log4j.jest.filter.LoggerFilter">
            <param name="excludeLoggers"
                   value="com.raycloud.ec.api,com.raycloud.ec.remote.subscriber,com.raycloud.dmj.tve,com.raycloud.dmj.services.filter,com.raycloud.dmj.business.sync.SyncCoverBusiness,com.raycloud.dmj.services.alibaba.basis.AliApiFacade,com.raycloud.dmj.business.audit.AutoAuditParserBusiness,com.raycloud.ec.remote.publisher.AsyncFireRemoteEventsPolicy,com.raycloud.dmj.services.trades.im.business.ItemMatchBusiness,com.raycloud.dmj.services.filter.support,com.raycloud.cache.spring.RaycloudCache"/>
            <param name="ignoreErrorLevel" value="true"/>
        </filter>
    </appender>

    <logger name="com.raycloud.dmj" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="ons"/>
        <appender-ref ref="CONSOLE"/>
    </logger>

    <root>
        <level value="INFO"/>
        <appender-ref ref="CONSOLE"/>
    </root>

</log4j:configuration>