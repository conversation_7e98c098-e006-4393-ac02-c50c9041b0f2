<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.raycloud.dmj</groupId>
        <artifactId>kmerp-account-boots</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>kmerp-account-web-boot</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.raycloud.dmj</groupId>
            <artifactId>kmerp-account-export-backend</artifactId>
        </dependency>

        <dependency>
            <groupId>com.raycloud.dmj</groupId>
            <artifactId>kmerp-account-web-frontend</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>HdrHistogram</artifactId>
                    <groupId>org.hdrhistogram</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jna</artifactId>
                    <groupId>net.java.dev.jna</groupId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>

    <build>
        <finalName>kmerp-account-web-boot</finalName>
    </build>

    <profiles>
        <profile>
            <id>gray</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <boot-name>kmerp-account-web-boot-gray</boot-name>
                <boot-env>gray</boot-env>
            </properties>
            <build>
                <finalName>kmerp-account-web-boot-gray</finalName>
                <resources>
                    <resource>
                        <directory>src/main/resources</directory>
                        <filtering>true</filtering>
                    </resource>
                </resources>
            </build>
        </profile>
        <profile>
            <id>gray2</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <boot-name>kmerp-account-web-boot-gray2</boot-name>
                <boot-env>gray2</boot-env>
            </properties>
            <build>
                <finalName>kmerp-account-web-boot-gray2</finalName>
                <resources>
                    <resource>
                        <directory>src/main/resources</directory>
                        <filtering>true</filtering>
                    </resource>
                </resources>
            </build>
        </profile>
        <profile>
            <id>gray3</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <boot-name>kmerp-account-web-boot-gray3</boot-name>
                <boot-env>gray3</boot-env>
                <mq-name-server>erp-mq-nameserver.superboss.cc:9877</mq-name-server>
            </properties>
            <build>
                <finalName>kmerp-account-web-boot-gray3</finalName>
                <resources>
                    <resource>
                        <directory>src/main/resources</directory>
                        <filtering>true</filtering>
                    </resource>
                </resources>
            </build>
        </profile>
        <profile>
            <id>test</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <boot-name>kmerp-account-web-boot-gray3</boot-name>
                <boot-env>gray3</boot-env>
                <mq-name-server>localhost:9876</mq-name-server>
            </properties>
            <build>
                <finalName>kmerp-account-web-boot-gray3</finalName>
                <resources>
                    <resource>
                        <directory>src/main/resources</directory>
                        <filtering>true</filtering>
                    </resource>
                </resources>
            </build>
        </profile>
        <profile>
            <id>gray4</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <boot-name>kmerp-account-web-boot-gray4</boot-name>
                <boot-env>gray4</boot-env>
            </properties>
            <build>
                <finalName>kmerp-account-web-boot-gray4</finalName>
                <resources>
                    <resource>
                        <directory>src/main/resources</directory>
                        <filtering>true</filtering>
                    </resource>
                </resources>
            </build>
        </profile>
        <profile>
            <id>gray5</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <boot-name>kmerp-account-web-boot-gray5</boot-name>
                <boot-env>gray5</boot-env>
            </properties>
            <build>
                <finalName>kmerp-account-web-boot-gray5</finalName>
                <resources>
                    <resource>
                        <directory>src/main/resources</directory>
                        <filtering>true</filtering>
                    </resource>
                </resources>
            </build>
        </profile>
        <profile>
            <id>gray6</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <boot-name>kmerp-account-web-boot-gray6</boot-name>
                <boot-env>gray6</boot-env>
            </properties>
            <build>
                <finalName>kmerp-account-web-boot-gray6</finalName>
                <resources>
                    <resource>
                        <directory>src/main/resources</directory>
                        <filtering>true</filtering>
                    </resource>
                </resources>
            </build>
        </profile>
        <profile>
            <id>vipprod</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <boot-name>kmerp-account-web-boot-vipprod</boot-name>
                <boot-env>vipprod</boot-env>
            </properties>
            <build>
                <finalName>kmerp-account-web-boot-vipprod</finalName>
                <resources>
                    <resource>
                        <directory>src/main/resources</directory>
                        <filtering>true</filtering>
                    </resource>
                </resources>
            </build>
        </profile>
        <profile>
            <id>preissue</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <boot-name>kmerp-account-web-boot-preissue</boot-name>
                <boot-env>preissue</boot-env>
            </properties>
            <build>
                <finalName>kmerp-account-web-boot-preissue</finalName>
                <resources>
                    <resource>
                        <directory>src/main/resources</directory>
                        <filtering>true</filtering>
                    </resource>
                </resources>
            </build>
        </profile>
        <profile>
            <id>preissue2</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <boot-name>kmerp-account-web-boot-preissue2</boot-name>
                <boot-env>preissue2</boot-env>
            </properties>
            <build>
                <finalName>kmerp-account-web-boot-preissue2</finalName>
                <resources>
                    <resource>
                        <directory>src/main/resources</directory>
                        <filtering>true</filtering>
                    </resource>
                </resources>
            </build>
        </profile>
        <profile>
            <id>prod</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <boot-name>kmerp-account-web-boot-prod</boot-name>
                <boot-env>prod</boot-env>
                <mq-name-server>localhost:9876</mq-name-server>
                <mq-name-server>erp-mq-nameserver.superboss.cc:9877</mq-name-server>
            </properties>
            <build>
                <finalName>kmerp-account-web-boot-prod</finalName>
                <resources>
                    <resource>
                        <directory>src/main/resources</directory>
                        <filtering>true</filtering>
                    </resource>
                </resources>
            </build>
        </profile>
    </profiles>
</project>