package com.raycloud.dmj;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.raycloud.dmj.account.IPlatformAuthDubbo;
import com.raycloud.dmj.account.common.CreateAuthUrlReq;
import com.raycloud.dmj.account.core.cleancategory.domain.request.CategoryAnalyzeReq;
import com.raycloud.dmj.account.core.cleancategory.service.CategoryAnalyzeService;
import com.raycloud.dmj.account.core.enums.DateTypeEnum;
import com.raycloud.dmj.account.core.enums.FileTypeEnum;
import com.raycloud.dmj.account.core.enums.RawDataSourceEnum;
import com.raycloud.dmj.account.core.rawdata.req.OriginalDataCallbackReq;
import com.raycloud.dmj.account.core.rawdata.req.OriginalDataParam;
import com.raycloud.dmj.account.core.rawdata.service.RawDataStorageService;
import com.raycloud.dmj.account.core.rawdata.utils.RawDataDateUtil;
import com.raycloud.dmj.account.core.rawdata.utils.oss.OSSClientHelper;
import com.raycloud.dmj.account.core.rawdata.utils.oss.OssUtils;
import com.raycloud.dmj.account.enums.DataType;
import com.raycloud.dmj.account.infra.utils.DateUtils;
import com.raycloud.dmj.account.web.KmerpDataWebApplication;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.jupiter.api.Test;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.time.LocalDate;

@SpringBootTest(classes = KmerpDataWebApplication.class)
public class TestApplication {

    @DubboReference
    private IPlatformAuthDubbo platformAuthDubbo;
    @Resource
    private RedissonClient redissonClient;

    @Resource
    private RawDataStorageService rawDataStorageService;
    @Test
    public void test1() {
        RLock lock = redissonClient.getLock("123");
        System.out.println(lock.tryLock());
    }


    @Test
    public void test() {
        CreateAuthUrlReq createAuthUrlReq = new CreateAuthUrlReq();
        createAuthUrlReq.setShopId(123L);
        System.out.println(platformAuthDubbo.createAuthUrl(createAuthUrlReq));
    }

    @Test
    public void downLoad() {

        String objectName =
                OssUtils.getSharedDataObjectName(
                        DataType.BILL.getValue()) + "_" + ********* + "_" + 21401 + "_" + LocalDate.of(2025, 7, 1);

        String filePath = "/Users/<USER>/Downloads/account_checking_system/shared_data/bill_*********_21401_2025-07-01.csv";
        String fileDownloadPath = "/Users/<USER>/Downloads/account_checking_system/shared_data/bill_*********_21401_2025-07-011.csv";



      OssUtils.downloadObject(objectName,fileDownloadPath);
        String ossUrl = OSSClientHelper.expireUrl(objectName);
        System.out.println(ossUrl);

    }

    @Test
    public void run14() {
//        FileAnalyzeRecordDO byId = fileAnalyzeRecordDao.getById(8155L);

//        rawDataStorageService.rpaRawDataStorage(8310L);
        //feature
        //直播红包
//        String json="{\"companyId\":10438,\"fileList\":[{\"blockId\":\"zk_sycm_0010_1\",\"content\":\"https://xiaozhi-client.oss-cn-hangzhou.aliyuncs.com/rpa/Temp/天猫直播红包-财务详情_10438_119595_1753677988582.zip\",\"dataRange\":\"2025-06\",\"dateType\":2,\"extendParam\":{\"serialNo\":\"taobaolive_COUPON_202506_220000007_407700539_2215010578451_online\"},\"fileName\":\"20250728_TB_天猫直播红包-财务详情_2_10438_119595_1753669595213_天猫直播红包-taobaolive_COUPON_202506_220000007_407700539_2215010578451_online-财务详情_20250601至20250630.zip\",\"fileType\":\"zip\",\"realEndDataRange\":\"2025-06-30 00:00:00\",\"realStartDataRange\":\"2025-06-01 00:00:00\",\"shopUniId\":119595},{\"blockId\":\"zk_sycm_0010_1\",\"content\":\"https://xiaozhi-client.oss-cn-hangzhou.aliyuncs.com/rpa/Temp/天猫直播红包-财务详情_10438_119595_1753677358343.zip\",\"dataRange\":\"2025-06\",\"dateType\":2,\"extendParam\":{\"serialNo\":\"taobaolive_COUPON_202506_220000123_407700539_2215010578451_online\"},\"fileName\":\"20250728_TB_天猫直播红包-财务详情_0_10438_119595_1753669595281_天猫直播红包-taobaolive_COUPON_202506_220000123_407700539_2215010578451_online-财务详情_20250601至20250630.zip\",\"fileType\":\"zip\",\"realEndDataRange\":\"2025-06-30 00:00:00\",\"realStartDataRange\":\"2025-06-01 00:00:00\",\"shopUniId\":119595},{\"blockId\":\"zk_sycm_0010_1\",\"content\":\"https://xiaozhi-client.oss-cn-hangzhou.aliyuncs.com/rpa/Temp/天猫直播红包-财务详情_10438_119595_1753674346854.zip\",\"dataRange\":\"2025-06\",\"dateType\":2,\"extendParam\":{\"serialNo\":\"taobaolive_COUPON_202506_220003004_407700539_2214101433551_online\"},\"fileName\":\"20250728_TB_天猫直播红包-财务详情_1_10438_119595_1753669595355_天猫直播红包-taobaolive_COUPON_202506_220003004_407700539_2214101433551_online-财务详情_20250601至20250630.zip\",\"fileType\":\"zip\",\"realEndDataRange\":\"2025-06-30 00:00:00\",\"realStartDataRange\":\"2025-06-01 00:00:00\",\"shopUniId\":119595},{\"blockId\":\"zk_sycm_0010_1\",\"content\":\"https://xiaozhi-client.oss-cn-hangzhou.aliyuncs.com/rpa/Temp/天猫直播红包-财务详情_10438_119595_1753672437591.zip\",\"dataRange\":\"2025-06\",\"dateType\":2,\"extendParam\":{\"serialNo\":\"taobaolive_COUPON_202506_220000123_407700539_2214101433551_online\"},\"fileName\":\"20250728_TB_天猫直播红包-财务详情_3_10438_119595_1753669595422_天猫直播红包-taobaolive_COUPON_202506_220000123_407700539_2214101433551_online-财务详情_20250601至20250630.zip\",\"fileType\":\"zip\",\"realEndDataRange\":\"2025-06-30 00:00:00\",\"realStartDataRange\":\"2025-06-01 00:00:00\",\"shopUniId\":119595},{\"blockId\":\"zk_sycm_0010_2\",\"content\":\"https://xiaozhi-client.oss-cn-hangzhou.aliyuncs.com/rpa/Temp/天猫直播红包-财务总览_10438_119595_1753678710232.csv\",\"dataRange\":\"2025-06\",\"dateType\":2,\"extendParam\":{\"serialNo\":\"taobaolive_COUPON_202506_220000123_407700539_2215010578451_online\"},\"fileName\":\"20250728_TB_天猫直播红包-财务总览_0_10438_119595_1753669620201_天猫直播红包-taobaolive_COUPON_202506_220000123_407700539_2215010578451_online-财务总览_20250601至20250630.csv\",\"fileType\":\"csv\",\"realEndDataRange\":\"2025-06-30 00:00:00\",\"realStartDataRange\":\"2025-06-01 00:00:00\",\"shopUniId\":119595},{\"blockId\":\"zk_sycm_0010_2\",\"content\":\"https://xiaozhi-client.oss-cn-hangzhou.aliyuncs.com/rpa/Temp/天猫直播红包-财务总览_10438_119595_1753677461129.csv\",\"dataRange\":\"2025-06\",\"dateType\":2,\"extendParam\":{\"serialNo\":\"taobaolive_COUPON_202506_220003004_407700539_2214101433551_online\"},\"fileName\":\"20250728_TB_天猫直播红包-财务总览_1_10438_119595_1753669620261_天猫直播红包-taobaolive_COUPON_202506_220003004_407700539_2214101433551_online-财务总览_20250601至20250630.csv\",\"fileType\":\"csv\",\"realEndDataRange\":\"2025-06-30 00:00:00\",\"realStartDataRange\":\"2025-06-01 00:00:00\",\"shopUniId\":119595},{\"blockId\":\"zk_sycm_0010_2\",\"content\":\"https://xiaozhi-client.oss-cn-hangzhou.aliyuncs.com/rpa/Temp/天猫直播红包-财务总览_10438_119595_1753672479491.csv\",\"dataRange\":\"2025-06\",\"dateType\":2,\"extendParam\":{\"serialNo\":\"taobaolive_COUPON_202506_220000007_407700539_2215010578451_online\"},\"fileName\":\"20250728_TB_天猫直播红包-财务总览_2_10438_119595_1753669620324_天猫直播红包-taobaolive_COUPON_202506_220000007_407700539_2215010578451_online-财务总览_20250601至20250630.csv\",\"fileType\":\"csv\",\"realEndDataRange\":\"2025-06-30 00:00:00\",\"realStartDataRange\":\"2025-06-01 00:00:00\",\"shopUniId\":119595},{\"blockId\":\"zk_sycm_0010_2\",\"content\":\"https://xiaozhi-client.oss-cn-hangzhou.aliyuncs.com/rpa/Temp/天猫直播红包-财务总览_10438_119595_1753673758528.csv\",\"dataRange\":\"2025-06\",\"dateType\":2,\"extendParam\":{\"serialNo\":\"taobaolive_COUPON_202506_220000123_407700539_2214101433551_online\"},\"fileName\":\"20250728_TB_天猫直播红包-财务总览_3_10438_119595_1753669620376_天猫直播红包-taobaolive_COUPON_202506_220000123_407700539_2214101433551_online-财务总览_20250601至20250630.csv\",\"fileType\":\"csv\",\"realEndDataRange\":\"2025-06-30 00:00:00\",\"realStartDataRange\":\"2025-06-01 00:00:00\",\"shopUniId\":119595}],\"processType\":62}";
        //微信
        String json="{\"companyId\":10438,\"fileList\":[{\"blockId\":\"zk_sycm_0007_1\",\"content\":\"https://xiaozhi-client.oss-cn-hangzhou.aliyuncs.com/rpa/Temp/淘宝天猫微信账单-日汇总_10438_119595_1753692279476.xlsx\",\"dataRange\":\"2025-07-27\",\"dateType\":1,\"fileName\":\"20250728_TB_淘宝天猫微信账单-日汇总_0_10438_119595_1753690937342_微信账单-日汇总_20250727至20250727.xlsx\",\"fileType\":\"xlsx\",\"realEndDataRange\":\"2025-07-27 23:59:59\",\"realStartDataRange\":\"2025-07-27 00:00:00\",\"shopUniId\":119595},{\"blockId\":\"zk_sycm_0007_2\",\"content\":\"https://xiaozhi-client.oss-cn-hangzhou.aliyuncs.com/rpa/Temp/淘宝天猫微信账单-日汇总-下载明细_10438_119595_1753697800929.xlsx\",\"dataRange\":\"2025-07-27\",\"dateType\":1,\"fileName\":\"20250728_TB_淘宝天猫微信账单-日汇总-下载明细_0_10438_119595_1753690968637_微信账单-明细_20250727至20250727.xlsx\",\"fileType\":\"xlsx\",\"realEndDataRange\":\"2025-07-27 23:59:59\",\"realStartDataRange\":\"2025-07-27 00:00:00\",\"shopUniId\":119595}],\"processType\":55}";
        //保证金
//        String json="{\"companyId\":10438,\"processType\":58,\"fileList\":[{\"dateType\":1,\"dataRange\":\"2025-07-28\",\"blockId\":\"zk_sycm_0008\",\"content\":\"https://xiaozhi-client.oss-cn-hangzhou.aliyuncs.com/rpa/Temp/天猫保证金账单_10438_119595_1753798946529.zip\",\"fileName\":\"20250729_TB_天猫保证金账单_0_10438_119595_1753795223338_保证金账户_20250728至20250728.zip\",\"fileType\":\"zip\",\"realStartDataRange\":1753632000000,\"realEndDataRange\":1753718399000,\"shopUniId\":119595,\"extendParam\":null}]}";
        //消费积分
//        String json="{\"companyId\":10438,\"fileList\":[{\"blockId\":\"zk_sycm_0009_1\",\"content\":\"https://xiaozhi-client.oss-cn-hangzhou.aliyuncs.com/rpa/Temp/天猫消费积分_10438_119595_1753761330460.csv\",\"dataRange\":\"2025-06\",\"dateType\":2,\"fileName\":\"20250729_TB_天猫消费积分_0_10438_119595_1753753548881_天猫积分账单_消费积分_消费积分_20250601至20250630.csv\",\"fileType\":\"csv\",\"realEndDataRange\":\"2025-06-30 00:00:00\",\"realStartDataRange\":\"2025-06-01 00:00:00\",\"shopUniId\":119595}],\"processType\":59}";
        //淘金币
//        String json="{\"companyId\":10438,\"fileList\":[{\"blockId\":\"zk_sycm_0009_3\",\"content\":\"https://xiaozhi-client.oss-cn-hangzhou.aliyuncs.com/rpa/Temp/天猫淘金币_10438_119595_1753865257557.csv\",\"dataRange\":\"2025-06\",\"dateType\":2,\"fileName\":\"20250730_TB_天猫淘金币_0_10438_119595_1753857178376_天猫积分账单_淘金币合作费用_淘金币合作费用_20250601至20250630.csv\",\"fileType\":\"csv\",\"realEndDataRange\":\"2025-06-30 00:00:00\",\"realStartDataRange\":\"2025-06-01 00:00:00\",\"shopUniId\":119595}],\"processType\":64}";
        //淘宝联盟
//        String json="{\"companyId\":10438,\"fileList\":[{\"blockId\":\"zk_sycm_0009_2\",\"content\":\"https://xiaozhi-client.oss-cn-hangzhou.aliyuncs.com/rpa/Temp/天猫淘宝联盟_10438_119595_1753869918259.csv\",\"dataRange\":\"2025-03\",\"dateType\":2,\"fileName\":\"20250730_TB_天猫淘宝联盟_0_10438_119595_1753860867777_淘宝联盟_淘宝联盟合作费用_淘宝联盟合作费用_20250301至20250331.csv\",\"fileType\":\"csv\",\"realEndDataRange\":\"2025-03-31 23:59:59\",\"realStartDataRange\":\"2025-03-01 23:59:59\",\"shopUniId\":119595}],\"processType\":63}";
        OriginalDataCallbackReq originalDataCallbackReq = JSON.parseObject(json, OriginalDataCallbackReq.class);
        rawDataStorageService.rpaOriginalDataCallback(originalDataCallbackReq);
    }

    @Resource
    private CategoryAnalyzeService categoryAnalyzeService;

    @Test
    public void run15() {
        String json="{\"companyId\":10438,\"fileList\":[{\"blockId\":\"zk_sycm_0010_1\",\"content\":\"https://xiaozhi-client.oss-cn-hangzhou.aliyuncs.com/rpa/Temp/天猫直播红包-财务详情_10438_119595_1753164267829.zip\",\"dataRange\":\"2025-06\",\"dateType\":2,\"processType\":62,\"extendParam\":{\"serialNo\":\"taobaolive_COUPON_202506_220003004_407700539_2214101433551_online\"},\"fileName\":\"20250722_TB_天猫直播红包-财务详情_1_10438_119595_1753154519438_天猫直播红包-taobaolive_COUPON_202506_220003004_407700539_2214101433551_online-财务详情_20250601至20250630.zip\",\"fileType\":\"zip\",\"realEndDataRange\":\"2025-06-30 00:00:00\",\"realStartDataRange\":\"2025-06-01 00:00:00\",\"shopUniId\":119595},{\"blockId\":\"zk_sycm_0010_1\",\"content\":\"https://xiaozhi-client.oss-cn-hangzhou.aliyuncs.com/rpa/Temp/天猫直播红包-财务详情_10438_119595_1753155970486.zip\",\"dataRange\":\"2025-06\",\"dateType\":2,\"processType\":62,\"extendParam\":{\"serialNo\":\"taobaolive_COUPON_202506_220000007_407700539_2215010578451_online\"},\"fileName\":\"20250722_TB_天猫直播红包-财务详情_2_10438_119595_1753154519537_天猫直播红包-taobaolive_COUPON_202506_220000007_407700539_2215010578451_online-财务详情_20250601至20250630.zip\",\"fileType\":\"zip\",\"realEndDataRange\":\"2025-06-30 00:00:00\",\"realStartDataRange\":\"2025-06-01 00:00:00\",\"shopUniId\":119595},{\"blockId\":\"zk_sycm_0010_1\",\"content\":\"https://xiaozhi-client.oss-cn-hangzhou.aliyuncs.com/rpa/Temp/天猫直播红包-财务详情_10438_119595_1753159305259.zip\",\"dataRange\":\"2025-06\",\"processType\":62,\"dateType\":2,\"extendParam\":{\"serialNo\":\"taobaolive_COUPON_202506_220000123_407700539_2214101433551_online\"},\"fileName\":\"20250722_TB_天猫直播红包-财务详情_3_10438_119595_1753154519593_天猫直播红包-taobaolive_COUPON_202506_220000123_407700539_2214101433551_online-财务详情_20250601至20250630.zip\",\"fileType\":\"zip\",\"realEndDataRange\":\"2025-06-30 00:00:00\",\"realStartDataRange\":\"2025-06-01 00:00:00\",\"shopUniId\":119595},{\"blockId\":\"zk_sycm_0010_1\",\"processType\":62,\"content\":\"https://xiaozhi-client.oss-cn-hangzhou.aliyuncs.com/rpa/Temp/天猫直播红包-财务详情_10438_119595_1753158330376.zip\",\"dataRange\":\"2025-06\",\"dateType\":2,\"extendParam\":{\"serialNo\":\"taobaolive_COUPON_202506_220000123_407700539_2215010578451_online\"},\"fileName\":\"20250722_TB_天猫直播红包-财务详情_0_10438_119595_1753154519641_天猫直播红包-taobaolive_COUPON_202506_220000123_407700539_2215010578451_online-财务详情_20250601至20250630.zip\",\"fileType\":\"zip\",\"realEndDataRange\":\"2025-06-30 00:00:00\",\"realStartDataRange\":\"2025-06-01 00:00:00\",\"shopUniId\":119595},{\"blockId\":\"zk_sycm_0010_2\",\"content\":\"https://xiaozhi-client.oss-cn-hangzhou.aliyuncs.com/rpa/Temp/天猫直播红包-财务总览_10438_119595_1753161204422.csv\",\"dataRange\":\"2025-06\",\"processType\":62,\"dateType\":2,\"extendParam\":{\"serialNo\":\"taobaolive_COUPON_202506_220000123_407700539_2215010578451_online\"},\"fileName\":\"20250722_TB_天猫直播红包-财务总览_0_10438_119595_1753154545891_天猫直播红包-taobaolive_COUPON_202506_220000123_407700539_2215010578451_online-财务总览_20250601至20250630.csv\",\"fileType\":\"csv\",\"realEndDataRange\":\"2025-06-30 00:00:00\",\"realStartDataRange\":\"2025-06-01 00:00:00\",\"shopUniId\":119595},{\"blockId\":\"zk_sycm_0010_2\",\"content\":\"https://xiaozhi-client.oss-cn-hangzhou.aliyuncs.com/rpa/Temp/天猫直播红包-财务总览_10438_119595_1753161316996.csv\",\"dataRange\":\"2025-06\",\"dateType\":2,\"processType\":62,\"extendParam\":{\"serialNo\":\"taobaolive_COUPON_202506_220003004_407700539_2214101433551_online\"},\"fileName\":\"20250722_TB_天猫直播红包-财务总览_1_10438_119595_1753154545947_天猫直播红包-taobaolive_COUPON_202506_220003004_407700539_2214101433551_online-财务总览_20250601至20250630.csv\",\"fileType\":\"csv\",\"realEndDataRange\":\"2025-06-30 00:00:00\",\"realStartDataRange\":\"2025-06-01 00:00:00\",\"shopUniId\":119595},{\"blockId\":\"zk_sycm_0010_2\",\"content\":\"https://xiaozhi-client.oss-cn-hangzhou.aliyuncs.com/rpa/Temp/天猫直播红包-财务总览_10438_119595_1753158030813.csv\",\"dataRange\":\"2025-06\",\"dateType\":2,\"processType\":62,\"extendParam\":{\"serialNo\":\"taobaolive_COUPON_202506_220000007_407700539_2215010578451_online\"},\"fileName\":\"20250722_TB_天猫直播红包-财务总览_2_10438_119595_1753154545995_天猫直播红包-taobaolive_COUPON_202506_220000007_407700539_2215010578451_online-财务总览_20250601至20250630.csv\",\"fileType\":\"csv\",\"realEndDataRange\":\"2025-06-30 00:00:00\",\"realStartDataRange\":\"2025-06-01 00:00:00\",\"shopUniId\":119595},{\"blockId\":\"zk_sycm_0010_2\",\"content\":\"https://xiaozhi-client.oss-cn-hangzhou.aliyuncs.com/rpa/Temp/天猫直播红包-财务总览_10438_119595_1753162438437.csv\",\"dataRange\":\"2025-06\",\"dateType\":2,\"processType\":62,\"extendParam\":{\"serialNo\":\"taobaolive_COUPON_202506_220000123_407700539_2214101433551_online\"},\"fileName\":\"20250722_TB_天猫直播红包-财务总览_3_10438_119595_1753154546070_天猫直播红包-taobaolive_COUPON_202506_220000123_407700539_2214101433551_online-财务总览_20250601至20250630.csv\",\"fileType\":\"csv\",\"realEndDataRange\":\"2025-06-30 00:00:00\",\"realStartDataRange\":\"2025-06-01 00:00:00\",\"shopUniId\":119595}]}";        OriginalDataCallbackReq originalDataCallbackReq = JSON.parseObject(json, OriginalDataCallbackReq.class);

//        rawDataStorageService.rpaRawDataStorage(7922L);
        CategoryAnalyzeReq categoryAnalyzeReq = new CategoryAnalyzeReq();
        categoryAnalyzeReq.setCompanyId(10438L);
        categoryAnalyzeReq.setShopId(119595L);
        categoryAnalyzeReq.setSource(RawDataSourceEnum.ALIPAY);
        categoryAnalyzeReq.setDataRange(20250728);
        categoryAnalyzeReq.setDateType(DateTypeEnum.DAY);
        categoryAnalyzeService.categoryAnalyze(categoryAnalyzeReq);
    }

    @Test
    public void run16() {
//        FileAnalyzeRecordDO byId = fileAnalyzeRecordDao.getById(8155L);
        CategoryAnalyzeReq categoryAnalyzeReq = new CategoryAnalyzeReq();
        categoryAnalyzeReq.setCompanyId(10438L);
        categoryAnalyzeReq.setShopId(119595L);
        categoryAnalyzeReq.setSource(RawDataSourceEnum.ALIPAY);
        categoryAnalyzeReq.setDataRange(20250728);
        categoryAnalyzeReq.setDateType(DateTypeEnum.DAY);
        categoryAnalyzeService.categoryAnalyze(categoryAnalyzeReq);
//        rawDataStorageService.rpaRawDataStorage(8347L);
        //feature
       /* OriginalDataCallbackReq originalDataCallbackReq = new OriginalDataCallbackReq();
        originalDataCallbackReq.setCompanyId(10438L);
        originalDataCallbackReq.setProcessType(Math.toIntExact(RawDataSourceEnum.ALIPAY.getRpaSourceId()));
        OriginalDataParam originalDataParam = new OriginalDataParam();
        originalDataParam.setDateType(DateTypeEnum.DAY.getCode());
        originalDataParam.setDataRange("2025-07-28");
        originalDataParam.setRealStartDataRange(DateUtils.parse("2025-07-28 00:00:00"));
        originalDataParam.setRealEndDataRange(DateUtils.parse("2025-07-29 00:00:00"));
        originalDataParam.setFileType(FileTypeEnum.ZIP.getFileType());
        originalDataParam.setFileName("2025-07-01.csv");
        originalDataParam.setBlockId("tm_zfb_api_01");
        originalDataParam.setShopUniId(119595L);
        originalDataParam.setContent("http://wx-erpcrm.oss-cn-zhangjiakou.aliyuncs.com/account_checking_system/original_import/bill_243125_900207507_2025-07-28.csv.zip?Expires=**********&OSSAccessKeyId=LTAI5tFAfPeVWer1Szq2BTRR&Signature=4SduE/QkeKR0ng1/vLo6eK5GZ20=");
        originalDataCallbackReq.setFileList(Lists.newArrayList(originalDataParam));
        rawDataStorageService.rpaOriginalDataCallback(originalDataCallbackReq);*/
    }




}
