<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>原始账单上传</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#64748B',
                        success: '#10B981',
                        danger: '#EF4444',
                        warning: '#F59E0B',
                        info: '#3B82F6',
                    },
                    fontFamily: {
                        inter: ['Inter', 'system-ui', 'sans-serif'],
                    },
                    boxShadow: {
                        'card': '0 10px 15px -3px rgba(0, 0, 0, 0.05), 0 4px 6px -2px rgba(0, 0, 0, 0.03)',
                        'card-hover': '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
                    }
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .form-input-focus {
                @apply focus:ring-2 focus:ring-primary/50 focus:border-primary focus:outline-none;
            }
            .btn-primary {
                @apply bg-primary hover:bg-primary/90 text-white font-medium py-2 px-4 rounded-lg transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98] shadow-md hover:shadow-lg;
            }
            .btn-secondary {
                @apply bg-white border border-gray-300 hover:bg-gray-50 text-gray-700 font-medium py-2 px-4 rounded-lg transition-all duration-300;
            }
            .card {
                @apply bg-white rounded-xl shadow-card p-6 transition-all duration-300 hover:shadow-card-hover;
            }
            .form-label {
                @apply block text-sm font-medium text-gray-700 mb-1;
            }
            .form-input {
                @apply w-full px-3 py-2 border border-gray-300 rounded-lg transition-all duration-200 form-input-focus;
            }
            .form-select {
                @apply w-full px-3 py-2 border border-gray-300 rounded-lg appearance-none bg-white form-input-focus;
            }
            .form-error {
                @apply text-danger text-sm mt-1;
            }
            .input-valid {
                @apply border-success focus:ring-success/50 focus:border-success;
            }
            .input-invalid {
                @apply border-danger focus:ring-danger/50 focus:border-danger;
            }
            .fade-in {
                animation: fadeIn 0.5s ease-in-out;
            }
            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(10px); }
                to { opacity: 1; transform: translateY(0); }
            }
            .pulse {
                animation: pulse 1.5s infinite;
            }
            @keyframes pulse {
                0% { opacity: 1; }
                50% { opacity: 0.6; }
                100% { opacity: 1; }
            }
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen font-inter flex flex-col">
<div class="container mx-auto px-4 py-8 flex-grow flex flex-col">
    <header class="mb-8 fade-in">
        <div class="flex items-center mb-2">
            <i class="fa fa-file-text-o text-primary text-3xl mr-3"></i>
            <h1 class="text-[clamp(1.5rem,3vw,2.5rem)] font-bold text-gray-800">原始账单上传</h1>
        </div>
        <p class="text-gray-600">请上传您的账单文件，系统将自动处理并导入数据</p>
    </header>

    <main class="flex-grow flex flex-col">
        <div class="max-w-2xl mx-auto w-full">
            <div class="card mb-6 fade-in">
                <form id="uploadForm" class="space-y-5">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
                        <div>
                            <label for="billType" class="form-label">账单类型 <span class="text-danger">*</span></label>
                            <select id="billType" name="billType" class="form-select" required>
                                <option value="">请选择账单类型</option>
                                <option value="1">日账单</option>
                                <option value="2">月账单</option>
                                <option value="3">年账单</option>
                            </select>
                            <p id="billTypeError" class="form-error hidden">请选择账单类型</p>
                        </div>
                        <div>
                            <label for="fileType" class="form-label">文件类型 <span class="text-danger">*</span></label>
                            <select id="fileType" name="fileType" class="form-select" required>
                                <option value="">请选择文件类型</option>
                                <option value="xlsx">xlsx</option>
                                <option value="csv">csv</option>
                                <option value="xls">xls</option>
                                <option value="zip">zip</option>
                            </select>
                            <p id="fileTypeError" class="form-error hidden">请选择文件类型</p>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-5">
                        <div>
                            <label for="startTime" class="form-label">开始时间</label>
                            <input type="datetime-local" id="startTime" name="startTime" class="form-input">
                        </div>
                        <div>
                            <label for="endTime" class="form-label">结束时间</label>
                            <input type="datetime-local" id="endTime" name="endTime" class="form-input">
                        </div>
                    </div>

                    <div>
                        <label for="dataType" class="form-label">数据类型描述</label>
                        <input type="text" id="dataType" name="dataType" class="form-input" placeholder="请输入数据类型描述">
                    </div>

                    <div>
                        <label for="companyId" class="form-label">公司ID <span class="text-danger">*</span></label>
                        <input type="number" id="companyId" name="companyId" class="form-input" required placeholder="请输入公司ID">
                        <p id="companyIdError" class="form-error hidden">请输入有效的公司ID</p>
                    </div>

                    <div>
                        <label for="shopId" class="form-label">店铺ID <span class="text-danger">*</span></label>
                        <input type="text" id="shopId" name="shopId" class="form-input" required placeholder="请输入店铺ID">
                        <p id="shopIdError" class="form-error hidden">请输入有效的店铺ID</p>
                    </div>

                    <div>
                        <label class="form-label">上传文件 <span class="text-danger">*</span></label>
                        <div id="dropZone" class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-lg hover:border-primary transition-all duration-300 cursor-pointer bg-gray-50">
                            <div class="space-y-1 text-center">
                                <i class="fa fa-cloud-upload text-3xl text-gray-400 mb-2"></i>
                                <div class="flex text-sm text-gray-600 justify-center">
                                    <label for="file" class="relative cursor-pointer bg-white rounded-md font-medium text-primary hover:text-primary/80 focus-within:outline-none">
                                        <span>上传文件</span>
                                        <input id="file" name="file" type="file" class="sr-only" required accept=".xlsx,.csv,.xls,.zip">
                                    </label>
                                    <p class="pl-1">或拖放文件</p>
                                </div>
                                <p class="text-xs text-gray-500" id="fileInfo">
                                    支持 xlsx, csv, xls, zip 文件 (最大 50MB)
                                </p>
                            </div>
                        </div>
                        <p id="fileError" class="form-error hidden">请上传有效的文件</p>
                    </div>

                    <div class="flex justify-end space-x-3 pt-4">
                        <button type="button" id="resetBtn" class="btn-secondary">
                            <i class="fa fa-refresh mr-1"></i> 重置
                        </button>
                        <button type="submit" id="submitBtn" class="btn-primary">
                            <i class="fa fa-upload mr-1"></i> 提交
                        </button>
                    </div>
                </form>
            </div>

            <div id="messageBox" class="mb-6 p-4 rounded-lg hidden fade-in">
                <div id="successMessage" class="hidden bg-success/10 border border-success/30 text-success p-4 rounded-lg">
                    <div class="flex items-center">
                        <i class="fa fa-check-circle text-xl mr-2"></i>
                        <span id="successText">上传成功！</span>
                    </div>
                </div>
                <div id="errorMessage" class="hidden bg-danger/10 border border-danger/30 text-danger p-4 rounded-lg">
                    <div class="flex items-center">
                        <i class="fa fa-exclamation-circle text-xl mr-2"></i>
                        <span id="errorText">上传失败！</span>
                    </div>
                </div>
                <div id="loadingMessage" class="hidden bg-info/10 border border-info/30 text-info p-4 rounded-lg">
                    <div class="flex items-center">
                        <i class="fa fa-spinner fa-spin text-xl mr-2"></i>
                        <span>正在处理，请稍候...</span>
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>

<footer class="bg-gray-800 text-white py-6 mt-8">
    <div class="container mx-auto px-4 text-center">
        <p>© 2025 数据处理平台 | 原始账单上传系统</p>
    </div>
</footer>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const uploadForm = document.getElementById('uploadForm');
        const fileInput = document.getElementById('file');
        const fileInfo = document.getElementById('fileInfo');
        const messageBox = document.getElementById('messageBox');
        const successMessage = document.getElementById('successMessage');
        const errorMessage = document.getElementById('errorMessage');
        const loadingMessage = document.getElementById('loadingMessage');
        const successText = document.getElementById('successText');
        const errorText = document.getElementById('errorText');
        const resetBtn = document.getElementById('resetBtn');
        const dropZone = document.getElementById('dropZone');

        // 表单字段
        const formFields = {
            billType: document.getElementById('billType'),
            fileType: document.getElementById('fileType'),
            companyId: document.getElementById('companyId'),
            shopId: document.getElementById('shopId'),
            file: fileInput
        };

        // 错误消息元素
        const errorElements = {
            billType: document.getElementById('billTypeError'),
            fileType: document.getElementById('fileTypeError'),
            companyId: document.getElementById('companyIdError'),
            shopId: document.getElementById('shopIdError'),
            file: document.getElementById('fileError')
        };

        // 文件大小限制 (50MB)
        const MAX_FILE_SIZE = 50 * 1024 * 1024;

        // 动态设置日期时间选择器的默认值
        const now = new Date();
        const dateTimeStr = now.toISOString().slice(0, 16);
        document.getElementById('startTime').value = dateTimeStr;
        document.getElementById('endTime').value = dateTimeStr;

        // 添加表单验证
        function validateForm() {
            let isValid = true;

            Object.keys(formFields).forEach(field => {
                if (formFields[field].hasAttribute('required') && !formFields[field].value) {
                    showError(field, '此字段为必填项');
                    isValid = false;
                } else {
                    hideError(field);
                }
            });

            // 额外验证
            if (formFields.file.value) {
                const file = formFields.file.files[0];
                if (file.size > MAX_FILE_SIZE) {
                    showError('file', '文件大小不能超过 50MB');
                    isValid = false;
                }
            }

            return isValid;
        }

        // 显示错误
        function showError(field, message) {
            formFields[field].classList.add('input-invalid');
            errorElements[field].textContent = message;
            errorElements[field].classList.remove('hidden');
        }

        // 隐藏错误
        function hideError(field) {
            formFields[field].classList.remove('input-invalid');
            errorElements[field].classList.add('hidden');
        }

        // 显示消息
        function showMessage(type, message) {
            // 先隐藏所有消息
            [successMessage, errorMessage, loadingMessage].forEach(msg => {
                msg.classList.add('hidden');
            });

            messageBox.classList.remove('hidden');

            if (type === 'success') {
                successText.textContent = message;
                successMessage.classList.remove('hidden');
                messageBox.classList.remove('bg-danger/5', 'bg-info/5');
                messageBox.classList.add('bg-success/5');

                // 3秒后自动隐藏
                setTimeout(() => {
                    messageBox.classList.add('hidden');
                }, 3000);
            } else if (type === 'error') {
                errorText.textContent = message;
                errorMessage.classList.remove('hidden');
                messageBox.classList.remove('bg-success/5', 'bg-info/5');
                messageBox.classList.add('bg-danger/5');
            } else if (type === 'loading') {
                loadingMessage.classList.remove('hidden');
                messageBox.classList.remove('bg-success/5', 'bg-danger/5');
                messageBox.classList.add('bg-info/5');
            }
        }

        // 文件选择事件
        fileInput.addEventListener('change', function() {
            if (this.files.length > 0) {
                const file = this.files[0];
                const fileSize = (file.size / (1024 * 1024)).toFixed(2);
                fileInfo.textContent = `已选择: ${file.name} (${fileSize} MB)`;

                // 验证文件类型
                const fileExtension = file.name.split('.').pop().toLowerCase();
                const allowedExtensions = ['xlsx', 'csv', 'xls', 'zip'];

                if (!allowedExtensions.includes(fileExtension)) {
                    showError('file', '请上传有效的文件类型 (xlsx, csv, xls, zip)');
                } else {
                    hideError('file');
                }
            } else {
                fileInfo.textContent = '支持 xlsx, csv, xls, zip 文件 (最大 50MB)';
                hideError('file');
            }
        });

        // 拖放功能
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, preventDefaults, false);
        });

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        ['dragenter', 'dragover'].forEach(eventName => {
            dropZone.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, unhighlight, false);
        });

        function highlight() {
            dropZone.classList.add('border-primary', 'bg-primary/5');
        }

        function unhighlight() {
            dropZone.classList.remove('border-primary', 'bg-primary/5');
        }

        dropZone.addEventListener('drop', handleDrop, false);

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;

            if (files.length > 0) {
                fileInput.files = files;
                // 触发文件选择事件
                const event = new Event('change');
                fileInput.dispatchEvent(event);
            }
        }

        // 重置按钮事件
        resetBtn.addEventListener('click', function() {
            uploadForm.reset();
            fileInfo.textContent = '支持 xlsx, csv, xls, zip 文件 (最大 50MB)';

            // 隐藏所有错误和消息
            Object.keys(errorElements).forEach(field => {
                hideError(field);
            });

            messageBox.classList.add('hidden');
        });

        // 表单提交事件
        uploadForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            // 验证表单
            if (!validateForm()) {
                return;
            }

            showMessage('loading');

            const formData = new FormData(this);

            // 处理开始时间和结束时间格式
            ['startTime', 'endTime'].forEach(field => {
                const input = document.getElementById(field);
                if (input.value) {
                    // 将 YYYY-MM-DDTHH:mm 格式转换为 YYYY-MM-DD HH:mm:ss
                    const dateTime = new Date(input.value);
                    const formatted = formatDateTime(dateTime);
                    formData.set(field, formatted);
                } else {
                    formData.delete(field);
                }
            });

            try {
                // 实际项目中请替换为真实API地址
                const response = await fetch('http://pubaccount.superboss.cc/erpaccount/rawData/upload', {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error(`HTTP错误! 状态码: ${response.status}`);
                }

                const result = await response.json();

                // 根据响应结果处理
                if (result.result === 200) {
                    showMessage('success', '上传成功！');
                    uploadForm.reset();
                    fileInfo.textContent = '支持 xlsx, csv, xls, zip 文件 (最大 50MB)';
                } else {
                    showMessage('error', result.errorMessage || '上传失败，未知错误');
                }
            } catch (error) {
                showMessage('error', '网络错误，请稍后再试');
                console.error('上传失败:', error);
            }
        });

        // 格式化日期时间为 YYYY-MM-DD HH:mm:ss
        function formatDateTime(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');

            return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        }
    });
</script>
</body>
</html>