package com.raycloud.dmj.account.web;

import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.r2dbc.R2dbcAutoConfiguration;


@Slf4j
@SpringBootApplication(
        scanBasePackages = {
                "com.raycloud.dmj"
        },
        exclude = {
                DataSourceAutoConfiguration.class,
                R2dbcAutoConfiguration.class
        }
)
@EnableDubbo(
        scanBasePackages = {
                "com.raycloud.dmj"
        }
)
public class KmerpDataWebApplication {

    public static void main(String[] args) {
        SpringApplication.run(KmerpDataWebApplication.class, args);
        System.out.println("kmerp-account-web-boot started!");
    }

}
