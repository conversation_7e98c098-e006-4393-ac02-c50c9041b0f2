[{"sheet": "Sheet1", "sheetType": 2, "skipHead": 1, "headConfig": [{"head": "0", "headType": 1, "type": "String", "checker": "notnull", "value": "", "translator": "integer", "transValue": "", "key": "key1"}, {"head": "1", "headType": 1, "type": "int", "checker": "", "value": "", "translator": "integer", "transValue": "", "key": "key2"}, {"head": "13", "headType": 1, "type": "date", "checker": "notnull", "value": "", "translator": "date", "transValue": "YYYY-MM-DD HH:MM:SS", "key": "key3"}], "dataMapping": [{"tableName": "test", "dataType": 1, "tableFields": [{"tableField": "test", "formula": "key1", "type": 1, "sourceType": 1, "checker": "", "value": "", "translator": "", "transValue": ""}, {"tableField": "test2", "formula": "key1 + key2", "type": 1, "sourceType": 2, "checker": "", "value": "", "translator": "", "transValue": ""}, {"tableField": "test3", "formula": "key3", "type": 1, "sourceType": 1, "checker": "", "value": "", "translator": "", "transValue": ""}]}], "filter": [{"key": "key1", "type": "keyword", "value": "339"}]}]