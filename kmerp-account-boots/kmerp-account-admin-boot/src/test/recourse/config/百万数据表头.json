[{"sheet": "Sheet1", "sheetType": 2, "skipHead": 1, "headConfig": [{"head": "日期", "headType": 2, "type": "String", "checker": "notnull", "value": "", "translator": "integer", "transValue": "", "key": "key1"}, {"head": "成交花费(元)", "headType": 2, "type": "int", "checker": "", "value": "", "translator": "string", "transValue": "", "key": "key2"}, {"head": "曝光量", "headType": 2, "checker": "notnull", "value": "", "translator": "integer", "transValue": "", "key": "key3"}, {"head": "关注量", "headType": 2, "checker": "notnull", "value": "", "translator": "integer", "transValue": "", "key": "key4"}], "dataMapping": [{"tableName": "test", "dataType": 1, "tableFields": [{"tableField": "test1", "formula": "key1", "type": 1, "sourceType": 1, "checker": "", "value": "", "translator": "", "transValue": ""}, {"tableField": "test2", "formula": "key1 + key2", "type": 1, "sourceType": 2, "checker": "", "value": "", "translator": "", "transValue": ""}, {"tableField": "test3", "formula": "key3 + key4", "type": 1, "sourceType": 2, "checker": "", "value": "", "translator": "", "transValue": ""}]}], "filter": [{"key": "key1", "type": "keyword", "value": "2057999"}]}]