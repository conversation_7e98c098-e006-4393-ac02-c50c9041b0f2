[{"sheet": "支付宝账单数据", "sheetType": 2, "skipHead": 1, "headConfig": [{"head": "店铺名称", "headType": 2, "checker": "", "value": "", "translator": "string", "transValue": "", "key": "key1"}, {"head": "服务流水号", "headType": 2, "checker": "", "value": "", "translator": "", "transValue": "", "key": "key2"}, {"head": "业务流水号", "headType": 2, "checker": "notnull", "value": "", "translator": "string", "transValue": "", "key": "key3"}, {"head": "商户订单号", "headType": 2, "checker": "", "value": "", "translator": "string", "transValue": "", "key": "key4"}, {"head": "商户名称", "headType": 2, "checker": "", "value": "", "translator": "string", "transValue": "", "key": "key5"}, {"head": "发生时间", "headType": 2, "checker": "", "value": "", "translator": "string", "transValue": "", "key": "key6"}, {"head": "对方账号", "headType": 2, "checker": "", "value": "", "translator": "string", "transValue": "", "key": "key7"}, {"head": "收入金额(元)", "headType": 2, "checker": "", "value": "", "translator": "float", "transValue": "", "key": "key8"}, {"head": "支出金额(元)", "headType": 2, "checker": "", "value": "", "translator": "float", "transValue": "", "key": "key9"}, {"head": "账户余额(元)", "headType": 2, "checker": "", "value": "", "translator": "float", "transValue": "", "key": "key10"}, {"head": "交易渠道", "headType": 2, "checker": "", "value": "", "translator": "string", "transValue": "", "key": "key11"}, {"head": "业务类型", "headType": 2, "checker": "", "value": "", "translator": "string", "transValue": "", "key": "key12"}, {"head": "备注", "headType": 2, "checker": "", "value": "", "translator": "string", "transValue": "", "key": "key13"}, {"head": "业务描述", "headType": 2, "checker": "", "value": "", "translator": "string", "transValue": "", "key": "key14"}, {"head": "业务订单来源", "headType": 2, "checker": "", "value": "", "translator": "mapping", "transValue": "{\n  \"自有平台\": 1,\n  \"京东\": 2,\n  \"线下\": 3,\n  \"天猫\": 4,\n  \"拼多多\": 5,\n  \"淘宝\": 6\n}", "key": "key15"}, {"head": "业务源订单号", "headType": 2, "checker": "", "value": "", "translator": "string", "transValue": "", "key": "key16"}, {"head": "业务订单ID", "headType": 2, "checker": "", "value": "", "translator": "string", "transValue": "", "key": "key17"}, {"head": "下载时间", "headType": 2, "checker": "", "value": "", "translator": "date", "transValue": "YYYY-MM-DD HH:MM:SS", "key": "key18"}], "dataMapping": [{"tableName": "alipay_platform_original_bill_details", "dataType": 1, "batchNo": {"tableField": "batch_no", "formula": "&{batch_no_time}", "sourceType": 3, "bePutStorage": true, "translator": "MD5", "transValue": ""}, "uniqueKey": {"tableField": "biz_key", "formula": "&{business_order_no}_&{batch_no_time}", "sourceType": 3, "bePutStorage": true, "translator": "MD5", "transValue": ""}, "tableSourceConfig": "{\n    \"dataSourceCode\": \"DS001\",\n    \"dataSourceType\": \"MySQL\",\n    \"dateField\": \"batch_time\",\n    \"tableSplitFlag\": 1,\n    \"tableSplitConfig\": {\n        \"tableSplitNum\": \"10\",\n        \"tableSplitField\": \"id\",\n        \"tableSplitStrategy\": \"hash\"\n    }\n}", "tableFields": [{"tableField": "mall_name", "formula": "key1", "sourceType": 1, "bePutStorage": true, "checker": "", "value": "", "translator": "", "transValue": ""}, {"tableField": "batch_time", "formula": "key6", "sourceType": 1, "bePutStorage": true, "checker": "", "value": "", "translator": "date", "transValue": "YYYY-MM-DD HH:MM:SS"}, {"tableField": "batch_no_time", "formula": "key6", "bePutStorage": false, "sourceType": 1, "checker": "", "value": "", "translator": "dateFormat", "transValue": "YYYY-MM-DD hh:MM:SS,YYYY-MM-DD"}, {"tableField": "service_order_no", "formula": "key2", "bePutStorage": true, "sourceType": 1, "checker": "", "value": "", "translator": "", "transValue": ""}, {"tableField": "business_order_no", "formula": "key3", "bePutStorage": true, "sourceType": 1, "checker": "", "value": "", "translator": "", "transValue": ""}, {"tableField": "merchant_order_no", "formula": "key4", "bePutStorage": true, "sourceType": 1, "checker": "", "value": "", "translator": "", "transValue": ""}, {"tableField": "merchant_name", "formula": "key5", "bePutStorage": true, "sourceType": 1, "checker": "", "value": "", "translator": "", "transValue": ""}, {"tableField": "occur_time", "formula": "key6", "bePutStorage": true, "sourceType": 1, "checker": "", "value": "", "translator": "", "transValue": ""}, {"tableField": "opposite_account", "formula": "key7", "bePutStorage": true, "sourceType": 1, "checker": "", "value": "", "translator": "", "transValue": ""}, {"tableField": "income_amount", "formula": "key8 * 100", "sourceType": 2, "bePutStorage": true, "checker": "", "value": "", "translator": "integer", "transValue": "float"}, {"tableField": "expend_amount", "formula": "key9 * 100", "sourceType": 2, "bePutStorage": true, "checker": "", "value": "", "translator": "integer", "transValue": "float"}, {"tableField": "account_balance", "formula": "key10 * 100", "sourceType": 2, "bePutStorage": true, "checker": "", "value": "", "translator": "integer", "transValue": "float"}, {"tableField": "transaction_channel", "formula": "key11", "sourceType": 1, "bePutStorage": true, "checker": "", "value": "", "translator": "", "transValue": ""}, {"tableField": "business_type", "formula": "key12", "sourceType": 1, "bePutStorage": true, "checker": "", "value": "", "translator": "", "transValue": ""}, {"tableField": "remark", "formula": "key13", "sourceType": 1, "bePutStorage": true, "checker": "", "value": "", "translator": "", "transValue": ""}, {"tableField": "business_description", "formula": "key14", "sourceType": 1, "bePutStorage": true, "checker": "", "value": "", "translator": "", "transValue": ""}, {"tableField": "business_order_source", "formula": "key15", "sourceType": 1, "bePutStorage": true, "checker": "", "value": "", "translator": "", "transValue": ""}, {"tableField": "business_source_order_no", "formula": "key16", "sourceType": 1, "bePutStorage": true, "checker": "", "value": "", "translator": "", "transValue": ""}, {"tableField": "business_order_id", "formula": "key17", "sourceType": 1, "bePutStorage": true, "checker": "", "value": "", "translator": "", "transValue": ""}, {"tableField": "download_time", "formula": "key18", "bePutStorage": true, "sourceType": 1, "checker": "", "value": "", "translator": "", "transValue": ""}]}], "filter": []}]