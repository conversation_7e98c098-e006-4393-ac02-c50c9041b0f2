[{"sheet": "23", "sheetType": 1, "skipHead": 1, "headConfig": [{"head": "1", "headType": 2, "checker": "notnull", "value": "", "translator": "", "transValue": "", "key": "key1"}, {"head": "2m3m", "headType": 2, "checker": "", "value": "", "translator": "string", "transValue": "", "key": "key2"}, {"head": "5", "headType": 2, "type": "date", "checker": "notnull", "value": "", "translator": "integer", "transValue": "", "key": "key3"}], "dataMapping": [{"tableName": "test", "dataType": 1, "tableFields": [{"tableField": "test", "formula": "key1", "type": 1, "sourceType": 1, "checker": "", "value": "", "translator": "", "transValue": ""}, {"tableField": "test2", "formula": "key1 + key2", "type": 1, "sourceType": 2, "checker": "", "value": "", "translator": "", "transValue": ""}, {"tableField": "test3", "formula": "key3", "sourceType": 1, "checker": "", "value": "", "translator": "", "transValue": ""}]}], "filter": [{"key": "key1", "type": "keyword", "value": "339"}]}]