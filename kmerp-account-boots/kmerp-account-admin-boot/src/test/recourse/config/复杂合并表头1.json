[{"sheet": "Sheet1", "sheetType": 2, "skipHead": 4, "headConfig": [{"head": "父类商家_商品营业额_商家商品补贴_单票4", "headType": 2, "checker": "notnull", "value": "", "translator": "integer", "transValue": "", "key": "key1"}, {"head": "订单应收", "headType": 2, "checker": "notnull", "value": "", "translator": "integer", "transValue": "", "key": "key2"}, {"head": "父类商家_1", "headType": 2, "checker": "notnull", "value": "", "translator": "integer", "transValue": "", "key": "key3"}, {"head": "父类商家_商品营业额_商家商品补贴_单票3", "headType": 2, "checker": "notnull", "value": "", "translator": "mapping", "transValue": "{\"1\":\"开始\",\"2\":\"结束\"}", "key": "key4"}], "dataMapping": [{"tableName": "test", "dataType": 1, "batchNo": {"tableField": "test5", "formula": "key1", "sourceType": 1, "translator": "MD5", "transValue": ""}, "uniqueKey": {"tableField": "test6", "formula": "key1", "sourceType": 1, "translator": "MD5", "transValue": ""}, "tableFields": [{"tableField": "test", "formula": "key1", "sourceType": 1, "checker": "", "value": "", "translator": "string", "transValue": ""}, {"tableField": "test2", "formula": "key1 + key2", "sourceType": 2, "checker": "", "value": "", "translator": "", "transValue": ""}, {"tableField": "test3", "formula": "key3", "sourceType": 1, "checker": "", "value": "", "translator": "", "transValue": ""}, {"tableField": "test4", "formula": "key3 + key4", "sourceType": 2, "checker": "", "value": "", "translator": "", "transValue": ""}]}], "filter": [{"key": "key3", "type": "keyword", "value": "c63bae281505074c0460ced7330b5b75"}]}, {"sheet": "Sheet2", "sheetType": 2, "skipHead": 1, "headConfig": [{"head": "id", "headType": 2, "checker": "notnull", "value": "", "translator": "integer", "transValue": "", "key": "key1"}, {"head": "company_id", "headType": 2, "checker": "", "value": "", "translator": "integer", "transValue": "", "key": "key2"}, {"head": "created", "headType": 2, "checker": "notnull", "value": "", "translator": "", "transValue": "", "key": "key3"}], "dataMapping": [{"tableName": "test1", "dataType": 1, "tableFields": [{"tableField": "test1", "formula": "key1", "sourceType": 1, "checker": "", "value": "", "translator": "", "transValue": ""}, {"tableField": "test2", "formula": "key1 + key2", "sourceType": 2, "checker": "", "value": "", "translator": "", "transValue": ""}, {"tableField": "test3", "formula": "key3", "sourceType": 1, "checker": "", "value": "", "translator": "", "transValue": ""}]}, {"tableName": "test2", "dataType": 2, "tableFields": [{"tableField": "test3", "formula": "key3", "sourceType": 1, "checker": "", "value": "", "translator": "", "transValue": ""}]}], "filter": [{"key": "key1", "type": "keyword", "value": "339"}, {"key": "key2", "type": "keyword", "value": "339"}]}]