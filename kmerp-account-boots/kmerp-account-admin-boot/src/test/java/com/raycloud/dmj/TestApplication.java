package com.raycloud.dmj;

import com.raycloud.dmj.account.boot.KmerpDataAdminApplication;
import com.raycloud.dmj.account.core.base.dao.*;
import com.raycloud.dmj.account.core.cleancategory.service.AnalyzeRuleService;
import com.raycloud.dmj.account.core.cleancategory.strategy.CategoryAnalyzeSelect;
import com.raycloud.dmj.account.core.cleancategory.strategy.param.CategoryAnalyzeParam;
import com.raycloud.dmj.account.core.enums.DateTypeEnum;
import com.raycloud.dmj.account.core.enums.MonitorStatusEnum;
import com.raycloud.dmj.account.core.enums.RawDataSourceEnum;
import com.raycloud.dmj.account.core.rawdata.callback.RawDataStorageBatchCallback;
import com.raycloud.dmj.account.core.rawdata.callback.param.BatchCallbackContext;
import com.raycloud.dmj.account.core.rawdata.domains.*;
import com.raycloud.dmj.account.core.rawdata.handle.RawDataStorageHandle;
import com.raycloud.dmj.account.core.rawdata.service.RawDataStorageService;
import com.raycloud.dmj.table.api.plus.insert.Inserts;
import com.raycloud.dmj.table.api.plus.insert.api.InsertBatchPoint;
import com.raycloud.dmj.table.api.plus.insert.batch.InsertBatchSQL;
import com.raycloud.dmj.table.api.plus.utils.CollectionUtils;
import com.raycloud.dmj.utils.ConfigFileReadUtils;
import com.raycloud.readexcel.analyzeexcel.AnalyzeExcelFactory;
import com.raycloud.readexcel.context.ProcessingContext;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.io.File;
import java.util.*;

@SpringBootTest(classes = KmerpDataAdminApplication.class)
public class TestApplication {

    @Autowired
    private FileAnalyzeConfigDao fileAnalyzeConfigDao;

    @Autowired
    private RawDataStorageHandle rawDataStorageHandle;

    @Autowired
    private FileAnalyzeRecordDao fileAnalyzeRecordDao;


    @Autowired
    private FileAnalyzeTableConfigDao fileAnalyzeTableConfigDao;

    @Test
    public void contextLoads() {
        InsertBatchPoint insert = Inserts.insert()
                .into("table_x")
                .columns("id", "name", "dt", "score")
                .values(
                        CollectionUtils.combine(
                                Arrays.asList(1, "san", new Date(), 0),
                                Arrays.asList(2, "lee", new Date(), 0)
                        )
                )
                .batch()
                .comments("批量多条Insert测试");
        InsertBatchSQL sql = insert.toSql();
        String sqlPattern = sql.getSqlPattern();
        System.out.println(sqlPattern);

    }


    /**
     * 复杂合并表头和多sheet
     */
    @Test
    public void run1() {
        ProcessingContext<ImportStaffInfo> objectProcessingContext = new ProcessingContext<>();
        objectProcessingContext.setExcelConfig(ConfigFileReadUtils.readList("config/复杂合并表头1.json"));
        AnalyzeExcelFactory.<ImportStaffInfo>readLocalFileExcel(new File(TestApplication.class.getClassLoader().getResource("").getPath() + "/files/复杂表头和多sheet.xlsx"))
                .dataAnalyzeFailStop(false)
                .dataAnalyzeAloneSheet(false)
                .analyzeData(objectProcessingContext, new RawDataStorageBatchCallback(rawDataStorageHandle, new BatchCallbackContext())).doRead();

    }

    @Test
    public void run2() {
        ProcessingContext<ImportStaffInfo> objectProcessingContext = new ProcessingContext<>();
        objectProcessingContext.setExcelConfig(ConfigFileReadUtils.readList("config/简单表头1.json"));
        objectProcessingContext.setUserDefinedData(new ImportStaffInfo());

        AnalyzeExcelFactory.<ImportStaffInfo>readLocalFileExcel(new File(TestApplication.class.getClassLoader().getResource("").getPath() + "/files/简单表头.xlsx"))
                .dataAnalyzeFailStop(false)
                .dataAnalyzeAloneSheet(false)
                .analyzeData(objectProcessingContext, new RawDataStorageBatchCallback(rawDataStorageHandle, new BatchCallbackContext())).doRead();

    }

    @Resource
    private RawDataStorageService rawDataStorageService;;

    @Resource
    private CategoryAnalyzeRuleDao categoryAnalyzeRuleDao;
    @Resource
    private TmallAlipayRawBillDao tmallAlipayRawBillDao;

    @Resource
    private AnalyzeRuleService analyzeRuleService;

    @Test
    public void run12() {
        analyzeRuleService.initAnalyzeRule(10438L,12121125L);
    }

    @Resource
    private CategoryAnalyzeSelect categoryAnalyzeSelect;

    @Test
    public void run24() {

        CategoryAnalyzeParam categoryAnalyzeParam = new CategoryAnalyzeParam();
        categoryAnalyzeParam.setCompanyId(2L);
        categoryAnalyzeParam.setShopId(2L);
        categoryAnalyzeParam.setSource(RawDataSourceEnum.ALIPAY);
        categoryAnalyzeParam.setDateType(DateTypeEnum.DAY);
        categoryAnalyzeParam.setDataRange(20250516);
        categoryAnalyzeParam.setDataRange(20250516);
        categoryAnalyzeSelect.execute(categoryAnalyzeParam);

    }

    @Resource
    private SubCategoryDao subCategoryDao;


    /**
     * biz简单表头
     */
    @Test
    public void run8() {
        ProcessingContext<ImportStaffInfo> objectProcessingContext = new ProcessingContext<>();
        objectProcessingContext.setExcelConfig(ConfigFileReadUtils.readList("config/biz简单表头.json"));

        BatchCallbackContext batchCallbackContext = new BatchCallbackContext();
        Map<String, Long> sheetConfigIdMap = new HashMap<>();
        sheetConfigIdMap.put("支付宝账单数据_2", 1L);
        batchCallbackContext.setSheetConfigIdMap(sheetConfigIdMap);
        batchCallbackContext.setLastRecordId(1L);
        batchCallbackContext.setDateType(DateTypeEnum.MONTH.getCode());
        HashSet<String> handleBatchNoList = new HashSet<>();
        batchCallbackContext.setHandleBatchNoSet(handleBatchNoList);

        AnalyzeExcelFactory.<ImportStaffInfo>readLocalFileExcel(new File(TestApplication.class.getClassLoader().getResource("").getPath() + "/files/biz简单表头.xlsx"))
                .dataAnalyzeFailStop(false)
                .dataAnalyzeAloneSheet(false)
                .analyzeData(objectProcessingContext, new RawDataStorageBatchCallback(rawDataStorageHandle, batchCallbackContext)).doRead();
////        AnalyzeExcelResult analyzeExcelResult = AnalyzeExcelFactory.readOssExcel("http://oss-cn-zhangjiakou.aliyuncs.com", "LTAI5tFAfPeVWer1Szq2BTRR", "******************************", "wx-erpcrm", "chat_msg_media/wrhF1YCAAAZls2QZnLc4bneVKKTO-ksw/file/55b1a730-67bd-4c90-861e-0f64398d6bce_店铺授权表格第三季度.xlsx")
////                .dataAnalyzeFailStop(false)
////                .dataAnalyzeAloneSheet(true)
////                .analyzeData(objectProcessingContext)
////                .doRead();
//
    }


    @Autowired
    private FileOriginalDataMonitorDao fileOriginalDataMonitorDao;

    /**
     * 批量修改
     */
    @Test
    public void run() {


        HashMap<String, MonitorStatusEnum> codeStatusMap = new HashMap<>();
        codeStatusMap.put("5", MonitorStatusEnum.IMPORT_SUCCESS);
        codeStatusMap.put("4", MonitorStatusEnum.IMPORT_SUCCESS);
        codeStatusMap.put("3", MonitorStatusEnum.IMPORTING);

        HashSet<String> batchCodes = new HashSet<>();
        batchCodes.add("5");
        batchCodes.add("4");
        batchCodes.add("3");

    }

    /**
     * 批量修改
     */
    @Test
    public void run6() {


        HashMap<String, MonitorStatusEnum> codeStatusMap = new HashMap<>();
        codeStatusMap.put("5", MonitorStatusEnum.IMPORT_SUCCESS);
        codeStatusMap.put("4", MonitorStatusEnum.IMPORT_SUCCESS);
        codeStatusMap.put("3", MonitorStatusEnum.IMPORTING);

        HashSet<String> batchCodes = new HashSet<>();
        batchCodes.add("5");
        batchCodes.add("4");
        batchCodes.add("3");
        FileOriginalDataMonitorDO fileOriginalDataMonitorDO = new FileOriginalDataMonitorDO();
        fileOriginalDataMonitorDO.setBatchCode("6");
        fileOriginalDataMonitorDO.setDataStatus(MonitorStatusEnum.IMPORT_SUCCESS.getStatus());

        fileOriginalDataMonitorDao.insert(fileOriginalDataMonitorDO);
    }

    @Test
    public void run7() {


        HashMap<String, MonitorStatusEnum> codeStatusMap = new HashMap<>();
        codeStatusMap.put("5", MonitorStatusEnum.IMPORT_SUCCESS);
        codeStatusMap.put("4", MonitorStatusEnum.IMPORT_SUCCESS);
        codeStatusMap.put("3", MonitorStatusEnum.IMPORTING);

        HashSet<String> batchCodes = new HashSet<>();
        batchCodes.add("5");
        batchCodes.add("4");
        batchCodes.add("3");
        FileOriginalDataMonitorDO fileOriginalDataMonitorDO = new FileOriginalDataMonitorDO();
        fileOriginalDataMonitorDO.setId(1L);
        fileOriginalDataMonitorDO.setBatchCode("1");
        fileOriginalDataMonitorDO.setVersion(869);
        fileOriginalDataMonitorDO.setDataStatus(MonitorStatusEnum.IMPORTING.getStatus());
        fileOriginalDataMonitorDao.updateById(fileOriginalDataMonitorDO);
    }


    @Test
    public void run4() {
        FileAnalyzeRecordDO fileAnalyzeRecordDO = FileAnalyzeRecordDO.builder()
                .id(1L)
                .dataType("dataType")
                .dateType(1)
                .fileStartDataRange(new Date())
                .fileEndDataRange(new Date())
                .analyzeStatus(1)
                .errorMsg("errorMsg")
                .assignStartDataRange(new Date())
                .assignEndDataRange(new Date())
                .matchingConfigIds("matchingConfigIds")
                .created(new Date())
                .modified(new Date())
                .createBy("createBy").build();
        Long l = fileAnalyzeRecordDao.addFileAnalyzeRecord(fileAnalyzeRecordDO);
        System.out.println(l);
    }

    @Test
    public void run5() {
        List<FileAnalyzeConfigDO> fileAnalyzeConfigDOS = fileAnalyzeConfigDao.queryFileAnalyzeConfig("657821");
        System.out.println(fileAnalyzeConfigDOS);
        List<FileAnalyzeTableConfigDO> fileAnalyzeTableConfigDOS = fileAnalyzeTableConfigDao.queryFileAnalyzeTableConfig(Arrays.asList(1L, 2L));
        System.out.println(fileAnalyzeTableConfigDOS);
    }



    public static void main(String[] args) {
        // 构建规则：顶层为OR关系，包含两个条件



    }
}
