package com.raycloud.dmj.utils;

import com.raycloud.readexcel.domain.ExcelConfig;
import com.raycloud.readexcel.util.FileUtils;

import java.util.List;

import static com.raycloud.readexcel.util.ExcelConfigUtils.parseExcelMultipleConfig;
import static com.raycloud.readexcel.util.ExcelConfigUtils.parseExcelSingleConfig;

public class ConfigFileReadUtils {



    public static List<ExcelConfig> readList(String filePath){
        String s = FileUtils.readResourceFileToString(filePath);
        return parseExcelMultipleConfig(s);
    }

    public static List<ExcelConfig> readSinge(String filePath){
        String s = FileUtils.readFileToString(filePath);
        return parseExcelSingleConfig(s);
    }

}
