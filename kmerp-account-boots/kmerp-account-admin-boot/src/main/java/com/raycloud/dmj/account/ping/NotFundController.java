package com.raycloud.dmj.account.ping;

import org.springframework.context.annotation.Scope;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RequestMapping("/${web.dynamic.path}")
@RestController
@Scope("prototype")
public class NotFundController {

//    @RequestMapping(value = "/notFound")
//    @ResponseBody
//    public BaseResponse<Object> handleNotFound() {
//
//        return new FailResponse<>(HttpStatus.NOT_FOUND.value(), "您请求的资源不存在：404");
//    }
//
//    @RequestMapping(value = "/getPing")
//    @ResponseBody
//    public BaseResponse<Object> getPing() {
//        return new BaseResponse<>(HttpStatus.OK.value());
//    }

}
