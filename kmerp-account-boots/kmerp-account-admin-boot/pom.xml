<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.raycloud.dmj</groupId>
        <artifactId>kmerp-account-boots</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>kmerp-account-admin-boot</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <webXmlPath>src/main/erptj</webXmlPath>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.raycloud.dmj</groupId>
            <artifactId>kmerp-account-admin-frontend</artifactId>
        </dependency>
        <dependency>
            <groupId>com.raycloud.dmj</groupId>
            <artifactId>kmerp-account-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.raycloud.dmj</groupId>
            <artifactId>kmerp-account-profiles</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>gray</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <boot-name>kmerp-account-admin-boot-gray</boot-name>
                <boot-env>gray</boot-env>
            </properties>
            <build>
                <finalName>kmerp-account-admin-boot-gray</finalName>
                <resources>
                    <resource>
                        <directory>src/main/resources</directory>
                        <filtering>true</filtering>
                    </resource>
                </resources>
            </build>
        </profile>
        <profile>
            <id>gray2</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <boot-name>kmerp-account-admin-boot-gray2</boot-name>
                <boot-env>gray2</boot-env>
            </properties>
            <build>
                <finalName>kmerp-account-admin-boot-gray2</finalName>
                <resources>
                    <resource>
                        <directory>src/main/resources</directory>
                        <filtering>true</filtering>
                    </resource>
                </resources>
            </build>
        </profile>
        <profile>
            <id>gray3</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <webXmlPath>src/main/erptj</webXmlPath>
                <boot-name>kmerp-account-admin-boot-gray3</boot-name>
                <boot-env>gray3</boot-env>
                <mq-name-server>erp-mq-nameserver.superboss.cc:9877</mq-name-server>
            </properties>
            <build>
                <finalName>kmerp-account-admin-boot-gray3</finalName>
                <resources>
                    <resource>
                        <directory>src/main/resources</directory>
                        <filtering>true</filtering>
                        <excludes>
                            <exclude>config-cache/**</exclude>
                        </excludes>
                    </resource>
                    <!-- 复制gray3环境的Redis配置 -->
                    <resource>
                        <directory>src/main/resources/config-cache</directory>
                        <targetPath>config-cache</targetPath>
                        <includes>
                            <include>dynamic-redis-gray3.properties</include>
                        </includes>
                        <filtering>true</filtering>
                    </resource>
                </resources>
            </build>
        </profile>
        <profile>
            <id>test</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <boot-name>kmerp-account-admin-boot-gray3</boot-name>
                <boot-env>gray3</boot-env>
                <mq-name-server>localhost:9876</mq-name-server>
            </properties>
            <build>
                <finalName>kmerp-account-admin-boot-gray3</finalName>
                <resources>
                    <resource>
                        <directory>src/main/resources</directory>
                        <filtering>true</filtering>
                    </resource>
                </resources>
            </build>
        </profile>
        <profile>
            <id>gray4</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <boot-name>kmerp-account-admin-boot-gray4</boot-name>
                <boot-env>gray4</boot-env>
            </properties>
            <build>
                <finalName>kmerp-account-admin-boot-gray4</finalName>
                <resources>
                    <resource>
                        <directory>src/main/resources</directory>
                        <filtering>true</filtering>
                    </resource>
                </resources>
            </build>
        </profile>
        <profile>
            <id>gray5</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <boot-name>kmerp-account-admin-boot-gray5</boot-name>
                <boot-env>gray5</boot-env>
            </properties>
            <build>
                <finalName>kmerp-account-admin-boot-gray5</finalName>
                <resources>
                    <resource>
                        <directory>src/main/resources</directory>
                        <filtering>true</filtering>
                    </resource>
                </resources>
            </build>
        </profile>
        <profile>
            <id>gray6</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <boot-name>kmerp-account-admin-boot-gray6</boot-name>
                <boot-env>gray6</boot-env>
            </properties>
            <build>
                <finalName>kmerp-account-admin-boot-gray6</finalName>
                <resources>
                    <resource>
                        <directory>src/main/resources</directory>
                        <filtering>true</filtering>
                    </resource>
                </resources>
            </build>
        </profile>
        <profile>
            <id>vipprod</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <boot-name>kmerp-account-admin-boot-vipprod</boot-name>
                <boot-env>vipprod</boot-env>
            </properties>
            <build>
                <finalName>kmerp-account-admin-boot-vipprod</finalName>
                <resources>
                    <resource>
                        <directory>src/main/resources</directory>
                        <filtering>true</filtering>
                    </resource>
                </resources>
            </build>
        </profile>
        <profile>
            <id>preissue</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <boot-name>kmerp-account-admin-boot-preissue</boot-name>
                <boot-env>preissue</boot-env>
            </properties>
            <build>
                <finalName>kmerp-account-admin-boot-preissue</finalName>
                <resources>
                    <resource>
                        <directory>src/main/resources</directory>
                        <filtering>true</filtering>
                    </resource>
                </resources>
            </build>
        </profile>
        <profile>
            <id>preissue2</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <boot-name>kmerp-account-admin-boot-preissue2</boot-name>
                <boot-env>preissue2</boot-env>
            </properties>
            <build>
                <finalName>kmerp-account-admin-boot-preissue2</finalName>
                <resources>
                    <resource>
                        <directory>src/main/resources</directory>
                        <filtering>true</filtering>
                    </resource>
                </resources>
            </build>
        </profile>
        <profile>
            <id>prod</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <boot-name>kmerp-account-admin-boot-prod</boot-name>
                <boot-env>prod</boot-env>
                <spring.profiles.active>prod</spring.profiles.active>
                <mq-name-server>erp-mq-nameserver.superboss.cc:9877</mq-name-server>
            </properties>
            <build>
                <finalName>kmerp-account-admin-boot-prod</finalName>
                <resources>
                    <resource>
                        <directory>src/main/resources</directory>
                        <filtering>true</filtering>
                        <excludes>
                            <exclude>config-cache/**</exclude>
                        </excludes>
                    </resource>
                    <!-- 复制prod环境的Redis配置 -->
                    <resource>
                        <directory>src/main/resources/config-cache</directory>
                        <targetPath>config-cache</targetPath>
                        <includes>
                            <include>dynamic-redis-prod.properties</include>
                        </includes>
                        <filtering>true</filtering>
                    </resource>
                </resources>
            </build>
        </profile>
    </profiles>

    <build>
        <finalName>kmerp-account-admin-boot</finalName>
    </build>
</project>