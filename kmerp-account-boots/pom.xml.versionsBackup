<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.raycloud.dmj</groupId>
        <artifactId>kmerp-account-system</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <packaging>pom</packaging>
    <modules>
        <module>kmerp-account-admin-boot</module>
        <module>kmerp-account-export-boot</module>
        <module>kmerp-account-sync-boot</module>
        <module>kmerp-account-web-boot</module>
        <module>kmerp-account-dubbo-boot</module>
    </modules>
    <artifactId>kmerp-account-boots</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.raycloud.dmj</groupId>
            <artifactId>kmerp-account-profiles</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.raycloud.dmj.kmbi</groupId>
            <artifactId>kmbi-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>