package com.raycloud.dmj.account.infra.utils;

import org.slf4j.helpers.MessageFormatter;

public class StringFormatUtils {

    /**
     * 格式化字符串
     * demo: format("Hello, {}", "World");
     * @param format
     * @param args
     * @return
     */
    public static String format(String format, Object... args) {
        return MessageFormatter.arrayFormat(format, args).getMessage();
    }


    // 驼峰转下划线工具方法
    public static String humpToUnderline(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            if (Character.isUpperCase(c)) {
                sb.append("_").append(Character.toLowerCase(c));
            } else {
                sb.append(c);
            }
        }
        return sb.toString();
    }
}