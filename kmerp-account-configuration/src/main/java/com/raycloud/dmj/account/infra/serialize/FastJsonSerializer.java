package com.raycloud.dmj.account.infra.serialize;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.raycloud.dmj.account.infra.utils.StringUtils;

import static com.alibaba.fastjson2.JSONWriter.Feature.SortMapEntriesByKeys;

public class FastJsonSerializer {

    public static String toJSONString(Object obj) {
        return JSON.toJSONString(
                obj,
                SortMapEntriesByKeys
        );
    }

    public static JSONObject parseObject(String json) {
        if (StringUtils.isBlank(json)){
            return null;
        }
        return JSON.parseObject(json);
    }

    public static JSONArray parseArray(String json) {
        if (StringUtils.isBlank(json)){
            return null;
        }
        return JSON.parseArray(json);
    }

    public static <T> T parseObject(String json, Class<T> clazz) {
        if (StringUtils.isBlank(json)){
            return null;
        }
        return JSON.parseObject(json, clazz);
    }

    public static <T> T parseObject(String json, TypeReference<T> typeReference) {
        if (StringUtils.isBlank(json)){
            return null;
        }
        return JSON.parseObject(json, typeReference);
    }

    public static JSONObject toJsonObject(Object json) {
        return JSON.parseObject(toJSONString(json));
    }
}
