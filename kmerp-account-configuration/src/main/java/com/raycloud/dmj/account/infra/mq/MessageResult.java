package com.raycloud.dmj.account.infra.mq;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class MessageResult<T> implements Serializable {

    private Integer code;

    private String message;

    private T data;

    private Throwable exception;

    public static <T> MessageResult<T> success() {
        return success(null);
    }

    public static <T> MessageResult<T> success(T data) {
        MessageResult<T> result = new MessageResult<>();
        result.setCode(200);
        result.setMessage("消息推送成功");
        result.setData(data);
        return result;
    }

    public static <T> MessageResult<T> fail(Throwable e) {
        MessageResult<T> result = new MessageResult<>();
        result.setCode(500);
        result.setMessage("消息推送异常");
        result.setData(null);
        result.setException(e);
        return result;
    }
}
