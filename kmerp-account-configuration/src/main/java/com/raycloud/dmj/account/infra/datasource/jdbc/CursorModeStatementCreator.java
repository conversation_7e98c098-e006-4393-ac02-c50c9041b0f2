package com.raycloud.dmj.account.infra.datasource.jdbc;

import org.springframework.jdbc.core.PreparedStatementCreator;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * statement生成器
 */
public class CursorModeStatementCreator implements PreparedStatementCreator {

    private String sql;

    private Integer fetchSize = 50;

    public CursorModeStatementCreator(String sql) {
        this.sql = sql;
    }

    public CursorModeStatementCreator(String sql, Integer fetchSize) {
        this.sql = sql;
        this.fetchSize = fetchSize;
    }

    @Override
    public PreparedStatement createPreparedStatement(Connection con) throws SQLException {
        // 如果是pg的话，默认就是这些参数
        PreparedStatement preparedStatement = con.prepareStatement(
                sql,
                ResultSet.TYPE_FORWARD_ONLY,
                ResultSet.CONCUR_READ_ONLY
        );
        // 最长时间2小时
        preparedStatement.setQueryTimeout(7200);
        preparedStatement.setFetchDirection(ResultSet.FETCH_FORWARD);
        String driverName = con.getMetaData().getDriverName().toLowerCase();
        if (driverName.contains("mysql")) {
            // mysql特殊一点，Integer.MIN_VALUE 时开启流式模式，mysql流式要比游标性能好一些
            preparedStatement.setFetchSize(Integer.MIN_VALUE);
        } else if (driverName.contains("postgresql")) {
            // 开启事务，PostgreSQL必须这么做，如果不开启事务不会使用游标方式，并且查询会消耗大量的JVM内存，最终导致OOM
//            if (con.getAutoCommit()){
//                con.setAutoCommit(false);
//                ConnectionHolder.set(con);
//            }
            preparedStatement.setFetchSize(fetchSize);
        } else {
            // 强制流模式
            preparedStatement.setFetchSize(Integer.MIN_VALUE);
        }
        return preparedStatement;
    }
}
