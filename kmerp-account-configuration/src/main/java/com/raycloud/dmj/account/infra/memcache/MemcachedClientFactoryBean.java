package com.raycloud.dmj.account.infra.memcache;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * 解决原版Memcache初始化bug
 */
@Slf4j
@Component("memcachedClientFactoryBean")
public class MemcachedClientFactoryBean extends AbstractMemcachedClientFactoryBean implements ApplicationContextAware, InitializingBean {

    @Value("${cache.diamond.config.new:ocs.m-vy13dc3d92ed4504_erp}")
    private String cacheDiamondConfig;


    @Override
    public void afterPropertiesSet() throws Exception {
        initMemcachedClient(cacheDiamondConfig);
    }

}
