package com.raycloud.dmj.account.infra.memcache;

import net.rubyeye.xmemcached.MemcachedClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
public class MemcachedConfiguration {

    @Bean(name = "memcachedClientNew")
    @Primary
    public MemcachedClient memcachedClientNew(MemcachedClientFactoryBean factoryBean) throws Exception {
        return factoryBean.getObject();
    }
}
