package com.raycloud.dmj.account.infra.mq.local;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.raycloud.dmj.account.infra.mq.MessageResult;
import com.raycloud.dmj.services.utils.ClueIdUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.integration.dsl.IntegrationFlow;
import org.springframework.integration.dsl.IntegrationFlows;
import org.springframework.integration.handler.GenericHandler;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.MessageHeaders;
import org.springframework.util.ObjectUtils;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Slf4j
@Configuration
public class LocalMessageQueueConfiguration implements ApplicationListener<ContextRefreshedEvent> {

    /**
     * key: event class name
     * value : listeners
     */
    private Map<String, List<LocalEventListener>> localEventListenerMap = new HashMap<>();

    private static final ExecutorService executor = new ThreadPoolExecutor(
            16,
            16,
            60,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(1000),
            new ThreadFactoryBuilder().setNameFormat("local-event-thread-%d").setDaemon(false).build(),
            new ThreadPoolExecutor.AbortPolicy()
    );

    @Bean(name = "localSyncMessageChannel")
    public MessageChannel localSyncMessageChannel() {
        DirectChannel channel = new DirectChannel();
        channel.setFailover(true);
        channel.setMaxSubscribers(Integer.MAX_VALUE);
        return channel;
    }

    @Bean
    public IntegrationFlow localSyncMessageFlow(
            MessageChannel localSyncMessageChannel
    ) {
        return IntegrationFlows
                .from(localSyncMessageChannel)
                .handle(new GenericHandler<LocalEvent<?>>() {
                    @Override
                    public MessageResult<Object> handle(LocalEvent payload, MessageHeaders headers) {
                        try {
                            send(payload);
                            return MessageResult.success();
                        } catch (Throwable e) {
                            log.error("local消息推送异常", e);
                            return MessageResult.fail(e);
                        }
                    }
                })
                .get();
    }


    public void send(LocalEvent<?> localEvent) {
        String name = localEvent.getClass().getName();
        List<LocalEventListener> listeners = localEventListenerMap.get(name);
        if (ObjectUtils.isEmpty(listeners)) {
            return;
        }
        if (localEvent.async()) {
            for (LocalEventListener listener : listeners) {
                executor.execute(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            ClueIdUtil.setClueId(localEvent.clueId());
                            listener.onEvent(localEvent);
                        } catch (Throwable e) {
                            log.error("异步执行本地消息异常", e);
                        } finally {
                            ClueIdUtil.removeClueId();
                        }
                    }
                });
            }
        } else {
            for (LocalEventListener listener : listeners) {
                listener.onEvent(localEvent);
            }
        }
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        ApplicationContext context = event.getApplicationContext();
        Map<String, LocalEventListener> beansOfType = context.getBeansOfType(LocalEventListener.class);
        if (ObjectUtils.isEmpty(beansOfType)) {
            return;
        }
        for (Map.Entry<String, LocalEventListener> entry : beansOfType.entrySet()) {
            String key = entry.getKey();
            LocalEventListener listener = entry.getValue();
            log.info("local event listener register : {}", key);

            Type[] interfaces = listener.getClass().getGenericInterfaces();
            if (ObjectUtils.isEmpty(interfaces)) {
                continue;
            }

            for (Type type : interfaces) {
                if (type instanceof ParameterizedType) {
                    ParameterizedType parameterizedType = (ParameterizedType) type;
                    if (LocalEventListener.class.equals(parameterizedType.getRawType())) {
                        Type[] typeArgs = parameterizedType.getActualTypeArguments();
                        Type typeArg = typeArgs[0];
                        String typeName = typeArg.getTypeName();
                        List<LocalEventListener> listeners = localEventListenerMap.computeIfAbsent(typeName, k -> new ArrayList<>());
                        listeners.add(listener);
                        break;
                    }
                }
            }
        }

    }
}
