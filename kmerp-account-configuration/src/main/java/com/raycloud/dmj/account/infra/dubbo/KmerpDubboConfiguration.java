package com.raycloud.dmj.account.infra.dubbo;

import com.raycloud.commons.xserialnumber.api.IGUIDServiceDubbo;
import com.raycloud.commons.xserialnumber.api.XSerialNumberService;
import com.raycloud.dmj.data.chessboard.ExportApi;
import com.raycloud.dmj.data.chessboard.MonitorApi;
import com.raycloud.dmj.domain.Configurable;
import com.raycloud.dmj.services.account.ICompanyService;
import com.raycloud.dmj.services.account.ICustomPrivilegeService;
import com.raycloud.dmj.services.account.IPrivilegeService;
import com.raycloud.dmj.services.account.IStaffService;
import com.raycloud.dmj.services.basis.IDownloadCenterService;
import com.raycloud.dmj.services.basis.IIndexDubboService;
import com.raycloud.dmj.services.user.IShopService;
import com.raycloud.dmj.services.user.IUserService;
import com.raycloud.secret_api.api.SecretRequest;
import lombok.Getter;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@EnableConfigurationProperties(KmerpDubboProperties.class)
@Configuration
public class KmerpDubboConfiguration {

    @DubboReference(version = "${kmerp.dubbo.version.base}")
    private IDownloadCenterService downloadCenterService;

    @DubboReference(version = "${kmerp.dubbo.version.base}")
    private IPrivilegeService privilegeService;

    @DubboReference(version = "${kmerp.dubbo.version.base}")
    private ICustomPrivilegeService customPrivilegeService;

    @DubboReference(version = "${kmerp.dubbo.version.base}")
    private ICompanyService companyService;

    @DubboReference(version = "${kmerp.dubbo.version.base}")
    private IStaffService staffService;

    @DubboReference(version = "${kmerp.dubbo.version.base}")
    private IUserService userService;

    @DubboReference(version = "${kmerp.dubbo.version.base}")
    private IShopService shopService;

    @DubboReference(version = "${kmerp.dubbo.version.base}")
    private IIndexDubboService indexDubboService;

    @DubboReference(version = "${kmerp.dubbo.version.base}")
    private Configurable configurable;

    @DubboReference(version = "${kmerp.dubbo.version.chessboard}")
    private ExportApi exportApi;

    @DubboReference(version = "${kmerp.dubbo.version.chessboard}")
    private MonitorApi monitorApi;

    /**
     * 加解密服务
     */
    @DubboReference(version = "")
    private SecretRequest secretRequest;

    /**
     * 短号算法
     */
    @DubboReference(version = "1.0",group = "prod")
    private XSerialNumberService xSerialNumberService;

    /**
     * 雪花算法
     */
    @DubboReference(version = "1.0",group = "guid")
    private IGUIDServiceDubbo guidService;
}
