package com.raycloud.dmj.account.infra.repository.base;

import com.raycloud.dmj.account.infra.common.BizException;
import com.raycloud.dmj.account.infra.common.KmerpRuntime;
import com.raycloud.dmj.account.infra.datasource.KmerpDatasourceConfiguration;
import com.raycloud.dmj.account.infra.datasource.jdbc.BatchResultSetExtractor;
import com.raycloud.dmj.account.infra.datasource.jdbc.CursorModeStatementCreator;
import com.raycloud.dmj.data.export.core.ICallback;
import com.raycloud.dmj.kmbi.connector.jdbc.JdbcConnector;
import com.raycloud.dmj.table.api.plus.common.base.SQL;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.*;
import org.springframework.transaction.TransactionStatus;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;
import java.util.function.Function;

@Slf4j
@RequiredArgsConstructor
@Configuration
public class DbQuery {

    private final KmerpDatasourceConfiguration kmerpDatasourceConfiguration;

    public KmerpDatasourceConfiguration getDatasource() {
        return kmerpDatasourceConfiguration;
    }

    public List<Map<String, Object>> queryList(JdbcConnector connector, SQL sql) {
        if (ObjectUtils.isEmpty(sql) || ObjectUtils.isEmpty(sql.getSqlCode())) {
            return null;
        }
        if (connector == null) {
            throw new BizException("connector is null");
        }

        executeBefore(sql, connector.connectorId());
        List<Object> args = sql.getArgs();
        if (CollectionUtils.isEmpty(args)) {
            return connector.getJdbcTemplate().queryForList(sql.getSqlCode());
        } else {
            return connector.getJdbcTemplate()
                    .queryForList(
                            sql.getSqlCode(),
                            args.toArray(new Object[0])
                    );
        }
    }

    public List<Map<String, Object>> queryList(String connectorId, SQL sql) {
        if (ObjectUtils.isEmpty(sql) || ObjectUtils.isEmpty(sql.getSqlCode())) {
            return null;
        }
        JdbcConnector connector = getDatasource().getConnector(connectorId);
        if (connector == null) {
            throw new BizException("connector not found:" + connectorId);
        }
        return queryList(connector, sql);
    }

    public Map<String, Object> queryOne(JdbcConnector connector, SQL sql) {
        if (ObjectUtils.isEmpty(sql) || ObjectUtils.isEmpty(sql.getSqlCode())) {
            return null;
        }
        if (connector == null) {
            throw new BizException("connector is null");
        }
        executeBefore(sql, connector.connectorId());
        List<Object> args = sql.getArgs();
        List<Map<String, Object>> result = null;
        if (CollectionUtils.isEmpty(args)) {
            result = connector.getJdbcTemplate().queryForList(sql.getSqlCode());
        } else {
            result = connector.getJdbcTemplate()
                    .queryForList(
                            sql.getSqlCode(),
                            args.toArray(new Object[0])
                    );
        }
        if (CollectionUtils.isEmpty(result)) {
            return null;
        }
        return result.get(0);
    }


    public Map<String, Object> queryOne(String connectorId, SQL sql) {
        if (ObjectUtils.isEmpty(sql) || ObjectUtils.isEmpty(sql.getSqlCode())) {
            return null;
        }
        JdbcConnector connector = getDatasource().getConnector(connectorId);
        if (connector == null) {
            throw new BizException("connector not found:" + connectorId);
        }
        return queryOne(connector, sql);
    }

    public <T> List<T> querySingleList(JdbcConnector connector, SQL sql, Class<T> clazz) {
        if (ObjectUtils.isEmpty(sql) || ObjectUtils.isEmpty(sql.getSqlCode())) {
            return null;
        }
        if (clazz == null) {
            throw new BizException("entity class is null");
        }
        if (connector == null) {
            throw new BizException("connector is null");
        }
        executeBefore(sql, connector.connectorId());
        List<Object> args = sql.getArgs();
        if (CollectionUtils.isEmpty(args)) {
            return connector.getJdbcTemplate()
                    .queryForList(
                            sql.getSqlCode(),
                            clazz
                    );
        } else {
            return connector.getJdbcTemplate()
                    .queryForList(
                            sql.getSqlCode(),
                            clazz,
                            sql.getArgs().toArray(new Object[0])
                    );
        }
    }

    public <T> List<T> querySingleList(String connectorId, SQL sql, Class<T> clazz) {
        if (ObjectUtils.isEmpty(sql) || ObjectUtils.isEmpty(sql.getSqlCode())) {
            return null;
        }
        if (clazz == null) {
            throw new BizException("entity class is null");
        }
        JdbcConnector connector = getDatasource().getConnector(connectorId);
        if (connector == null) {
            throw new BizException("connector not found:" + connectorId);
        }
        return querySingleList(connector, sql, clazz);
    }


    public <T> List<T> queryList(JdbcConnector connector, SQL sql, Class<T> clazz) {
        if (ObjectUtils.isEmpty(sql) || ObjectUtils.isEmpty(sql.getSqlCode())) {
            return null;
        }
        if (clazz == null) {
            throw new BizException("entity class is null");
        }
        if (connector == null) {
            throw new BizException("connector is null");
        }
        executeBefore(sql, connector.connectorId());
        List<Object> args = sql.getArgs();
        if (CollectionUtils.isEmpty(args)) {
            return connector.getJdbcTemplate()
                    .query(
                            sql.getSqlCode(),
                            new BeanPropertyRowMapper<>(clazz)
                    );
        } else {
            return connector.getJdbcTemplate()
                    .query(
                            sql.getSqlCode(),
                            new BeanPropertyRowMapper<>(clazz),
                            sql.getArgs().toArray(new Object[0])
                    );
        }
    }

    public <T> List<T> queryList(String connectorId, SQL sql, Class<T> clazz) {
        if (ObjectUtils.isEmpty(sql) || ObjectUtils.isEmpty(sql.getSqlCode())) {
            return null;
        }
        if (clazz == null) {
            throw new BizException("entity class is null");
        }
        JdbcConnector connector = getDatasource().getConnector(connectorId);
        if (connector == null) {
            throw new BizException("connector not found:" + connectorId);
        }
        return queryList(connector, sql, clazz);
    }

    public <T> T queryOne(JdbcConnector connector, SQL sql, Class<T> clazz) {
        if (ObjectUtils.isEmpty(sql) || ObjectUtils.isEmpty(sql.getSqlCode())) {
            return null;
        }
        if (clazz == null) {
            throw new BizException("entity class is null");
        }
        if (connector == null) {
            throw new BizException("connector is null");
        }
        executeBefore(sql, connector.connectorId());
        List<Object> args = sql.getArgs();
        List<T> result = null;
        if (CollectionUtils.isEmpty(args)) {
            result = connector.getJdbcTemplate().query(
                    sql.getSqlCode(),
                    new BeanPropertyRowMapper<>(clazz)
            );
        } else {
            result = connector.getJdbcTemplate()
                    .query(
                            sql.getSqlCode(),
                            new BeanPropertyRowMapper<>(clazz),
                            sql.getArgs().toArray(new Object[0])
                    );
        }
        if (CollectionUtils.isEmpty(result)) {
            return null;
        }
        return result.get(0);
    }

    public <T> T queryOne(String connectorId, SQL sql, Class<T> clazz) {
        if (ObjectUtils.isEmpty(sql) || ObjectUtils.isEmpty(sql.getSqlCode())) {
            return null;
        }
        if (clazz == null) {
            throw new BizException("entity class is null");
        }
        JdbcConnector connector = getDatasource().getConnector(connectorId);
        if (connector == null) {
            throw new BizException("connector not found:" + connectorId);
        }
        return queryOne(connector, sql, clazz);
    }

    public void execute(JdbcConnector connector, String sql) {
        if (ObjectUtils.isEmpty(sql)) {
            return;
        }
        if (connector == null) {
            throw new BizException("connector is null");
        }
        executeBefore(sql, connector.connectorId());
        connector.getJdbcTemplate().execute(sql);
    }

    public void execute(String connectorId, String sql) {
        if (ObjectUtils.isEmpty(sql)) {
            return;
        }
        JdbcConnector connector = getDatasource().getConnector(connectorId);
        if (connector == null) {
            throw new BizException("connector not found:" + connectorId);
        }
        execute(connector, sql);
    }


    public void cursor(String connectorId, SQL sql, ICallback<List<Map<String, Object>>> callback) {
        if (ObjectUtils.isEmpty(connectorId)) {
            throw new BizException("connectorId is null");
        }
        if (ObjectUtils.isEmpty(sql) || ObjectUtils.isEmpty(sql.getSqlCode())) {
            return;
        }

        KmerpDatasourceConfiguration datasource = getDatasource();
        JdbcConnector connector = datasource.getConnector(connectorId);
        if (connector == null) {
            throw new BizException("connector not found:" + connectorId);
        }

        executeBefore(sql, connector.connectorId());

        JdbcTemplate jdbcTemplate = connector.getJdbcTemplate();
        // 游标查询需要开事务
        datasource.doTransaction(connector, new Function<TransactionStatus, Object>() {
            @Override
            public Object apply(TransactionStatus transactionStatus) {
                PreparedStatementSetter setter = null;
                if (!ObjectUtils.isEmpty(sql.getSqlCode())) {
                    setter = new ArgumentPreparedStatementSetter(sql.getArgs().toArray(new Object[0]));
                }

                jdbcTemplate.query(
                        new CursorModeStatementCreator(sql.getSqlCode()),
                        setter,
                        new BatchResultSetExtractor<>(
                                new ColumnMapRowMapper(),
                                callback,
                                1000
                        )
                );

                return true;
            }
        });
    }


    private void executeBefore(Object sql, String connectorId) {
        if (KmerpRuntime.printSQL()) {
            System.out.println();
            System.out.println("ConnectorId: " + connectorId);
            if (sql instanceof SQL) {
                System.out.println(sql);
            } else {
                System.out.println("SQL: " + sql);
            }
            System.out.println("----------------------------------------------------------");
        }
    }
}