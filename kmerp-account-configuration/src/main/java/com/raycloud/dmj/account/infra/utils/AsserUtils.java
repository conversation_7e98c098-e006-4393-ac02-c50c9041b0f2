package com.raycloud.dmj.account.infra.utils;

import com.raycloud.dmj.account.infra.common.BusinessException;
import com.raycloud.dmj.account.infra.common.ErrorCodeEnum;
import org.springframework.util.StringUtils;

import java.util.Collection;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class AsserUtils {

    // 禁止实例化
    private AsserUtils() {}

    /**
     * 检查对象是否为null，为null则抛出异常
     * @param obj 待检查对象
     * @param message 错误描述
     */
    public static void notNull(Object obj,String message) {
        if (obj == null) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR.getCode(), message);
        }
    }

    /**
     * 检查字符串是否为空，为空则抛出异常
     * @param str 待检查字符串
     * @param message 错误码枚举
     */
    public static void notEmpty(String str, String message) {
        if (str == null || str.trim().isEmpty()) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR.getCode(), message);
        }
    }

    /**
     * 检查字符串是否包含文本，不包含则抛出异常
     * @param text 待检查字符串
     * @param message
     */
    public static void hasText(String text, String message) {
        if (!StringUtils.hasText(text)) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR.getCode(), message);
        }
    }

    /**
     * 检查集合是否为空，为空则抛出异常
     * @param collection 待检查集合
     * @param message 错误码枚举
     */
    public static void notEmpty(Collection<?> collection, String message) {
        if (collection == null || collection.isEmpty()) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR.getCode(), message);
        }
    }

    /**
     * 检查Map是否为空，为空则抛出异常
     * @param map 待检查Map
     * @param message 错误码枚举
     */
    public static void notEmpty(Map<?, ?> map, String message) {
        if (map == null || map.isEmpty()) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR.getCode(), message);
        }
    }

    /**
     * 检查数组是否为空，为空则抛出异常
     * @param array 待检查数组
     * @param message 错误码枚举
     */
    public static void notEmpty(Object[] array, String message) {
        if (array == null || array.length == 0) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR.getCode(), message);
        }
    }

    /**
     * 检查条件是否为真，为假则抛出异常
     * @param condition 待检查条件
     * @param message 错误码枚举
     */
    public static void isTrue(boolean condition, String message) {
        if (!condition) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR.getCode(), message);
        }
    }

    /**
     * 检查两个对象是否相等，不相等则抛出异常
     * @param obj1 第一个对象
     * @param obj2 第二个对象
     * @param message 错误码枚举
     */
    public static void equals(Object obj1, Object obj2, String message) {
        if (!Objects.equals(obj1, obj2)) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR.getCode(), message);
        }
    }

    /**
     * 检查字符串长度是否在指定范围内
     * @param str 待检查字符串
     * @param min 最小长度
     * @param max 最大长度
     * @param message 错误码枚举
     */
    public static void length(String str, int min, int max, String message) {
        if (str == null) {
            if (min > 0) {
                throw new BusinessException(ErrorCodeEnum.PARAM_ERROR.getCode(), message);
            }
        } else {
            int length = str.length();
            if (length < min || length > max) {
                throw new BusinessException(ErrorCodeEnum.PARAM_ERROR.getCode(), message);
            }
        }
    }

    /**
     * 检查数字是否在指定范围内
     * @param num 待检查数字
     * @param min 最小值
     * @param max 最大值
     * @param message 错误码枚举
     */
    public static void range(Number num, double min, double max, String message) {
        if (num == null) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR.getCode(), message);
        }
        double value = num.doubleValue();
        if (value < min || value > max) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR.getCode(), message);
        }
    }
}