package com.raycloud.dmj.account.infra.repository;

import com.raycloud.dmj.account.infra.datasource.KmerpDatasourceConfiguration;
import com.raycloud.dmj.account.infra.repository.base.DbQuery;
import com.raycloud.dmj.account.infra.repository.base.DbRepository;
import com.raycloud.dmj.account.infra.repository.cache.MemoryRepositoryStorage;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.kmbi.connector.jdbc.JdbcConnector;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.TransactionStatus;

import java.util.function.Function;

@Slf4j
@RequiredArgsConstructor
@Configuration
public class Repositories {

    private final MemoryRepositoryStorage dbRepositoryStorage = new MemoryRepositoryStorage();

    private final KmerpDatasourceConfiguration kmerpDatasourceConfiguration;

    @Getter
    private final DbQuery dbQuery;

    public KmerpDatasourceConfiguration getDatasource() {
        return kmerpDatasourceConfiguration;
    }

    @SuppressWarnings("unchecked")
    public DbRepository getRepository(String connectorId) {
        JdbcConnector connector = kmerpDatasourceConfiguration.getConnector(connectorId);
        DbRepository repository = (DbRepository) dbRepositoryStorage.get(
                connectorId,
                DbRepository.class,
                () -> new DbRepository(kmerpDatasourceConfiguration, dbQuery, connector)
        );
        repository.setConnector(connector);
        return repository;
    }

    public DbRepository getIndexRepository() {
        return getRepository(Connectors.getIndexMysql());
    }

    public DbRepository getTradeRepository(Staff staff) {
        return getRepository(Connectors.getTradeMysql(staff));
    }

    public DbRepository getTradeAdbPgRepository(Staff staff) {
        return getRepository(Connectors.getTradePg(staff));
    }

    public DbRepository getReportMainRepository() {
        return getRepository(Connectors.getReportMain());
    }

    public DbRepository getReportRepository(Staff staff) {
        return getRepository(Connectors.getReportMysql(staff));
    }

    public DbRepository getReportAdbPgRepository(Staff staff) {
        return getRepository(Connectors.getReportPg(staff));
    }

    public DbRepository getReportLindormRepository(Staff staff) {
        return getRepository(Connectors.getReportLindorm(staff));
    }

    public DbRepository getPurchaseRepository(Staff staff) {
        return getRepository(Connectors.getPurchaseMysql(staff));
    }

    public DbRepository getPurchaseAdbMySQLRepository(Staff staff) {
        return getRepository(Connectors.getPurchaseAdbMysql(staff));
    }

    public <T> T doTransaction(String connectorId, Function<TransactionStatus, T> function) {
        return kmerpDatasourceConfiguration.doTransaction(connectorId, function);
    }

}
