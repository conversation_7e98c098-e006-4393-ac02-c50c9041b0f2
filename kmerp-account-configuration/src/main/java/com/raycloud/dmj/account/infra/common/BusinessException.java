package com.raycloud.dmj.account.infra.common;


import lombok.Getter;

/**
 * 业务异常
 * <AUTHOR>
 */
@Getter
public class BusinessException extends RuntimeException {
    private final String errorCode;
    private final String errorMessage;


    /**
     * 使用预定义错误码
     * @param errorCodeEnum 错误枚举
     */
    public BusinessException(ErrorCodeEnum errorCodeEnum) {
        super(errorCodeEnum.getMessage());
        this.errorCode = errorCodeEnum.getCode();
        this.errorMessage = errorCodeEnum.getMessage();
    }


    /**
     * 自定义错误码和消息
     * @param errorCode 错误码
     * @param errorMessage 错误消息
     */
    public BusinessException(String errorCode, String errorMessage) {
        super(errorMessage);
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    /**
     * 自定义错误码和消息
     * @param errorCodeEnum 错误枚举
     * @param errorMessageTemplate 错误消息模板
     * @param args 错误消息参数
     */
    public BusinessException(ErrorCodeEnum errorCodeEnum, String errorMessageTemplate ,Object ...args) {
        super(String.format(errorMessageTemplate, args));
        this.errorCode = errorCodeEnum.getCode();
        this.errorMessage = String.format(errorMessageTemplate, args);
    }


    /**
     * 自定义错误码和消息
     * @param errorCodeEnum 错误枚举
     * @param errorMessage 错误消息
     */
    public BusinessException(ErrorCodeEnum errorCodeEnum, String errorMessage) {
        super(errorMessage);
        this.errorCode = errorCodeEnum.getCode();
        this.errorMessage = errorMessage;
    }


    /**
     * 使用预定义错误码和原始异常
     * @param errorCodeEnum 错误枚举
     * @param cause 原始异常
     */
    public BusinessException(ErrorCodeEnum errorCodeEnum, Throwable cause) {
        super(errorCodeEnum.getMessage(), cause);
        this.errorCode = errorCodeEnum.getCode();
        this.errorMessage = errorCodeEnum.getMessage();
    }

}