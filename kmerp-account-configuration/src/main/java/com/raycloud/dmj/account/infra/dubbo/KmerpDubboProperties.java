package com.raycloud.dmj.account.infra.dubbo;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@Data
@ConfigurationProperties(prefix = "kmerp.dubbo.version")
public class KmerpDubboProperties {

    /**
     * 首页dubbo版本
     */
    private String base = "erp-prod-0.0.1";

    /**
     * 交易dubbo版本
     */
    private String trade = "erp-trade-prod-0.0.1";

    /**
     * 售后dubbo版本
     */
    private String aftersale = "erp-aftersale-prod-0.0.1";

    /**
     * 商品dubbo版本
     */
    private String item = "erp-item-prod-0.0.1";

    /**
     * 打印dubbo版本
     */
    private String pt = "erp-pt-prod-0.0.1";

    /**
     * PDA dubbo版本
     */
    private String pda = "erp-pda-prod-0.0.1";

    /**
     * 采购dubbo版本
     */
    private String purchase = "erp-caigou-prod-0.0.1";

    /**
     * erptj dubbo版本
     */
    private String erptj = "erp-tj-prod-0.0.1";

    /**
     * 财务dubbo版本
     */
    private String fms = "erp-fms-prod-0.0.1";

    /**
     * 分销dubbo版本
     */
    private String dms = "erp-dms-prod-0.0.1";

    /**
     * 仓储dubbo版本
     */
    private String wms = "erp-wms-prod-0.0.1";

    /**
     * 物流预警dubbo版本
     */
    private String logistics = "erp-logistics-warning-prod-0.0.1";

    /**
     * 物流预警dubbo版本
     */
    private String itemSearch = "erp-item-search-prod-0.0.1";

    /**
     * erp报表下载中心dubbo版本
     */
    private String chessboard = "1.0.0";
}
