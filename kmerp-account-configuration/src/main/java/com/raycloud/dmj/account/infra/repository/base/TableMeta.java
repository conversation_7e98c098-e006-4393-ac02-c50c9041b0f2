package com.raycloud.dmj.account.infra.repository.base;

/**
 * 表信息
 */
public @interface TableMeta {

    /**
     * 软删除字段
     * @return
     */
    String softDeleteColumn() default "";

    /**
     * 表名
     * @return
     */
    String tableName();

    /**
     * 主键
     * @return
     */
    String primaryKey() default "";

    /**
     * 是否分表
     * 如果是分表则会进行分表号填充
     * @return
     */
    boolean sharding() default false;
}
