package com.raycloud.dmj.account.infra.utils;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * http 工具类，基于连接池管理
 *
 * <AUTHOR>
 * @date 2025/7/22
 */
@Slf4j
public class HttpUtils {

    private static final CloseableHttpClient httpClient;

    private static final int TIMEOUT = 200000;

    static {
        // 连接池初始化
        //1.创建连接池管理器
        PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager(60,
                TimeUnit.SECONDS);
        // 同时最多连接数
        connectionManager.setMaxTotal(400);
        // 路由最大链接数
        connectionManager.setDefaultMaxPerRoute(100);
        //2.创建httpclient对象
        httpClient = HttpClients.custom()
                .setConnectionManager(connectionManager)
                .evictExpiredConnections()
                .evictIdleConnections(10, TimeUnit.SECONDS)
                .disableAutomaticRetries()
                .build();

    }

    public static String httpGet(String url, Map<String, String> parms) {
       return httpGet(url, parms, null);
    }

    /**
     * get 请求
     * @param url 请求链接，get请求需要拼接参数
     * @param header 请求头
     * @param timeout 超时时间
     * @return
     */
    public static String httpGet(String url, Map<String, String> header, Integer timeout) {
        int requestTimeout = null == timeout ? TIMEOUT : timeout;
        String result = null;
        CloseableHttpResponse response = null;
        HttpGet httpGet = new HttpGet(url);
        if (MapUtils.isNotEmpty(header)) {
            header.forEach(httpGet::setHeader);
        }
        RequestConfig.Builder builder = RequestConfig.custom();
        builder.setSocketTimeout(requestTimeout)
                .setConnectTimeout(requestTimeout)
                .setConnectionRequestTimeout(requestTimeout);
        httpGet.setConfig(builder.build());
        try {
            response = httpClient.execute(httpGet);
            if (HttpStatus.SC_OK == response.getStatusLine().getStatusCode()) {
                result = EntityUtils.toString(response.getEntity());
            } else {
                log.info("http get request failed. url:{}, status:{}", url, response.getStatusLine().getStatusCode());
            }
        } catch (Exception e) {
            log.error("http get request error. url:{}", url, e);
        } finally {
            if (null != response) {
                try {
                    EntityUtils.consume(response.getEntity());
                } catch (IOException e) {

                }
            }

        }
        return result;
    }

    /**
     * post 请求 表单提交
     * @param url 请求url
     * @param params 参数
     * @param header 请求头
     * @param timeout 超时
     * @return
     */
    public static String httpPostForm(String url, Map<String, String> params, Map<String, String> header, Integer timeout) {
        int requestTimeout = null == timeout ? TIMEOUT : timeout;
        String result = null;
        CloseableHttpResponse response = null;
        List<NameValuePair> nameValuePairs = new ArrayList<>();
        if (MapUtils.isNotEmpty(params)) {
            params.forEach((key, value) -> nameValuePairs.add(new BasicNameValuePair(key, value)));
        }
        HttpPost httpPost = new HttpPost(url);
        if (MapUtils.isNotEmpty(header)) {
            header.forEach(httpPost::setHeader);
        }
        RequestConfig.Builder builder = RequestConfig.custom();
        builder.setSocketTimeout(requestTimeout)
                .setConnectTimeout(requestTimeout)
                .setConnectionRequestTimeout(requestTimeout);
        httpPost.setConfig(builder.build());
        try {
            httpPost.setEntity(new UrlEncodedFormEntity(nameValuePairs, StandardCharsets.UTF_8));
            response = httpClient.execute(httpPost);
            if (HttpStatus.SC_OK == response.getStatusLine().getStatusCode()) {
                result = EntityUtils.toString(response.getEntity());
            } else {
                log.info("http form post request failed. url:{}, status:{}", url, response.getStatusLine().getStatusCode());
            }
        } catch (Exception e) {
            log.error("http get request error. url:{}", url, e);
        } finally {
            if (null != response) {
                try {
                    EntityUtils.consume(response.getEntity());
                } catch (IOException e) {

                }
            }
        }
        return result;
    }

    /**
     * post 请求 json提交
     * @param url 请求url
     * @param params 参数
     * @param header 请求头
     * @param timeout 超时
     * @return
     */
    public static String httpPostJson(String url, Map<String, Object> params, Map<String, String> header, Integer timeout) {
        int requestTimeout = null == timeout ? TIMEOUT : timeout;
        String result = null;
        CloseableHttpResponse response = null;
        HttpPost httpPost = new HttpPost(url);
        if (MapUtils.isNotEmpty(header)) {
            header.forEach(httpPost::setHeader);
        }
        if (MapUtils.isNotEmpty(params)) {
            StringEntity se = new StringEntity(JSON.toJSONString(params), StandardCharsets.UTF_8.toString());
            se.setContentType(ContentType.APPLICATION_JSON.getMimeType());
            httpPost.setEntity(se);
        }
        RequestConfig.Builder builder = RequestConfig.custom();
        builder.setSocketTimeout(requestTimeout)
                .setConnectTimeout(requestTimeout)
                .setConnectionRequestTimeout(requestTimeout);
        httpPost.setConfig(builder.build());
        try {
            response = httpClient.execute(httpPost);
            if (HttpStatus.SC_OK == response.getStatusLine().getStatusCode()) {
                result = EntityUtils.toString(response.getEntity());
            } else {
                log.info("http json post request failed. url:{}, status:{}", url, response.getStatusLine().getStatusCode());
            }
        } catch (Exception e) {
            log.error("http get request error. url:{}", url, e);
        } finally {
            if (null != response) {
                try {
                    EntityUtils.consume(response.getEntity());
                } catch (IOException e) {

                }
            }
        }
        return result;
    }

    public static String httpPostJson(String url, Object body, Map<String, String> header, Integer timeout) {
        int requestTimeout = null == timeout ? TIMEOUT : timeout;
        String result = null;
        CloseableHttpResponse response = null;
        HttpPost httpPost = new HttpPost(url);
        if (MapUtils.isNotEmpty(header)) {
            header.forEach(httpPost::setHeader);
        }
        if (body != null) {
            StringEntity se = new StringEntity(JSON.toJSONString(body), StandardCharsets.UTF_8.toString());
            se.setContentType(ContentType.APPLICATION_JSON.getMimeType());
            httpPost.setEntity(se);
        }
        RequestConfig.Builder builder = RequestConfig.custom();
        builder.setSocketTimeout(requestTimeout)
                .setConnectTimeout(requestTimeout)
                .setConnectionRequestTimeout(requestTimeout);
        httpPost.setConfig(builder.build());
        try {
            response = httpClient.execute(httpPost);
            if (HttpStatus.SC_OK == response.getStatusLine().getStatusCode()) {
                result = EntityUtils.toString(response.getEntity());
            } else {
                log.info("http json post request failed. url:{}, status:{}", url, response.getStatusLine().getStatusCode());
            }
        } catch (Exception e) {
            log.error("http get request error. url:{}", url, e);
        } finally {
            if (null != response) {
                try {
                    EntityUtils.consume(response.getEntity());
                } catch (IOException e) {

                }
            }
        }
        return result;
    }

    /**
     * http 文件下载
     * @param url 文件可访问链接
     * @param header 请求头
     * @param toLocalFile 本地文件
     */
    public static void downloadToLocal(String url, Map<String, String> header, File toLocalFile) {
        if (StringUtils.isBlank(url)) {
            return;
        }
        HttpGet httpGet = new HttpGet(url);
        if (MapUtils.isNotEmpty(header)) {
            header.forEach(httpGet::addHeader);
        }
        CloseableHttpResponse response = null;
        httpGet.setConfig(RequestConfig.custom().build());
        try {
            response = httpClient.execute(httpGet);
            HttpEntity entity = response.getEntity();
            InputStream is = entity.getContent();
            FileOutputStream fileOutputStream = new FileOutputStream(toLocalFile);
            byte[] buffer = new byte[1024];
            int ch;
            while ((ch = is.read(buffer)) != -1) {
                fileOutputStream.write(buffer, 0, ch);
            }
            is.close();
            fileOutputStream.close();
        } catch (Exception e) {
            log.error("downloadToLocal error. url:{}", url, e);
        } finally {
            if (null != response) {
                try {
                    EntityUtils.consume(response.getEntity());
                } catch (IOException e) {

                }
            }
        }
    }
}
