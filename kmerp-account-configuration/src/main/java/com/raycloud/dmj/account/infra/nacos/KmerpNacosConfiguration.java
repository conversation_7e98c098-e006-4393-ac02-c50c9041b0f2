package com.raycloud.dmj.account.infra.nacos;

import com.alibaba.nacos.api.NacosFactory;
import com.alibaba.nacos.api.config.ConfigService;
import com.alibaba.nacos.api.exception.NacosException;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;

@ConditionalOnProperty(prefix = "kmerp.nacos", name = "enabled", havingValue = "true")
@Configuration
@EnableConfigurationProperties(KmerpNacosProperties.class)
public class KmerpNacosConfiguration {

    @Bean(name = "nacosConfigClient")
    public ConfigService nacosConfigClient(KmerpNacosProperties kmerpNacosProperties) throws NacosException{
        Properties properties = new Properties();
        properties.put("serverAddr", kmerpNacosProperties.getServerAddress());
        properties.put("namespace", kmerpNacosProperties.getNamespace());
        return NacosFactory.createConfigService(properties);
    }

}
