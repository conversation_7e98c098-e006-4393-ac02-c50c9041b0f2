package com.raycloud.dmj.account.infra.utils;

import com.raycloud.dmj.account.infra.common.BizException;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

public class ValueUtils {

    @SuppressWarnings("unchecked")
    public static <T> T to(Object value, Class<T> clazz) {
        if (String.class.equals(clazz)) {
            return (T) convertToString(value);
        } else if (BigDecimal.class.equals(clazz)) {
            return (T) convertToBigDecimal(value);
        } else if (Integer.class.equals(clazz)) {
            return (T) convertToInteger(value);
        } else if (Long.class.equals(clazz)) {
            return (T) convertToLong(value);
        } else if (Double.class.equals(clazz)) {
            return (T) convertToDouble(value);
        } else if (Boolean.class.equals(clazz)) {
            return (T) convertToBoolean(value);
        } else if (Date.class.equals(clazz)) {
            return (T) convertToDate(value);
        } else {
            throw new BizException("不支持的类型转换:" + clazz.getSimpleName());
        }
    }

    @SuppressWarnings("unchecked")
    public static <T> T to(Object value, Class<T> clazz,T defaultValue) {
        T val = to(value, clazz);
        if (val == null){
            return defaultValue;
        }
        return val;
    }

    public static String convertToString(Object value, String defaultValue) {
        String val = convertToString(value);
        if (value == null) {
            return defaultValue;
        }
        return val;
    }

    public static String convertToString(Object value) {
        if (ObjectUtils.isEmpty(value)) {
            return null;
        }
        if (value instanceof String) {
            return (String) value;
        } else if (value instanceof Date) {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return format.format((Date) value);
        }
        return String.valueOf(value);
    }

    public static Double convertToDouble(Object value, Double defaultValue) {
        Double val = convertToDouble(value);
        if (val == null) {
            return defaultValue;
        }
        return val;
    }

    public static Double convertToDouble(Object value) {
        if (ObjectUtils.isEmpty(value)) {
            return null;
        }
        if (value instanceof Double) {
            return (Double) value;
        } else if (double.class.isAssignableFrom(value.getClass())) {
            return (double) value;
        }

        String valueStr = String.valueOf(value);
        if (!NumberUtils.isCreatable(valueStr)) {
            return null;
        }
        return NumberUtils.createDouble(valueStr);
    }

    public static BigDecimal convertToBigDecimal(Object value, BigDecimal defaultValue) {
        BigDecimal val = convertToBigDecimal(value);
        if (val == null) {
            return defaultValue;
        }
        return val;
    }

    public static BigDecimal convertToBigDecimal(Object value) {
        if (ObjectUtils.isEmpty(value)) {
            return null;
        }
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }

        String valueStr = String.valueOf(value);
        if (!NumberUtils.isCreatable(valueStr)) {
            return null;
        }
        return NumberUtils.createBigDecimal(valueStr);
    }

    public static Integer convertToInteger(Object value, Integer defaultValue) {
        Integer val = convertToInteger(value);
        if (value == null) {
            return defaultValue;
        }
        return val;
    }

    public static Integer convertToInteger(Object value) {
        if (ObjectUtils.isEmpty(value)) {
            return null;
        }
        if (value instanceof Integer) {
            return (Integer) value;
        } else if (int.class.isAssignableFrom(value.getClass())) {
            return (int) value;
        }
        String valueStr = String.valueOf(value);
        if (!NumberUtils.isCreatable(valueStr)) {
            return null;
        }
        return NumberUtils.createInteger(valueStr);
    }

    public static Long convertToLong(Object value, Long defaultValue) {
        Long val = convertToLong(value);
        if (value == null) {
            return defaultValue;
        }
        return val;
    }

    public static Long convertToLong(Object value) {
        if (ObjectUtils.isEmpty(value)) {
            return null;
        }
        if (value instanceof Long) {
            return (Long) value;
        } else if (long.class.isAssignableFrom(value.getClass())) {
            return (long) value;
        }

        String valueStr = String.valueOf(value);
        if (!NumberUtils.isCreatable(valueStr)) {
            return null;
        }
        return NumberUtils.createLong(valueStr);
    }

    public static Boolean convertToBoolean(Object value, Boolean defaultValue) {
        Boolean val = convertToBoolean(value);
        if (val == null) {
            return defaultValue;
        }
        return val;
    }

    public static Boolean convertToBoolean(Object value) {
        if (ObjectUtils.isEmpty(value)) {
            return null;
        }
        if (value instanceof Boolean) {
            return (Boolean) value;
        } else if (boolean.class.isAssignableFrom(value.getClass())) {
            return (boolean) value;
        }

        String valueStr = String.valueOf(value);
        if ("1".equals(valueStr) || "true".equalsIgnoreCase(valueStr) || "True".equalsIgnoreCase(valueStr)) {
            return true;
        } else if ("0".equals(valueStr) || "false".equalsIgnoreCase(valueStr) || "False".equalsIgnoreCase(valueStr)) {
            return false;
        }
        return null;
    }

    public static Date convertToDate(Object value, Date defaultValue) {
        Date val = convertToDate(value);
        if (val == null) {
            return defaultValue;
        }
        return val;
    }

    public static Date convertToDate(Object value) {
        if (ObjectUtils.isEmpty(value)) {
            return null;
        }

        if (value instanceof LocalDateTime) {
            return Date.from(((LocalDateTime) value).atZone(ZoneId.systemDefault()).toInstant());
        } else if (value instanceof java.sql.Date) {
            return new Date(((java.sql.Date) value).getTime());
        } else if (value instanceof Timestamp) {
            return new Date(((Timestamp) value).getTime());
        } else if (value instanceof Date) {
            return (Date) value;
        } else if (value instanceof Long) {
            return new Date((Long) value);
        } else if (value instanceof String) {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            try {
                return format.parse((String) value);
            } catch (Exception e) {
                return null;
            }
        }
        return null;
    }
}
