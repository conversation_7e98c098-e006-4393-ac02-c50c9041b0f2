package com.raycloud.dmj.account.infra.redis;

import org.apache.commons.lang.StringUtils;
import org.springframework.data.redis.core.*;
import org.springframework.data.redis.core.ValueOperations;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class RayRedisTemplate {

    private RedisTemplate<String, Object> redisTemplate;

    private final String cacheKeyPrefix;

    public RayRedisTemplate() {
        this.cacheKeyPrefix = null;
    }

    public RayRedisTemplate(RedisTemplate<String, Object> redisTemplate, String cacheKeyPrefix) {
        this.redisTemplate = redisTemplate;
        this.cacheKeyPrefix = cacheKeyPrefix;
    }

    public boolean hasKey(String key) {

        return redisTemplate.hasKey(buildKey(key));
    }

    public boolean delete(String key) {

        return redisTemplate.delete(buildKey(key));
    }

    public void setObject(String key, Object value, Duration duration) {

        redisTemplate.opsForValue().set(buildKey(key), value, duration);
    }

    public <T> T getObject(String key, Class<T> tClass ) {

        ValueOperations<String, Object> operations = redisTemplate.opsForValue();
        Object ovl = operations.get(buildKey(key));

        return castValue(ovl, tClass);
    }

    public void setMap(String key, Map<?, ?> map, Duration duration) {
        HashOperations<String, Object, Object> hashOps = redisTemplate.opsForHash();
        delete(buildKey(key));
        hashOps.putAll(buildKey(key), map);
        redisTemplate.expire(buildKey(key), duration); // 设置过期时间
    }

    /**
     * 如果不存在，不允许添加
     * @param key
     * @param mapKey
     * @param mapValue
     */
    public void addMap(String key, Object mapKey, Object mapValue) {
        HashOperations<String, Object, Object> hashOps = redisTemplate.opsForHash();
        if(hasKey(buildKey(key))) {
            hashOps.put(buildKey(key), mapKey, mapValue);
        }else{
            throw new RuntimeException("object does not exist");
        }
    }

    /**
     * 可以不存在，进行添加
     * @param key
     * @param mapKey
     * @param mapValue
     * @param duration
     */
    public void addMap(String key, Object mapKey, Object mapValue, Duration duration) {
        HashOperations<String, Object, Object> hashOps = redisTemplate.opsForHash();
        hashOps.put(buildKey(key), mapKey, mapValue);
        redisTemplate.expire(buildKey(key), duration); // 设置过期时间
    }

    public <T> T getMapValue(String key, String mapKey, Class<T> tClass) {
        HashOperations<String, Object, Object> hashOps = redisTemplate.opsForHash();
        return castValue(hashOps.get(buildKey(key), mapKey), tClass);
    }

    public <T> void setList(String key, List<T> list, Duration duration) {
        ListOperations<String, Object> listOps = redisTemplate.opsForList();
        delete(buildKey(key));
        listOps.rightPushAll(buildKey(key), list);
        redisTemplate.expire(buildKey(key), duration); // 设置过期时间
    }

    public <T> void addList(String key, T val) {
        ListOperations<String, Object> listOps = redisTemplate.opsForList();
        if(hasKey(buildKey(key))) {
            listOps.rightPush(buildKey(key), val);
        }else{
            throw new RuntimeException("object does not exist");
        }
    }

    public <T> void addList(String key, T val, Duration duration) {
        ListOperations<String, Object> listOps = redisTemplate.opsForList();
        listOps.rightPush(buildKey(key), val);
        redisTemplate.expire(buildKey(key), duration); // 设置过期时间
    }

    public <T> List<T> getList(String key, Class<T> tClass) {

        ListOperations<String, Object> listOps = redisTemplate.opsForList();
        return castValues(listOps.range(buildKey(key), 0L, -1L), tClass);
    }

    public <T> List<T> getListPage(String key, long pageNo, long pageSize, Class<T> tClass) {
        long from = (pageNo - 1) * pageSize;
        if(pageSize <= 0 || pageNo <= 0){
            return new ArrayList<>();
        }
        long end = pageNo * pageSize - 1;

        return getListRange(key, from, end, tClass);
    }

    public <T> List<T> getListRange(String key, long fromIndex, long toIndex, Class<T> tClass) {
        ListOperations<String, Object> listOps = redisTemplate.opsForList();
        long size = listOps.size(buildKey(key));
        if (size <= 0){
            return null;
        }
        //大于等于列表的长度
        if(fromIndex >= toIndex || fromIndex >= size){
            return new ArrayList<>();
        }
        if(toIndex > size){
            toIndex = size -1;
        }
        return castValues(listOps.range(buildKey(key), fromIndex, toIndex), tClass);
    }

    private <T> T castValue(Object value, Class<T> tClass){
        if (tClass.isInstance(value)) {
            return tClass.cast(value);
        } else if (value == null){
            return null;
        }else{
            throw new ClassCastException("Cannot cast " + value.getClass().getName() + " to " + tClass.getName());
        }
    }

    private <T> List<T> castValues(List<Object> values, Class<T> tClass){
        if(values==null){
            return null;
        }
        List<T> tList = new ArrayList<>(values.size());
        for (Object value : values) {
            tList.add(castValue(value, tClass));
        }
        return tList;
    }

    private String buildKey(String key){
        if(StringUtils.isEmpty(cacheKeyPrefix)){
            return key;
        }else{
            return cacheKeyPrefix+key;
        }
    }

}
