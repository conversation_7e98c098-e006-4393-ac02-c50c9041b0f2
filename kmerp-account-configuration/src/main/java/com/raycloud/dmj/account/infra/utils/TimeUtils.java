package com.raycloud.dmj.account.infra.utils;

import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class TimeUtils {
    private final static String DATETIME_FORMAT_PARTTEN = "yyyy-MM-dd HH:mm:ss";

    private final static String DATE_FORMAT_PARTTEN = "yyyy-MM-dd";

    public final static DateTimeFormatter DATETIME_FORMATTER = DateTimeFormat.forPattern(DATETIME_FORMAT_PARTTEN);

    private final static DateTimeFormatter DATE_FORMATTER = DateTimeFormat.forPattern(DATE_FORMAT_PARTTEN);


    public static Date convertToDate(String dateString) {
        if (dateString == null) {
            return null;
        }
        if (dateString.length() <= DATE_FORMAT_PARTTEN.length()) {
            return DateTime.parse(dateString, DATE_FORMATTER).toDate();
        } else {
            return DateTime.parse(dateString, DATETIME_FORMATTER).toDate();
        }
    }

    public static Long getStartTime(LocalDate date) {
        if (date == null) {
            return null;
        }
        return date.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }

    public static Long getEndTime(LocalDate date) {
        if (date == null) {
            return null;
        }
        return date.atTime(23, 59, 59, 999_999_999).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }
}
