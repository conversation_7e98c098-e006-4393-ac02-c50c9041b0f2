package com.raycloud.dmj.account.infra.logger;

import com.raycloud.dmj.account.infra.utils.StringFormatUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedList;

/**
 * 高级日志，主要用于解决日志过大，kibana展示不全问题
 */
@Slf4j
public class AdvanceLogger {

    @Getter
    private boolean openSplit = true;

    public static AdvanceLogger log(Logger logger) {
        return splitLog(logger);
    }

    public static AdvanceLogger log(Class<?> clazz) {
        return splitLog(LoggerFactory.getLogger(clazz));
    }

    public static AdvanceLogger log(String logName) {
        return splitLog(LoggerFactory.getLogger(logName));
    }

    public static AdvanceLogger getLogger(Logger logger) {
        return splitLog(logger);
    }

    public static AdvanceLogger getLogger(Class<?> clazz) {
        return splitLog(LoggerFactory.getLogger(clazz));
    }

    public static AdvanceLogger getLogger(String logName) {
        return splitLog(LoggerFactory.getLogger(logName));
    }

    public static AdvanceLogger splitLog(Logger logger) {
        return new AdvanceLogger(true, logger);
    }

    public static AdvanceLogger splitLog(Class<?> clazz) {
        return new AdvanceLogger(true, LoggerFactory.getLogger(clazz));
    }

    public static AdvanceLogger splitLog(String logName) {
        return new AdvanceLogger(true, LoggerFactory.getLogger(logName));
    }

    private final Logger logger;

    public AdvanceLogger(Logger logger) {
        this.logger = logger;
    }

    public AdvanceLogger(boolean openSplit, Logger logger) {
        this.openSplit = openSplit;
        this.logger = logger;
    }

    public void error(String message, Throwable throwable) {
        if (isOpenSplit()) {
            advanceAppender(message, throwable);
        } else {
            logger.error(message, throwable);
        }
    }

    public void error(String message, Object... args) {
        logger.error(message, args);
    }

    public void info(String message, Object... args) {
        logger.info(message, args);
    }

    public void warn(String message, Object... args) {
        logger.warn(message, args);
    }

    public void debug(String message, Object... args) {
        logger.debug(message);
    }

    private void advanceAppender(Object message, Throwable throwable) {
        LinkedList<Throwable> tempQueue = new LinkedList<>();
        Throwable current = throwable;
        while (true) {
            tempQueue.addLast(current);
            Throwable cause = current.getCause();
            if (current == cause) {
                break;
            }
            current = cause;
            if (current == null) {
                break;
            }
        }

        int total = tempQueue.size();
        for (int i = 0; i < total; i++) {
            Throwable e = tempQueue.poll();
            stackTrace(i + 1, total, message, e);
        }
    }


    private void stackTrace(int no, int total, Object message, Throwable throwable) {
        if (throwable == null) {
            return;
        }
        logger.error(StringFormatUtils.format("{} , 当前日志片段：({}/{})", message, no, total), throwable);
    }


    public AdvanceLogger openConsole(boolean openConsole) {
        return this;
    }

    public AdvanceLogger openSplit(boolean openSplit) {
        return this;
    }

    public boolean isDebugEnabled() {
        return logger.isDebugEnabled();
    }

    public boolean isInfoEnabled() {
        return logger.isInfoEnabled();
    }

    public boolean isTraceEnabled() {
        return logger.isTraceEnabled();
    }

}
