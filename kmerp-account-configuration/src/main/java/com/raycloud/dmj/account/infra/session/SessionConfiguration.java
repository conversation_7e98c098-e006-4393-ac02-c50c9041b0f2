package com.raycloud.dmj.account.infra.session;

import com.raycloud.dmj.session.ocs.OcsSessionManager;
import net.rubyeye.xmemcached.MemcachedClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SessionConfiguration {

    @Bean
    public OcsSessionManager ocsSessionManager(){
        return new OcsSessionManager();
    }

    @Bean(name = "memcachedClientSession")
    public MemcachedClient memcachedClientSession(SessionMemcachedClientFactoryBean sessionMemcachedClientFactoryBean) throws Exception {
        return sessionMemcachedClientFactoryBean.getObject();
    }
}
