package com.raycloud.dmj.account.infra.utils;

import com.raycloud.dmj.kmbi.tool.uid.UniqueIdUtils;

import java.util.Arrays;
import java.util.concurrent.atomic.AtomicInteger;

public class IdUtils {

    private static final AtomicInteger adder = new AtomicInteger();
    private static final char[] BASE36_CHARS = "0123456789abcdefghijklmnopqrstuvwxyz".toCharArray();
    private static final int[] CHAR_TO_VALUE = new int[256];

    static {
        // 初始化非法字符标记
        Arrays.fill(CHAR_TO_VALUE, -1);
        // 建立字符到数值的映射关系
        for (int i = 0; i < BASE36_CHARS.length; i++) {
            char c = BASE36_CHARS[i];
            CHAR_TO_VALUE[c] = i;
        }
    }

    public static long genId() {
        int seed = adder.getAndIncrement();
        return UniqueIdUtils.unsafeIdGen(seed % 1024);
    }

    public static String gen62bit() {
        return UniqueIdUtils.convertTo62Bit(genId());
    }

    public static String gen36bit() {
        return convertTo36Bit(genId());
    }

    private static String convertTo36Bit(long number) {
        if (number == 0L) {
            return "0";
        } else if (number < 0L) {
            throw new IllegalArgumentException("数值必须为正数");
        } else {
            StringBuilder sb;
            for (sb = new StringBuilder(); number > 0L; number = number / 36L) {
                int remainder = (int) (number % 36L);
                sb.append(BASE36_CHARS[remainder]);
            }
            return sb.reverse().toString();
        }
    }
}
