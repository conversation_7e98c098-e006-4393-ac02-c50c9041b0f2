package com.raycloud.dmj.account.infra.repository.cache;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.RemovalCause;
import com.github.benmanes.caffeine.cache.RemovalListener;
import com.raycloud.dmj.account.infra.repository.base.DbRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.springframework.cache.Cache;
import org.springframework.cache.caffeine.CaffeineCacheManager;

import java.util.Collection;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

@Slf4j
public class MemoryRepositoryStorage {

    private static final String CACHE_NAME = "MEMORY_REPOSITORY_STORAGE";

    private CaffeineCacheManager manager = null;

    public MemoryRepositoryStorage() {
        this.manager = cacheManager();
    }

    private CaffeineCacheManager cacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(
                Caffeine.newBuilder()
                        .initialCapacity(16)
                        .maximumSize(40000)
                        // 每次访问后1小时后过期
                        .expireAfterAccess(1, TimeUnit.HOURS)
                        .removalListener(new RemovalListener<Object, Object>() {
                            @Override
                            public void onRemoval(@Nullable Object key, @Nullable Object value, @NonNull RemovalCause removalCause) {
                                if (removalCause.wasEvicted()) {
                                    if (value instanceof DbRepository) {
                                        ((DbRepository) value).close();
                                    }
                                }
                            }
                        })
        );
        return cacheManager;
    }

    public <T> T get(String uniqueKey, Class<T> clazz) {
        Cache cache = manager.getCache(CACHE_NAME);
        if (cache != null) {
            return cache.get(uniqueKey, clazz);
        }
        return null;
    }

    public <T> T get(String uniqueKey, Class<T> clazz, Supplier<T> creator) {
        T catalog = get(uniqueKey, clazz);
        if (catalog == null) {
            synchronized (uniqueKey) {
                catalog = get(uniqueKey, clazz);
                if (catalog != null) {
                    return catalog;
                }
                catalog = creator.get();
                save(uniqueKey, catalog);
                return catalog;
            }
        }
        return catalog;
    }


    public <T> void save(String uniqueKey, T catalog) {
        Cache cache = manager.getCache(CACHE_NAME);
        if (cache != null) {
            cache.put(uniqueKey, catalog);
        }
    }

    public void delete(String uniqueKey) {
        Cache cache = manager.getCache(CACHE_NAME);
        if (cache != null) {
            cache.evict(uniqueKey);
        }
    }

    public void close() {
        Collection<String> cacheNames = manager.getCacheNames();
        if (CollectionUtils.isEmpty(cacheNames)) {
            return;
        }
        for (String cacheName : cacheNames) {
            Cache cache = manager.getCache(cacheName);
            if (cache != null) {
                cache.clear();
            }
        }
    }
}
