package com.raycloud.dmj.account.infra.utils;

import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;

public class RandomUtils {

    private static final String CHAR_SET = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    private static final SecureRandom random = createSecureRandom();

    /**
     * 显式指定非阻塞算法
     * @return
     */
    private static SecureRandom createSecureRandom() {
        try {
            return SecureRandom.getInstance("NativePRNGNonBlocking");
        } catch (NoSuchAlgorithmException e) {
            // 回退到默认的非阻塞实现
            return new SecureRandom();
        }
    }

    /**
     * 生成头字符长度的随机字符串
     * @return
     */
    public static String genRandomKey(String head,int length) {
        String prefix = head;
        return prefix + genRandomKey(length);
    }

    /**
     * 生成64字符长度的随机字符串
     * @return
     */
    public static String gen64RandomKey() {
        return genRandomKey(64);
    }

    /**
     * 核心随机字符串生成方法
     * @param length
     * @return
     */
    public static String genRandomKey(int length) {
        if (length <= 0) {
            throw new IllegalArgumentException("Length must be positive");
        }

        StringBuilder sb = new StringBuilder(length);
        for (int i = 0; i < length; i++) {
            int index = random.nextInt(CHAR_SET.length());
            sb.append(CHAR_SET.charAt(index));
        }
        return sb.toString();
    }

}
