package com.raycloud.dmj.account.infra.datasource.filter;

import com.alibaba.druid.filter.stat.StatFilter;
import com.alibaba.druid.proxy.jdbc.JdbcParameter;
import com.alibaba.druid.proxy.jdbc.StatementProxy;
import com.raycloud.dmj.account.infra.common.AppContextHolder;
import com.raycloud.dmj.account.infra.logger.AdvanceLogger;
import com.raycloud.dmj.services.utils.ClueIdUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.log4j.MDC;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component("druidQueryLogFilter")
public class DruidQueryLogFilter extends StatFilter {

    private final AdvanceLogger logger = AdvanceLogger.log(DruidQueryLogFilter.class);

    @Override
    protected void statementExecuteQueryBefore(StatementProxy statement, String sql) {
        super.statementExecuteQueryBefore(statement, sql);
        log(statement, sql);
    }

    @Override
    protected void statementExecuteBefore(StatementProxy statement, String sql) {
        super.statementExecuteBefore(statement, sql);
        log(statement, sql);
    }

    private void log(StatementProxy statement, String sql) {
        try {
            Long clueId = ClueIdUtil.getClueId();
            if (clueId == null) {
                return;
            }

            long companyId = -1L;

            Object mdcCompanyId = MDC.get("companyId");
            if(mdcCompanyId instanceof String) {
                if (StringUtils.isNotBlank((String)mdcCompanyId) && NumberUtils.isCreatable((String)mdcCompanyId)) {
                    companyId = Long.parseLong((String)mdcCompanyId);
                }
            }else if(mdcCompanyId instanceof Long){
                companyId = (Long)mdcCompanyId;
            }

            if (companyId < 0L) {
                return;
            }

            if (sql == null) {
                return;
            }

            if (sql.contains("insert into") || sql.contains("INSERT INTO") || sql.contains("UPDATE ") || sql.contains("update ") || sql.contains("DELETE ") || sql.contains("delete ")) {
                return;
            } else if (!sql.contains("select") && !sql.contains("SELECT")) {
                return;
            }

            Map<Integer, JdbcParameter> parameters = statement.getParameters();
            List<Object> args = new ArrayList<>();
            List<Object> argTypes = new ArrayList<>();
            if (parameters != null) {
                for (Map.Entry<Integer, JdbcParameter> entry : parameters.entrySet()) {
                    JdbcParameter value = entry.getValue();
                    if (value==null){
                        continue;
                    }
                    Object valueValue = value.getValue();
                    args.add(valueValue);
                    if (valueValue == null){
                        // 这里只能忙猜,给个默认值
                        argTypes.add(Object.class.getName());
                    }else{
                        argTypes.add(valueValue.getClass().getName());
                    }
                }
            }


            DruidQueryLogger logger = AppContextHolder.getBean(DruidQueryLogger.class);
            logger.saveAsync(clueId, companyId, sql, args, argTypes);
        } catch (Throwable e) {
            logger.error("DruidQueryLogFilter异常", e);
        }
    }
}
