package com.raycloud.dmj.account.infra.memcache;

import com.raycloud.cache.CacheException;
import com.raycloud.cache.ICache;
import com.raycloud.cache.ISharedCache;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Map;

@RequiredArgsConstructor
@Component
public class MemcachedShared<PERSON>ache implements ISharedCache {

    private final ICache cache;

    @Override
    public <T> Map<String, T> gets(String... keys) throws CacheException {
        return cache.gets(keys);
    }

    @Override
    public <T> T get(String key) throws CacheException {
        return cache.get(key);
    }

    @Override
    public boolean set(String key, Object value, int expires) throws CacheException {
        return cache.set(key, value, expires);
    }

    @Override
    public boolean add(String key, Object value, int expires) throws CacheException {
        return cache.add(key, value, expires);
    }

    @Override
    public boolean delete(String key) throws CacheException {
        return cache.delete(key);
    }

    @Override
    public boolean touch(String key, int expires) throws CacheException {
        return cache.touch(key, expires);
    }

    @Override
    public long incr(String key, Long value) throws CacheException {
        cache.incr(key, value);
        return value + 1L;
    }

    @Override
    public long decr(String key, Long value) throws CacheException {
        cache.decr(key, value);
        return value - 1L;
    }
}
