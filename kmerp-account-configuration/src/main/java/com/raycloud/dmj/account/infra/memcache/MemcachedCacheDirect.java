package com.raycloud.dmj.account.infra.memcache;

import com.raycloud.cache.CacheException;
import com.raycloud.cache.ICache;
import com.raycloud.memsession.domain.ObjectProxy;
import com.raycloud.util.ProxyObjUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.rubyeye.xmemcached.MemcachedClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.*;

@Data
@Slf4j
@Component
public class MemcachedCacheDirect implements ICache, ApplicationListener<MemcachedConfigRefreshEvent> {

    @Value("${cache.app.key}")
    private String cacheAppKey;

    private MemcachedClient memcachedClient;

    public MemcachedCacheDirect(MemcachedClient memcachedClient) {
        this.memcachedClient = memcachedClient;
    }

    /**
     * 缓存有效期超时时间为1天
     */
    public static final Integer DEFAULT_EXPIRY_TIME = 24 * 3600;

    @Override
    public boolean set(String key, Object value) throws CacheException {
        return set(key, value, DEFAULT_EXPIRY_TIME);
    }

    /**
     * 双写
     */
    @Override
    public boolean set(String key, Object value, int exp) throws CacheException {
        String cacheKey = buildCacheKey(key);
        boolean result;
        try {
            result = memcachedClient.set(cacheKey, exp, value);
        } catch (Exception e) {
            throw new CacheException(e);
        }
        return result;
    }

    @Override
    public <T> T get(String key) throws CacheException {
        String cacheKey = buildCacheKey(key);
        try {
            T t = memcachedClient.get(cacheKey);
            return convert(t);
        } catch (Exception e) {
            throw new CacheException(e);
        }
    }

    @Override
    public <T> T get(String key, long timeout) throws CacheException {
        String cacheKey = buildCacheKey(key);
        try {
            T t = memcachedClient.get(cacheKey, timeout);
            return convert(t);
        } catch (Exception e) {
            throw new CacheException(e);
        }
    }

    @Override
    public <T> Map<String, T> gets(String... keys) throws CacheException {
        try {
            List<String> keyList = new ArrayList<String>();
            for (String key : keys) {
                keyList.add(buildCacheKey(key));
            }

            Map<String, T> map = memcachedClient.get(keyList);
            if (map == null || map.size() == 0) {
                return map;
            }

            Map<String, T> result = new HashMap<String, T>();
            for (Map.Entry<String, T> entry : map.entrySet()) {
                result.put(entry.getKey(), convert(entry.getValue()));
            }
            return result;
        } catch (Exception e) {
            throw new CacheException(e);
        }
    }

    @Override
    public boolean delete(String key) throws CacheException {
        String cacheKey = buildCacheKey(key);
        try {
            memcachedClient.delete(cacheKey);
        } catch (Exception e) {
            throw new CacheException(e);
        }

        return true;
    }

    @Override
    public void incr(String key, Long value) throws CacheException {
        String cacheKey = buildCacheKey(key);
        try {
            memcachedClient.incr(cacheKey, value);
        } catch (Exception e) {
            throw new CacheException(e);
        }
    }

    @Override
    public void decr(String key, Long value) throws CacheException {
        String cacheKey = buildCacheKey(key);
        try {
            memcachedClient.decr(cacheKey, value);
        } catch (Exception e) {
            throw new CacheException(e);
        }
    }

    @Override
    public boolean touch(String key, int newExpireTime) throws CacheException {
        boolean result;
        String cacheKey = buildCacheKey(key);
        try {
            result = memcachedClient.touch(cacheKey, newExpireTime);
        } catch (Exception e) {
            throw new CacheException(e);
        }
        return result;
    }


    @Override
    public <T> T getAndTouch(String key, int newExpireTime) throws CacheException {
        String cacheKey = buildCacheKey(key);
        try {
            T t = memcachedClient.getAndTouch(cacheKey, newExpireTime);

            return convert(t);
        } catch (Exception e) {
            throw new CacheException(e);
        }
    }

    @Override
    public boolean containKey(String key) throws CacheException {
        String cacheKey = buildCacheKey(key);
        try {
            //TODO 如果key对应的value为null则判断不准确
            return memcachedClient.get(cacheKey) != null;
        } catch (Exception e) {
            throw new CacheException(e);
        }
    }

    @Override
    public Set<String> keys() throws CacheException {
        // 这个接口实现不支持
        throw new CacheException("Not Supported keys");
    }

    @Override
    public boolean add(String key, Object value, int expireTime) throws CacheException {
        boolean result;
        String cacheKey = buildCacheKey(key);
        try {
            result = memcachedClient.add(cacheKey, expireTime, value);
        } catch (Exception e) {
            throw new CacheException(e);
        }
        return result;
    }

    /**
     * 因为原value通过dubbo进行了一层封装，所以取值的时候为了兼容，需要做一层转换
     *
     * @param obj
     * @return
     */
    private <T> T convert(T obj) throws Exception {
        if (obj instanceof ObjectProxy) {
            return (T) ProxyObjUtil.proxyToBean((ObjectProxy) obj);
        }
        return obj;
    }

    /**
     * OCS中缓存的key，原dubbo方式默认缓存的key是 appKey+"|-1|"+key
     */
    private String buildCacheKey(String key) {
        return cacheAppKey + "|-1|" + key;
    }

    @Override
    public void onApplicationEvent(MemcachedConfigRefreshEvent event) {
        log.info("接受到memcachedClient刷新事件");
        Object source = event.getSource();
        if (source instanceof MemcachedClient) {
            this.memcachedClient = (MemcachedClient) source;
        }
    }
}
