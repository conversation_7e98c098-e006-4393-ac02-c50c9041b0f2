package com.raycloud.dmj.account.infra.mq;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.integration.core.MessagingTemplate;
import org.springframework.integration.dsl.IntegrationFlow;
import org.springframework.integration.dsl.IntegrationFlows;
import org.springframework.integration.dsl.RouterSpec;
import org.springframework.integration.handler.GenericHandler;
import org.springframework.integration.router.ExpressionEvaluatingRouter;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.MessageHeaders;

import java.util.function.Consumer;

@Getter
@Configuration
public class MessageQueueConfiguration {

    @Bean(name = "defaultMessageTemplate")
    public MessagingTemplate defaultMessageTemplate() {
        return new MessagingTemplate(defaultInputMessageChannel());
    }

    @Bean(name = "defaultInputMessageChannel")
    public MessageChannel defaultInputMessageChannel() {
        DirectChannel channel = new DirectChannel();
        channel.setFailover(true);
        channel.setMaxSubscribers(Integer.MAX_VALUE);
        return channel;
    }

    @Bean(name = "defaultOutputMessageChannel")
    public MessageChannel defaultOutputMessageChannel() {
        DirectChannel channel = new DirectChannel();
        channel.setFailover(true);
        channel.setMaxSubscribers(Integer.MAX_VALUE);
        return channel;
    }

    @Bean
    public IntegrationFlow defaultMessageInputFlow(
            @Qualifier("localSyncMessageChannel") MessageChannel localSyncMessageChannel,
            @Qualifier("rocketMessageChannel") MessageChannel rocketMessageChannel,
            @Qualifier("redisMessageChannel") MessageChannel redisMessageChannel
    ) {
        return IntegrationFlows
                .from(defaultInputMessageChannel())
                .route("headers['messageQueueType']",
                        new Consumer<RouterSpec<String, ExpressionEvaluatingRouter>>() {
                            @Override
                            public void accept(RouterSpec<String, ExpressionEvaluatingRouter> expressionEvaluatingRouterRouterSpec) {
                                expressionEvaluatingRouterRouterSpec
                                        .resolutionRequired(false)
                                        .channelMapping(MessageQueueType.LOCAL_SYNC.name(), localSyncMessageChannel)
                                        .channelMapping(MessageQueueType.ROCKETMQ.name(), rocketMessageChannel)
                                        .channelMapping(MessageQueueType.REDIS.name(), redisMessageChannel)
                                        .defaultOutputChannel(defaultOutputMessageChannel());
                            }
                        }
                )
                .get();
    }

    @Bean
    public IntegrationFlow defaultMessageFlow(MessageChannel defaultOutputMessageChannel) {
        return IntegrationFlows
                .from(defaultOutputMessageChannel)
                .handle(new GenericHandler<Object>() {
                            @Override
                            public MessageResult<Object> handle(Object payload, MessageHeaders headers) {
                                MessageResult<Object> result = new MessageResult<>();
                                result.setCode(400);
                                result.setData(null);
                                result.setMessage("暂未支持:" + headers.get("messageQueueType") + "消息队列");
                                return result;
                            }
                        }
                )
                .get();
    }

}
