package com.raycloud.dmj.account.infra.datasource.filter;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.raycloud.dmj.account.infra.datasource.KmerpDatasourceConfiguration;
import com.raycloud.dmj.account.infra.logger.AdvanceLogger;
import com.raycloud.dmj.account.infra.repository.Connectors;
import com.raycloud.dmj.table.api.plus.common.base.SQL;
import com.raycloud.dmj.table.api.plus.insert.Inserts;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.redisson.api.listener.MessageListener;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.lang.reflect.Array;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@RequiredArgsConstructor
@Component
public class DruidQueryLogger implements ApplicationListener<ContextRefreshedEvent> {

    private final AdvanceLogger logger = AdvanceLogger.log(this.getClass());

    private final KmerpDatasourceConfiguration kmerpDatasourceConfiguration;

    private final RedissonClient client;

    private final ThreadPoolExecutor executor = new ThreadPoolExecutor(
            1,
            4,
            10,
            TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(1000),
            new ThreadFactoryBuilder().setNameFormat("sql-log-thread-pool-%d").setDaemon(false).build(),
            new ThreadPoolExecutor.DiscardPolicy()
    );

    /**
     * 开关缓存
     */
    private final Cache<String, Boolean> OPEN_CACHE = CacheBuilder.newBuilder()
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .maximumSize(2000)
            .build();


    public void init() {
        logger.info("druid query logger init ...");
        client.getTopic("erp-data:report-log:open")
                .addListener(Long.class, new MessageListener<Long>() {
                    @Override
                    public void onMessage(CharSequence channel, Long companyId) {
                        String key = "erp-data:report-log:company:" + companyId;
                        OPEN_CACHE.put(key, true);
                        System.out.println("> 公司：" + companyId + " , 开启SQL记录");
                    }
                });

        client.getTopic("erp-data:report-log:close")
                .addListener(Long.class, new MessageListener<Long>() {
                    @Override
                    public void onMessage(CharSequence channel, Long companyId) {
                        String key = "erp-data:report-log:company:" + companyId;
                        OPEN_CACHE.put(key, false);
                        System.out.println("> 公司：" + companyId + " , 关闭SQL记录");
                    }
                });
    }

    public void saveAsync(Long companyId, Long clueId, String sql, Object args, Object remark) {
        executor.execute(new Runnable() {
            @Override
            public void run() {
                save(companyId, clueId, sql, args, remark);
            }
        });
    }

    public void save(Long clueId, Long companyId, String sql, Object args, Object remark) {

        try {

            boolean check = openLogCheck(companyId);
            if (!check) {
                return;
            }

            sql = sql.replaceAll("'", "\\\\'");

            String queryArgs = "";
            List<Object> temp = new ArrayList<>();
            if (args != null) {
                if (args instanceof Collection) {
                    for (Object obj : ((Collection) args)) {
                        convertParameterData(temp, obj);
                    }
                } else if (args.getClass().isArray()) {
                    int length = Array.getLength(args);
                    if (length > 0) {
                        for (int i = 0; i < length; i++) {
                            Object obj = Array.get(args, i);
                            convertParameterData(temp, obj);
                        }
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(temp)) {
                queryArgs = temp.toString();
            }

            String queryArgTypes = "";
            if (remark != null) {
                queryArgTypes = remark.toString();
            }

            SQL valueSql = Inserts.insert()
                    .into("report_query_log")
                    .columns("clue_id", "company_id", "query_sql", "query_args", "query_remark")
                    .value(
                            Lists.newArrayList(
                                    clueId, companyId, sql, queryArgs, queryArgTypes
                            )
                    )
                    .constant()
                    .toSql();

            JdbcTemplate jdbcTemplate = kmerpDatasourceConfiguration.getJdbcTemplate(Connectors.getReportMain());
            jdbcTemplate.update(valueSql.getSqlCode());
        } catch (Exception e) {
            logger.error("save sql log error", e);
        }
    }

    private void convertParameterData(List<Object> temp, Object obj) {
        if (obj == null) {
            temp.add(obj);
        } else if (obj instanceof Date) {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String formatted = format.format(obj);
            temp.add(formatted);
        } else {
            temp.add(obj);
        }
    }

    public boolean openLogCheck(Long companyId) {
        if (companyId == null || companyId < 0L) {
            return false;
        }

        String key = "erp-data:report-log:company:" + companyId;
        Boolean open = OPEN_CACHE.getIfPresent(key);
        if (open == null) {
            RBucket<String> bucket = client.getBucket(key);
            String openLog = String.valueOf(bucket.get());
            if (openLog != null && openLog.equalsIgnoreCase("1")) {
                OPEN_CACHE.put(key, true);
                return true;
            } else {
                OPEN_CACHE.put(key, false);
                return false;
            }
        }
        return open;

    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        init();
    }
}
