package com.raycloud.dmj.account.infra.redis;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;


@Slf4j
@EnableConfigurationProperties(KmerpRedisProperties.class)
@RequiredArgsConstructor
@Configuration
public class KmerpRedisConfiguration {

//    private final RedisConnectors redisConnectors;
//
//    public RedissonConnector getRedisConnector(String connectorId) {
//        GetConnectorConfigParameter parameter = new GetConnectorConfigParameter();
//        parameter.setConnectorId(connectorId);
//        parameter.setTenantUid(null);
//        parameter.setTenantDatabaseConnectorId(null);
//        return redisConnectors.getCacheConnector(parameter);
//    }
//
//    public RedissonClient getRedissonClient(String connectorId) {
//        RedissonConnector redissonConnector = getRedisConnector(connectorId);
//        if (redissonConnector == null) {
//            return null;
//        }
//        return redissonConnector.getRedissonClient();
//    }
//
//    @ConditionalOnProperty(name = "kmerp.redis.enable", havingValue = "true")
//    @Bean
//    public RedissonClient mainRedissonClient(KmerpRedisProperties kmerpRedisProperties) {
//        GetConnectorConfigParameter parameter = new GetConnectorConfigParameter();
//        parameter.setConnectorId(kmerpRedisProperties.getConnectorId());
//        parameter.setTenantUid(null);
//        parameter.setTenantDatabaseConnectorId(null);
//        RedissonConnector connector = redisConnectors.getConnector(parameter);
//        log.info("[mainRedissonClient] bean created");
//        return connector.getRedissonClient();
//    }
//
//    @Bean
//    @ConditionalOnMissingBean(
//            name = {"redisTemplate"}
//    )
//    public RedisTemplate<Object, Object> redisTemplate(RedisConnectionFactory redisConnectionFactory) {
//        RedisTemplate<Object, Object> template = new RedisTemplate();
//        template.setConnectionFactory(redisConnectionFactory);
//        return template;
//    }
//
//    @Bean
//    @ConditionalOnMissingBean({StringRedisTemplate.class})
//    public StringRedisTemplate stringRedisTemplate(RedisConnectionFactory redisConnectionFactory) {
//        StringRedisTemplate template = new StringRedisTemplate();
//        template.setConnectionFactory(redisConnectionFactory);
//        return template;
//    }
//
//    @Bean
//    @ConditionalOnMissingBean({RedisConnectionFactory.class})
//    public RedissonConnectionFactory redissonConnectionFactory(RedissonClient redisson) {
//        return new RedissonConnectionFactory(redisson);
//    }

}
