package com.raycloud.dmj.account.infra.utils;

import com.raycloud.dmj.account.infra.common.BizException;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

public class CollectionMapUtils {

    public static <T> Set<T> extractSet(List<Map<String, Object>> data, String key, Class<T> keyValueClass) {
        if (ObjectUtils.isEmpty(data)) {
            return new HashSet<>();
        }
        if (ObjectUtils.isEmpty(key)) {
            throw new BizException("集合Map提取,Key不能为空");
        }
        if (keyValueClass == null) {
            throw new BizException("集合Map提取,KeyValueClass不能为空");
        }
        return data.stream()
                .map(item -> item.get(key))
                .map(keyValueClass::cast)
                .collect(Collectors.toSet());
    }

    public static <T> List<T> extractList(List<Map<String, Object>> data, String key, Class<T> keyValueClass) {
        if (ObjectUtils.isEmpty(data)) {
            return new ArrayList<>();
        }
        if (ObjectUtils.isEmpty(key)) {
            throw new BizException("集合Map提取,Key不能为空");
        }
        if (keyValueClass == null) {
            throw new BizException("集合Map提取,KeyValueClass不能为空");
        }
        return data.stream()
                .map(item -> item.get(key))
                .map(keyValueClass::cast)
                .collect(Collectors.toList());
    }

    public static <K, V> Map<K, V> convertToMap(List<V> data, Function<V, K> keyFunc) {
        if (ObjectUtils.isEmpty(data)) {
            return new HashMap<>();
        }
        if (ObjectUtils.isEmpty(data)) {
            throw new BizException("集合转换Map,keyFunc不能为空");
        }
        return data.stream()
                .collect(
                        Collectors.toMap(
                                keyFunc,
                                Function.identity(),
                                (v1, v2) -> v2)
                );
    }
}
