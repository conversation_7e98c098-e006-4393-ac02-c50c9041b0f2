package com.raycloud.dmj.account.infra.utils;


public class ExceptionUtils {

    public static String getDigestMessage(Throwable throwable) {

        if (throwable == null) {
            return null;
        }
        String headStackMessage = getStackMessage(throwable);

        String tailStackMessage = getTailStackMessage(throwable);

        if (tailStackMessage == null) {
            return headStackMessage;
        }

        return headStackMessage + " ...... \n" + tailStackMessage;
    }

    private static String getStackMessage(Throwable cause) {
        if (cause == null) {
            return null;
        }
        StackTraceElement[] stackTrace = cause.getStackTrace();
        if (stackTrace == null || stackTrace.length == 0) {
            return null;
        }

        int lineCount = 0;
        LineMessageUtils messageTemplate = LineMessageUtils.create();
        String name = cause.getClass().getName();
        String message = cause.getMessage();
        if (message.length() > 1000) {
            message = message.substring(0,500) + " ...(超长省略)... " + message.substring(message.length()-500);
        }
        messageTemplate.appendLine(name + " : " + message);
        for (StackTraceElement element : stackTrace) {
            int lineNumber = element.getLineNumber();
            String fileName = element.getFileName();
            String className = element.getClassName();
            String methodName = element.getMethodName();

            messageTemplate.appendLine(className + "." + methodName + "(" + fileName + ":" + lineNumber + ")");
            lineCount++;
            if (lineCount > 5) {
                return messageTemplate.build();
            }
        }
        return messageTemplate.build();
    }

    private static String getTailStackMessage(Throwable throwable) {

        if (throwable == null) {
            return null;
        }

        Throwable tail = throwable.getCause();
        if (tail == null) {
            return null;
        }

        while (true) {
            Throwable cause = tail.getCause();
            if (cause == null) {
                break;
            }
            tail = cause;
        }

        return getStackMessage(tail);
    }
}
