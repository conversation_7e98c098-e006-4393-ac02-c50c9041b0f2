package com.raycloud.dmj.account.infra.datasource.jdbc;

import com.raycloud.dmj.data.export.core.ICallback;
import org.apache.log4j.Logger;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.ResultSetExtractor;
import org.springframework.jdbc.core.RowMapper;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 批次结果集提取器
 * @param <T>
 */
public class BatchResultSetExtractor<T> implements ResultSetExtractor<List<T>> {

    private final static Logger logger = Logger.getLogger(BatchResultSetExtractor.class);

    /**
     * 回调阀值
     */
    private Integer threshold = 4000;

    /**
     * 数据行映射器
     */
    private RowMapper<T> rowMapper;

    /**
     * 回调函数
     */
    private ICallback<List<T>> callback;

    /**
     * 数据读取累加器
     */
    private AtomicInteger counter = new AtomicInteger(0);

    public BatchResultSetExtractor(RowMapper<T> rowMapper, ICallback<List<T>> callback) {
        this.rowMapper = rowMapper;
        this.callback = callback;
    }

    public BatchResultSetExtractor(RowMapper<T> rowMapper, ICallback<List<T>> callback,Integer threshold) {
        this.threshold = threshold;
        this.rowMapper = rowMapper;
        this.callback = callback;
    }

    /**
     * 提取数据
     * @param rs
     * @return
     * @throws SQLException
     * @throws DataAccessException
     */
    @Override
    public List<T> extractData(ResultSet rs) throws SQLException, DataAccessException {
        List<T> results = new ArrayList<T>();
        int rowNum = 0;
        try{
            while (rs.next()) {
                counter.incrementAndGet();
                results.add(this.rowMapper.mapRow(rs, rowNum++));
                if (results.size() == threshold) {

                    logger.info("已读取数据条数："+counter.get());
                    // 写入文件
                    callback.accept(results);
                    results.clear();

                    logger.info("已处理数据条数："+counter.get());
                }
            }

            if (results.size() > 0) {

                logger.info("已读取数据条数："+counter.get());
                // 写入文件
                callback.accept(results);
                results.clear();

                logger.info("已处理数据条数："+counter.get());
            }

        }catch (Exception e){
            logger.error("数据提取时异常",e);
            throw new SQLException(e);
        }
        return new ArrayList<>();
    }

}
