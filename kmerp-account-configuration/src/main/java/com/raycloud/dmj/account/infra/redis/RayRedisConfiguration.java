package com.raycloud.dmj.account.infra.redis;

import org.apache.commons.lang.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.redisson.spring.data.connection.RedissonConnectionFactory;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.cache.RedisCacheWriter;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import javax.annotation.Resource;
import java.time.Duration;


@Configuration
@EnableCaching
public class RayRedisConfiguration {

    @Resource
    private RayRedisSingleServerConfiguration singleServerConfiguration;

    @Bean
    public RedissonClient redissonClient(ServerConfig serverConfig) {
        Config config = singleServerConfiguration.createConfig(serverConfig);

        return Redisson.create(config);
    }

    @Bean
    public RedissonConnectionFactory redissonConnectionFactory(RedissonClient redissonClient) {

        return new RedissonConnectionFactory(redissonClient);
    }

    @Bean(name = "redisTemplate")
    public RedisTemplate<String, Object> redisTemplate(RedissonConnectionFactory redissonConnectionFactory) {
        RedisTemplate<String, Object> redisTemplate = new RedisTemplate<>();
        redisTemplate.setConnectionFactory(redissonConnectionFactory);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(new GenericJackson2JsonRedisSerializer());
        redisTemplate.setHashKeySerializer(new StringRedisSerializer());
        redisTemplate.setHashValueSerializer(new GenericJackson2JsonRedisSerializer());
        redisTemplate.afterPropertiesSet();
        return redisTemplate;
    }

    @Bean
    public RayRedisTemplate rayRedisTemplate(RedisTemplate<String, Object> redisTemplate, ServerConfig serverConfig) {
        return new RayRedisTemplate(redisTemplate, serverConfig.getCacheKeyPrefix());
    }

    @Bean
    public RedisCacheManager cacheManager(RedissonConnectionFactory redisConnectionFactory, ServerConfig serverConfig) {

        RedisCacheConfiguration redisCacheConfiguration = RedisCacheConfiguration.defaultCacheConfig()
                //默认是分钟
                .entryTtl(Duration.ofMinutes(serverConfig.getDefaultExpire()))
                .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair.fromSerializer(new GenericJackson2JsonRedisSerializer()));

        if(StringUtils.isNotEmpty(serverConfig.getCacheKeyPrefix())){
            redisCacheConfiguration.prefixCacheNameWith(serverConfig.getCacheKeyPrefix());
        }

        return new RayRedisCacheManager(RedisCacheWriter.nonLockingRedisCacheWriter(redisConnectionFactory), redisCacheConfiguration);
    }

}
