package com.raycloud.dmj.account.infra.repository.base;

import com.raycloud.dmj.account.infra.datasource.KmerpDatasourceConfiguration;
import com.raycloud.dmj.account.infra.repository.cache.MemoryRepositoryStorage;
import com.raycloud.dmj.kmbi.connector.jdbc.JdbcConnector;
import com.raycloud.dmj.table.api.plus.common.base.SQL;
import lombok.Data;
import org.springframework.transaction.TransactionStatus;

import java.util.List;
import java.util.Map;
import java.util.function.Function;

@Data
public class DbRepository {

    protected KmerpDatasourceConfiguration kmerpDatasourceConfiguration;

    protected JdbcConnector connector;

    protected MemoryRepositoryStorage tableStorage;

    protected DbQuery dbQuery;

    public DbRepository(KmerpDatasourceConfiguration configuration, DbQuery dbQuery, JdbcConnector connector) {
        this.kmerpDatasourceConfiguration = configuration;
        this.dbQuery = dbQuery;
        this.connector = connector;
        this.tableStorage = new MemoryRepositoryStorage();
    }

    @SuppressWarnings("unchecked")
    public <T> TableRepository<T> getTable(Class<T> entityClass, Integer tableNo) {
        TableRepository<T> repository = (TableRepository<T>) tableStorage.get(
                entityClass.getName() + "#" + tableNo,
                TableRepository.class,
                () -> {
                    TableRepository<T> temp = new TableRepository<>(connector, entityClass);
                    temp.init(tableNo);
                    return temp;
                }
        );
        repository.setConnector(connector);
        return repository;
    }

    public <T> TableRepository<T> getTable(Class<T> entityClass) {
        return getTable(entityClass, null);
    }

    public void close() {
        tableStorage.close();
    }

    public List<Map<String, Object>> queryList(SQL sql) {
        return dbQuery.queryList(connector, sql);
    }

    public Map<String, Object> queryOne(SQL sql) {
        return dbQuery.queryOne(connector, sql);
    }

    public <R> List<R> queryList(SQL sql, Class<R> clazz) {
        return dbQuery.queryList(connector, sql, clazz);
    }

    public <R> R queryOne(SQL sql, Class<R> clazz) {
        return dbQuery.queryOne(connector, sql, clazz);
    }

    public void execute(String sql) {
        dbQuery.execute(connector, sql);
    }

    public <R> R doTransaction(Function<TransactionStatus, R> function) {
        return kmerpDatasourceConfiguration.doTransaction(connector, function);
    }
}
