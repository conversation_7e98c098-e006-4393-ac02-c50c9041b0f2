package com.raycloud.dmj.account.infra.session;

import com.raycloud.cache.CacheException;
import com.raycloud.cache.ICache;
import com.raycloud.dmj.account.infra.common.BusinessException;
import com.raycloud.dmj.account.infra.common.ErrorCodeEnum;
import com.raycloud.dmj.account.infra.dubbo.KmerpDubboConfiguration;
import com.raycloud.dmj.domain.Status;
import com.raycloud.dmj.domain.account.Company;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.domain.utils.ArrayUtils;
import com.raycloud.dmj.services.account.ICompanyService;
import com.raycloud.dmj.services.account.IStaffService;
import com.raycloud.dmj.services.user.IUserService;
import com.raycloud.dmj.session.ISession;
import com.raycloud.dmj.session.ISessionManager;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.session.controller.StaffSessionContext;
import com.raycloud.dmj.session.enums.NormalMessageEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.ModelAttribute;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <pre>
 * ERP中使用到的Controller的基类，所有ERP的Controller都必须继承这个类。
 * 基类会引用 {@link ISessionManager}, {@link ICache}, {@link IUserService}, {@link ICompanyService}, {@link IStaffService}，所以子类不需要再次引用这些服务
 * </pre>
 * <AUTHOR>
 * @date 2018/7/13
 **/
public class SessionController {

    protected final Logger logger = Logger.getLogger(this.getClass());

    private static final String DEFAULT_DOMAIN = "superboss.cc";

    private static final String Staff_Cache_Key_Pre = "staff_";
    private static final String Company_Cache_Key_Pre = "company_fill_profile_";
    private static final String user_cache_key_pre = "company_users_";

    @Resource
    protected ICache cache;

    @Resource
    protected ISessionManager sessionManager;

    @Resource
    protected KmerpDubboConfiguration kmerpDubboConfiguration;

    @Deprecated
    @Autowired
    protected HttpServletRequest request;

    @Deprecated
    protected HttpServletResponse response;

    @ModelAttribute
    public void setResponse(HttpServletResponse response){
        this.response = response;
    }

    @Deprecated
    public HttpServletRequest getRequest() {
        return request;
    }

    public void setRequest(HttpServletRequest request) {
        this.request = request;
    }

    @Deprecated
    public HttpServletResponse getResponse() {
        return response;
    }

    /**
     * 是否为开发模式，此选项需要设置tomcat的启动参数,dev.open=true，不要在线上环境开启此参数
     *
     * @return
     */
    protected boolean isDevMode() {
        String devOpen = System.getProperty("dev.open");
        return StringUtils.equals(devOpen, "true");
    }

    protected ISession getSession() throws SessionException {
        return sessionManager.getSession(request, response);
    }

    protected ISession createSession(Staff staff) throws SessionException {
        return sessionManager.createStaffSession(staff, request, response, getDomain());
    }

    protected ISession createScmAppSession(Staff staff) throws SessionException {
        return sessionManager.createScmAppStaffSession(staff, request, response, getDomain());
    }

    protected ISession createSameSiteNoneSession(Staff staff) throws SessionException {
        return sessionManager.createSameSiteNoneSession(staff, request, response, getDomain());
    }

    /**
     * 从会话缓存中获取员工详情信息
     *
     * @return
     * @throws SessionException
     * @throws CacheException
     */
    public Staff getStaff() throws SessionException {
        return handleSessionStaff(getLightStaff());
    }

    /**
     * 获取对帐系统用户信息
     * @return
     */
    public AccountUser getAccountUser() {
        Staff staff = null;
        try {
            staff = getStaff();
            AccountUser accountUser = new AccountUser();
            accountUser.setCompanyId(staff.getCompanyId());
            accountUser.setAccountName(staff.getAccountName());
            return accountUser;
        } catch (SessionException e) {
            logger.error(e.getMessage(), e);
            throw new BusinessException(ErrorCodeEnum.SESSION_ERRoR,e.getMessage());
        }
    }

    protected Staff handleSessionStaff(Staff staff) {
        staff.setUsers(getUsers(staff));
        staff.setUserIdMap(toUserIdMap(staff));
        return staff;
    }

    /**
     * 获取用户绑定的平台用户数据
     *
     * @param staff
     * @return
     */
    private List<User> getUsers(Staff staff) {
        List<User> list = getUserFromCache(staff);

        if (staff.isDefaultStaff()) {
            for (User user : list) {
                user.setStaff(staff);
            }
            return list;
        }

        Long[] userGroups = ArrayUtils.toLongArray(staff.getUserGroup());
        if (userGroups == null) {
            return new ArrayList<User>();
        }

        Map<Long, Integer> map = new HashMap<Long, Integer>(userGroups.length, 1);
        for (Long group : userGroups) {
            map.put(group, 1);
        }

        List<User> result = new ArrayList<User>();
        // 转换为map
        for (User user : list) {
            if (map.containsKey(user.getId())) {
                user.setStaff(staff);
                result.add(user);
            }
        }

        return result;
    }

    private List<User> getUserFromCache(Staff staff) {
        List<User> list = new ArrayList<User>();
        try {
            list = cache.get(user_cache_key_pre + staff.getCompanyId());
        } catch (CacheException e) {
            logger.error("获取Users缓存失败 message" + e.getMessage(), e);
        }
        if (CollectionUtils.isEmpty(list)) {
            list = kmerpDubboConfiguration.getUserService().queryByCompanyId(staff.getCompanyId());
        }
        return list;
    }

    private Map<Long, User> toUserIdMap(Staff staff) {
        if (staff.getUsers().size() == 0) {
            return new HashMap<Long, User>();
        }

        Map<Long, User> map = new HashMap<Long, User>(staff.getUsers().size(), 1);
        for (User user : staff.getUsers()) {
            map.put(user.getId(), user);
        }
        return map;
    }

    /**
     * 获取公司的信息
     *
     * @param staff
     * @return
     */
    public Company getCompany(Staff staff) {
        try {
            Company company = cache.get(Company_Cache_Key_Pre + staff.getCompanyId());
            if (company != null) {
                return company;
            }
        } catch (CacheException e) {
            logger.error("从缓存中获取company失败", e);
        }

        return kmerpDubboConfiguration.getCompanyService().queryByIdAndProfile(staff, staff.getCompanyId());
    }

    /**
     * 直接从会话中获取员工信息，获取轻量级的会话，不包含menus，users，warehouses信息
     * @return
     * @throws SessionException
     */
    public Staff getLightStaff() throws SessionException {
        Staff sessionStaff = StaffSessionContext.get();
        if (null != sessionStaff) {
            return sessionStaff;
        }

        ISession session = getSession();
        sessionStaff = session.getStaff();
        if (null == sessionStaff) {
            throw new SessionException(NormalMessageEnum.third.getNormalMessage());
        }

        //从cache中获取完成的Staff详细信息
        String shadowToken = sessionStaff.getShadowToken();
        String isVague = sessionStaff.getIsVague();

        sessionStaff = queryStaff(sessionStaff.getId());
        if (null == sessionStaff) {
            throw new SessionException(NormalMessageEnum.third.getNormalMessage());
        }

        if (canKickOut(sessionStaff, session.canKickOut())) {
            throw new SessionException(NormalMessageEnum.second.getNormalMessage());
        }

        sessionStaff.setShadowToken(shadowToken);
        sessionStaff.setIsVague(isVague);
        sessionStaff.setCompany(getCompany(sessionStaff));

        StaffSessionContext.put(sessionStaff);
        return sessionStaff;
    }

    private boolean canKickOut(Staff sessionStaff, boolean canKickOut) {
        if (sessionStaff.getIsSingleLogin() != null && sessionStaff.getIsSingleLogin() == 1) {
            return canKickOut;
        }

        return false;
    }

    private Staff queryStaff(Long staffId) {
        try {
            Staff staff = cache.get(Staff_Cache_Key_Pre + staffId);
            if (staff != null) {
                return staff;
            }
        } catch (CacheException e) {
            logger.error("从缓存中获取staff失败", e);
        }

        return kmerpDubboConfiguration.getStaffService().get(staffId);
    }

    protected Status successResponse() {
        return Status.buildSuccessStatus();
    }

    /**
     * @return
     */
    protected String getDomain(){
        String domain = System.getProperty("session.domain");
        if(domain == null){
            domain = DEFAULT_DOMAIN;
        }
        return domain;
    }

}
