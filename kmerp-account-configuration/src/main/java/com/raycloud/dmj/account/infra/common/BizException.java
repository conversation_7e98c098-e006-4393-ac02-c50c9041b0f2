package com.raycloud.dmj.account.infra.common;


import com.raycloud.dmj.account.infra.utils.StringFormatUtils;

public class BizException extends RuntimeException {

    public BizException() {
    }

    public BizException(String message) {
        super(message);
    }

    public BizException(String message, Throwable cause) {
        super(message, cause);
    }

    public BizException(Throwable cause) {
        super(cause);
    }

    public BizException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }

    public static BizException create(String messageTemplate, Object... args) {
        return new BizException(StringFormatUtils.format(messageTemplate, args));
    }
}
