package com.raycloud.dmj.account.infra.common;

import edu.emory.mathcs.backport.java.util.Arrays;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

/**
 * 获取Spring上下文
 */
@Slf4j
@Component
public class AppContextHolder implements ApplicationContextAware {

    @Getter
    private static ConfigurableApplicationContext context;

    @Override
    public void setApplicationContext(@NonNull ApplicationContext applicationContext) throws BeansException {
        context = (ConfigurableApplicationContext) applicationContext;
    }

    public static boolean hasBean(Class<?> beanClass) {
        String[] beanDefinitionNames = context.getBeanNamesForType(beanClass);
        return !ObjectUtils.isEmpty(beanDefinitionNames);
    }

    public static <T> T getBean(Class<T> beanClass) {
        return context.getBean(beanClass);
    }

    public static boolean hasBean(String beanName) {
        String[] beanDefinitionNames = context.getBeanDefinitionNames();
        if (ObjectUtils.isEmpty(beanDefinitionNames)) {
            return false;
        }
        return Arrays.asList(beanDefinitionNames).contains(beanName);
    }

    public static Object getBean(String beanName) {
        return context.getBean(beanName);
    }

    public static <T> T getBean(String beanName, Class<T> beanClass) {
        return context.getBean(beanName, beanClass);
    }

}
