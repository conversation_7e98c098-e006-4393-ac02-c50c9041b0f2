package com.raycloud.dmj.account.infra.serialize.configuration;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.raycloud.dmj.account.infra.serialize.deserializer.StringToDateDeserializer;
import com.raycloud.dmj.account.infra.serialize.serializer.DateToStringSerializer;
import com.raycloud.dmj.account.infra.serialize.serializer.LocalDateTimeToStringSerializer;
import com.raycloud.dmj.account.infra.serialize.serializer.LongToStringSerializer;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.TimeZone;

@Configuration
public class JacksonConfiguration {

    /**
     * 如果配置这个Bean，会覆盖掉默认的ObjectMapper，并且application配置内容不生效
     *
     * @return
     */
    @Bean
    public ObjectMapper objectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();

        // 注册 Java 8 时间模块
        JavaTimeModule javaTimeModule = new JavaTimeModule();

        // 添加自定义序列化/反序列化格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(formatter));
        javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(formatter));

        // 配置 Date 类型序列化格式
        javaTimeModule.addSerializer(Date.class, new DateToStringSerializer());
        javaTimeModule.addDeserializer(Date.class, new StringToDateDeserializer());

        // 配置 Long 类型序列化格式
        SimpleModule simpleModule = new SimpleModule();
        simpleModule.addSerializer(Long.class, new LongToStringSerializer());

        objectMapper.registerModules(javaTimeModule, simpleModule);
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);


        return objectMapper;
    }

    @Bean
    public Jackson2ObjectMapperBuilderCustomizer customJackson() {
        return builder -> {
            // 设置全局日期格式
            builder.simpleDateFormat("yyyy-MM-dd HH:mm:ss");
            // 设置默认时区
            builder.timeZone(TimeZone.getTimeZone("Asia/Shanghai"));
            // 允许单引号
            builder.featuresToEnable(JsonParser.Feature.ALLOW_SINGLE_QUOTES);
        };
    }

    @Bean
    public Jackson2ObjectMapperBuilder jackson2ObjectMapperBuilder() {
        return new Jackson2ObjectMapperBuilder()
                .serializers(
                        // 处理包装类型 Long
                        new LongToStringSerializer(),
                        // 处理基本类型 Date
                        new DateToStringSerializer(),
                        // 处理日期类型 LocalDateTime
                        new LocalDateTimeToStringSerializer()
                );
    }

}
