package com.raycloud.dmj.account.infra.session;

import com.raycloud.dmj.account.infra.memcache.AbstractMemcachedClientFactoryBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * user: hj
 * time: 2019/07/08 14:46
 * desc: 作为会话使用的memcacheClient
 * 最后让多环境会话共享,缓存隔离
 */
@Component("sessionMemcachedClientFactoryBean")
public class SessionMemcachedClientFactoryBean extends AbstractMemcachedClientFactoryBean implements InitializingBean {

    @Value("${session.diamond.config:}")
    private String sessionDiamondConfig;

    @Override
    public void afterPropertiesSet() throws Exception {
        initMemcachedClient(sessionDiamondConfig);
    }
}
