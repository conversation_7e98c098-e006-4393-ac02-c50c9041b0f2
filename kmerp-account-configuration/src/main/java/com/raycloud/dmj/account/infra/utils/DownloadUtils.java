package com.raycloud.dmj.account.infra.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.UUID;
@Slf4j
public class DownloadUtils {

    /**
     * 网络资源下载，指定下载的文件名
     *
     * @param sourceUrl 资源链接
     * @param downloadPath 存储路径
     * @param fileName 指定下载的文件名
     * @return 下载到本地的文件
     */
    public static File downloadFile(String sourceUrl, String downloadPath) {
        FileOutputStream fileOutputStream = null;
        BufferedInputStream bf = null;
        // 生成随机下载文件名称
        File uFile = new File(downloadPath );
        // 确保目标目录存在
        File parentDir = uFile.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            if (!parentDir.mkdirs()) {
                log.error("Failed to create directory: {}", parentDir.getAbsolutePath());
                return null;
            }
        }
        try {
            URL url = new URL(sourceUrl);
            HttpURLConnection urlConnection = (HttpURLConnection) url.openConnection();
            urlConnection.setConnectTimeout(30 * 1000);
            urlConnection.setReadTimeout(30 * 1000);
            InputStream inputStream = urlConnection.getInputStream();
            bf = new BufferedInputStream(inputStream);
            fileOutputStream = new FileOutputStream(uFile);
            byte[] buf = new byte[2048];
            int length = bf.read(buf);
            while (length != -1) {
                fileOutputStream.write(buf, 0, length);
                length = bf.read(buf);
            }
        } catch (Exception e) {
            //下载失败需要删掉文件
            FileUtils.deleteQuietly(uFile);
            log.error("download file :{} error:{}", sourceUrl, e.getMessage(), e);
            return null;
        } finally {
            try {
                if (null != bf) {
                    bf.close();
                }
                if (fileOutputStream != null) {
                    fileOutputStream.close();
                }
            } catch (IOException e) {
                log.error("close stream error:{}", e.getMessage(), e);
            }

        }
        return uFile;
    }

}
