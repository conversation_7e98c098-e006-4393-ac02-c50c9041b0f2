package com.raycloud.dmj.account.infra.common;

import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.net.InetAddress;

@RequiredArgsConstructor
@Component
@Data
public class KmerpRuntime implements ApplicationContextAware {

    private static ApplicationContext applicationContext;

    @Value("${spring.application.name}")
    private String applicationName;

    private final KmerpProperties properties;

    private static String ipAddr = "0.0.0.0";

    public static boolean printSQL() {
        KmerpRuntime bean = applicationContext.getBean(KmerpRuntime.class);
        return bean.getProperties().isPrintSql();
    }

    public static String env() {
        KmerpRuntime bean = applicationContext.getBean(KmerpRuntime.class);
        return bean.getProperties().getEnv();
    }

    public static String appName() {
        return System.getProperty("applicationName");
    }

    public static String ip() {
        try {
            if ("0.0.0.0".equals(ipAddr)) {
                String instanceIp = InetAddress.getLocalHost().getHostAddress();
                if (StringUtils.isNotBlank(instanceIp)) {
                    ipAddr = instanceIp;
                }
            }
        } catch (Exception ignored) {

        }
        return ipAddr;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        KmerpRuntime.applicationContext = applicationContext;
        System.setProperty("applicationEnv", properties.getEnv());
        System.setProperty("applicationName", applicationName);
    }
}
