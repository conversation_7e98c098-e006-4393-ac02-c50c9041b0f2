package com.raycloud.dmj.account.infra.mq.rocket;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.io.Serializable;

@Data
@ConfigurationProperties(prefix = "kmerp.mq.rocket.producer")
public class KmerpRocketMQProducerProperties implements Serializable {

    private boolean enable;

    private String nameServer;

    private String producerGroupName;

    private String unitName;

}
