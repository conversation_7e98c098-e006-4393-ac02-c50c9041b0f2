package com.raycloud.dmj.account.infra.mq.rocket;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.io.Serializable;

@Data
@ConfigurationProperties(prefix = "kmerp.mq.rocket.consumer")
public class KmerpRocketMQConsumerProperties implements Serializable {

    private boolean enable;

    private String nameServer;

    private String consumerGroupName;

    private String unitName;

    private String[] topics;

    private Integer consumeThreadMin;

    private Integer consumeThreadMax;

    private Integer consumeMessageBatchMaxSize;

    private Integer pullBatchSize;
}
