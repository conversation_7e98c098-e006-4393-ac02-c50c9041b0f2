package com.raycloud.dmj.account.infra.redis;

import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

@Component
@Getter
@Setter
@PropertySource("classpath:/config-cache/dynamic-redis-${spring.profiles.active}.properties")
public class ServerConfig {

    @Value("${project.redis.address}")
    private String address;
    @Value("${project.redis.coordinate}")
    private String coordinate;
    @Value("${project.redis.password}")
    private String password;
    @Value("${project.redis.cacheKeyPrefix}")
    private String cacheKeyPrefix;
    @Value("${project.redis.defaultExpire}")
    private int defaultExpire;
    @Value("${project.redis.database}")
    private int database;
    @Value("${project.redis.connectionPoolSize}")
    private int connectionPoolSize;
    @Value("${project.redis.connectionMinimumIdleSize}")
    private int connectionMinimumIdleSize;
    @Value("${project.redis.subscriptionConnectionPoolSize}")
    private int subscriptionConnectionPoolSize;
    @Value("${project.redis.subscriptionConnectionMinimumIdleSize}")
    private int subscriptionConnectionMinimumIdleSize;
    @Value("${project.redis.retryAttempts}")
    private int retryAttempts;
    @Value("${project.redis.retryInterval}")
    private int retryInterval;
    @Value("${project.redis.timeout}")
    private int timeout;
    @Value("${project.redis.connectTimeout}")
    private int connectTimeout;

}
