package com.raycloud.dmj.account.infra.memcache;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.raycloud.cache.ocs.hold.OcsSessionDirectHolder;
import com.taobao.diamond.manager.DiamondManager;
import com.taobao.diamond.manager.ManagerListener;
import com.taobao.diamond.manager.impl.DefaultDiamondManager;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.rubyeye.xmemcached.MemcachedClient;
import net.rubyeye.xmemcached.MemcachedClientBuilder;
import net.rubyeye.xmemcached.XMemcachedClientBuilder;
import net.rubyeye.xmemcached.auth.AuthInfo;
import net.rubyeye.xmemcached.buffer.SimpleBufferAllocator;
import net.rubyeye.xmemcached.command.BinaryCommandFactory;
import net.rubyeye.xmemcached.impl.DefaultKeyProvider;
import net.rubyeye.xmemcached.impl.KetamaMemcachedSessionLocator;
import net.rubyeye.xmemcached.transcoders.SerializingTranscoder;
import net.rubyeye.xmemcached.utils.AddrUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.FactoryBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ConfigurableApplicationContext;

import javax.annotation.PreDestroy;
import java.net.InetSocketAddress;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executor;


@Slf4j
@Data
public abstract class AbstractMemcachedClientFactoryBean implements FactoryBean<MemcachedClient>, ApplicationContextAware {

    private boolean initialized = false;

    /**
     * Memcached配置
     */
    private String config;

    /**
     * 具体的服务器地址
     */
    private String serverAddr;

    /**
     * Memcache客户端
     */
    private MemcachedClient memcachedClient;

    /**
     * 容器上下文，用于传递刷新消息
     */
    private ApplicationContext applicationContext;


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Override
    public MemcachedClient getObject() throws Exception {
        return getMemcachedClient();
    }

    @Override
    public Class<?> getObjectType() {
        return MemcachedClient.class;
    }

    @PreDestroy
    public void destroy() {
        if (getMemcachedClient() != null) {
            try {
                getMemcachedClient().shutdown();
            } catch (Exception e) {
                log.error("关闭memcachedClient出错！", e);
            }
        }
    }

    public void initMemcachedClient(String cacheDiamondConfig) {
        if (initialized) {
            return;
        }
        log.info("[{}] 初始化MemcachedClient:{} 开始", this.getClass().getSimpleName(), cacheDiamondConfig);
        if (StringUtils.isBlank(cacheDiamondConfig)) {
            throw new IllegalArgumentException("cache.diamond.config 配置信息为空，请检查diamond坐标是否配置");
        }
        String[] xy = cacheDiamondConfig.split("\\.");
        if (xy.length != 2) {
            log.error("cache.diamond.config 配置信息错误，请检查diamond坐标配置是否正确");
            throw new IllegalArgumentException("cache.diamond.config 配置信息错误，请检查diamond坐标配置是否正确");
        }

        DiamondManager manager = new DefaultDiamondManager(xy[0], xy[1], new ManagerListener() {
            public Executor getExecutor() {
                return null;
            }

            public void receiveConfigInfo(String configInfo) {
                log.info("diamond config changed:{}", configInfo);
                try {
                    refreshMemcached(configInfo);
                } catch (Exception e) {
                    log.error("diamond回调refreshMemcached失败!", e);
                    throw new RuntimeException("diamond回调refreshMemcached失败!");
                }
            }
        });
        try {
            String configInfo = manager.getAvailableConfigureInfomation(5000);
            refreshMemcached(configInfo);
        } catch (Exception e) {
            log.error("解析Memcache配置异常", e);
            throw new RuntimeException("解析Memcache配置异常,系统退出!");
        }
        this.setConfig(cacheDiamondConfig);
        this.initialized = true;

        log.info("[{}] 初始化MemcachedClient:{} 完成", this.getClass().getSimpleName(), cacheDiamondConfig);
    }

    private void refreshMemcached(String configInfo) throws Exception {
        log.info("Refresh Memcache Config:{}", configInfo);

        JSONObject memConfig = JSON.parseObject(configInfo);
        JSONObject memcachedClientConfig = memConfig.getJSONObject("memcachedClient");
        String servers = memcachedClientConfig.getString("servers");
        String name = memcachedClientConfig.getString("name");
        String pwd = memcachedClientConfig.getString("pwd");
        String port = memcachedClientConfig.getString("port");

        String newServerAddr = new StringBuilder()
                .append(servers)
                .append(port)
                .append(name)
                .append(pwd)
                .toString();

        String serverAddr = getServerAddr();
        if (StringUtils.isBlank(serverAddr) || StringUtils.isNotBlank(newServerAddr) && newServerAddr.trim().equalsIgnoreCase(getServerAddr())) {
            MemcachedClient newMemClient = createdMemcachedClient(servers, port, name, pwd);
            try {
                MemcachedClient oldmemcachedClient = getObject();
                if (oldmemcachedClient != null) {
                    oldmemcachedClient.shutdown();
                }
            } catch (Exception e) {
                log.error("关闭之前的memcachedClient报错", e);
                throw new IllegalArgumentException("关闭之前的memcachedClient报错!");
            }
            resetNewMemcachedClient(newServerAddr, newMemClient);
        } else {
            log.info("Memcache配置无变化，无需刷新");
        }

    }


    private void resetNewMemcachedClient(String newServerAddr, MemcachedClient memcachedClient) {
        this.setServerAddr(newServerAddr);
        this.setMemcachedClient(memcachedClient);

        // holder 刷新
        OcsSessionDirectHolder.setMemcachedClientForOcsSessionDirect(memcachedClient);
        // 订阅刷新
        if (applicationContext instanceof ConfigurableApplicationContext) {
            ConfigurableApplicationContext context = (ConfigurableApplicationContext) applicationContext;
            if (context.isRunning() && initialized) {
                context.publishEvent(new MemcachedConfigRefreshEvent(memcachedClient));
            }
        }

        if (log.isDebugEnabled()) {
            log.debug("重设MemcachedClient成功.");
        }
    }


    private MemcachedClient createdMemcachedClient(String servers, String port, String name, String pwd) throws Exception {
        Map<InetSocketAddress, InetSocketAddress> serverMap = getServerMap(servers + ":" + port);
        MemcachedClientBuilder builder = new XMemcachedClientBuilder(serverMap);
        configBuilder(builder, servers, port, name, pwd);
        MemcachedClient memcachedClient = builder.build();
        memcachedClient.setOpTimeout(MemcachedClient.DEFAULT_OP_TIMEOUT);
        return memcachedClient;
    }

    private Map<InetSocketAddress, InetSocketAddress> getServerMap(String servers) {
        Map<InetSocketAddress, InetSocketAddress> serverMap = null;
        if (servers != null && !servers.isEmpty()) {
            serverMap = AddrUtil.getAddressMap(servers);
        }
        return serverMap;
    }

    private void configBuilder(MemcachedClientBuilder builder, String servers, String port, String name, String pwd) {
        builder.setConfiguration(XMemcachedClientBuilder.getDefaultConfiguration());
        builder.setBufferAllocator(new SimpleBufferAllocator());
        builder.setSessionLocator(new KetamaMemcachedSessionLocator());
        builder.setTranscoder(new SerializingTranscoder());
        builder.setCommandFactory(new BinaryCommandFactory());
        builder.setConnectionPoolSize(MemcachedClient.DEFAULT_CONNECTION_POOL_SIZE);
        if (name != null) {
            Map<InetSocketAddress, AuthInfo> authInfoMap = new HashMap<InetSocketAddress, AuthInfo>();
            InetSocketAddress inetSocketAddress = new InetSocketAddress(servers, Integer.parseInt(port));
            AuthInfo authInfo = AuthInfo.plain(name, pwd);
            authInfoMap.put(inetSocketAddress, authInfo);
            builder.setAuthInfoMap(authInfoMap);
        }

        builder.setFailureMode(false);
        builder.setKeyProvider(DefaultKeyProvider.INSTANCE);
        builder.setMaxQueuedNoReplyOperations(MemcachedClient.DEFAULT_MAX_QUEUED_NOPS);
        builder.setName(null);
        builder.setEnableHealSession(true);
        builder.setHealSessionInterval(MemcachedClient.DEFAULT_HEAL_SESSION_INTERVAL);
    }


}
