package com.raycloud.dmj.account.infra.datasource;

import com.raycloud.dmj.account.infra.common.BizException;
import com.raycloud.dmj.kmbi.connector.adapter.JdbcConnectors;
import com.raycloud.dmj.kmbi.connector.api.req.GetConnectorConfigParameter;
import com.raycloud.dmj.kmbi.connector.jdbc.JdbcConnector;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.transaction.support.TransactionTemplate;

import javax.sql.DataSource;
import java.util.function.Function;

@RequiredArgsConstructor
@Configuration
public class KmerpDatasourceConfiguration {

    private final JdbcConnectors jdbcConnectors;

    public JdbcConnector getConnector(String connectorId) {
        GetConnectorConfigParameter parameter = new GetConnectorConfigParameter(
                connectorId,
                null,
                null
        );
        JdbcConnector connector = jdbcConnectors.getCacheConnector(parameter);
        if (connector == null) {
            throw new BizException("获取链接失败:" + connectorId);
        }
        return connector;
    }

    public JdbcTemplate getJdbcTemplate(String connectorId) {
        JdbcConnector connector = getConnector(connectorId);
        if (connector == null) {
            throw new BizException("获取链接失败:" + connectorId);
        }
        return connector.getJdbcTemplate();
    }

    public DataSource getDataSource(String connectorId) {
        JdbcConnector connector = getConnector(connectorId);
        if (connector == null) {
            throw new BizException("获取链接失败:" + connectorId);
        }
        return connector.getDataSource();
    }

    public TransactionTemplate getTxTemplate(String connectorId) {
        JdbcConnector connector = getConnector(connectorId);
        if (connector == null) {
            throw new BizException("获取链接失败:" + connectorId);
        }
        return connector.getTransactionTemplate();
    }

    public <T> T doTransaction(String connectorId, Function<TransactionStatus, T> function) {
        JdbcConnector connector = getConnector(connectorId);
        return doTransaction(connector, function);
    }

    public <T> T doTransaction(JdbcConnector connector, Function<TransactionStatus, T> function) {
        PlatformTransactionManager platformTransactionManager = connector.getPlatformTransactionManager();

        // 定义事务属性（传播行为、隔离级别等）
        DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
        definition.setIsolationLevel(TransactionDefinition.ISOLATION_READ_COMMITTED);
        definition.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRED);

        // 开启事务
        TransactionStatus status = platformTransactionManager.getTransaction(definition);

        try {
            T apply = function.apply(status);
            platformTransactionManager.commit(status);
            return apply;
        } catch (Throwable e) {
            platformTransactionManager.rollback(status);
            throw e;
        }
    }
}
