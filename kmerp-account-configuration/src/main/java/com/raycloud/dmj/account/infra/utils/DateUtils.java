package com.raycloud.dmj.account.infra.utils;

import com.raycloud.dmj.account.infra.common.BusinessException;
import com.raycloud.dmj.account.infra.common.ErrorCodeEnum;
import org.apache.commons.lang3.StringUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */
public class DateUtils {


    /**
     * 默认的时间格式(秒)
     */
    public static final String DEFAULT_FORMAT = "yyyy-MM-dd HH:mm:ss";

    /**
     * 获取两个日期之间的所有日期（包括起始日期和结束日期）
     *
     * @param startTime 起始日期
     * @param endTime 结束日期
     * @return 日期列表
     */
    public static List<Date> getDatesBetween(Date startTime, Date endTime) {
        List<Date> dates = new ArrayList<>();

        // 确保起始日期不晚于结束日期
        if (startTime.after(endTime)) {
            return dates;
        }

        // 使用Calendar进行日期递增
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startTime);

        // 循环添加每一天，直到超过结束日期
        while (!calendar.getTime().after(endTime)) {
            // 添加当前日期的副本，避免后续修改影响已添加的日期
            dates.add(new Date(calendar.getTimeInMillis()));
            // 日期加1天
            calendar.add(Calendar.DATE, 1);
        }

        return dates;
    }

    /**
     * 获取指定时间当天开始的时间，即当天的00:00:00
     * @param date 指定的时间
     * @return 返回当天开始的时间
     */
    public static Date getDayStartTime(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取指定时间当天结束的时间，即当天的23:59:59
     * @param date 指定的时间
     * @return 返回当天结束的时间
     */
    public static Date getDayEndTime(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    /**
     * 获取指定时间当月开始的时间，即当月第一天的00:00:00
     * @param date 指定的时间
     * @return 返回当月开始的时间
     */
    public static Date getMonthStartTime(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取指定时间当月结束的时间，即当月最后一天的23:59:59
     * @param date 指定的时间
     * @return 返回当月结束的时间
     */
    public static Date getMonthEndTime(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    /**
     * 获取指定时间当年开始的时间，即当年第一天的00:00:00
     * @param date 指定的时间
     * @return 返回当年开始的时间
     */
    public static Date getYearStartTime(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.MONTH, Calendar.JANUARY);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取指定时间当年结束的时间，即当年最后一天的23:59:59
     * @param date 指定的时间
     * @return 返回当年结束的时间
     */
    public static Date getYearEndTime(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.MONTH, Calendar.DECEMBER);
        calendar.set(Calendar.DAY_OF_MONTH, 31);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }



    /**
     * 获取指定时间点指定小时前的时间
     * 偏移
     * @param baseDate 基准时间点
     * @param offset    小时数，可以是正数（表示未来的时间）或负数（表示过去的时间）
     * @return 指定小时前的Date对象
     */
    public static Date addHoursDate(Date baseDate, int offset) {
        if (baseDate == null) {
            return null;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(baseDate);
        calendar.add(Calendar.HOUR_OF_DAY, offset);
        return calendar.getTime();
    }

    /**
     * 获取指定偏移量的日时间
     * @param date 指定的时间
     * @param offset 偏移量，正数表示未来，负数表示过去
     * @return 返回偏移后的时间
     */
    public static Date getOffsetDay(Date date, int offset) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, offset);
        return calendar.getTime();
    }

    /**
     * 获取指定偏移量的月时间
     * @param date 指定的时间
     * @param offset 偏移量，正数表示未来，负数表示过去
     * @return 返回偏移后的时间
     */
    public static Date getOffsetMonth(Date date, int offset) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.MONTH, offset);
        return calendar.getTime();
    }

    /**
     * 获取指定偏移量的年时间
     * @param date 指定的时间
     * @param offset 偏移量，正数表示未来，负数表示过去
     * @return 返回偏移后的时间
     */
    public static Date getOffsetYear(Date date, int offset) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.YEAR, offset);
        return calendar.getTime();
    }

    /**
     * 将字符串格式的日期转换为Date对象
     * @param dateStr 待转换的日期字符串
     * @return 转换后的Date对象
     */
    public static Date parse(String dateStr){
        return parse(dateStr, DEFAULT_FORMAT);
    }

    /**
     * 将字符串格式的日期转换为指定格式的Date对象
     * @param dateStr 待转换的日期字符串
     * @param format 日期格式字符串
     * @return 转换后的Date对象
     */
    public static Date parse(String dateStr, String format){
        if (dateStr == null) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        try {
            return sdf.parse(dateStr);
        } catch (Exception e) {
            throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR.getCode(), "日期转换异常！");
        }
    }

    /**
     * 将字符串格式的日期转换为指定格式的Date对象
     * @param dateStr 待转换的日期字符串
     * @param format 日期格式字符串
     * @return 转换后的Date对象
     */
    public static Date parseEndTime(String dateStr, String format,Integer dataType){
        Date date = parse(dateStr, format);
        return getEndDayByDateType(date,dataType);
    }

    /**
     * 判断date1是否在date2之后
     * @param date1 待判断的日期
     * @param date2 基准日期
     * @return true: date1在date2之后；false: date1在date2之前或date1等于date2
     */
    public static boolean isAfter(Date date1, Date date2){
        return date1.after(date2);
    }

    /**
     * 将Date格式化为默认指定格式的字符串
     * @param date     待格式化的日期
     * @return 格式化后的日期字符串
     */
    public static String formatDate(Date date) {
        if (date == null) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(DEFAULT_FORMAT);
        return sdf.format(date);
    }

    /**
     * 将Date格式化为默认指定格式的字符串
     * @param date     待格式化的日期
     * @return 格式化后的日期字符串
     */
    public static String formatDate(Date date,String  format) {
        if (date == null || StringUtils.isBlank(format)) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(date);
    }


    /**
     * 判断时间是否在指定范围内
     *
     * @param time  待检查时间
     * @param start 开始时间（包含）
     * @param end   结束时间（包含）
     * @return true: 在范围内；false: 不在范围内或时间为空
     */
    public static boolean isInRange(Date time, Date start, Date end) {
        if (time == null || start == null || end == null) {
            return false;
        }
        return time.getTime() >= start.getTime() && time.getTime() <= end.getTime();
    }

    /**
     * 根据时间类型获取当前时间的后一天的开始时间
     */
    public static Date getStartOfNextDay(Date date,Integer dateType) {
        if (date == null || dateType == null) {
            throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR.getCode(), "日期转换异常！");
        }
        switch (dateType) {
            case 1:
                return getDayStartTime(getOffsetDay(date, 1));
            case  2:
                return getDayStartTime(getOffsetMonth(date, 1));
            case  3:
                return getDayStartTime(getOffsetYear(date, 1));
            default:
                throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR.getCode(), "异常时间类型");
        }
    }

    /**
     * 根据时间类型获取当前时间的结束时间
     */
    public static Date getEndDayByDateType(Date date,Integer dateType) {
        if (date == null || dateType == null) {
            throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR.getCode(), "日期转换异常！");
        }
        switch (dateType) {
            case 1:
                return getDayEndTime(date);
            case  2:
                return getMonthEndTime(date);
            case  3:
                return getYearEndTime(date);
            default:
                throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR.getCode(), "异常时间类型");
        }
    }
}
