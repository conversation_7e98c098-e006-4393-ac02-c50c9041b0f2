package com.raycloud.dmj.account.infra.repository;

import com.raycloud.dmj.account.infra.repository.base.TableRepository;
import com.raycloud.dmj.domain.account.Staff;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;

@Slf4j
@RequiredArgsConstructor
public abstract class AbstractRepository<T> implements InitializingBean {

    @Getter
    private final Repositories repositories;

    private Class<T> entityClass;

    public TableRepository<T> getTradeMySQL(Staff staff) {
        return repositories.getTradeRepository(staff).getTable(entityClass);
    }

    public TableRepository<T> getTradeMySQL(Staff staff, Integer tableNo) {
        return repositories.getTradeRepository(staff).getTable(entityClass, tableNo);
    }

    public TableRepository<T> getTradeAdbPg(Staff staff) {
        return repositories.getTradeAdbPgRepository(staff).getTable(entityClass);
    }

    public TableRepository<T> getTradeAdbPg(Staff staff, Integer tableNo) {
        return repositories.getTradeAdbPgRepository(staff).getTable(entityClass, tableNo);
    }

    public TableRepository<T> getReportMySQL(Staff staff) {
        return repositories.getReportRepository(staff).getTable(entityClass);
    }

    public TableRepository<T> getReportMySQL(Staff staff, Integer tableNo) {
        return repositories.getReportRepository(staff).getTable(entityClass, tableNo);
    }

    public TableRepository<T> getReportAdbPg(Staff staff) {
        return repositories.getReportAdbPgRepository(staff).getTable(entityClass);
    }

    public TableRepository<T> getReportAdbPg(Staff staff, Integer tableNo) {
        return repositories.getReportAdbPgRepository(staff).getTable(entityClass, tableNo);
    }

    public TableRepository<T> getReportLindorm(Staff staff) {
        return repositories.getReportLindormRepository(staff).getTable(entityClass);
    }

    public TableRepository<T> getReportLindorm(Staff staff, Integer tableNo) {
        return repositories.getReportLindormRepository(staff).getTable(entityClass, tableNo);
    }

    public TableRepository<T> getReportMain() {
        return repositories.getReportMainRepository().getTable(entityClass);
    }

    public TableRepository<T> getReportMain(Integer tableNo) {
        return repositories.getReportMainRepository().getTable(entityClass, tableNo);
    }

    public TableRepository<T> getIndexMain() {
        return repositories.getIndexRepository().getTable(entityClass);
    }

    public TableRepository<T> getIndexMain(Integer tableNo) {
        return repositories.getIndexRepository().getTable(entityClass, tableNo);
    }

    public TableRepository<T> getPurchaseMySQL(Staff staff) {
        return repositories.getPurchaseRepository(staff).getTable(entityClass);
    }

    public TableRepository<T> getPurchaseMySQL(Staff staff, Integer tableNo) {
        return repositories.getPurchaseRepository(staff).getTable(entityClass, tableNo);
    }

    public TableRepository<T> getPurchaseAdbMySQL(Staff staff) {
        return repositories.getPurchaseAdbMySQLRepository(staff).getTable(entityClass);
    }

    public TableRepository<T> getPurchaseAdbMySQL(Staff staff, Integer tableNo) {
        return repositories.getPurchaseAdbMySQLRepository(staff).getTable(entityClass, tableNo);
    }


    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("Repository Bean Init:{}", this.getClass().getSimpleName());
        init();
    }

    @SuppressWarnings("unchecked")
    protected void init() {
        Type type = getClass().getGenericSuperclass();
        while (true) {
            if (type instanceof ParameterizedType) {
                ParameterizedType parameterizedType = (ParameterizedType) type;
                if (AbstractRepository.class.equals(parameterizedType.getRawType())) {
                    Type[] typeArgs = parameterizedType.getActualTypeArguments();
                    this.entityClass = (Class<T>) typeArgs[0];
                    break;
                } else {
                    type = ((Class<?>) parameterizedType.getRawType()).getGenericSuperclass();
                }
            } else if (type instanceof Class) {
                type = ((Class<?>) type).getGenericSuperclass();
            } else {
                throw new IllegalStateException("无法推断泛型类型，请检查子类定义");
            }
        }
    }


}
