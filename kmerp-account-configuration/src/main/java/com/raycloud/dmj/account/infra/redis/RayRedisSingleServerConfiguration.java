package com.raycloud.dmj.account.infra.redis;

import com.raycloud.secret_api.api.SecretRequest;
import com.taobao.diamond.manager.DiamondManager;
import com.taobao.diamond.manager.ManagerListener;
import com.taobao.diamond.manager.impl.DefaultDiamondManager;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.redisson.codec.JsonJacksonCodec;
import org.redisson.config.Config;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executor;

@Component
public class RayRedisSingleServerConfiguration {

    private final Logger logger = Logger.getLogger(this.getClass());

    @Resource
    private SecretRequest secretRequest;

    private DiamondManager manager = null;

    /**
     * 密码的密文
     */
    private String password;

    public Config createConfig(ServerConfig serverConfig) {
        //解密密码
        String password = getPassword(serverConfig);

        Config config = new Config();
        config.useSingleServer()
                .setAddress(serverConfig.getAddress())
                .setPassword(password)
                .setRetryAttempts(serverConfig.getRetryAttempts())
                .setRetryInterval(serverConfig.getRetryInterval())
                .setTimeout(serverConfig.getTimeout())
                .setDatabase(serverConfig.getDatabase())
                .setConnectTimeout(serverConfig.getConnectTimeout())
                .setConnectionMinimumIdleSize(serverConfig.getConnectionMinimumIdleSize())
                .setConnectionPoolSize(serverConfig.getConnectionPoolSize())
                .setSubscriptionConnectionMinimumIdleSize(serverConfig.getSubscriptionConnectionMinimumIdleSize())
                .setSubscriptionConnectionPoolSize(serverConfig.getSubscriptionConnectionPoolSize());

        config.setCodec( new JsonJacksonCodec());

        return config;
    }

    /**
     * 获取密码，并监控密码的变更
     * @param serverConfig 配置
     * @return 解密后的密码
     */
    private String getPassword(ServerConfig serverConfig) {
        String coordinate = serverConfig.getCoordinate();

        if(StringUtils.isEmpty(coordinate)) {
            return serverConfig.getPassword();
        }

        String preUrl = serverConfig.getAddress();
        if(preUrl == null){
            throw new RuntimeException("请将url配置信息设置在coordinate之前");
        }
        Map<String,String> params = new HashMap<String, String>();
        String trueJdbcUrl = preUrl.trim();
        String databaseType = "redis";
        //获取数据库信息
        {
            int index = trueJdbcUrl.indexOf("?");
            if(index > 0){
                trueJdbcUrl = trueJdbcUrl.substring(0,index);
            }

            index = trueJdbcUrl.indexOf("://");
            databaseType = trueJdbcUrl.substring(0, index);
            trueJdbcUrl = trueJdbcUrl.substring(index + 3);
            try{
                params.put("urlName", URLEncoder.encode(trueJdbcUrl, "UTF-8"));
                params.put("databaseType", URLEncoder.encode(databaseType,"UTF-8"));
                params.put("projectName",URLEncoder.encode((String)System.getProperties().get("user.home"),"UTF-8"));
                DefaultDiamondManager.penetrateParams.set(params);
            }catch(Exception e){
                logger.error("项目启动 Redis 初始化时，上下文获取异常", e);
                System.exit(-1);
            }
        }

        try{
            String[] jdbcParis = coordinate.split("\\.");
            manager = new DefaultDiamondManager(jdbcParis[0],jdbcParis[1], new DefaultManagerListener());

            password =  manager.getAvailableConfigureInfomation(5000);
            System.out.println("【"+databaseType+"】的coordinate配置["+coordinate+"="+password+"]");
            try {
                if(password != null && password.equals("dbexceptionnotallowed")){
                    throw new RuntimeException("db access not allowed,coordinate="+coordinate);
                }
                return secretRequest.decode(password);
            } catch (Exception e) {
                logger.error("项目启动 Redis 初始化时，密码解密时发生了异常：["+coordinate+"="+password+"]", e);
                throw new RuntimeException("项目启动 Redis 初始化时，密码解密时发生了异常：["+coordinate+"="+password+"]");
            }
        }finally{
            DefaultDiamondManager.penetrateParams.remove();
        }
    }

    private class DefaultManagerListener implements ManagerListener{

        public Executor getExecutor() {
            return null;
        }
        public void receiveConfigInfo(String newPassword) {
            int retry = 1;
            int retryTime = 5;
            while(retry <= retryTime) {
                try {
                    if(newPassword!=null && newPassword.equals(password)) {
                        resetPassword(secretRequest.decode(password));
                    }
                    return;
                } catch (Exception e) {
                    logger.error("Redis 的密码解密时，尝试处理第["+retry+"]次，发生了异常："+ password, e);
                    retry ++;
                }
            }
        }
    }

    /**
     * 重置链接的密码
     * @param password 密码
     */
    public void resetPassword(String password){
        //后面可以考虑，当密码变更后该怎么处理
    }




}
