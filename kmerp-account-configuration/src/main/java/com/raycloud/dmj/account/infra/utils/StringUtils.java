package com.raycloud.dmj.account.infra.utils;

import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.CharUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.io.IOException;
import java.io.StringReader;
import java.util.*;

public class StringUtils extends org.apache.commons.lang3.StringUtils {

    public static List<String> arrayStringConvertToList(String str) {
        if (isBlank(str)) {
            return null;
        }
        String content = null;
        if (str.startsWith("{") && str.endsWith("}")) {
            String substring = str.substring(1, str.length() - 1);
            if (isBlank(substring)) {
                return null;
            }
            content = substring;
        } else if (str.startsWith("[") && str.endsWith("]")) {
            String substring = str.substring(1, str.length() - 1);
            if (isBlank(substring)) {
                return null;
            }
            content = substring;
        } else {
            content = str;
        }


        if (isBlank(content)) {
            return null;
        }
        content = content.replaceAll("\"", "");
        return toStringList(content);
    }

    public static Set<String> arrayStringConvertToSet(String str) {
        if (isBlank(str)) {
            return null;
        }
        String content = null;
        if (str.startsWith("{") && str.endsWith("}")) {
            String substring = str.substring(1, str.length() - 1);
            if (isBlank(substring)) {
                return null;
            }
            content = substring;
        } else if (str.startsWith("[") && str.endsWith("]")) {
            String substring = str.substring(1, str.length() - 1);
            if (isBlank(substring)) {
                return null;
            }
            content = substring;
        } else {
            content = str;
        }

        if (isBlank(content)) {
            return null;
        }
        content = content.replaceAll("\"", "");
        return toStringSet(content);
    }

    public static Set<Object> toLongObjectSet(String str) {
        if (isBlank(str)) {
            return new HashSet<>();
        }
        str = alignArrayString(str);

        Set<Object> set = new HashSet<>();
        Iterable<String> split = Splitter.on(",").split(str);
        for (String next : split) {
            if ("\"\"".equals(next)) {
                continue;
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(next)) {
                boolean creatable = NumberUtils.isCreatable(next.trim());
                if (!creatable) {
                    throw new IllegalArgumentException("检测发现存在非数字:" + next);
                }
                set.add(NumberUtils.toLong(next));
            }
        }
        return set;
    }

    public static Set<Long> toLongSet(String str) {
        if (isBlank(str)) {
            return new HashSet<>();
        }
        str = alignArrayString(str);

        Set<Long> set = new HashSet<>();
        Iterable<String> split = Splitter.on(",").split(str);
        for (String next : split) {
            if ("\"\"".equals(next)) {
                continue;
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(next)) {
                boolean creatable = NumberUtils.isCreatable(next.trim());
                if (!creatable) {
                    throw new IllegalArgumentException("检测发现存在非数字:" + next);
                }
                set.add(NumberUtils.toLong(next));
            }
        }
        return set;
    }

    public static List<Long> toLongList(String str) {

        if (isBlank(str)) {
            return new ArrayList<>();
        }
        str = alignArrayString(str);

        List<Long> list = new ArrayList<>();
        Iterable<String> split = Splitter.on(",").split(str);
        for (String next : split) {
            if ("\"\"".equals(next)) {
                continue;
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(next)) {
                boolean creatable = NumberUtils.isCreatable(next.trim());
                if (!creatable) {
                    throw new IllegalArgumentException("检测发现存在非数字:" + next);
                }
                list.add(NumberUtils.toLong(next));
            }
        }
        return list;
    }

    public static Set<Integer> toIntegerSet(String str) {

        if (isBlank(str)) {
            return new HashSet<>();
        }
        str = alignArrayString(str);

        Set<Integer> set = new HashSet<>();
        Iterable<String> split = Splitter.on(",").split(str);
        for (String next : split) {
            if ("\"\"".equals(next)) {
                continue;
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(next)) {
                boolean creatable = NumberUtils.isCreatable(next.trim());
                if (!creatable) {
                    throw new IllegalArgumentException("检测发现存在非数字:" + next);
                }
                set.add(NumberUtils.toInt(next));
            }
        }
        return set;
    }

    public static List<Integer> toIntegerList(String str) {

        if (isBlank(str)) {
            return new ArrayList<>();
        }
        str = alignArrayString(str);

        List<Integer> list = new ArrayList<>();
        Iterable<String> split = Splitter.on(",").split(str);
        for (String next : split) {
            if ("\"\"".equals(next)) {
                continue;
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(next)) {
                boolean creatable = NumberUtils.isCreatable(next.trim());
                if (!creatable) {
                    throw new IllegalArgumentException("检测发现存在非数字:" + next);
                }
                list.add(NumberUtils.toInt(next));
            }
        }
        return list;
    }


    public static Set<Object> toStringObjectSet(String str) {

        if (isBlank(str)) {
            return new HashSet<>();
        }
        str = alignArrayString(str);

        Set<Object> set = new HashSet<>();
        Iterable<String> split = Splitter.on(",").split(str);
        for (String next : split) {
            if ("\"\"".equals(next)) {
                continue;
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(next)) {
                set.add(next.trim());
            }
        }
        return set;
    }

    public static Set<String> toStringSet(String str) {

        if (isBlank(str)) {
            return new HashSet<>();
        }
        str = alignArrayString(str);

        Set<String> set = new LinkedHashSet<>();
        Iterable<String> split = Splitter.on(",").split(str);
        for (String next : split) {
            if ("\"\"".equals(next)) {
                continue;
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(next)) {
                set.add(next.trim());
            }
        }
        return set;
    }

    public static List<String> toStringList(String str) {

        if (isBlank(str)) {
            return new ArrayList<>();
        }

        str = alignArrayString(str);

        List<String> list = new ArrayList<>();
        Iterable<String> split = Splitter.on(",").split(str);
        for (String next : split) {
            if ("\"\"".equals(next)) {
                continue;
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(next)) {
                list.add(next.trim());
            }
        }
        return list;
    }


    public static Properties toProperties(String content) {
        Properties properties = new Properties();
        if (content == null) {
            return properties;
        }
        try {
            properties.load(new StringReader(content));
        } catch (IOException e) {
            e.printStackTrace();
        }
        return properties;
    }

    /**
     * 将驼峰命名的字符串转换为下划线格式，并处理数字与字母的分隔
     *
     * @param input 驼峰命名字符串
     * @return 转换后的下划线格式字符串
     */
    public static String camelToUnderscoreWithDigits(String input) {
        // 处理 null 或空字符串的情况
        if (input == null || input.trim().isEmpty()) {
            return input;
        }

        // Step 1: 合并所有正则表达式，一次性完成转换
        String result = input.trim()
                // 1. 小写/数字后跟大写字母的情况（包含数字边界）
                .replaceAll("([a-z0-9])([A-Z])", "$1_$2")
                // 2. 连续大写后跟小写字母的情况（如 "ABCDef" -> "ABC_Def"）
                .replaceAll("([A-Z])([A-Z][a-z])", "$1_$2")
                // 3. 字母后接数字或数字后接字母的情况
                .replaceAll("(?<=\\D)(?=\\d)|(?<=\\d)(?=\\D)", "_")
                // 转换为小写
                .toLowerCase();

        // Step 2: 合并连续下划线并返回
        return result.replaceAll("_+", "_");
    }

    public static String toUnderlineFormat(String name) {
        if (isEmpty(name)) {
            return name;
        }
        StringBuilder result = new StringBuilder();
        result.append(lowerCaseName(name.substring(0, 1)));
        for (int i = 1; i < name.length(); i++) {
            String s = name.substring(i, i + 1);
            String slc = lowerCaseName(s);
            if (!s.equals(slc)) {
                result.append("_").append(slc);
            } else {
                result.append(s);
            }
        }
        return result.toString();
    }

    private static String lowerCaseName(String name) {
        return name.toLowerCase(Locale.US);
    }

    public static String toCamelFormat(String name) {
        StringBuilder result = new StringBuilder();
        // 快速检查
        if (name == null || name.isEmpty()) {
            // 没必要转换
            return "";
        } else if (!name.contains("_")) {
            // 不含下划线，仅将首字母小写
            return name.substring(0, 1).toLowerCase() + name.substring(1);
        }
        // 用下划线将原始字符串分割
        String camels[] = name.split("_");
        for (String camel : camels) {
            // 跳过原始字符串中开头、结尾的下换线或双重下划线
            if (camel.isEmpty()) {
                continue;
            }
            // 处理真正的驼峰片段
            if (result.length() == 0) {
                // 第一个驼峰片段，全部字母都小写
                result.append(camel.toLowerCase());
            } else {
                // 其他的驼峰片段，首字母大写
                result.append(camel.substring(0, 1).toUpperCase());
                result.append(camel.substring(1).toLowerCase());
            }
        }
        return result.toString();
    }

    public static String firstCharUppercase(String str) {
        if (org.apache.commons.lang3.StringUtils.isBlank(str)) {
            return str;
        }
        char charAt = str.charAt(0);
        String first = org.apache.commons.lang3.StringUtils.upperCase(CharUtils.toString(charAt));
        return first + str.substring(1);
    }


    public static String defaultValue(String str, String defaultValue) {
        if (str == null) {
            return defaultValue;
        }
        return str;
    }


    public static String convertToSQLInParameterByString(Collection<String> collection) {
        if (CollectionUtils.isEmpty(collection)) {
            return null;
        }
        List<String> list = new ArrayList<>();
        for (String data : collection) {
            list.add("'" + data + "'");
        }
        return Joiner.on(",").join(list);
    }


    public static String convertToSQLInParameter(Collection<?> collection) {
        if (CollectionUtils.isEmpty(collection)) {
            return null;
        }
        return Joiner.on(",").join(collection);
    }


    public static Set<String> convertToSQLFuzzyArgs(Set<String> args) {
        if (CollectionUtils.isEmpty(args)) {
            return null;
        }
        Set<String> set = new HashSet<>();
        for (String arg : args) {
            set.add("%" + arg + "%");
        }
        return set;
    }

    public static Set<String> convertToSQLRightFuzzyArgs(Set<String> args) {
        if (CollectionUtils.isEmpty(args)) {
            return null;
        }
        Set<String> set = new HashSet<>();
        for (String arg : args) {
            set.add(arg + "%");
        }
        return set;
    }


    /**
     * 对齐数组类型字符串
     *
     * @param str
     * @return
     */
    public static String alignArrayString(String str) {
        if (isBlank(str)) {
            return str;
        }
        if (str.startsWith("{") && str.endsWith("}")) {
            return str.substring(1, str.length() - 1).trim();
        } else if (str.startsWith("[") && str.endsWith("]")) {
            return str.substring(1, str.length() - 1).trim();
        }
        return str;
    }

}
