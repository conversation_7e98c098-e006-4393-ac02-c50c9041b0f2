package com.raycloud.dmj.account.infra.repository.parameter;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class PageQueryParameter implements Serializable {

    private boolean enablePage = true;

    private int pageNo = 1;

    private int pageSize = 100;

    /**
     * id / uid
     */
    private String orderByColumn;

    /**
     * desc / asc
     */
    private String orderType;
}
