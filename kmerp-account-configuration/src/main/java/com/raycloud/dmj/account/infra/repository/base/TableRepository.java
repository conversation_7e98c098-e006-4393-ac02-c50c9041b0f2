package com.raycloud.dmj.account.infra.repository.base;

import com.google.common.collect.Lists;
import com.raycloud.dmj.account.infra.common.BizException;
import com.raycloud.dmj.account.infra.repository.parameter.PageParameter;
import com.raycloud.dmj.account.infra.repository.parameter.SelectParameter;
import com.raycloud.dmj.account.infra.repository.parameter.SortParameter;
import com.raycloud.dmj.account.infra.repository.parameter.WhereParameter;
import com.raycloud.dmj.kmbi.connector.jdbc.JdbcConnector;
import com.raycloud.dmj.table.api.plus.common.base.SQL;
import com.raycloud.dmj.table.api.plus.common.enums.LinkMode;
import com.raycloud.dmj.table.api.plus.common.enums.OrderType;
import com.raycloud.dmj.table.api.plus.component.$;
import com.raycloud.dmj.table.api.plus.component.column.ColumnComponent;
import com.raycloud.dmj.table.api.plus.component.condition.ConditionComponent;
import com.raycloud.dmj.table.api.plus.component.condition.utils.Conditions;
import com.raycloud.dmj.table.api.plus.delete.Deletes;
import com.raycloud.dmj.table.api.plus.insert.Inserts;
import com.raycloud.dmj.table.api.plus.query.Queries;
import com.raycloud.dmj.table.api.plus.query.api.*;
import com.raycloud.dmj.table.api.plus.update.Updates;
import dev.miku.r2dbc.mysql.util.AssertUtils;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;


public class TableRepository<T> {

    @Getter
    protected String primaryKey;

    @Getter
    protected String tableName;

    @Getter
    protected String softDeleteColumn;

    @Getter
    protected Class<T> entityClass;

    @Getter
    @Setter
    protected JdbcConnector connector;

    @Getter
    protected boolean noPrimaryKey;

    @Getter
    protected boolean noSoftDelete;

    public TableRepository(JdbcConnector connector, Class<T> entityClass) {
        this.connector = connector;
        this.entityClass = entityClass;
    }

    public void init(Integer tableNo) {
        TableMeta tableMeta = entityClass.getDeclaredAnnotation(TableMeta.class);
        if (tableMeta == null) {
            throw new BizException("缺少@TableMeta注解:" + entityClass.getName());
        }

        // 表名处理
        String tableName = tableMeta.tableName();
        if (StringUtils.isBlank(tableName)) {
            throw new BizException("@TableMeta注解缺少 tableName:" + entityClass.getName());
        }
        if (tableMeta.sharding()) {
            if (tableNo == null) {
                throw new BizException("缺少分表号 tableNo:" + tableName);
            }
            this.tableName = tableName.replace("?", String.valueOf(tableNo));
        }

        // 主键处理
        if (StringUtils.isBlank(tableMeta.primaryKey())) {
            this.noPrimaryKey = true;
        } else {
            this.primaryKey = tableMeta.primaryKey();
        }

        // 软删除字段处理
        if (StringUtils.isBlank(tableMeta.softDeleteColumn())) {
            this.noSoftDelete = true;
        } else {
            this.softDeleteColumn = tableMeta.softDeleteColumn();
        }
    }

    public T get(Long uid) {
        if (noPrimaryKey) {
            throw new IllegalArgumentException("缺少主键,不可通过主键获取数据");
        }
        String primaryKey = getPrimaryKey();
        List<ConditionComponent<?>> conditions = new ArrayList<>();
        conditions.add($.and(primaryKey, LinkMode.EQUAL, uid));
        return get(conditions);
    }

    public T get(List<ConditionComponent<?>> conditions) {
        List<T> list = getList(conditions, 1);
        if (ObjectUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    public List<T> getList(List<Long> primaryKeyList) {
        if (noPrimaryKey) {
            throw new IllegalArgumentException("缺少主键,不可通过主键获取数据");
        }
        String primaryKey = getPrimaryKey();
        List<ConditionComponent<?>> conditions = new ArrayList<>();
        conditions.add(Conditions.and(primaryKey, LinkMode.IN, primaryKeyList));
        return getList(conditions, 0);
    }

    public List<T> getList(Collection<ConditionComponent<?>> conditions) {
        return getList(conditions, 0);
    }

    public List<T> getList(Collection<ConditionComponent<?>> conditions, int limit) {
        return getList(null, conditions, limit);
    }

    public List<T> getList(List<String> columnNames, Collection<ConditionComponent<?>> conditions) {
        return getList(columnNames, conditions, 0);
    }

    public List<T> getList(List<String> columnNames, Collection<ConditionComponent<?>> conditions, int limit) {
        JdbcTemplate jdbcTemplate = getTemplate();
        String tableName = getTableName();

        QueryWhereAfter after = Queries.create()
                .from(tableName)
                .where(
                        conditions
                );

        QueryLimit select = null;
        if (ObjectUtils.isEmpty(columnNames)) {
            select = after.select();
        } else {
            select = after.select(columnNames.toArray(new String[0]));
        }

        SQL sql = null;
        if (limit > 0) {
            sql = select.limit(limit).toSql();
        } else {
            sql = select.toSql();
        }

        return jdbcTemplate.query(
                sql.getSqlCode(),
                new BeanPropertyRowMapper<>(entityClass),
                sql.getArgs().toArray(new Object[0])
        );
    }

    public List<T> getPage(List<ConditionComponent<?>> conditions, PageParameter page) {
        SelectParameter select = new SelectParameter();
        SortParameter sort = new SortParameter();
        if (!noPrimaryKey) {
            sort.setOrderByName(primaryKey);
            sort.setOrderType("asc");
            sort.setEnable(true);
        } else {
            sort.setEnable(false);
        }

        WhereParameter where = new WhereParameter();
        if (!CollectionUtils.isEmpty(conditions)) {
            where.setConditions(conditions);
            where.setNoCondition(false);
        } else {
            where.setNoCondition(true);
        }
        return getPage(select, where, sort, page);
    }

    public List<T> getPage(SelectParameter select, WhereParameter where, SortParameter sort, PageParameter page) {

        JdbcTemplate jdbcTemplate = getTemplate();

        boolean selectAll = select.isAll();
        List<String> columnNames = select.getColumnNames();
        if (!selectAll && CollectionUtils.isEmpty(columnNames)) {
            throw new IllegalArgumentException("不能为空：columnNames");
        }
        List<ColumnComponent<?>> columns = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(columnNames)) {
            for (String columnName : columnNames) {
                columns.add($.create(columnName));
            }
        }

        boolean noCondition = where.isNoCondition();
        List<ConditionComponent<?>> conditions = where.getConditions();
        if (!noCondition && CollectionUtils.isEmpty(conditions)) {
            throw new IllegalArgumentException("不能为空：conditions");
        }

        boolean sortEnable = sort.isEnable();
        boolean pageEnable = page.isEnablePage();

        String tableName = getTableName();

        QueryPoint point = null;
        QueryFrom from = Queries.create()
                .from(tableName);

        if (!noCondition) {
            QueryWhereAfter after = from.where(conditions);
            if (sortEnable) {
                QuerySelect order = after.orderBy($.create(sort.getOrderByName(), OrderType.find(sort.getOrderType())));
                if (selectAll) {
                    QueryLimit selected = order.select();
                    if (pageEnable) {
                        point = selected.page(page.getPageNo(), page.getPageSize());
                    } else {
                        point = selected;
                    }
                } else {
                    QueryLimit selected = order.select(columns);
                    if (pageEnable) {
                        point = selected.page(page.getPageNo(), page.getPageSize());
                    } else {
                        point = selected;
                    }
                }
            } else {
                QueryLimit selected;
                if (selectAll) {
                    selected = from.select();
                } else {
                    selected = from.select(columns);
                }
                if (pageEnable) {
                    point = selected.page(page.getPageNo(), page.getPageSize());
                } else {
                    point = selected;
                }
            }
        } else {
            if (sortEnable) {
                QuerySelect order = from.orderBy($.create(sort.getOrderByName(), OrderType.find(sort.getOrderType())));
                if (selectAll) {
                    QueryLimit selected = order.select();
                    if (pageEnable) {
                        point = selected.page(page.getPageNo(), page.getPageSize());
                    } else {
                        point = selected;
                    }
                } else {
                    QueryLimit selected = order.select(columns);
                    if (pageEnable) {
                        point = selected.page(page.getPageNo(), page.getPageSize());
                    } else {
                        point = selected;
                    }
                }
            } else {
                QueryLimit selected;
                if (selectAll) {
                    selected = from.select();
                } else {
                    selected = from.select(columns);
                }
                if (pageEnable) {
                    point = selected.page(page.getPageNo(), page.getPageSize());
                } else {
                    point = selected;
                }
            }
        }

        SQL sql = point.toSql();

        return jdbcTemplate.query(
                sql.getSqlCode(),
                new BeanPropertyRowMapper<>(entityClass),
                sql.getArgs().toArray(new Object[0])
        );
    }

    public List<Long> getPrimaryKeyValueList(Collection<ConditionComponent<?>> conditions, int limit) {
        JdbcTemplate jdbcTemplate = getTemplate();

        String tableName = getTableName();
        String primaryKey = getPrimaryKey();

        QueryLimit select = Queries.create()
                .from(tableName)
                .where(
                        conditions
                )
                .select(
                        primaryKey
                );

        SQL sql = null;
        if (limit > 0) {
            sql = select.limit(limit).toSql();
        } else {
            sql = select.toSql();
        }

        return jdbcTemplate.queryForList(
                sql.getSqlCode(),
                Long.class,
                sql.getArgs().toArray(new Object[0])
        );
    }

    public Long count(WhereParameter where) {
        boolean noCondition = where.isNoCondition();
        List<ConditionComponent<?>> conditions = where.getConditions();
        if (!noCondition && CollectionUtils.isEmpty(conditions)) {
            throw new IllegalArgumentException("不能为空：conditions");
        }
        return count(conditions);
    }

    public Long count(Collection<ConditionComponent<?>> conditions) {
        JdbcTemplate jdbcTemplate = getTemplate();
        String tableName = getTableName();

        SQL sql = Queries.create()
                .from(tableName)
                .where(
                        conditions
                )
                .select(
                        $.count("*")
                )
                .toSql();

        return jdbcTemplate.queryForObject(
                sql.getSqlCode(),
                Long.class,
                sql.getArgs().toArray(new Object[0])
        );
    }

    public <R> R queryOne(SQL sql,Class<R> clazz){
        List<R> res = queryList(sql, clazz);
        if (ObjectUtils.isEmpty(res)){
            return null;
        }
        return res.get(0);
    }

    public <R> List<R> queryList(SQL sql,Class<R> clazz){
        JdbcTemplate jdbcTemplate = getTemplate();
        return jdbcTemplate.queryForList(
                sql.getSqlCode(),
                clazz,
                sql.getArgs().toArray(new Object[0])
        );
    }

    public Map<String,Object> queryMapOne(SQL sql){
        List<Map<String,Object>> res = queryMapList(sql);
        if (ObjectUtils.isEmpty(res)){
            return null;
        }
        return res.get(0);
    }

    public List<Map<String,Object>> queryMapList(SQL sql){
        JdbcTemplate jdbcTemplate = getTemplate();
        return jdbcTemplate.queryForList(
                sql.getSqlCode(),
                sql.getArgs().toArray(new Object[0])
        );
    }

    public boolean add(T entity) {
        Assert.notNull(entity, "不能为空：entity");
        JdbcTemplate jdbcTemplate = getTemplate();

        String tableName = getTableName();

        SQL sql = Inserts.insert()
                .intoByEntity(tableName, entity)
                .columnNameCamelToUnderline()
                .toSql();

        return jdbcTemplate.update(
                sql.getSqlCode(),
                sql.getArgs().toArray(new Object[0])
        ) > 0;
    }

    public boolean replace(T entity) {
        Assert.notNull(entity, "不能为空：entity");
        JdbcTemplate jdbcTemplate = getTemplate();

        String tableName = getTableName();

        SQL sql = Inserts.replace()
                .intoByEntity(tableName, entity)
                .columnNameCamelToUnderline()
                .toSql();

        return jdbcTemplate.update(
                sql.getSqlCode(),
                sql.getArgs().toArray(new Object[0])
        ) > 0;
    }

    public boolean addBatch(List<T> entityList) {
        Assert.notNull(entityList, "不能为空：entityList");

        JdbcTemplate jdbcTemplate = getTemplate();

        String tableName = getTableName();

        SQL sql = Inserts.insert()
                .intoByEntities(tableName, (Collection<Object>) entityList)
                .columnNameCamelToUnderline()
                .toSql();

        return jdbcTemplate.update(
                sql.getSqlCode(),
                sql.getArgs().toArray(new Object[0])
        ) > 0;
    }

    public boolean replaceBatch(List<T> entityList) {
        Assert.notNull(entityList, "不能为空：entityList");

        JdbcTemplate jdbcTemplate = getTemplate();

        String tableName = getTableName();

        SQL sql = Inserts.replace()
                .intoByEntities(tableName, (Collection<Object>) entityList)
                .columnNameCamelToUnderline()
                .toSql();

        return jdbcTemplate.update(
                sql.getSqlCode(),
                sql.getArgs().toArray(new Object[0])
        ) > 0;
    }


    public boolean updateByPrimaryKey(T entity, Long uid) {
        if (noPrimaryKey) {
            throw new IllegalArgumentException("缺少主键,不可通过主键更新数据");
        }
        Assert.notNull(entity, "不能为空：entity");
        Assert.notNull(uid, "不能为空：uid");

        String primaryKey = getPrimaryKey();

        List<ConditionComponent<?>> conditions = new ArrayList<>();
        conditions.add($.and(primaryKey, LinkMode.EQUAL, uid));

        return update(entity, conditions);
    }

    public boolean update(T entity, List<ConditionComponent<?>> conditions) {
        Assert.notNull(entity, "不能为空：entity");
        Assert.notEmpty(conditions, "不能为空：conditions");

        JdbcTemplate jdbcTemplate = getTemplate();

        String tableName = getTableName();

        SQL sql = Updates.create()
                .table(tableName)
                .where(
                        conditions
                )
                .updateForEntity(entity, true)
                .updateValueIgnoreConditionKey()
                .onlyUpdateValueNotNull()
                .toSql();

        return jdbcTemplate.update(
                sql.getSqlCode(),
                sql.getArgs().toArray(new Object[0])
        ) > 0;
    }

    public boolean updateBatchByMap(Map<Long, Map<String, Object>> mapList) {
        if (noPrimaryKey) {
            throw new IllegalArgumentException("缺少主键,不可通过主键更新数据");
        }
        AssertUtils.requireNonNull(!ObjectUtils.isEmpty(mapList), "不能为空:map");

        JdbcTemplate jdbcTemplate = getTemplate();

        String tableName = getTableName();
        String primaryKey = getPrimaryKey();

        String sqlStr = "";
        List<Object[]> args = Lists.newArrayList();

        for (Map.Entry<Long, Map<String, Object>> entry : mapList.entrySet()) {
            SQL sql = Updates.create()
                    .table(tableName)
                    .where(
                            Conditions.and(primaryKey, LinkMode.EQUAL, entry.getKey())
                    )
                    .updateForMap(entry.getValue())
                    .updateValueIgnoreConditionKey()
                    .onlyUpdateValueNotNull()
                    .toSql();

            if (StringUtils.isBlank(sqlStr)) {
                sqlStr = sql.getSqlCode();
            }
            args.add(sql.getArgs().toArray(new Object[0]));
        }

        jdbcTemplate.batchUpdate(sqlStr, args);

        return true;
    }

    public boolean softDeleteByPrimaryKey(Long uid) {
        return softDeleteByPrimaryKeyList(Lists.newArrayList(uid));
    }

    public boolean softDeleteByPrimaryKeyList(List<Long> uidList) {
        Assert.notEmpty(uidList, "不能为空：uidList");

        String primaryKey = getPrimaryKey();
        List<ConditionComponent<?>> conditions = new ArrayList<>();
        conditions.add($.and(primaryKey, LinkMode.IN, uidList));
        return softDelete(conditions);
    }

    public boolean softDelete(List<ConditionComponent<?>> conditions) {

        if (noSoftDelete) {
            throw new IllegalArgumentException("缺少软删除状态字段,不可进行软删除");
        }

        JdbcTemplate jdbcTemplate = getTemplate();
        String tableName = getTableName();


        SQL sql = Updates.create()
                .table(tableName)
                .where(
                        conditions
                )
                .update(
                        $.updateKeyValue(softDeleteColumn, 0)
                )
                .updateValueIgnoreConditionKey()
                .onlyUpdateValueNotNull()
                .toSql();

        jdbcTemplate.update(sql.getSqlCode(), sql.getArgs().toArray(new Object[0]));
        return true;
    }

    public boolean deleteByPrimaryKeyList(List<Long> uidList) {
        Assert.notEmpty(uidList, "不能为空：uidList");

        String primaryKey = getPrimaryKey();
        List<ConditionComponent<?>> conditions = new ArrayList<>();
        conditions.add($.and(primaryKey, LinkMode.IN, uidList));
        return delete(conditions);
    }

    public boolean delete(List<ConditionComponent<?>> conditions) {

        Assert.notEmpty(conditions, "不能为空：conditions");

        JdbcTemplate jdbcTemplate = getTemplate();

        String tableName = getTableName();

        SQL sql = Deletes.create()
                .from(tableName)
                .where(
                        conditions
                )
                .toSql();

        jdbcTemplate.update(sql.getSqlCode(), sql.getArgs().toArray(new Object[0]));
        return true;
    }

    public boolean execute(String sql) {
        JdbcTemplate jdbcTemplate = getTemplate();
        return jdbcTemplate.update(sql) > 0;
    }

    protected JdbcTemplate getTemplate() {
        return connector.getJdbcTemplate();
    }

}
