package com.raycloud.dmj.account.infra.mq.rocket;

import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@EnableConfigurationProperties(value = {
        KmerpRocketMQProducerProperties.class,
        KmerpRocketMQConsumerProperties.class
})
@Configuration
public class KmerpRocketMQConfiguration {

    @ConditionalOnProperty(prefix = "kmerp.mq.rocket.producer", name = "enable", havingValue = "true")
    @Bean
    public DefaultMQProducer rocketMQProducer(KmerpRocketMQProducerProperties kmerpRocketMQProducerProperties) throws MQClientException {
        DefaultMQProducer producer = new DefaultMQProducer();
        producer.setNamesrvAddr(kmerpRocketMQProducerProperties.getNameServer());
        producer.setProducerGroup(kmerpRocketMQProducerProperties.getProducerGroupName());
        producer.setUnitName(kmerpRocketMQProducerProperties.getUnitName());
        producer.setSendMessageWithVIPChannel(false);
        producer.start();
        return producer;
    }

    @ConditionalOnProperty(prefix = "kmerp.mq.rocket.producer", name = "enable", havingValue = "false")
    @Bean
    public DefaultMQProducer rocketMQMockProducer(KmerpRocketMQProducerProperties kmerpRocketMQProducerProperties) throws MQClientException {
        DefaultMQProducer producer = new DefaultMQProducer();
        producer.setNamesrvAddr(kmerpRocketMQProducerProperties.getNameServer());
        producer.setProducerGroup(kmerpRocketMQProducerProperties.getProducerGroupName());
        producer.setUnitName(kmerpRocketMQProducerProperties.getUnitName());
        producer.setSendMessageWithVIPChannel(false);
        return producer;
    }
}
