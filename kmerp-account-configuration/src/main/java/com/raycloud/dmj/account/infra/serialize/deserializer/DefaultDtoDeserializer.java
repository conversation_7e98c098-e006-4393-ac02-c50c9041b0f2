package com.raycloud.dmj.account.infra.serialize.deserializer;

import com.alibaba.fastjson.parser.DefaultJSONParser;
import com.alibaba.fastjson.parser.JSONToken;
import com.alibaba.fastjson.parser.deserializer.ObjectDeserializer;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;

import java.lang.reflect.Type;

public class DefaultDtoDeserializer implements ObjectDeserializer {

    @Override
    public <T> T deserialze(DefaultJSONParser parser, Type type, Object fieldName) {
        // 获取原始 JSON 值
        Object value = parser.parse();

        // 处理字符串格式
        if (value instanceof String) {
            String strValue = (String) value;
            return JSON.parseObject(strValue, type); // 将字符串解析为目标类
        }
        // 处理对象格式（直接转换）
        else if (value instanceof JSONObject) {
            return  ((JSONObject) value).to(type);
        }
        return null;
    }

    @Override
    public int getFastMatchToken() {
        return JSONToken.UNDEFINED; // 处理所有类型
    }
}
