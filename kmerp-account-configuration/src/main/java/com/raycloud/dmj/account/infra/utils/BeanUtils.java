package com.raycloud.dmj.account.infra.utils;

import com.alibaba.fastjson2.JSON;
import org.springframework.util.ObjectUtils;

import java.util.List;

public class BeanUtils extends org.springframework.beans.BeanUtils {

    public static <T> T copy(Object source, Class<T> clazz) {
        if (ObjectUtils.isEmpty(source)) {
            return null;
        }
        return JSON.parseObject(JSON.toJSONString(source), clazz);
    }

    public static <T> List<T> copyList(List<?> source, Class<T> clazz) {
        if (ObjectUtils.isEmpty(source)) {
            return null;
        }
        return JSON.parseArray(JSON.toJSONString(source), clazz);
    }

}
