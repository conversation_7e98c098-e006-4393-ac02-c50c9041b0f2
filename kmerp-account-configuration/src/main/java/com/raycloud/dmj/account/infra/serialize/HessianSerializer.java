package com.raycloud.dmj.account.infra.serialize;

import com.alibaba.com.caucho.hessian.io.Hessian2Input;
import com.alibaba.com.caucho.hessian.io.Hessian2Output;
import org.springframework.util.Base64Utils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;

public class HessianSerializer {

    /**
     * 序列化对象到字节数组
     * @param obj
     * @return
     */
    public static byte[] serialize(Object obj) {
        try {
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            Hessian2Output out = new Hessian2Output(bos);
            out.writeObject(obj);
            out.close();
            return bos.toByteArray();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 从字节数组反序列化对象
     * @param data
     * @return
     * @param <T>
     */
    public static <T> T deserialize(byte[] data) {
        try {
            ByteArrayInputStream bis = new ByteArrayInputStream(data);
            Hessian2Input in = new Hessian2Input(bis);
            @SuppressWarnings("unchecked")
            T obj = (T) in.readObject();
            in.close();
            return obj;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


    /**
     * 序列化对象到字节数组
     * @param obj
     * @return
     */
    public static String serializeToBase64(Object obj) {

        try {
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            Hessian2Output out = new Hessian2Output(bos);
            out.writeObject(obj);
            out.close();
            byte[] byteArray = bos.toByteArray();
            return Base64Utils.encodeToString(byteArray);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 从字节数组反序列化对象
     */
    public static <T> T deserializeFromBase64(String data, Class<T> clazz) {
        try {
            byte[] bytes = Base64Utils.decodeFromString(data);
            ByteArrayInputStream bis = new ByteArrayInputStream(bytes);
            Hessian2Input in = new Hessian2Input(bis);
            @SuppressWarnings("unchecked")
            T obj = (T) in.readObject(clazz);
            in.close();
            return obj;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }


    public static Object deserializeFromBase64(String data) {
        try {
            byte[] bytes = Base64Utils.decodeFromString(data);
            ByteArrayInputStream bis = new ByteArrayInputStream(bytes);
            Hessian2Input in = new Hessian2Input(bis);
            Object obj = in.readObject();
            in.close();
            return obj;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
