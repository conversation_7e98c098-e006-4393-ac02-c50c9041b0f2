package com.raycloud.dmj.account.infra.session;

import com.raycloud.erp.db.model.DbInjector;
import lombok.Data;

import java.io.Serializable;

/**
 * 对账系统中的用户信息
 * <AUTHOR>
 */
@Data
public class AccountUser implements Serializable, DbInjector {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;
    /**
     * 公司id
     */
    private Long companyId;

    /**
     * 帐号id
     */
    private Long accountId;
    /**
     * 账号名称
     */
    private String accountName;

    @Override
    public Integer getDbKey() {
        return 0;
    }
}