package com.raycloud.dmj.account.infra.common;

import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum ErrorCodeEnum {

    //系统异常
    SYSTEM_ERROR("SYSTEM_ERROR", "系统异常！"),
    //配置异常
    CONFIG_ERROR("CONFIG_ERROR", "配置异常！"),
    //参数异常
    PARAM_ERROR("PARAM_ERROR", "参数校验异常！"),

    DB_ERROR("DB_ERROR", "操作数据库异常！"),

    ROCKETMQ_ERROR("ROCKETMQ_ERROR", "操作RocketMQ异常！"),

    /**
     * 文件处理错误
     */
    FILE_HANDLE_ERROR("FILE_HANDLE_ERROR", "文件处理错误！"),

    /**
     * 未找到对应的策略处理器
     */
    STRATEGY_NOT_FOUND("STRATEGY_NOT_FOUND", "未找到对应的策略处理器！"),

    /**
     * 资金账户编码已存在
     */
    FUND_ACCOUNT_CODE_EXISTS("FUND_ACCOUNT_CODE_EXISTS", "资金账户编码已存在！"),

    /**
     * 该资金账户下，已存在同名子类别
     */
    SUB_CATEGORY_NAME_EXISTS("SUB_CATEGORY_NAME_EXISTS", "该资金账户下，已存在同名子类别！"),

    /**
     * 该资金账户下，已存在同名类别
     */
    CATEGORY_NAME_EXISTS("CATEGORY_NAME_EXISTS", "该资金账户下，已存在同名类别！"),

    /**
     * 子类别不存在
     */
    SUB_CATEGORY_NOT_EXISTS("SUB_CATEGORY_NOT_EXISTS", "子类别不存在！"),

    /**
     * 解析规则不存在
     */
    ANALYZE_RULE_NOT_EXISTS("ANALYZE_RULE_NOT_EXISTS", "解析规则不存在！"),

    /**
     * 店铺不存在
     */
    SHOP_NOT_EXISTS("SHOP_NOT_EXISTS", "店铺不存在！"),

    /**
     * 资金账户不存在
     */
    FUND_ACCOUNT_NOT_EXISTS("FUND_ACCOUNT_NOT_EXISTS", "资金账户不存在！"),


    /**
     * 批量保存失败
     */
    BATCH_SAVE_ERROR("BATCH_SAVE_ERROR", "批量保存失败！"),

    /**
     * 账单确认失败
     */
    CONFIRM_ERROR("CONFIRM_ERROR", "账单确认失败！"),

    /**
     *
     */
    SESSION_ERRoR("SESSION_ERROR","会话异常，请重新登录！"),

    /**
     * 上期文件未导入完成
     */
    FILE_NOT_IMPORT_FINISH( "FILE_NOT_IMPORT_FINISH", "文件未导入完成！" ),

    /**
     * 分类解析中，请务重复解析！
     */
    ANALYZE_ING("ANALYZE_ING", "分类解析中，请勿重复解析！"),


    /**
     * 分类解析任务不存在
     */
    ANALYZE_TASK_NOT_EXISTS("ANALYZE_TASK_NOT_EXISTS", "分类解析任务不存在！"),

    FUND_ACCOUNT_NAME_EXISTS("FUND_ACCOUNT_NAME_EXISTS", "资金账户名称已存在！");
    private final String code;
    private final String message;

    ErrorCodeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

}