package com.raycloud.dmj.account.infra.repository.parameter;

import com.raycloud.dmj.table.api.plus.component.condition.ConditionComponent;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
public class WhereParameter implements Serializable {

    private boolean noCondition;

    private List<ConditionComponent<?>> conditions;

}
