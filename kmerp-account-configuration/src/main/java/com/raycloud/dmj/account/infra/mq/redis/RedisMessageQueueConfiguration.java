package com.raycloud.dmj.account.infra.mq.redis;

import com.raycloud.dmj.account.infra.mq.MessageResult;
import com.raycloud.dmj.account.infra.redis.KmerpRedisConfiguration;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RTopic;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.integration.dsl.IntegrationFlow;
import org.springframework.integration.dsl.IntegrationFlows;
import org.springframework.integration.handler.GenericHandler;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.MessageHeaders;

import javax.annotation.Resource;

@Slf4j
@RequiredArgsConstructor
@Configuration
public class RedisMessageQueueConfiguration {

    private final KmerpRedisConfiguration kmerpRedisConfiguration;
    @Resource
    private RedissonClient client;

    @Bean(name = "redisMessageChannel")
    public MessageChannel redisMessageChannel() {
        DirectChannel channel = new DirectChannel();
        channel.setFailover(true);
        channel.setMaxSubscribers(Integer.MAX_VALUE);
        return channel;
    }

    @Bean
    public IntegrationFlow redisMessageFlow(
            MessageChannel redisMessageChannel
    ) {
        return IntegrationFlows
                .from(redisMessageChannel)
                .handle(new GenericHandler<RedisEvent>() {
                    @Override
                    public MessageResult<Object> handle(RedisEvent payload, MessageHeaders headers) {
                        try {
                            send(payload);
                            return MessageResult.success();
                        } catch (Throwable e) {
                            log.error("redis消息推送异常", e);
                            return MessageResult.fail(e);
                        }
                    }
                })
                .get();
    }

    private void send(RedisEvent event) {
//        RedissonClient client = kmerpRedisConfiguration.getRedissonClient(event.getConnectorId());
        if (client == null) {
            throw new IllegalArgumentException("未能获取到Redis客户端:" + event.getConnectorId());
        }
        RTopic topic = client.getTopic(event.getTopic());
        topic.publish(event.getPayload());
    }
}
