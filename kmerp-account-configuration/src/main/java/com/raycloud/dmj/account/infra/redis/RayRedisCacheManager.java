package com.raycloud.dmj.account.infra.redis;

import org.springframework.data.redis.cache.RedisCache;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.cache.RedisCacheWriter;
import org.springframework.util.StringUtils;

import java.time.Duration;

public class RayRedisCacheManager extends RedisCacheManager {

    public RayRedisCacheManager(RedisCacheWriter cacheWriter, RedisCacheConfiguration defaultCacheConfiguration) {
        super(cacheWriter, defaultCacheConfiguration);
    }

    /**
     * 针对@Cacheable设置缓存过期时间
     * @param name
     * @param cacheConfig
     * @return
     */
    @Override
    protected RedisCache createRedisCache(String name, RedisCacheConfiguration cacheConfig) {
        if(StringUtils.isEmpty(name) || !name.matches(".*#\\d+")){
            //不符合规则或者空的时候默认处理
            return super.createRedisCache(name, cacheConfig);
        }
        int index = name.lastIndexOf("#");
        String newName = name.substring(0, index);

        // 解析TTL
        long ttl = Long.parseLong(name.substring(index + 1));
        //注意单位我此处用的是秒，而非毫秒
        cacheConfig = cacheConfig.entryTtl(Duration.ofSeconds(ttl));

        return super.createRedisCache(newName, cacheConfig);
    }


}
