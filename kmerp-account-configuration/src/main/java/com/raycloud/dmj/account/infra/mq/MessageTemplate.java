package com.raycloud.dmj.account.infra.mq;

import com.raycloud.dmj.account.infra.mq.event.MessageEvent;
import com.raycloud.dmj.account.infra.mq.local.LocalEvent;
import com.raycloud.dmj.account.infra.mq.redis.RedisEvent;
import com.raycloud.dmj.account.infra.mq.rocket.RocketEvent;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.core.MessagingTemplate;

import java.util.HashMap;
import java.util.Map;

@RequiredArgsConstructor
@Configuration
public class MessageTemplate {

    private final MessagingTemplate defaultMessageTemplate;

    public <T> MessageResult<T> push(MessageEvent event) {
        if (event == null) {
            return null;
        }

        Map<String, Object> headers = new HashMap<>();

        if (event instanceof LocalEvent) {
            headers.put("messageQueueType", MessageQueueType.LOCAL_SYNC.name());
        } else if (event instanceof RocketEvent) {
            headers.put("messageQueueType", MessageQueueType.ROCKETMQ.name());
        } else if (event instanceof RedisEvent) {
            headers.put("messageQueueType", MessageQueueType.REDIS.name());
        } else {
            throw new IllegalArgumentException("不支持的事件类型:" + event.getClass().getSimpleName());
        }

        return defaultMessageTemplate.convertSendAndReceive(
                "defaultInputMessageChannel",
                event,
                headers,
                MessageResult.class
        );

    }
}
