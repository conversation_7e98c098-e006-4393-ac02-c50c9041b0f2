package com.raycloud.dmj.account.infra.utils;

import java.util.regex.Pattern;

public class SQLSafeUtils {

    // 正则表达式，用于匹配可能表明SQL注入的字符
    private static final String SQL_INJECTION_PATTERN =
            "('.+--)|(--)|(\\|\\|)|(%7C%7C)|(%27)|(%3D%27)|(%3B)|(%20union%20)|(%20select%20)|(" +
                    "%20from%20)|(%20where%20)|(%20insert%20)|(%20update%20)|(%20delete%20)|(%20drop%20)|" +
                    "(\\*\\*)|(%2A%2A)|(%27%20union%20)|(%27%20select%20)|(%27%20from%20)|(%27%20where%20)|" +
                    "(admin\\()|(select\\()|(insert\\()";
    // 编译正则表达式，提高匹配效率
    private static final Pattern pattern = Pattern.compile(SQL_INJECTION_PATTERN, Pattern.CASE_INSENSITIVE);

    /**
     * 检测字符串是否包含SQL注入风险
     * @param input 需要检测的字符串
     * @return 如果存在SQL注入风险，返回true；否则返回false。
     */
    public static boolean hasRisk(String input) {
        if (input == null) {
            return false;
        }
        return pattern.matcher(input).find();
    }

    public static void main(String[] args) {
        // 测试字符串
        String safeInput = "This is a safe input";
        String riskyInput = "'; DELETE FROM users; --";
        String riskyInput2 = "admin' or 1=1--";
        // 检测字符串
        System.out.println("Is safe input SQL injection risk? " + hasRisk(safeInput));
        System.out.println("Is risky input SQL injection risk? " + hasRisk(riskyInput));
        System.out.println("Is risky input SQL injection risk? " + hasRisk(riskyInput2));
    }
}
