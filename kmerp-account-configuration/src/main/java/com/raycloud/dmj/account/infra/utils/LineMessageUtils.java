package com.raycloud.dmj.account.infra.utils;

public class LineMessageUtils {

    public static LineMessageUtils create(){
        return new LineMessageUtils();
    }

    private StringBuilder builder = new StringBuilder();

    public LineMessageUtils appendLine(String message){
        builder.append(message).append("\n");
        return this;
    }

    public LineMessageUtils appendLine(String message, Object... args){
        String format = String.format(message, args);
        builder.append(format).append("\n");
        return this;
    }

    public LineMessageUtils appendLine(){
        builder.append("--------------------------------\n");
        return this;
    }

    public String build(){
        return builder.toString();
    }
}
