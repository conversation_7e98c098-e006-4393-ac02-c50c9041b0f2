package com.raycloud.dmj.account.infra.utils;

import org.apache.commons.collections4.CollectionUtils;

import java.util.*;
import java.util.function.Function;

public class ConvertUtils extends ValueUtils{

    /**
     * 集合map 提取指定key的value
     *
     * @param maps
     * @param key
     * @param valueClass
     * @param <T>
     * @return
     */
    public static <T> Set<T> mapValueToSet(Collection<Map<String, Object>> maps, String key, Class<T> valueClass) {
        return mapValueToSet(maps, key, valueClass, null);
    }

    public static <T> Set<T> mapValueToSet(Collection<Map<String, Object>> maps, String key, Class<T> valueClass, Function<T, T> filter) {
        if (CollectionUtils.isEmpty(maps)) {
            return null;
        }
        Set<T> sets = new HashSet<>();
        for (Map<String, Object> map : maps) {
            if (!map.containsKey(key)) {
                continue;
            }
            Object val = map.get(key);
            if (val != null && val.getClass().equals(valueClass)) {
                if (filter != null) {
                    T apply = filter.apply((T) val);
                    if (apply != null) {
                        sets.add(apply);
                    }
                } else {
                    sets.add((T) val);
                }
            }
        }
        return sets;
    }


    public static <T> Set<T> toSet(Collection<?> input,Class<T> outputType){
        if (input == null){
            return null;
        }

        if (input.isEmpty()){
            return new HashSet<>();
        }

        // 目标类型 与 当前类型一致
        if (input instanceof Set){
            Object first = input.stream().findFirst().orElse(null);
            if (first!=null && first.getClass().equals(outputType)){
                return (Set)input;
            }
        }

        Set<T> out = new HashSet<>(input.size());
        for (Object in : input) {
            T temp = ValueUtils.to(in, outputType);
            if (temp!=null){
                out.add(temp);
            }
        }
        return out;
    }

    public static <T> List<T> toList(Collection<?> input, Class<T> outputType){
        if (input == null){
            return null;
        }

        if (input.isEmpty()){
            return new ArrayList<>();
        }

        // 目标类型 与 当前类型一致
        if (input instanceof List){
            Object first = input.stream().findFirst().orElse(null);
            if (first!=null && first.getClass().equals(outputType)){
                return (List)input;
            }
        }

        List<T> out = new ArrayList<>(input.size());
        for (Object in : input) {
            T temp = ValueUtils.to(in, outputType);
            if (temp!=null){
                out.add(temp);
            }
        }
        return out;
    }
}
