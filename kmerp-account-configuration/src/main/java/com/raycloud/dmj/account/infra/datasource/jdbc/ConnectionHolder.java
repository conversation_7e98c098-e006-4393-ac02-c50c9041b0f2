package com.raycloud.dmj.account.infra.datasource.jdbc;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import java.sql.Connection;
import java.sql.SQLException;

public class ConnectionHolder {

    protected static final Log logger = LogFactory.getLog(ConnectionHolder.class);

    public static ThreadLocal<Connection> CON_LOCAL = new ThreadLocal<>();

    public static void set(Connection connection){
        CON_LOCAL.set(connection);
    }

    public static void commit(){
        Connection connection = CON_LOCAL.get();
        try {
            if (connection!=null && !connection.getAutoCommit()){
                connection.commit();
                connection.setAutoCommit(true);
            }
        } catch (SQLException throwables) {
            logger.error("手动提交事务时发生异常",throwables);
        }finally {
            CON_LOCAL.remove();
        }
    }
}
