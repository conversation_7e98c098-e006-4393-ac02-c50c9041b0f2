package com.raycloud.dmj.account.infra.mq.rocket;

import com.raycloud.dmj.account.infra.mq.MessageResult;
import com.raycloud.dmj.account.infra.serialize.HessianSerializer;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.exception.MQBrokerException;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.remoting.exception.RemotingException;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.integration.dsl.IntegrationFlow;
import org.springframework.integration.dsl.IntegrationFlows;
import org.springframework.integration.handler.GenericHandler;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.MessageHeaders;

@Slf4j
@RequiredArgsConstructor
@Configuration
public class RocketMessageQueueConfiguration {

    private final DefaultMQProducer defaultMQProducer;

    @Bean(name = "rocketMessageChannel")
    public MessageChannel rocketMessageChannel() {
        DirectChannel channel = new DirectChannel();
        channel.setFailover(true);
        channel.setMaxSubscribers(Integer.MAX_VALUE);
        return channel;
    }

    @Bean
    public IntegrationFlow rocketMessageFlow(
            MessageChannel rocketMessageChannel
    ) {
        return IntegrationFlows
                .from(rocketMessageChannel)
                .handle(new GenericHandler<RocketEvent>() {
                    @Override
                    public MessageResult<SendResult> handle(RocketEvent payload, MessageHeaders headers) {
                        try {
                            SendResult send = send(payload);
                            return MessageResult.success(send);
                        } catch (Throwable e) {
                            log.error("rocketmq消息推送异常", e);
                            return MessageResult.fail(e);
                        }
                    }
                })
                .get();
    }


    private SendResult send(RocketEvent event) throws MQBrokerException, RemotingException, InterruptedException, MQClientException {
        Message message = new Message();
        message.setTopic(event.getTopic());
        message.setBody(HessianSerializer.serialize(event.getPayload()));

        return defaultMQProducer.send(message);
    }
}
