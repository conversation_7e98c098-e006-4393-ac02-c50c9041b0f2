package com.raycloud.dmj.account.infra.repository;

import com.raycloud.dmj.domain.account.Staff;

public class Connectors {

    public static String getIndexMysql() {
        return "NACOS:KMERP:MYSQL:INDEX" ;
    }

    public static String getTradeMysql(Staff staff) {
        return "NACOS:KMERP:MYSQL:" + staff.getDbNo();
    }

    public static String getTradePg(Staff staff) {
        return "NACOS:KMERP:ADBPG:" + staff.getDbNo();
    }

    public static String getReportMain() {
        return "NACOS:KMERP:MYSQL:RP-MAIN";
    }

    public static String getReportMysql(Staff staff) {
        return "NACOS:KMERP:MYSQL:RP-" + staff.getReportDbNo();
    }

    public static String getReportPg(Staff staff) {
        return "NACOS:KMERP:ADBPG:RP-" + staff.getReportPgDbNo();
    }

    public static String getReportLindorm(Staff staff) {
        return "NACOS:KMERP:LINDORM:RP-" + staff.getReportDbNo();
    }

    public static String getPurchaseMysql(Staff staff) {
        return "NACOS:KMERP:MYSQL:CG-" + staff.getCaigouDbKey();
    }

    public static String getPurchaseAdbMysql(Staff staff) {
        return "NACOS:KMERP:ADBMYSQL:CG-" + staff.getCaigouDbKey();
    }


}
