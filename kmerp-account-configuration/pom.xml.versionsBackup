<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.raycloud.dmj</groupId>
        <artifactId>kmerp-account-system</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>kmerp-account-configuration</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>

        <!-- 导出依赖 提交任务 START -->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.raycloud.dmj</groupId>
            <artifactId>chessboard-dubbo-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.raycloud.dmj</groupId>
            <artifactId>erp-data-export-plugin</artifactId>
        </dependency>

        <dependency>
            <groupId>com.raycloud</groupId>
            <artifactId>secret-api-interface</artifactId>
        </dependency>
        <!-- 导出依赖 提交任务 END -->

        <!-- 引入快麦ERP的会话管理 START -->
        <dependency>
            <groupId>com.raycloud.dmj</groupId>
            <artifactId>erp-session-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.raycloud.dmj</groupId>
            <artifactId>erp-session-ocs</artifactId>
        </dependency>

        <dependency>
            <groupId>com.raycloud.dmj</groupId>
            <artifactId>erp-session-base</artifactId>
        </dependency>

        <dependency>
            <groupId>com.raycloud.cache</groupId>
            <artifactId>cache-ocs-direct</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.raycloud.cache</groupId>
            <artifactId>cache-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.raycloud.cache</groupId>
            <artifactId>cache-spring</artifactId>
        </dependency>

        <dependency>
            <groupId>com.googlecode.xmemcached</groupId>
            <artifactId>xmemcached</artifactId>
        </dependency>

        <dependency>
            <groupId>com.raycloud.dmj</groupId>
            <artifactId>erp-tb-common</artifactId>
        </dependency>
        <!-- 引入快麦ERP的会话管理 END -->

        <!-- ERP基础依赖 START -->
        <dependency>
            <groupId>com.raycloud.dmj</groupId>
            <artifactId>dmj-services-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.raycloud.dmj</groupId>
            <artifactId>dmj-domain-basis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.raycloud.erp</groupId>
            <artifactId>erp-db-model</artifactId>
        </dependency>
        <!-- ERP基础依赖 END -->

        <dependency>
            <groupId>com.raycloud.dmj.kmbi</groupId>
            <artifactId>kmbi-connector-adapter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.raycloud.dmj.kmbi</groupId>
            <artifactId>kmbi-jdbc-dependency</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>checker-qual</artifactId>
                    <groupId>org.checkerframework</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.raycloud.dmj.kmbi</groupId>
            <artifactId>kmbi-redission-dependency</artifactId>
        </dependency>

        <dependency>
            <groupId>com.raycloud.dmj.kmbi</groupId>
            <artifactId>kmbi-dubbo-dependency</artifactId>
        </dependency>

        <dependency>
            <groupId>com.raycloud.dmj.kmbi</groupId>
            <artifactId>kmbi-rocketmq-dependency</artifactId>
        </dependency>


        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-integration</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-batch</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>objenesis</artifactId>
                    <groupId>org.objenesis</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.integration</groupId>
            <artifactId>spring-integration-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>org.ow2.asm</groupId>
            <artifactId>asm</artifactId>
        </dependency>

    </dependencies>
</project>