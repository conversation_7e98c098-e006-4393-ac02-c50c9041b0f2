<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.raycloud.dmj.account.core.mapper.shop.ShopInfoMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.raycloud.dmj.account.core.base.domain.ShopInfoDO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="shop_code" property="shopCode" jdbcType="VARCHAR"/>
        <result column="title" property="title" jdbcType="VARCHAR"/>
        <result column="short_title" property="shortTitle" jdbcType="VARCHAR"/>
        <result column="affiliated_company_id" property="affiliatedCompanyId" jdbcType="BIGINT"/>
        <result column="amount" property="amount" jdbcType="DECIMAL"/>
        <result column="account_status" property="accountStatus" jdbcType="INTEGER"/>
        <result column="enable_status" property="enableStatus" jdbcType="INTEGER"/>
        <result column="company_id" property="companyId" jdbcType="BIGINT"/>
        <result column="created" property="created" jdbcType="TIMESTAMP"/>
        <result column="modified" property="modified" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, shop_code, title, short_title, affiliated_company_id, amount,
        account_status, enable_status, company_id, created, modified
    </sql>

    <!-- 复杂查询：根据条件查询店铺列表 -->
    <select id="selectShopListWithCondition" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM shop_info
        <where>
            <if test="companyId != null">
                AND company_id = #{companyId}
            </if>
            <if test="shopCode != null and shopCode != ''">
                AND shop_code LIKE CONCAT('%', #{shopCode}, '%')
            </if>
            <if test="title != null and title != ''">
                AND title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="accountStatus != null">
                AND account_status = #{accountStatus}
            </if>
            AND enable_status = 1
        </where>
        ORDER BY created DESC
    </select>

    <!-- 统计查询 -->
    <select id="countByCondition" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM shop_info
        <where>
            <if test="companyId != null">
                AND company_id = #{companyId}
            </if>
            <if test="shopCode != null and shopCode != ''">
                AND shop_code LIKE CONCAT('%', #{shopCode}, '%')
            </if>
            <if test="title != null and title != ''">
                AND title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="accountStatus != null">
                AND account_status = #{accountStatus}
            </if>
            AND enable_status = 1
        </where>
    </select>

    <!-- 批量插入 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO shop_info (shop_code, title, short_title, affiliated_company_id, amount,
                              account_status, enable_status, company_id, created, modified)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.shopCode}, #{item.title}, #{item.shortTitle}, #{item.affiliatedCompanyId},
             #{item.amount}, #{item.accountStatus}, #{item.enableStatus}, #{item.companyId},
             #{item.created}, #{item.modified})
        </foreach>
    </insert>

    <!-- 动态更新 -->
    <update id="updateByIdSelective" parameterType="com.raycloud.dmj.account.core.base.domain.ShopInfoDO">
        UPDATE shop_info
        <set>
            <if test="title != null">
                title = #{title},
            </if>
            <if test="shortTitle != null">
                short_title = #{shortTitle},
            </if>
            <if test="amount != null">
                amount = #{amount},
            </if>
            <if test="accountStatus != null">
                account_status = #{accountStatus},
            </if>
            <if test="enableStatus != null">
                enable_status = #{enableStatus},
            </if>
            modified = NOW()
        </set>
        WHERE id = #{id}
    </update>

</mapper>