package com.raycloud.dmj.account.core.platform.dao;

import com.raycloud.dmj.account.core.platform.base.domain.SharedDataInfoDO;

import java.time.LocalDate;

public interface SharedDataInfoDao {

    Long insert(SharedDataInfoDO record);

    SharedDataInfoDO getById(Long id);

    SharedDataInfoDO getByType(Long shopId, Long companyId, String type, LocalDate date);

    int updateById(SharedDataInfoDO record);
}
