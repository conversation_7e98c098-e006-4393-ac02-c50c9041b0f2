package com.raycloud.dmj.account.core.shop.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ShopUpdateAmountRequest {

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 期初应收余额
     */
    private BigDecimal amount;

    /**
     * 期初时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startDate;

    /**
     * 对帐维度类型 1：自然月 2:财务月
     */
    private Integer reconciliationType;

    /**
     * 财务月截止时间(每个月多少号) 示例：1 代表每个月1号，最大28
     */
    private Integer reconciliationDate;
}
