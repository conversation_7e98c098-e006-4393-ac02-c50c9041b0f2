package com.raycloud.dmj.account.core.base.dao;

import com.raycloud.dmj.account.core.base.domain.ShopInfoDO;
import com.raycloud.dmj.account.core.shop.req.ShopInfoRequest;
import com.raycloud.dmj.account.core.shop.req.ShopUpdateAmountRequest;

import java.util.List;

/**
 * 店铺信息Dao
 */
public interface ShopInfoDao {

    List<ShopInfoDO> getShopList(ShopInfoRequest request, Long companyId);

    Long addShopInfo(ShopInfoDO shopInfoDO);

    Integer updateShopAmountState(List<Long> shopIdList, Integer accountStatus, Long companyId);

    ShopInfoDO getShopInfoById(Long shopId);

    /**
     * 根据Ids查询店铺信息
     * @param companyId 公司id
     * @param ids id集合
     * @return 店铺信息
     */
    List<ShopInfoDO> listShopInfoByIds( Long companyId,List<Long> ids);

    Integer updateShopAmount(Long shopId, ShopUpdateAmountRequest example, Long companyId);

    Integer updateShopCompany(List<Long> shopIdList, Long affiliatedCompanyId, Long companyId);

    Integer updateShopInfo(ShopInfoDO shopInfoDO);

    Long getPageInfo(ShopInfoRequest request, Long companyId);
}
