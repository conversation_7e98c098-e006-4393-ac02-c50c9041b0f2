package com.raycloud.dmj.account.core.tj.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 当前登录的用户信息
 */
@Setter
@Getter
public class TenantEmployeeVo implements Serializable {

    /**
     * 系统唯一编码（每个系统都会分配一个唯一不变的编码）
     */
    private String sysCode;

    /**
     * 外部租户编码
     */
    private Long tenantId;

    /**
     * 租户的名称
     */
    private String tenantName;

    /**
     * 外部租户编码
     */
    private String tenantOuterCode;

    /**
     * 员工ID
     */
    private Long employeeId;

    /**
     * 员工名称
     */
    private String employeeName;

    /**
     * 外部租户的员工编码
     */
    private String employeeOuterCode;

    /**
     * 是否是管理员
     */
    private boolean administrator = false;

    /**
     * 用户的角色列表
     */
    private List<RoleBaseVo> roles;

    /**
     * 当前用户所拥有的所有权限列表
     */
    private List<EmployeePermissionVo> permissions;

}
