package com.raycloud.dmj.account.core.rawdata.domains;

import com.alibaba.fastjson2.annotation.JSONField;
import com.raycloud.dmj.account.core.rawdata.utils.IntegerKeyToStringMapSerializer;
import lombok.Data;

import java.util.Map;

/**
 * 文件解析数量信息
 */
@Data
public class FileAnalyzeCountInfo {
    /**
     * 文件名称
     */
    private String fileName;

    /**
     * sheet信息
     */
    @JSONField(serializeUsing = IntegerKeyToStringMapSerializer.class)
    private Map<Integer,FileSheetCountInfo> fileSheetCountInfoMap;
}
