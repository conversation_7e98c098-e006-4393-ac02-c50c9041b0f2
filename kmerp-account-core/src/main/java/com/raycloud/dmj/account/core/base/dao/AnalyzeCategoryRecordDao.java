package com.raycloud.dmj.account.core.base.dao;

import com.raycloud.dmj.account.core.cleancategory.domain.object.AnalyzeCategoryRecordDO;

/**
 * <AUTHOR>
 */
public interface AnalyzeCategoryRecordDao {


    /**
     * 根据ID修改解析状态
     */
    void updateAnalyzeStatusById(AnalyzeCategoryRecordDO categoryDO);

    /**
     * 插入
     * @param categoryDO 类别
     */
    long insert(AnalyzeCategoryRecordDO categoryDO);


    /**
     * 根据公司、门店、数据源、账期查询
     * @param companyId 公司ID
     * @param shopId 门店ID
     * @param dataSource 数据源
     * @param dataRange 数据范围
     * @return 类别
     */
    AnalyzeCategoryRecordDO getByShopAndSourceAndDataRange(Long companyId, Long shopId,Integer dataSource, Integer dataRange);

}
