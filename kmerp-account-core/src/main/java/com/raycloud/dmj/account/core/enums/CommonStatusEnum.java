package com.raycloud.dmj.account.core.enums;

import lombok.Getter;

/**
 * 通用状态枚举
 * 0:弃用 1:启用
 * <AUTHOR>
 */
@Getter
public enum CommonStatusEnum {

    ON(1, "启用"),
    OFF(0, "弃用"),
    ;


    /**
     * 类型编码
     */
    private final Integer type;

    /**
     * 类型描述
     */
    private final String typeDesc;

    CommonStatusEnum(Integer type, String typeDesc) {
        this.type = type;
        this.typeDesc = typeDesc;
    }

    /**
     * 根据类型编码获取枚举实例，方便从数据库值转换为枚举
     * @param type 类型
     * @return 对应的枚举实例，若未匹配到则返回 null
     */
    public static CommonStatusEnum getByTypeCode(Integer type) {
        for (CommonStatusEnum enumObj : values()) {
            if (enumObj.getType().equals(type)) {
                return enumObj;
            }
        }
        return null;
    }
}
