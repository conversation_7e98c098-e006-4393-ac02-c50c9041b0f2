package com.raycloud.dmj.account.export.core.helper;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.raycloud.dmj.account.infra.dubbo.KmerpDubboConfiguration;
import com.raycloud.dmj.domain.account.Company;
import com.raycloud.dmj.domain.account.CompanyProfile;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.basis.StaffDataPrivilege;
import com.raycloud.dmj.services.account.ICompanyService;
import com.raycloud.dmj.services.account.IStaffService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Slf4j
@RequiredArgsConstructor
@Component
public class StaffHelper {

    private final KmerpDubboConfiguration kmerpDubboConfiguration;

    private Cache<Long, Staff> simpleStaffCache = CacheBuilder.newBuilder()
            .expireAfterAccess(1, TimeUnit.MINUTES)
            .maximumSize(200)
            .build();

    private Cache<Long, Staff> standardStaffCache = CacheBuilder.newBuilder()
            .expireAfterAccess(1, TimeUnit.MINUTES)
            .maximumSize(200)
            .build();

    private Cache<Long, Staff> defaultStaffCache = CacheBuilder.newBuilder()
            .expireAfterAccess(1, TimeUnit.MINUTES)
            .maximumSize(200)
            .build();

    public Staff defaultStaff(Long companyId) {
        Staff staff = simpleStaffCache.getIfPresent(companyId);
        if (staff != null) {
            return staff;
        }

        synchronized (companyId.toString()) {
            staff = defaultStaffCache.getIfPresent(companyId);
            if (staff != null) {
                return staff;
            }

            IStaffService staffService = kmerpDubboConfiguration.getStaffService();
            ICompanyService companyService = kmerpDubboConfiguration.getCompanyService();
            staff = staffService.queryDefaultStaffByCompanyId(companyId);
            if (staff != null) {
                Company company = staff.getCompany();
                if (company == null) {
                    company = companyService.queryById(companyId);
                    if (company == null) {
                        company = new Company();
                        company.setId(companyId);
                    }
                }
                CompanyProfile profile = company.getProfile();
                if (profile == null) {
                    profile = companyService.getCompanyProfile(companyId);
                    company.setProfile(profile);
                }
                staff.setCompany(company);
            } else {
                return null;
            }
            defaultStaffCache.put(companyId,staff);
        }

        return staff;
    }

    public Staff simulateStaff(Long companyId, Long staffId) {

        Staff staff = simpleStaffCache.getIfPresent(companyId);
        if (staff != null) {
            return staff;
        }

        synchronized (companyId.toString()) {
            staff = simpleStaffCache.getIfPresent(companyId);
            if (staff != null) {
                return staff;
            }

            staff = new Staff();
            staff.setCompanyId(companyId);
            staff.setId(staffId);


            ICompanyService companyService = kmerpDubboConfiguration.getCompanyService();
            CompanyProfile profile = companyService.getCompanyProfile(companyId);
            Company company = new Company();
            company.setId(companyId);
            company.setProfile(profile);
            staff.setCompany(company);

            simpleStaffCache.put(companyId, staff);

        }

        return staff;
    }

    public Staff getStaff(Long staffId) {

        Staff staff = standardStaffCache.getIfPresent(staffId);
        if (staff != null) {
            return staff;
        }

        synchronized (staffId.toString()) {
            staff = standardStaffCache.getIfPresent(staffId);
            if (staff != null) {
                return staff;
            }

            IStaffService staffService = kmerpDubboConfiguration.getStaffService();
            ICompanyService companyService = kmerpDubboConfiguration.getCompanyService();
            staff = staffService.queryById(staffId);
            if (staff != null) {
                Long companyId = staff.getCompanyId();
                Company company = staff.getCompany();
                if (company == null) {
                    company = companyService.queryById(companyId);
                    staff.setCompany(company);
                }

                if (company.getProfile() == null) {
                    CompanyProfile profile = companyService.getCompanyProfile(companyId);
                    company.setProfile(profile);
                }

                String powerDataPrivilegeSettings = staff.getPowerDataPrivilegeSettings();
                if (powerDataPrivilegeSettings == null) {
                    staff.setPowerDataPrivilegeSettings("");
                }
            }
            standardStaffCache.put(staffId, staff);
        }
        return staff;
    }

    public Staff standardStaff(Long staffId) {

        Staff staff = standardStaffCache.getIfPresent(staffId);
        if (staff != null) {
            return staff;
        }

        synchronized (staffId.toString()) {
            staff = standardStaffCache.getIfPresent(staffId);
            if (staff != null) {
                return staff;
            }


            IStaffService staffService = kmerpDubboConfiguration.getStaffService();
            staff = staffService.get(staffId);

            standardStaffCache.put(staffId, staff);
        }
        return staff;
    }


    public StaffDataPrivilege getStaffPrivilege(Staff staff) {
        IStaffService staffService = kmerpDubboConfiguration.getStaffService();
        return staffService.queryStaffPrivilege(staff.getId());
    }
}
