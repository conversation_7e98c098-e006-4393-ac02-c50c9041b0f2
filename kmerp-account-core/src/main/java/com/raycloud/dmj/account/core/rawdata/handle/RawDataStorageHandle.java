package com.raycloud.dmj.account.core.rawdata.handle;

import com.raycloud.dmj.account.core.rawdata.handle.param.RawDataStorageBatchInsertContext;
import com.raycloud.dmj.account.core.rawdata.handle.param.UpdateBatchStatusReq;

/**
 * 原始数据导入处理器
 * <AUTHOR>
 */
public interface RawDataStorageHandle {


    /**
     * 批量插入原始数据
     * @param param 批量插入参数
     */
    void batchInsert(RawDataStorageBatchInsertContext param);


    /**
     * 根据本地批次号修改批次监控表状态
     * @param req 修改批次状态参数
     */
    void updateBatchStatus(UpdateBatchStatusReq req);


    /**
     * 处理一直在运行中状态的任务
     */
    void handleStuckTasks();


}
