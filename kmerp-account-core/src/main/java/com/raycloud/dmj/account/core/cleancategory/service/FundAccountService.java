package com.raycloud.dmj.account.core.cleancategory.service;

import com.raycloud.dmj.account.core.cleancategory.domain.request.AddFundAccountReq;
import com.raycloud.dmj.account.core.cleancategory.domain.request.EditFundAccountReq;
import com.raycloud.dmj.account.core.cleancategory.domain.request.QueryFundAccountReq;
import com.raycloud.dmj.account.core.cleancategory.domain.vo.FundAccountVO;
import com.raycloud.dmj.account.core.cleancategory.domain.vo.SimpleFundAccountVO;
import com.raycloud.dmj.account.core.common.TreeVO;
import com.raycloud.dmj.account.infra.session.AccountUser;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface FundAccountService {


    /**
     * 添加资金账户
     * @param accountUser 用户信息，从登陆态获取
     * @param req 添加资金账户请求
     * @return 新增的ID
     */
    Long addFundAccount(AccountUser accountUser, AddFundAccountReq req);



    /**
     * 编辑资金账户
     * @param accountUser 用户信息，从登陆态获取
     * @param req 编辑资金账户请求
     */
    void editFundAccount(AccountUser accountUser, EditFundAccountReq req);



    /**
     * 查询资金账户列表
     * @param accountUser 用户信息，从登陆态获取
     * @return 资金账户列表
     */
    List<FundAccountVO> queryFundAccountList(AccountUser accountUser);




    /**
     * 授权资金账户
     * @param accountUser 用户信息，从登陆态获取
     * @param id 资金账户ID
     */
    void authorizeFundAccount(AccountUser accountUser, Long id);


    /**
     * 确认期初
     * @param accountUser 用户信息，从登陆态获取
     * @param id 资金账户ID
     */
    void confirmStartPeriod(AccountUser accountUser, Long id);

    /**
     * 查询资金账户类型
     * @param accountUser 用户信息，从登陆态获取
     * @return 资金账户类型
     */
    List<TreeVO> queryFundTypeTree(AccountUser accountUser);

    /**
     * 查询资金账户树形结构
     * @param accountUser 用户信息，从登陆态获取
     * @return 资金账户树形结构
     */
    List<TreeVO> queryFundAccountTree(AccountUser accountUser);

    /**
     * 根据店铺ID集合查询资金账户简单信息
     * @param accountUser 用户信息
     * @param req 查询信息
     * @return 资金账户简单信息列表
     */
    List<SimpleFundAccountVO> getSimpleFundAccountList(AccountUser accountUser, QueryFundAccountReq req);
}
