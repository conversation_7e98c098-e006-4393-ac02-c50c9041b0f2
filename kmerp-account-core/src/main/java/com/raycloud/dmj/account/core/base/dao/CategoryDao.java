package com.raycloud.dmj.account.core.base.dao;

import com.raycloud.dmj.account.core.cleancategory.domain.QueryCategoryParam;
import com.raycloud.dmj.account.core.cleancategory.domain.object.CategoryDO;

import java.util.List;
import java.util.Set;

/**
 * 资金账户Dao
 * <AUTHOR>
 */
public interface CategoryDao {


    /**
     * 插入
     * @param categoryDO 类别
     * @return 插入的id
     */
    Long insert(CategoryDO categoryDO);



    /**
     * 根据公司ID和平台、分类查询类别
     * @param companyId 公司ID
     * @param platformCode 平台code
     * @param name 类别名称
     * @return 类别
     */
    CategoryDO getByPlatformAndFundIdAndCategoryAndName(Long companyId,Long fundAccountId, String platformCode, String  name);

    /**
     * 根据资金账户ID查询类别
     * @param companyId 公司ID
     * @param fundAccountId 资金账户ID
     * @return 类别
     */
    List<CategoryDO> listByFundId(Long companyId,Long fundAccountId);


    /**
     * 根据ID集合查询
     * @param companyId 公司ID
     * @param ids 平台code
     * @return 类别
     */
    List<CategoryDO> queryByIds(Long companyId, Set<Long> ids);


    /**
     * 根据参数查询
     * @param companyId 公司ID
     * @param queryCategoryParam 查询参数
     * @return 类别
     */
    List<CategoryDO> queryByParam(Long companyId, QueryCategoryParam queryCategoryParam);

    /**
     * 根据公司ID查询类别
     * @param companyId 公司ID
     * @return 类别
     */
    List<CategoryDO> queryByCompanyId(Long companyId);
}
