package com.raycloud.dmj.account.core.cleancategory.domain.request;

import lombok.Data;

import java.io.Serializable;


/**
 * 添加资金账户
 * <AUTHOR>
 */
@Data
public class EditCategoryReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 子类目ID不能为空
     */
    private Long id;

    /**
     * 分类编码
     */
    private String categoryGroupCode;


    /**
     * 类别名称
     */
    private String categoryName;

    /**
     * 子类别名称
     */
    private String subCategoryName;

    /**
     * 平台code
     */
    private String platformCode;


    /**
     * 资金账户ID
     */
    private Long fundAccountId;

    /**
     * 收支对象ID
     */
    private Long incomeExpenseObjectId;

    /**
     * 是否抵消
     */
    private Boolean offset;


}
