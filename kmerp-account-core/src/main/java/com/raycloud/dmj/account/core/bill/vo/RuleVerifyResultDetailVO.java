package com.raycloud.dmj.account.core.bill.vo;

import lombok.Data;
import java.util.List;

/**
 * 规则校验结果详细信息VO对象
 * <AUTHOR>
 */
@Data
public class RuleVerifyResultDetailVO {

    /**
     * 规则ID
     */
    private Long ruleId;

    /**
     * 规则名称
     */
    private String ruleName;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 按时间维度的校验结果列表
     */
    private List<RuleVerifyResultVo> verifyResultList;
}
