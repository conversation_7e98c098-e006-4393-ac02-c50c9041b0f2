package com.raycloud.dmj.account.core.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * Date:  2025/8/4
 */

@Getter
public enum AccountBalanceTypeEnum {
    /**
     * 流水写入
     */
    WRITE_FLOW(1, "流水写入"),
    /**
     * 人工写入
     */
    MANUAL_WRITE(2, "人工写入"),
    ;

    private final Integer typeCode;

    private final String typeDesc;


    AccountBalanceTypeEnum(Integer typeCode, String typeDesc) {
        this.typeCode = typeCode;
        this.typeDesc = typeDesc;
    }
}
