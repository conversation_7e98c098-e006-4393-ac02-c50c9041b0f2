package com.raycloud.dmj.account.core.cleancategory.service;

import com.raycloud.dmj.account.core.cleancategory.domain.object.CategoryDO;
import com.raycloud.dmj.account.core.cleancategory.domain.object.CategoryGroupDO;
import com.raycloud.dmj.account.core.cleancategory.domain.object.SubCategoryDO;
import com.raycloud.dmj.account.core.cleancategory.domain.request.*;
import com.raycloud.dmj.account.core.cleancategory.domain.vo.CategoryGroupVO;
import com.raycloud.dmj.account.core.cleancategory.domain.vo.SimpleSubCategoryVo;
import com.raycloud.dmj.account.core.cleancategory.domain.vo.SubCategoryVO;
import com.raycloud.dmj.account.core.common.PageListBase;
import com.raycloud.dmj.account.core.common.TreeVO;
import com.raycloud.dmj.account.infra.session.AccountUser;

import java.util.List;
import java.util.Map;

/**
 * 类别管理服务
 * <AUTHOR>
 */
public interface CategoryManageService {


    /**
     * 添加类别
     * @param accountUser 用户信息，从登陆态获取
     * @param req 添加资金账户请求
     * @return 新增的ID
     */
    Long addCategory(AccountUser accountUser, AddCategoryReq req);



    /**
     * 编辑分类
     * @param accountUser 用户信息，从登陆态获取
     * @param req 编辑资金账户请求
     */
    void editCategoryGroup(AccountUser accountUser, EditCategoryGroupReq req);



    /**
     * 分页查询类别
     * @param accountUser 用户信息，从登陆态获取
     * @param req 查询类别请求
     * @return 类别列表
     */
    PageListBase<SubCategoryVO> pageQueryCategoryList(AccountUser accountUser, QueryCategoryReq req);



    /**
     * 编辑类别
     * @param accountUser 用户信息，从登陆态获取
     * @param req 编辑类别
     */
    void editCategory(AccountUser accountUser, EditCategoryReq req);


    /**
     * 删除类别
     * @param accountUser 用户信息，从登陆态获取
     * @param req 删除类别
     */
    void deleteCategory(AccountUser accountUser, DeleteCategoryReq req);


    /**
     * 查询分类树
     * @param accountUser 用户信息，从登陆态获取
     * @param req 查询分类树
     * @return 分类树
     */
    List<TreeVO> queryCategoryGroupTree(AccountUser accountUser, QueryCategoryGroupTreeReq req);


    /**
     * 查询类别树
     * @param accountUser 用户信息，从登陆态获取
     * @param req 查询类别树
     * @return 类别树
     */
    List<TreeVO> queryCategoryTree(AccountUser accountUser, QueryCategoryTreeReq req);

    /**
     * 查询子类别树
     * @param accountUser 用户信息，从登陆态获取
     * @param req 查询子类别树
     * @return 子类别树
     */
    List<TreeVO> querySubCategoryTree(AccountUser accountUser, QuerySubCategoryTreeReq req);

    /**
     * 查询分类组
     * @param accountUser 用户信息，从登陆态获取
     * @param req 查询分类组
     * @return 分类组
     */
    List<CategoryGroupVO> listCategoryGroup(AccountUser accountUser, QueryCategoryGroupReq req);


    /**
     * 获取分类组映射
     * @param accountUser 用户信息，从登陆态获取
     * @return 分类组映射
     */
    Map<String, CategoryGroupDO> getCategoryGroupMap(AccountUser accountUser);

    /**
     * 获取分类映射
     * @param accountUser 用户信息，从登陆态获取
     * @return 分类映射
     */
    Map<Long, CategoryDO> getCategoryMap(AccountUser accountUser);

    /**
     * 获取子分类映射
     * @param accountUser 用户信息，从登陆态获取
     * @return 子分类映射
     */
    Map<Long, SubCategoryDO> getSubCategoryMap(AccountUser accountUser);

    /**
     * 获取简单子分类名称列表
     * @param accountUser 用户信息
     * @param req 请求参数
     * @return
     */
    List<SimpleSubCategoryVo> getSimpleSubCategoryVoByAccountId(AccountUser accountUser, QuerySubCategoryReq req);

    /**
     * 获取分类组映射
     * @param companyId 用户信息，从登陆态获取
     * @return 分类组映射
     */
    Map<String, String> getCategoryGroupMap(Long companyId);

    /**
     * 获取分类映射
     * @param companyId 用户信息，从登陆态获取
     * @return 分类映射
     */
    Map<Long, String> getCategoryMap(Long companyId);

    /**
     * 获取子分类映射
     * @param companyId 用户信息，从登陆态获取
     * @return 子分类映射
     */
    Map<Long, String> getSubCategoryMap(Long companyId);
}
