package com.raycloud.dmj.account.core.common;

import com.raycloud.dmj.domain.Sort;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 可分页的结果集类
 * <AUTHOR>
 *
 */
@Data
@NoArgsConstructor
public class PageListBase<T> implements IPageable<T>, Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 4854389014120920864L;

	protected Page page;
	
	protected Sort sort;
	
	protected Long total;
	
	protected List<T> list;

	@Override
	public Page getPage() {
		return page;
	}

	@Override
	public Sort getSort() {
		return sort;
	}

	@Override
	public Long getTotal() {
		return total;
	}

	@Override
	public List<T> getList() {
		return list;
	}

	public PageListBase<T> setPage(Page page) {
		this.page = page;
		return this;
	}

	public PageListBase<T> setSort(Sort sort) {
		this.sort = sort;
		return this;
	}

	public PageListBase<T> setTotal(Long total) {
		this.total = total;
		return this;
	}

	public PageListBase<T> setList(List<T> list) {
		this.list = list;
		return this;
	}
	/**
	 * 构造方法
	 */
	public PageListBase(List<T> list , Page page, Long total) {
		this.list = list;
		this.page = page;
		this.total = total;
	}

}
