package com.raycloud.dmj.account.core.rawdata.service;

import com.raycloud.dmj.account.core.rawdata.req.BillUploadRequest;
import com.raycloud.dmj.account.core.rawdata.req.OriginalDataCallbackReq;

/**
 * <AUTHOR>
 */
public interface RawDataStorageService {


    /**
     * rpa原始数据导入回调
     * @param req 请求
     */
    void rpaOriginalDataCallback(OriginalDataCallbackReq req);


    /**
     * 接受原始数据导入请求
     * @param request  request
     */
    void acceptRawDataStorageRequest(BillUploadRequest request);


    /**
     * 接收mq消息，导入文件数据
     * @param recordId 导入数据模板
     */
    void rpaRawDataStorage(Long recordId);


    /**
     * 处理导入任务
     */
    void handleStuckTasks();




}
