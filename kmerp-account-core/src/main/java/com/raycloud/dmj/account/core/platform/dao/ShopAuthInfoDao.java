package com.raycloud.dmj.account.core.platform.dao;

import com.raycloud.dmj.account.core.platform.base.domain.ShopAuthInfoDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface ShopAuthInfoDao {

    int insertOrUpdate(ShopAuthInfoDO shopAuthInfoDO);

    ShopAuthInfoDO selectByShopIdCompanyIdPlatform(Long shopId, Long companyId, String platformCode);

}
