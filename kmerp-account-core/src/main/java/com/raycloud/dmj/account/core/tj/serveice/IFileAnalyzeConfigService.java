package com.raycloud.dmj.account.core.tj.serveice;

import com.raycloud.dmj.account.core.tj.req.FileAnalyzeConfigRequest;
import com.raycloud.dmj.account.core.tj.req.FileSheetConfigRequest;
import com.raycloud.dmj.account.core.tj.vo.CheckerVo;
import com.raycloud.dmj.account.core.tj.vo.FileAnalyzeConfigVo;
import com.raycloud.dmj.account.core.tj.vo.FileSheetConfigVo;

import java.util.List;

public interface IFileAnalyzeConfigService {

    /**
     * 获取校验器列表
     * @return
     */
    List<CheckerVo> getCheckerList();

    /**
     * 获取转换器列表
     * @return
     */
    List<CheckerVo> getTranslatorList();

    /**
     * 获取文件解析配置列表
     * @return
     */
    List<FileAnalyzeConfigVo> getFileAnalyzeConfigList(String dataType);

    /**
     * 获取文件解析配置
     * @param id 配置ID
     * @return
     */
    FileAnalyzeConfigVo getFileAnalyzeConfig(Long id);

    /**
     * 添加文件解析配置
     * @param addFileAnalyzeConfig
     * @return
     */
    Long addFileAnalyzeConfig(FileAnalyzeConfigRequest addFileAnalyzeConfig);

    /**
     * 更新文件解析配置
     * @param fileAnalyzeConfig
     * @return
     */
    Long updateFileAnalyzeConfig(FileAnalyzeConfigRequest fileAnalyzeConfig);

    /**
     * 获取文件解析表配置列表
     * @param configId 配置ID
     * @return
     */
    List<FileSheetConfigVo> getFileAnalyzeTableConfigList(Long configId);

    /**
     * 添加文件解析表配置
     * @param fileSheetConfigRequest
     * @return
     */
    Long addFileAnalyzeTableConfig(FileSheetConfigRequest fileSheetConfigRequest);

    /**
     * 更新文件解析表配置
     * @param fileSheetConfigRequest
     * @return
     */
    Long updateFileAnalyzeTableConfig(FileSheetConfigRequest fileSheetConfigRequest);

    /**
     * 获取预处理器列表
     * @return
     */
    List<CheckerVo> getPreprocessorList();
}
