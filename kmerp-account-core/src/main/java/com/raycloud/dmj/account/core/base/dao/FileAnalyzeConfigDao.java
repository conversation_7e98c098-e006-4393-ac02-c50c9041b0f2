package com.raycloud.dmj.account.core.base.dao;

import com.raycloud.dmj.account.core.rawdata.domains.FileAnalyzeConfigDO;
import com.raycloud.dmj.account.core.tj.vo.FileAnalyzeConfigVo;

import java.util.List;

/**
 * Date:  2025/6/16
 *
 * <AUTHOR>
 */

public interface FileAnalyzeConfigDao {

    /**
     * 查询文件解析配置表
     */
    List<FileAnalyzeConfigDO> queryFileAnalyzeConfig(String dataType);

    /**
     * 查询文件解析配置VO列表
     * @param dataType 数据类型，为空时查询所有启用的配置
     * @return 配置列表
     */
    List<FileAnalyzeConfigVo> queryFileAnalyzeConfigVo(String dataType);

    /**
     * 检查字符串是否不为空（包含null、空字符串、空白字符串的检查）
     * @param str 待检查的字符串
     * @return true-不为空，false-为空
     */
    boolean isNotEmpty(String str);

    FileAnalyzeConfigDO queryFileAnalyzeConfigById(Long id);

    /**
     * 新增文件解析配置
     * @param fileAnalyzeConfig 文件解析配置对象
     * @return 新增记录的主键ID
     */
    Long addFileAnalyzeConfig(FileAnalyzeConfigDO fileAnalyzeConfig);

    /**
     * 更新文件解析配置
     * @param fileAnalyzeConfig 文件解析配置对象
     * @return 更新影响的行数
     */
    int updateFileAnalyzeConfig(FileAnalyzeConfigDO fileAnalyzeConfig);

    /**
     * 根据数据类型查询所有启用的配置（用于业务校验）
     * @param dataType 数据类型
     * @return 配置列表
     */
    List<FileAnalyzeConfigDO> queryEnabledConfigsByDataType(String dataType);
}
