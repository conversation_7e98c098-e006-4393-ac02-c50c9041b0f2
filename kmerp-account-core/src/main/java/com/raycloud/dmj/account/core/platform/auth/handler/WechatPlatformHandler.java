package com.raycloud.dmj.account.core.platform.auth.handler;

import com.alibaba.fastjson2.JSONObject;
import com.raycloud.dmj.account.common.SharedDataResponse;
import com.raycloud.dmj.account.core.enums.FileTypeEnum;
import com.raycloud.dmj.account.core.platform.auth.AbstractPlatformHandler;
import com.raycloud.dmj.account.core.platform.auth.impl.DubboUserService;
import com.raycloud.dmj.account.core.platform.base.domain.WechatFundsFlowProgressDO;
import com.raycloud.dmj.account.core.platform.base.domain.thirdparty.wechat.WxVideoBillApi;
import com.raycloud.dmj.account.core.platform.base.domain.thirdparty.wechat.exception.WxSyncReachQuotaException;
import com.raycloud.dmj.account.core.platform.base.domain.thirdparty.wechat.param.FundsFlowDetailParam;
import com.raycloud.dmj.account.core.platform.base.domain.thirdparty.wechat.param.FundsFlowListParam;
import com.raycloud.dmj.account.core.platform.base.domain.thirdparty.wechat.response.FundsFlowDetailResult;
import com.raycloud.dmj.account.core.platform.base.domain.thirdparty.wechat.response.FundsFlowListResult;
import com.raycloud.dmj.account.core.platform.dao.WechatFundsFlowProgressDao;
import com.raycloud.dmj.account.core.rawdata.utils.oss.OSSClientHelper;
import com.raycloud.dmj.account.core.rawdata.utils.oss.OssUtils;
import com.raycloud.dmj.account.enums.DataType;
import com.raycloud.dmj.account.enums.PlatformType;
import com.raycloud.dmj.account.infra.utils.DownloadUtils;
import com.raycloud.dmj.account.infra.utils.TimeUtils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WechatPlatformHandler extends AbstractPlatformHandler {


    @Resource
    private DubboUserService dubboUserService;

    @Resource
    private WechatFundsFlowProgressDao wechatFundsFlowProgressDao;

    @Value("${file-path.tmp}")
    private String tmpPath;

    @Override
    public PlatformType getPlatformType() {
        return PlatformType.WECHAT;
    }

    @Override
    public String getAuthUrl(PlatformType platform, Long companyId, Long shopId, String callbackUrl, Long recordId, Map<String, Object> extraData) {
        return "";
    }

    /**
     * 微信获取账单数据
     * note 微信平台对接口有调用次数限制  获取账单时 首先获取账单的列表 然后根据列表里的数据获取账单详情
     * 每次获取账单的时候 会提供下一页的nextkey 游标，
     * 若当日的调用次数使用完毕，则记录游标，待下一次继续获取，直到将数据获取完毕即可
     */
    @Override
    public SharedDataResponse getDataUrl(Long companyId, Long shopId, LocalDate billDate, DataType dataType, String extraData) {
        Integer page = 1;
        Integer pageSize = 100;
        String nextKey = null;
        String objectName = null;
        String filePath = getTmpPath(companyId, shopId, billDate, dataType);
        // 获取商家数据 当记录日期在今日之前，并且存在下一页标识的时候 继续获取之前的账单，直到追加到今日数据
        WechatFundsFlowProgressDO wechatFundsFlowProgressDO = wechatFundsFlowProgressDao.selectByShopIdCompanyId(shopId, companyId);
        if (wechatFundsFlowProgressDO != null && ObjectUtils.isNotEmpty(wechatFundsFlowProgressDO.getOverTime())
                && wechatFundsFlowProgressDO.getOverTime().isBefore(billDate)) {
            // nextKey 为空说明 overTime 已经完成了需要做下一天的
            if (ObjectUtils.isEmpty(wechatFundsFlowProgressDO.getNextKey())) {
                billDate = wechatFundsFlowProgressDO.getOverTime().plusDays(1);
            }else {
                billDate = wechatFundsFlowProgressDO.getOverTime();
            }
            page = wechatFundsFlowProgressDO.getPageNo();
            nextKey = wechatFundsFlowProgressDO.getNextKey();
            objectName = wechatFundsFlowProgressDO.getObjectName();
        }
        CSVPrinter fileWriter = getFileWriter(objectName, filePath);
        String sessionId = dubboUserService.getUser(shopId).getSessionId();
        List<FundsFlowDetailResult.FundsFlow> fundsFlowList = new LinkedList<>();
        try {
            FundsFlowListParam param = new FundsFlowListParam();
            param.setPage(page);
            param.setPageSize(pageSize);
            param.setStartTime(TimeUtils.getStartTime(billDate) / 1000);
            param.setEndTime(TimeUtils.getEndTime(billDate) / 1000);
            param.setNextKey(nextKey);
            FundsFlowListResult listResult = WxVideoBillApi.getFundsFlowList(param, sessionId);
            fundsFlowList.addAll(queryFundsFlowDetailList(listResult.getFlowIds(), sessionId));
            while (listResult.isSuccess() && listResult.getHasMore()) {
                param.setPage(++page);
                Optional.ofNullable(listResult.getNextKey()).ifPresent(param::setNextKey);
                nextKey = listResult.getNextKey();
                param.setNextKey(nextKey);
                listResult = WxVideoBillApi.getFundsFlowList(param, sessionId);
                fundsFlowList.addAll(queryFundsFlowDetailList(listResult.getFlowIds(), sessionId));
            }
        } catch (WxSyncReachQuotaException e) {
            // 记录该商家的nextKey 下一日继续调用获取
            appendWrite2Csv(fileWriter, fundsFlowList);
            objectName = getObjectName(companyId, shopId, billDate, dataType);
            OssUtils.uploadByFile(new File(filePath), objectName, true);
            recordWechatFundsFlowProgress(shopId, companyId, nextKey, page, billDate, objectName);
            return SharedDataResponse.error();
        }
        appendWrite2Csv(fileWriter, fundsFlowList);
        // 当日执行成功后 清除nextKey 并且将overTime 设置为下一日
        recordWechatFundsFlowProgress(shopId, companyId, null, 1, billDate, null);
        return SharedDataResponse.success(filePath);
    }

    public CSVPrinter getFileWriter(String objectName ,String filePath) {
        FileWriter writer = null;
        try {
            File file = new File(filePath);
            if (!file.exists()){
                FileUtils.createParentDirectories(file);
            }
            if (StringUtils.isEmpty(objectName)) {
                // 如果 objectName 为空，创建新的带表头的 CSV 文件
                writer = new FileWriter(filePath);
                CSVPrinter csvPrinter = new CSVPrinter(writer, CSVFormat.DEFAULT.withHeader(
                        "flowId", "fundsType", "fundsTypeDesc", "flowType", "amount", "balance", "relatedInfoList",
                        "bookkeepingTime", "remark"
                ));
                return csvPrinter;
            } else {
                // 如果 objectName 存在，从 OSS 下载文件并追加写入
                String url = OSSClientHelper.expireUrl(objectName);

                File downloadedFile = DownloadUtils.downloadFile(url, filePath);
                if (downloadedFile == null) {
                    throw new RuntimeException("Failed to download file from OSS: " + objectName);
                }
                // 使用追加模式打开文件
                writer = new FileWriter(filePath, true);
                // 不设置表头，因为已有文件中应该已经包含表头
                return new CSVPrinter(writer, CSVFormat.DEFAULT);
            }
        } catch (IOException e) {
            throw new RuntimeException("Error creating or downloading CSV file", e);
        }
    }

    public boolean appendWrite2Csv(CSVPrinter csvPrinter, List<FundsFlowDetailResult.FundsFlow> dataList) {
        try {
            if (CollectionUtils.isNotEmpty(dataList)) {
                for (FundsFlowDetailResult.FundsFlow detail : dataList) {
                    csvPrinter.printRecord(
                            detail.getFlowId(),
                            detail.getFundsType(),
                            detail.getFundsTypeDesc(),
                            detail.getFlowType(),
                            detail.getAmount(),
                            detail.getBalance(),
                            JSONObject.toJSONString(detail.getRelatedInfoList()),
                            detail.getBookkeepingTime(),
                            detail.getRemark()
                    );
                }
            }
            csvPrinter.flush();
            return true;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private String getObjectName(Long companyId, Long shopId, LocalDate billDate, DataType dataType) {
        return OssUtils.getSharedDataObjectName(dataType.getValue() + "_" + companyId + "_" + shopId + "_" + billDate + "." + FileTypeEnum.CSV);
    }

    private String getTmpPath(Long companyId, Long shopId, LocalDate billDate, DataType dataType) {
        return tmpPath + getObjectName(companyId, shopId, billDate, dataType);
    }

    public void recordWechatFundsFlowProgress(Long shopId, Long companyId, String nextKey, Integer page, LocalDate billDate, String objectName) {
        WechatFundsFlowProgressDO progressDO = new WechatFundsFlowProgressDO();
        progressDO.setCompanyId(companyId);
        progressDO.setShopId(shopId);
        progressDO.setNextKey(nextKey);
        progressDO.setPageNo(page);
        progressDO.setOverTime(billDate);
        progressDO.setObjectName(objectName);
        wechatFundsFlowProgressDao.insertOrUpdate(progressDO);
    }

    private List<FundsFlowDetailResult.FundsFlow> queryFundsFlowDetailList(List<String> flowIds, String sessionId) {
        if (CollectionUtils.isNotEmpty(flowIds)) {
            return flowIds.stream().map(flowId -> {
                FundsFlowDetailResult detailResult = WxVideoBillApi.getFundsFlowDetail(new FundsFlowDetailParam(flowId), sessionId);
                return detailResult.getFundsFlow();
            }).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

}
