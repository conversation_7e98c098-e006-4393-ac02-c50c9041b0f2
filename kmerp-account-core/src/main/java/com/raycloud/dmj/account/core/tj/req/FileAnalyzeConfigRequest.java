package com.raycloud.dmj.account.core.tj.req;

import lombok.Data;

import java.util.List;

@Data
public class FileAnalyzeConfigRequest {

    /**
     * 配置ID
     */
    private Long id;

    /**
     * Sheet类型】取值范围：1：下标，2：名称
     */
    private Integer sheetType;

    /**
     * 【Sheet名称】sheetType为1时：表示下标索引，填写数字，为2时：填写sheet名称
     */
    private String sheet;

    /**
     * 表头从第几行开始
     */
    private Integer headerStartIndex;

    /**
     * 表头从第几行结束
     */
    private Integer headerEndIndex;

    /**
     * 【数据类型】自定义输入
     */
    private String dataType;

    /**
     *【数据配置数量】子配置的数量
     */
    private int subConfigNum;

    /**
     *【文件表头配置】
     */
    private List<FileHeadConfigRequest> headConfig;

    /**
     * 【数据合法性过滤、清洗规则】
     */
    private List<FilterRequest> filter;
}
