package com.raycloud.dmj.account.core.platform.dao.impl;

import com.mysql.jdbc.Statement;

import com.raycloud.dmj.account.core.base.dao.impl.BaseDao;
import com.raycloud.dmj.account.core.enums.field.ShopInfoFieldEnum;
import com.raycloud.dmj.account.core.platform.base.domain.SharedDataInfoDO;
import com.raycloud.dmj.account.core.platform.base.domain.enums.SharedDataInfoFieldEnum;
import com.raycloud.dmj.account.core.platform.dao.SharedDataInfoDao;
import com.raycloud.dmj.account.infra.common.BusinessException;
import com.raycloud.dmj.account.infra.common.ErrorCodeEnum;
import com.raycloud.dmj.table.api.plus.common.ColumnValue;
import com.raycloud.dmj.table.api.plus.common.base.SQL;
import com.raycloud.dmj.table.api.plus.common.enums.LinkMode;
import com.raycloud.dmj.table.api.plus.component.$;
import com.raycloud.dmj.table.api.plus.component.column.utils.Columns;
import com.raycloud.dmj.table.api.plus.component.condition.ConditionComponent;
import com.raycloud.dmj.table.api.plus.component.condition.utils.Conditions;
import com.raycloud.dmj.table.api.plus.insert.Inserts;
import com.raycloud.dmj.table.api.plus.query.Queries;
import com.raycloud.dmj.table.api.plus.update.Updates;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Service;

import java.sql.PreparedStatement;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
public class SharedDataInfoDaoImpl extends BaseDao implements SharedDataInfoDao {

    private static final String TABLE_NAME = "shared_data_info";

    @Override
    public Long insert(SharedDataInfoDO record) {
        SQL sql = Inserts.insert()
                .into(TABLE_NAME)
                .columns(SharedDataInfoFieldEnum.getInsertFields())
                .valueForEntity(record)
                .columnNameCamelToUnderline()
                .toSql();

        KeyHolder keyHolder = new GeneratedKeyHolder();
        jdbcTemplate.update(
                connection -> {
                    PreparedStatement ps = connection.prepareStatement(
                            sql.getSqlCode(),
                            Statement.RETURN_GENERATED_KEYS
                    );
                    Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
                    for (int i = 0; i < args.length; i++) {
                        ps.setObject(i + 1, args[i]);
                    }
                    return ps;
                },
                keyHolder
        );
        return Objects.requireNonNull(keyHolder.getKey()).longValue();
    }

    @Override
    public SharedDataInfoDO getById(Long id) {
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(getByIdCondition(id))
                .select()
                .toSql();

        List<SharedDataInfoDO> records = jdbcTemplate.query(
                sql.getSqlCode(),
                new BeanPropertyRowMapper<>(SharedDataInfoDO.class),
                sql.getArgs().toArray()
        );
        return records.isEmpty() ? null : records.get(0);
    }

    @Override
    public SharedDataInfoDO getByType(Long shopId, Long companyId, String type, LocalDate date) {
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(SharedDataInfoFieldEnum.SHOP_ID.getFieldCode()), LinkMode.EQUAL, shopId),
                        Conditions.and(Columns.toColumn(SharedDataInfoFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId),
                        Conditions.and(Columns.toColumn(SharedDataInfoFieldEnum.TYPE.getFieldCode()), LinkMode.EQUAL, type),
                        Conditions.and(Columns.toColumn(SharedDataInfoFieldEnum.DATE.getFieldCode()), LinkMode.EQUAL, date)
                )
                .select()
                .toSql();

        List<SharedDataInfoDO> records = jdbcTemplate.query(
                sql.getSqlCode(),
                new BeanPropertyRowMapper<>(SharedDataInfoDO.class),
                sql.getArgs().toArray()
        );
        return records.isEmpty() ? null : records.get(0);
    }




    @Override
    public int updateById(SharedDataInfoDO record) {
        // 先查询现有记录
        SharedDataInfoDO existingRecord = getById(record.getId());
        if (existingRecord == null) {
            throw new BusinessException(ErrorCodeEnum.DB_ERROR, "shared_data_info update error");
        }

        List<ColumnValue> columnValues = new ArrayList<>();
        if (record.getShopId() != null) {
            columnValues.add($.updateKeyValue(SharedDataInfoFieldEnum.SHOP_ID.getFieldCode(), record.getShopId()));
        }
        if (record.getCompanyId() != null) {
            columnValues.add($.updateKeyValue(SharedDataInfoFieldEnum.COMPANY_ID.getFieldCode(), record.getCompanyId()));
        }
        if (record.getPlatformCode() != null) {
            columnValues.add($.updateKeyValue(SharedDataInfoFieldEnum.PLATFORM_CODE.getFieldCode(), record.getPlatformCode()));
        }
        if (record.getType() != null) {
            columnValues.add($.updateKeyValue(SharedDataInfoFieldEnum.TYPE.getFieldCode(), record.getType()));
        }
        if (record.getUrl() != null) {
            columnValues.add($.updateKeyValue(SharedDataInfoFieldEnum.URL.getFieldCode(), record.getUrl()));
        }
        if (record.getDate() != null) {
            columnValues.add($.updateKeyValue(SharedDataInfoFieldEnum.DATE.getFieldCode(), record.getDate()));
        }
        if (record.getModified() != null) {
            columnValues.add($.updateKeyValue(SharedDataInfoFieldEnum.MODIFIED.getFieldCode(), record.getModified()));
        }
        SQL sql = Updates.create()
                .table(TABLE_NAME)
                .where(Conditions.and(Columns.toColumn(ShopInfoFieldEnum.ID.getFieldCode()), LinkMode.EQUAL, record.getId()))
                .update(
                        columnValues
                ).toSql();
        return jdbcTemplate.update(sql.getSqlCode(), sql.getArgs().toArray());
    }

    private List<ConditionComponent<?>> getByIdCondition(Long id) {
        List<ConditionComponent<?>> conditions = new ArrayList<>();
        conditions.add(Conditions.and(
                Columns.toColumn(SharedDataInfoFieldEnum.ID.getFieldCode()),
                LinkMode.EQUAL,
                id
        ));
        return conditions;
    }
}