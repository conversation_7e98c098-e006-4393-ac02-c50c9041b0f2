package com.raycloud.dmj.account.core.cleancategory.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 分类表DO
 * <AUTHOR>
 */

@Data
public class SubCategoryVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 修改时间
     */
    private Date modified;

    /**
     * 分类编码
     */
    private String categoryGroupCode;

    /**
     * 分类名称
     */
    private String categoryGroupName;

    /**
     * 资金账户ID
     */
    private Long fundAccountId;

    /**
     * 资金账户名称
     */
    private String fundAccountName;

    /**
     * 类别ID
     */
    private Long categoryId;

    /**
     * 类别名称
     */
    private String categoryName;

    /**
     * 子类别ID
     */
    private Long subCategoryId;

    /**
     * 子类别名称
     */
    private String subCategoryName;

    /**
     * 是否可抵消
     */
    private Boolean offset;

    /**
     * 收支对象ID
     */
    private Long incomeExpenseObjectId;

    /**
     * 收支对象名称
     */
    private String incomeExpenseObjectName;


}