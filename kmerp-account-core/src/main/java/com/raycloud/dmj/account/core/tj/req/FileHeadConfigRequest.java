package com.raycloud.dmj.account.core.tj.req;

import lombok.Data;

@Data
public class FileHeadConfigRequest {

    /**
     * 【列名唯一键】自定义输入（长度限制64个字），配置内唯一，不能重复
     */
    private String key;

    /**
     * 【列头类型】取值范围：1：下标，2：名称  ，配置内统一使用一种方式
     */
    private Integer headType;

    /**
     * 【列头名称】headType为1时：表示下标索引，填写数字，为2时：填写表头名称，配置内唯一，不能重复
     */
    private String head;

    /**
     * 【校验器】校验器的code
     */
    private String checker;

    /**
     * 【校验器的入参】自定义输入，自定义参数
     */
    private int value;

    /**
     * 【转换器】转换器的code
     */
    private String translator;

    /**
     * 【转换器的入参】自定义输入，自定义参数
     */
    private String transValue;

    /**
     * 预处理器类型
     */
    private String preprocessor;

    /**
     * 预处理器配置规则
     */
    private String preprocessorRule;
}
