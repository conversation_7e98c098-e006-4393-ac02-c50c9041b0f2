package com.raycloud.dmj.account.core.rawdata.utils;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.raycloud.dmj.account.core.rawdata.domains.FileAnalyzeTableConfigDO;

import java.util.List;

public class JsonUtils {
    public String convertToJsonUsingGson(List<FileAnalyzeTableConfigDO> list) {
        try {
            Gson gson = new GsonBuilder()
                    .setPrettyPrinting() // 美化输出（可选）
                    .create();
            return gson.toJson(list);
        } catch (Exception e) {
            e.printStackTrace();
            return "[]";
        }
    }
}
