package com.raycloud.dmj.account.core.base.dao.impl;

import com.raycloud.dmj.account.core.base.dao.BillVerifyRuleDao;
import com.raycloud.dmj.account.core.base.domain.BillVerifyRuleDO;
import com.raycloud.dmj.account.core.bill.request.BillVerifyRuleRequest;
import com.raycloud.dmj.account.core.enums.CommonStatusEnum;
import com.raycloud.dmj.account.core.enums.field.BillVerifyRuleFieldEnum;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import com.raycloud.dmj.table.api.plus.common.ColumnValue;
import com.raycloud.dmj.table.api.plus.common.base.SQL;
import com.raycloud.dmj.table.api.plus.common.enums.LinkMode;
import com.raycloud.dmj.table.api.plus.component.$;
import com.raycloud.dmj.table.api.plus.component.column.utils.Columns;
import com.raycloud.dmj.table.api.plus.component.condition.ConditionComponent;
import com.raycloud.dmj.table.api.plus.component.condition.utils.Conditions;
import com.raycloud.dmj.table.api.plus.insert.Inserts;
import com.raycloud.dmj.table.api.plus.query.Queries;
import com.raycloud.dmj.table.api.plus.update.Updates;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 账单核验规则表DAO实现类
 * <AUTHOR>
 */
@Slf4j
@Repository
public class BillVerifyRuleDaoImpl extends BaseDao implements BillVerifyRuleDao {

    private static final String TABLE_NAME = "bill_verify_rule";

    @Override
    public Long insert(BillVerifyRuleDO billVerifyRuleDO) {
        AsserUtils.notNull(billVerifyRuleDO, "账单核验规则不能为空！");
        SQL sql = Inserts.insert()
                .into(TABLE_NAME)
                .columns(BillVerifyRuleFieldEnum.getInsertFields())
                .valueForEntity(billVerifyRuleDO)
                .columnNameCamelToUnderline()
                .toSql();

        KeyHolder keyHolder = new GeneratedKeyHolder();
        jdbcTemplate.update(
                connection -> {
                    PreparedStatement ps = connection.prepareStatement(
                            sql.getSqlCode(),
                            Statement.RETURN_GENERATED_KEYS
                    );
                    Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
                    for (int i = 0; i < args.length; i++) {
                        ps.setObject(i + 1, args[i]);
                    }
                    return ps;
                },
                keyHolder
        );
        // 返回生成的主键ID
        Number key = keyHolder.getKey();
        return key != null ? key.longValue() : null;
    }

    @Override
    public BillVerifyRuleDO queryById(Long id, Long companyId) {
        AsserUtils.notNull(id, "ID不能为空！");
        AsserUtils.notNull(companyId, "公司ID不能为空！");

        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(BillVerifyRuleFieldEnum.ID.getFieldCode()), LinkMode.EQUAL, id),
                        Conditions.and(Columns.toColumn(BillVerifyRuleFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId),
                        Conditions.and(Columns.toColumn(BillVerifyRuleFieldEnum.ENABLE_STATUS.getFieldCode()), LinkMode.EQUAL, 1)
                )
                .select()
                .toSql();

        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();

        // 添加调试日志
        log.debug("SQL: {}", sql.getSqlCode());
        log.debug("Args length: {}", args.length);
        for (int i = 0; i < args.length; i++) {
            log.debug("Arg[{}]: {}", i, args[i]);
        }

        List<BillVerifyRuleDO> list = jdbcTemplate.query(
                sql.getSqlCode(),
                new BeanPropertyRowMapper<>(BillVerifyRuleDO.class),
                args
        );
        return ObjectUtils.isEmpty(list) ? null : list.get(0);
    }

    @Override
    public int updateById(BillVerifyRuleDO billVerifyRuleDO) {
        AsserUtils.notNull(billVerifyRuleDO, "账单核验规则不能为空！");
        AsserUtils.notNull(billVerifyRuleDO.getId(), "ID不能为空！");
        AsserUtils.notNull(billVerifyRuleDO.getCompanyId(), "公司ID不能为空！");

        billVerifyRuleDO.setModified(new Date());

        SQL sql = Updates.create()
                .table(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(BillVerifyRuleFieldEnum.ID.getFieldCode()), LinkMode.EQUAL, billVerifyRuleDO.getId()),
                        Conditions.and(Columns.toColumn(BillVerifyRuleFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, billVerifyRuleDO.getCompanyId()),
                        Conditions.and(Columns.toColumn(BillVerifyRuleFieldEnum.ENABLE_STATUS.getFieldCode()), LinkMode.EQUAL, 1)
                )
                .update(getUpdateColumnValueList(billVerifyRuleDO))
                .toSql();

        return jdbcTemplate.update(sql.getSqlCode(), sql.getArgs().toArray());
    }

    public List<ColumnValue> getUpdateColumnValueList(BillVerifyRuleDO verifyRuleDO){
        List<ColumnValue> updateFields = new ArrayList<>();
        if (StringUtils.isNotEmpty(verifyRuleDO.getRuleName())) {
            updateFields.add($.updateKeyValue(BillVerifyRuleFieldEnum.RULE_NAME.getFieldCode(), verifyRuleDO.getRuleName()));
        }
        if (StringUtils.isNotEmpty(verifyRuleDO.getRuleContent())) {
            updateFields.add($.updateKeyValue(BillVerifyRuleFieldEnum.RULE_CONTENT.getFieldCode(), verifyRuleDO.getRuleContent()));
        }
        if (ObjectUtils.isNotEmpty(verifyRuleDO.getAmount())) {
            updateFields.add($.updateKeyValue(BillVerifyRuleFieldEnum.AMOUNT.getFieldCode(), verifyRuleDO.getAmount()));
        }
        if (ObjectUtils.isNotEmpty(verifyRuleDO.getShopId())) {
            updateFields.add($.updateKeyValue(BillVerifyRuleFieldEnum.SHOP_ID.getFieldCode(), verifyRuleDO.getAmount()));
        }
        if (ObjectUtils.isNotEmpty(verifyRuleDO.getEffectiveTime())) {
            updateFields.add($.updateKeyValue(BillVerifyRuleFieldEnum.EFFECTIVE_TIME.getFieldCode(), verifyRuleDO.getEffectiveTime()));
        }
        if (ObjectUtils.isNotEmpty(verifyRuleDO.getInvalidTime())) {
            updateFields.add($.updateKeyValue(BillVerifyRuleFieldEnum.INVALID_TIME.getFieldCode(), verifyRuleDO.getInvalidTime()));
        }
        if (ObjectUtils.isNotEmpty(verifyRuleDO.getDateType())) {
            updateFields.add($.updateKeyValue(BillVerifyRuleFieldEnum.DATE_TYPE.getFieldCode(), verifyRuleDO.getDateType()));
        }
        if (ObjectUtils.isNotEmpty(verifyRuleDO.getJudgmentType())) {
            updateFields.add($.updateKeyValue(BillVerifyRuleFieldEnum.JUDGMENT_TYPE.getFieldCode(), verifyRuleDO.getJudgmentType()));
        }
        if (ObjectUtils.isNotEmpty(verifyRuleDO.getIsExceptionTip())) {
            updateFields.add($.updateKeyValue(BillVerifyRuleFieldEnum.IS_EXCEPTION_TIP.getFieldCode(), verifyRuleDO.getIsExceptionTip()));
        }
        if (ObjectUtils.isNotEmpty(verifyRuleDO.getStatus())) {
            updateFields.add($.updateKeyValue(BillVerifyRuleFieldEnum.STATUS.getFieldCode(), verifyRuleDO.getStatus()));
        }
        updateFields.add($.updateKeyValue(BillVerifyRuleFieldEnum.MODIFIED.getFieldCode(), new Date()));
        return updateFields;
    }

    @Override
    public int deleteById(Long id, Long companyId) {
        AsserUtils.notNull(id, "ID不能为空！");
        AsserUtils.notNull(companyId, "公司ID不能为空！");

        SQL sql = Updates.create()
                .table(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(BillVerifyRuleFieldEnum.ID.getFieldCode()), LinkMode.EQUAL, id),
                        Conditions.and(Columns.toColumn(BillVerifyRuleFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId),
                        Conditions.and(Columns.toColumn(BillVerifyRuleFieldEnum.ENABLE_STATUS.getFieldCode()), LinkMode.EQUAL, 1)
                )
                .update(
                        $.updateKeyValue(BillVerifyRuleFieldEnum.ENABLE_STATUS.getFieldCode(),  CommonStatusEnum.OFF.getType()),
                        $.updateKeyValue(BillVerifyRuleFieldEnum.MODIFIED.getFieldCode(), new Date())
                )
                .toSql();

        return jdbcTemplate.update(sql.getSqlCode(), sql.getArgs().toArray());
    }

    @Override
    public int bulkDeletion(List<Long> idList, Long companyId) {
        AsserUtils.notNull(idList, "ID集合不能为空！");
        AsserUtils.notNull(companyId, "公司ID不能为空！");

        SQL sql = Updates.create()
                .table(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(BillVerifyRuleFieldEnum.ID.getFieldCode()), LinkMode.IN, idList),
                        Conditions.and(Columns.toColumn(BillVerifyRuleFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId),
                        Conditions.and(Columns.toColumn(BillVerifyRuleFieldEnum.ENABLE_STATUS.getFieldCode()), LinkMode.EQUAL, 1)
                )
                .update(
                        $.updateKeyValue(BillVerifyRuleFieldEnum.ENABLE_STATUS.getFieldCode(),  CommonStatusEnum.OFF.getType()),
                        $.updateKeyValue(BillVerifyRuleFieldEnum.MODIFIED.getFieldCode(), new Date())
                )
                .toSql();

        return jdbcTemplate.update(sql.getSqlCode(), sql.getArgs().toArray());
    }

    @Override
    public List<BillVerifyRuleDO> queryByParam(BillVerifyRuleRequest request, Long companyId) {
        AsserUtils.notNull(companyId, "公司ID不能为空！");

        List<ConditionComponent<?>> conditionComponents = getConditionComponents(request, companyId);

        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(conditionComponents)
                .select()
                .toSql();

        return jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(BillVerifyRuleDO.class),sql.getArgs().toArray());
    }

    @Override
    public List<BillVerifyRuleDO> queryListByAccountId(Long shopId, Long companyId) {
        AsserUtils.notNull(companyId, "公司ID不能为空！");
        AsserUtils.notNull(shopId, "资金账户ID不能为空！");

        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(BillVerifyRuleFieldEnum.SHOP_ID.getFieldCode()), LinkMode.EQUAL, shopId),
                        Conditions.and(Columns.toColumn(BillVerifyRuleFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId),
                        Conditions.and(Columns.toColumn(BillVerifyRuleFieldEnum.ENABLE_STATUS.getFieldCode()), LinkMode.EQUAL, 1)
                )
                .select()
                .toSql();

        return jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(BillVerifyRuleDO.class),sql.getArgs().toArray());
    }

    @Override
    public int enableRule(List<Long> idList, Long companyId) {
        AsserUtils.notNull(idList, "ID不能为空！");
        AsserUtils.notNull(companyId, "公司ID不能为空！");

        SQL sql = Updates.create()
                .table(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(BillVerifyRuleFieldEnum.ID.getFieldCode()), LinkMode.IN, idList),
                        Conditions.and(Columns.toColumn(BillVerifyRuleFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId),
                        Conditions.and(Columns.toColumn(BillVerifyRuleFieldEnum.ENABLE_STATUS.getFieldCode()), LinkMode.EQUAL, 1)
                )
                .update(
                        $.updateKeyValue(BillVerifyRuleFieldEnum.STATUS.getFieldCode(), CommonStatusEnum.ON.getType()),
                        $.updateKeyValue(BillVerifyRuleFieldEnum.MODIFIED.getFieldCode(), new Date())
                )
                .toSql();

        return jdbcTemplate.update(sql.getSqlCode(), sql.getArgs().toArray());
    }

    @Override
    public int shutDownRule(List<Long> idList, Long companyId) {
        AsserUtils.notNull(idList, "ID不能为空！");
        AsserUtils.notNull(companyId, "公司ID不能为空！");

        SQL sql = Updates.create()
                .table(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(BillVerifyRuleFieldEnum.ID.getFieldCode()), LinkMode.IN, idList),
                        Conditions.and(Columns.toColumn(BillVerifyRuleFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId),
                        Conditions.and(Columns.toColumn(BillVerifyRuleFieldEnum.ENABLE_STATUS.getFieldCode()), LinkMode.EQUAL, 1)
                )
                .update(
                        $.updateKeyValue(BillVerifyRuleFieldEnum.STATUS.getFieldCode(),  CommonStatusEnum.OFF.getType()),
                        $.updateKeyValue(BillVerifyRuleFieldEnum.MODIFIED.getFieldCode(), new Date())
                )
                .toSql();

        return jdbcTemplate.update(sql.getSqlCode(), sql.getArgs().toArray());
    }

    private List<ConditionComponent<?>> getConditionComponents(BillVerifyRuleRequest request, Long companyId) {
        List<ConditionComponent<?>> conditions = new ArrayList<>();

        conditions.add(Conditions.and(Columns.toColumn(BillVerifyRuleFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId));
        conditions.add(Conditions.and(Columns.toColumn(BillVerifyRuleFieldEnum.ENABLE_STATUS.getFieldCode()), LinkMode.EQUAL, 1));

        if (request != null) {
            if (request.getShopIdList() != null) {
                conditions.add(Conditions.and(Columns.toColumn(BillVerifyRuleFieldEnum.SHOP_ID.getFieldCode()), LinkMode.IN, request.getShopIdList()));
            }
            if (StringUtils.isNotBlank(request.getRuleName())) {
                conditions.add(Conditions.and(Columns.toColumn(BillVerifyRuleFieldEnum.RULE_NAME.getFieldCode()), LinkMode.LIKE_FULL_MATCH, request.getRuleName()));
            }
        }

        return conditions;
    }
}
