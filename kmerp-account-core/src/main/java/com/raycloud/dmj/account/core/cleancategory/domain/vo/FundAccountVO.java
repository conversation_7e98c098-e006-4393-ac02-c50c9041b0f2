package com.raycloud.dmj.account.core.cleancategory.domain.vo;

import com.raycloud.dmj.account.core.enums.AccountTypeEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 账户信息VO
 * <AUTHOR>
 */
@Setter
@Getter
public class FundAccountVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键 ID
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 修改时间
     */
    private Date modified;

    /**
     * 店铺 ID
     */
    private String shopId;

    /**
     * 店铺编码
     */
    private String shopCode;

    /**
     * 店铺名称
     */
    private String title;

    /**
     * 店铺简称
     */
    private String shortTitle;

    /**
     * 公司 ID
     */
    private Long companyId;

    /**
     * 资金账户名称（支付宝、微信、默认账户）
     */
    private String accountName;

    /**
     * 资金账户类型（1-系统支付宝、2-系统微信）
     * 可结合下方枚举类 AccountTypeEnum 使用，限定取值范围
     * @see AccountTypeEnum
     */
    private Integer type;

    /**
     * 账户类型名称
     */
    private String typeName;

    /**
     * 来源 1-系统，2-手动
     */
    private Integer source;

    /**
     * 账户编码
     */
    private String accountCode;

    /**
     * 期初余额
     */
    private BigDecimal startBalance;

    /**
     * 期初时间
     */
    private Date startDate;

    /**
     * 是否确认初期，0-未确认，1-确认
     */
    private Integer confirmStartPeriod;

    /**
     * 是否授权，0-未授权，1-授权
     */
    private Integer authorize;

}