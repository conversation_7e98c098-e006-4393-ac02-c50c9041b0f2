package com.raycloud.dmj.account.core.scheduled.impl;

import com.raycloud.dmj.account.ISharedDataDubbo;
import com.raycloud.dmj.account.common.DubboResponse;
import com.raycloud.dmj.account.common.SharedDataResponse;
import com.raycloud.dmj.account.common.ShopBillUrlRequest;
import com.raycloud.dmj.account.core.rawdata.req.OriginalDataCallbackReq;
import com.raycloud.dmj.account.core.rawdata.req.OriginalDataParam;
import com.raycloud.dmj.account.core.rawdata.service.RawDataStorageService;
import com.raycloud.dmj.account.core.rawdata.utils.DownloadFileByUrlUtil;
import com.raycloud.dmj.account.core.rawdata.utils.UnzipProcessor;
import com.raycloud.dmj.account.core.scheduled.IScheduledService;
import com.raycloud.dmj.account.core.scheduled.ScheduledType;
import com.raycloud.dmj.account.enums.PlatformType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class AlipayBillStorageScheduled implements IScheduledService {


    @Resource
    private RawDataStorageService rawDataStorageService;

    @DubboReference(check = false)
    private ISharedDataDubbo SharedDataDubbo;

    @Value("${file-path.tmp}")
    private String tmpPath;

    @Override
    public ScheduledType getScheduledType() {
        return ScheduledType.AliPAY_BILL;
    }

    @Override
    public void doTask(Map<String, Object> params) {
        String recordData = (String)params.get("recordData");
        LocalDate billDate = LocalDate.now().plusDays(-1);
        if (recordData != null){
            billDate = LocalDate.parse(recordData);
        }
        log.info("alipay bill data storage billDate:{}" , billDate);
        // todo 接入授权列表功能
        List<ShopBillUrlRequest> billUrlRequestList = Lists.newArrayList();
        ShopBillUrlRequest request = new ShopBillUrlRequest();
        request.setPlatformType(PlatformType.ALIPAY);
        request.setBillDate(billDate);
        request.setShopId(900178460L);
        request.setCompanyId(21401L);
        billUrlRequestList.add(request);

        for (ShopBillUrlRequest shopBillUrlRequest : billUrlRequestList) {
            DubboResponse<SharedDataResponse> response = SharedDataDubbo.getDataUrl(shopBillUrlRequest);
            String url = response.getData().getUrl();
            String zipPath = getTmpPath(request.getCompanyId(), request.getShopId(), request.getBillDate(), ".zip");
            DownloadFileByUrlUtil.downloadFile(url, zipPath);
            String unzipFolderPath = getTmpPath(request.getCompanyId(), request.getShopId(), request.getBillDate(), "");
            List<String> unzippedFiles = UnzipProcessor.unzip(zipPath, unzipFolderPath,null);
            for (String filePath : unzippedFiles) {
                File file = new File(filePath);
                // 检查文件名是否包含"明细"且是CSV文件
                if (file.getName().contains("明细") && filePath.toLowerCase().endsWith(".csv")) {
                    OriginalDataCallbackReq originalDataCallbackReq = new OriginalDataCallbackReq();
                    originalDataCallbackReq.setCompanyId(request.getCompanyId());
                    originalDataCallbackReq.setProcessType(1);
                    List<OriginalDataParam> originalDataParams = Lists.newArrayList();
                    OriginalDataParam originalDataParam = new OriginalDataParam();
                    // todo @liuLang 待完善

                    originalDataParams.add(originalDataParam);
                    originalDataCallbackReq.setFileList(originalDataParams);
                    rawDataStorageService.rpaOriginalDataCallback(originalDataCallbackReq);
                }
            }
        }

        // 解压zip 文件

    }

    public String getTmpPath(Long companyId, Long shopId, LocalDate billDate, String suffix) {
        return tmpPath + "alipay_bill_" + companyId + "_" + shopId + "_" + billDate + suffix;
    }


}
