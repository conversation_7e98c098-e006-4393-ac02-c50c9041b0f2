package com.raycloud.dmj.account.core.platform.auth.handler;

import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.jd.open.api.sdk.DefaultJdClient;
import com.jd.open.api.sdk.JdClient;
import com.jd.open.api.sdk.domain.jinsuanpan.FLDailyBillDetailProvider.response.queryDailyBillDetail.FinBillDetailMO;
import com.jd.open.api.sdk.request.jinsuanpan.PopLedgerBillQueryDailyBillDetailRequest;
import com.jd.open.api.sdk.response.jinsuanpan.PopLedgerBillQueryDailyBillDetailResponse;
import com.raycloud.dmj.account.common.SharedDataResponse;
import com.raycloud.dmj.account.core.enums.FileTypeEnum;
import com.raycloud.dmj.account.core.platform.auth.AbstractPlatformHandler;
import com.raycloud.dmj.account.core.platform.auth.impl.DubboUserService;
import com.raycloud.dmj.account.core.platform.base.domain.ShopAuthInfoDO;
import com.raycloud.dmj.account.core.platform.common.constant.JDConstant;
import com.raycloud.dmj.account.core.platform.service.IShopAuthInfoService;
import com.raycloud.dmj.account.core.rawdata.utils.oss.OSSClientHelper;
import com.raycloud.dmj.account.core.rawdata.utils.oss.OssUtils;
import com.raycloud.dmj.account.enums.AuthStatus;
import com.raycloud.dmj.account.enums.DataType;
import com.raycloud.dmj.account.enums.PlatformType;
import com.raycloud.dmj.account.exeception.AuthDisableException;
import com.raycloud.dmj.account.exeception.DataNotExistException;
import com.raycloud.dmj.account.exeception.DubboBizException;
import com.raycloud.dmj.account.infra.utils.DateUtils;
import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.services.user.IUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@Service
public class JDPopPlatformHandler extends AbstractPlatformHandler {

    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @DubboReference(check = false, registry = "erpZk", version = "erp-prod-0.0.1")
    private IUserService UserService;

    @Resource
    private IShopAuthInfoService shopAuthInfoService;

    @Value("${file-path.tmp}")
    private String tmpPath;

    @Resource
    private DubboUserService dubboUserService;


    @Override
    public PlatformType getPlatformType() {
        return PlatformType.JD_POP;
    }

    @Override
    public String getAuthUrl(PlatformType platform, Long companyId, Long shopId, String callbackUrl, Long recordId, Map<String, Object> extraData) {
        // todo 授权时间待定
        insertOrUpdatePlatformAuth(
                shopId,
                companyId,
                AuthStatus.AUTHORIZE.getValue(),
                "",
                null,
                new JSONObject(extraData));
        return "";
    }

    public SharedDataResponse getDataUrl(Long companyId, Long shopId, LocalDate billDate, DataType dataType, String extraData) {
        PopLedgerBillQueryDailyBillDetailRequest request = getRequestParam(shopId, companyId);
        String sessionId = dubboUserService.getUser(shopId).getSessionId();
        int pageNo = 1;
        // 默认pageSize=200
        request.setPageNum(pageNo);
        // 生成 CSV 文件路径，使用当前时间作为文件名
        String filePath = tmpPath + "bill_detail_" + System.currentTimeMillis() + ".csv";
        boolean fileCreated = false;
        try (FileWriter writer = new FileWriter(filePath);
             CSVPrinter csvPrinter = new CSVPrinter(writer, CSVFormat.DEFAULT.withHeader(
                     "memberId", "billDate", "outTradeNo", "orderId", "rfBusiId", "detailNo", "skuId", "skuName",
                     "rfBusiType", "feeName", "direction", "detailStatus", "remark", "happenTime", "billingTime",
                     "deliveredTime", "finishTime", "updateTime", "bal", "currency", "venderId", "jdShopId",
                     "shopId", "shopName", "extendNode", "skuNum", "refundSkuNum"
             ))) {
            while (true) {
                List<FinBillDetailMO> dataList;
                try {
                    dataList = incrementGetBill(sessionId, request);
                } catch (AuthDisableException e) {
                    log.error("京东授权已过期, 请到店铺管理页面重新授权, 并同步店铺信息", e);
                    return SharedDataResponse.error(SharedDataResponse.AuthDisableCode, "京东授权已过期, 请到店铺管理页面重新授权, 并同步店铺信息");
                } catch (DataNotExistException e) {
                    log.error("京东数据不存在, 请联系相关技术!", e);
                    return SharedDataResponse.error(SharedDataResponse.DataNotExistCode, "京东数据不存在, 请联系相关技术!");
                } catch (Exception e) {
                    return SharedDataResponse.error();
                }
                // 写入本次获取的数据到 CSV 文件中
                fileCreated = appendWrite2Csv(csvPrinter, dataList);
                // 如果当前页数据不足200条，则说明没有更多数据了
                if (dataList.size() < 200) {
                    break;
                }
                request.setPageNum(++pageNo);
            }

            // 如果没有任何数据写入，返回空文件名
            if (!fileCreated) {
                return SharedDataResponse.success(StringUtils.EMPTY);
            }

        } catch (IOException e) {
            log.error("写入CSV文件失败", e);
            return SharedDataResponse.error();
        }
        return SharedDataResponse.success(filePath);
    }

    public boolean appendWrite2Csv(CSVPrinter csvPrinter, List<FinBillDetailMO> dataList) throws IOException {
        if (CollectionUtils.isNotEmpty(dataList)) {
            for (FinBillDetailMO detail : dataList) {
                csvPrinter.printRecord(
                        detail.getMemberId(),
                        detail.getBillDate(),
                        detail.getOutTradeNo(),
                        detail.getOrderId(),
                        detail.getRfBusiId(),
                        detail.getDetailNo(),
                        detail.getSkuId(),
                        detail.getSkuName(),
                        detail.getRfBusiType(),
                        detail.getFeeName(),
                        detail.getDirection(),
                        detail.getDetailStatus(),
                        detail.getRemark(),
                        DateUtils.formatDate(detail.getHappenTime()),
                        DateUtils.formatDate(detail.getBillingTime()),
                        DateUtils.formatDate(detail.getDeliveredTime()),
                        DateUtils.formatDate(detail.getFinishTime()),
                        DateUtils.formatDate(detail.getUpdateTime()),
                        detail.getBal(),
                        detail.getCurrency(),
                        detail.getVenderId(),
                        detail.getJdShopId(),
                        detail.getShopId(),
                        detail.getShopName(),
                        detail.getExtendNode(),
                        detail.getSkuNum(),
                        detail.getRefundSkuNum()
                );

            }
        }
        csvPrinter.flush();
        return true;
    }

    public List<FinBillDetailMO> incrementGetBill(String sessionId, PopLedgerBillQueryDailyBillDetailRequest request) throws Exception {

        JdClient client = new DefaultJdClient(JDConstant.SERVER_URL_PROXY, sessionId, JDConstant.APPKEY, JDConstant.APPSECRET);
            PopLedgerBillQueryDailyBillDetailResponse response = client.execute(request);
            if (response == null) {
                throw new DubboBizException("京东接口返回为空, 请联系相关技术!");
            } else if (response.getResult() == null) {
                if (StringUtils.isNotBlank(response.getZhDesc())) {
                    if (response.getZhDesc().contains("已过期")) {
                        throw new AuthDisableException("京东授权已过期, 请到店铺管理页面重新授权, 并同步店铺信息");
                    } else {
                        throw new DubboBizException("京东接口返回报错信息为空, 请联系相关技术!");
                    }
                } else {
                    throw new DubboBizException("京东接口返回报错信息为空, 请联系相关技术!");
                }
            } else if (response.getResult().getSuccess() == null || !response.getResult().getSuccess()) {
                if ("10011".equalsIgnoreCase(response.getResult().getResultCode()) || "10012".equalsIgnoreCase(response.getResult().getResultCode())) {
                    //如果返回10011码, 则说明当天没有数据, 直接终止循环, 并设置新的同步时间
                    throw new DataNotExistException("数据为空");
                } else {
                    throw new DubboBizException("京东接口返回success = false, 请联系相关技术!");
                }
            } else {
                return response.getResult().getData();
            }
    }

    private PopLedgerBillQueryDailyBillDetailRequest getRequestParam(Long shopId, Long companyId) {
        PopLedgerBillQueryDailyBillDetailRequest request = new PopLedgerBillQueryDailyBillDetailRequest();
        ShopAuthInfoDO shopAuthInfo = shopAuthInfoService.getShopAuthInfoByShopIdAndPlatformCode(shopId, companyId, getPlatformType().getValue());
        if (shopAuthInfo == null || StringUtils.isBlank(shopAuthInfo.getExtraData())) {
            throw new DubboBizException(shopId + "京东授权已过期, 请到店铺管理页面重新授权, 并同步店铺信息");
        }
        String extraData = shopAuthInfo.getExtraData();
        JSONObject extraJSON = JSONObject.parseObject(extraData);
        String memberId = extraJSON.getString(JDConstant.JD_SECOND_MERCHANT_NO);
        if(StringUtils.isBlank(memberId)) {
            throw new DubboBizException(shopId + ":京东授权未提供二级商家号");
        }
        request.setMemberId(memberId);
        // todo 先写死测试
//        request.setMemberId("144556049001");

        request.setType("3");
        LocalDate date = LocalDate.now().minusDays(1);
        // 获取当天的开始和结束时间
        LocalDateTime startDateTime = date.atStartOfDay();
        LocalDateTime endDateTime = date.atTime(23, 59, 59);
        request.setStartDate(toDateTimeString(startDateTime));
        request.setEndDate(toDateTimeString(endDateTime));
        return request;
    }

    public static String toDateTimeString(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return dateTime.format(DATETIME_FORMATTER);
    }



}
