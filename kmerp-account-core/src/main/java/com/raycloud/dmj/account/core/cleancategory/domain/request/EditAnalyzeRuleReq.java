package com.raycloud.dmj.account.core.cleancategory.domain.request;

import com.raycloud.dmj.account.core.cleancategory.domain.AnalyzeRuleInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * 添加解析规则请求
 * <AUTHOR>
 */
@Data
public class EditAnalyzeRuleReq implements Serializable {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 平台code
     */
    private String platformCode;

    /**
     * 规则内容
     */
    private List<List<AnalyzeRuleInfo>> analyzeRuleContent;


    /**
     * 子类别ID
     */
    private Long subCategoryId;
}
