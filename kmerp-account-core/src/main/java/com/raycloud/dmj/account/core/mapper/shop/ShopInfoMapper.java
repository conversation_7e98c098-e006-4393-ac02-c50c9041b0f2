package com.raycloud.dmj.account.core.mapper.shop;

import com.raycloud.dmj.account.core.base.domain.ShopInfoDO;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 店铺信息Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface ShopInfoMapper {

    /**
     * 根据ID查询店铺信息
     */
    @Select("SELECT * FROM shop_info WHERE id = #{id}")
    ShopInfoDO selectById(@Param("id") Long id);

    /**
     * 根据店铺编码查询
     */
    @Select("SELECT * FROM shop_info WHERE shop_code = #{shopCode}")
    ShopInfoDO selectByShopCode(@Param("shopCode") String shopCode);

    /**
     * 插入店铺信息
     */
    @Insert("INSERT INTO shop_info (shop_code,platform_code,title, short_title, affiliated_company_id, amount, " +
            "account_status, enable_status, company_id, created, modified) " +
            "VALUES (#{shopCode},#{platformCode} ,#{title}, #{shortTitle}, #{affiliatedCompanyId}, #{amount}, " +
            "#{accountStatus}, #{enableStatus}, #{companyId}, #{created}, #{modified})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(ShopInfoDO shopInfoDO);

    /**
     * 更新店铺信息
     */
    @Update("UPDATE shop_info SET title = #{title}, short_title = #{shortTitle}, " +
            "amount = #{amount}, account_status = #{accountStatus}, modified = #{modified} " +
            "WHERE id = #{id}")
    int updateById(ShopInfoDO shopInfoDO);

    /**
     * 根据公司ID查询店铺列表
     */
    @Select("SELECT * FROM shop_info WHERE company_id = #{companyId} AND enable_status = 1")
    List<ShopInfoDO> selectByCompanyId(@Param("companyId") Long companyId);

    /**
     * 复杂查询方法 - 使用XML实现
     */
    List<ShopInfoDO> selectShopListWithCondition(@Param("shopCode") String shopCode,
                                                 @Param("title") String title,
                                                 @Param("accountStatus") Integer accountStatus,
                                                 @Param("companyId") Long companyId);

    /**
     * 统计查询
     */
    Long countByCondition(@Param("shopCode") String shopCode,
                         @Param("title") String title,
                         @Param("accountStatus") Integer accountStatus,
                         @Param("companyId") Long companyId);

    /**
     * 批量插入
     */
    int batchInsert(@Param("list") List<ShopInfoDO> shopInfoDOList);
}
