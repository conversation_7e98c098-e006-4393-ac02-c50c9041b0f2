package com.raycloud.dmj.account.core.enums.field;

import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 直播红包汇总表字段
 * <AUTHOR>
 */

@Getter
public enum TmallLiveRedEnvelopeSumRawBillFieldEnum {
    ID("id", "主键ID"),
    BILLING_MONTH("billing_month", "账单月份"),
    RED_ENVELOPE_TYPE("red_envelope_type", "红包类型"),
    INVOICE_TYPE("invoice_type", "发票类型"),
    INVOICE_TITLE("invoice_title", "发票抬头"),
    CURRENT_INVOICE_AMOUNT("current_invoice_amount", "当期开票金额（元）"),
    CURRENT_REFUND_AMOUNT("current_refund_amount", "当期退款金额（元）"),
    DUE_INVOICE_AMOUNT("due_invoice_amount", "应开票金额（元）"),
    INVOICED_AMOUNT("invoiced_amount", "已开发票金额（元）"),
    ANCHOR_NAME("anchor_name", "主播名称"),
    BUYER_TAX_ID("buyer_tax_id", "购方税号"),
    BANK_NAME("bank_name", "开户银行"),
    BANK_ACCOUNT("bank_account", "银行账户"),
    COMPANY_PHONE("company_phone", "企业电话"),
    COMPANY_ADDRESS("company_address", "企业地址"),
    INVOICE_RECIPIENT("invoice_recipient", "收票人"),
    RECIPIENT_PHONE("recipient_phone", "收票人电话"),
    RECIPIENT_ADDRESS("recipient_address", "收票人地址"),
    ANCHOR_REMARK("anchor_remark", "主播备注"),
    SERIAL_NO("serial_no", "与明细表关联字段"),
    BIZ_KEY("biz_key", "唯一键"),
    BATCH_NO("batch_no", "批次号"),
    BATCH_TIME("batch_time", "批次时间 格式如********"),
    COMPANY_ID("company_id", "租户ID"),
    SHOP_ID("shop_id", "店铺ID"),
    CREATED("created", "创建时间");

    /**
     * 字段编码
     */
    private final String fieldCode;

    /**
     * 字段描述
     */
    private final String fieldDesc;

    TmallLiveRedEnvelopeSumRawBillFieldEnum(String fieldCode, String fieldDesc) {
        this.fieldCode = fieldCode;
        this.fieldDesc = fieldDesc;
    }

    /**
     * 获取插入字段
     * @return 插入字段
     */
    public static Set<String> getInsertFields() {
        Set<TmallLiveRedEnvelopeSumRawBillFieldEnum> filterSet = Collections.singleton(TmallLiveRedEnvelopeSumRawBillFieldEnum.ID);
        return Arrays.stream(TmallLiveRedEnvelopeSumRawBillFieldEnum.values())
                .filter(x ->!filterSet.contains(x))
                .map(x -> x.fieldCode)
                .collect(Collectors.toSet());
    }
}