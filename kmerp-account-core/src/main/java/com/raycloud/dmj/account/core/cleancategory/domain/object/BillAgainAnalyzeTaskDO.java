package com.raycloud.dmj.account.core.cleancategory.domain.object;

import com.alibaba.fastjson2.JSONObject;
import com.raycloud.dmj.account.core.enums.AgainAnalyzeStatusEnum;
import com.raycloud.dmj.account.core.enums.feature.BillAgainAnalyzeTaskFeatureEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;

/**
 * 账单重洗任务表
 *
 * <AUTHOR>
 */
@Data
public class BillAgainAnalyzeTaskDO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 修改时间
     */
    private Date modified;

    /**
     * 资金账户ID
     */
    private Long fundAccountId;

    /**
     * 平台code
     */
    private String platformCode;

    /**
     * 重洗开始时间
     */
    private Date reprocessStartTime;

    /**
     * 重洗结束时间
     */
    private Date reprocessEndTime;

    /**
     * 任务状态，0-清洗中，30-清洗失败，50-清洗成功
     *
     * @see AgainAnalyzeStatusEnum
     */
    private Integer status;

    /**
     * 完成日期
     */
    private Date completionDate;

    /**
     * 公司ID
     */
    private Long companyId;


    /**
     * 扩展字段JSON
     */
    private String feature;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 获取扩展字段
     *
     * @param featureEnum 扩展字段枚举
     * @return 扩展字段
     */
    public String getFeature(BillAgainAnalyzeTaskFeatureEnum featureEnum) {
        JSONObject jsonObject = StringUtils.isNotBlank(feature) ? JSONObject.parseObject(feature) : new JSONObject();
        return jsonObject.getString(featureEnum.getFieldCode());
    }

    /**
     * 添加扩展字段
     *
     * @param featureEnum 扩展字段枚举
     * @param value       值
     */
    public void putFeature(BillAgainAnalyzeTaskFeatureEnum featureEnum, Object value) {
        JSONObject jsonObject = StringUtils.isNotBlank(feature) ? JSONObject.parseObject(feature) : new JSONObject();
        jsonObject.put(featureEnum.getFieldCode(), value);
        feature = jsonObject.toJSONString();
    }

}