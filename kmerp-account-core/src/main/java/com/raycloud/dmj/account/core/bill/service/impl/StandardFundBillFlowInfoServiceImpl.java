package com.raycloud.dmj.account.core.bill.service.impl;

import com.raycloud.dmj.account.core.base.dao.CategoryDao;
import com.raycloud.dmj.account.core.base.dao.CategoryGroupDao;
import com.raycloud.dmj.account.core.base.dao.StandardFundBillFlowInfoDao;
import com.raycloud.dmj.account.core.base.dao.SubCategoryDao;
import com.raycloud.dmj.account.core.base.domain.StandardFundBillFlowInfoDO;
import com.raycloud.dmj.account.core.bill.parameter.StandardFundBillFlowParameter;
import com.raycloud.dmj.account.core.bill.request.AddStandardFundBillFlowInfoRequest;
import com.raycloud.dmj.account.core.bill.request.FundBillSplitFlowRequest;
import com.raycloud.dmj.account.core.bill.request.StandardFundBillFlowRequest;
import com.raycloud.dmj.account.core.bill.request.StandardFundBillSplitFlowRequest;
import com.raycloud.dmj.account.core.bill.service.IStandardFundBillFlowInfoService;
import com.raycloud.dmj.account.core.bill.vo.StandardFundBillFlowInfoVO;
import com.raycloud.dmj.account.core.cleancategory.domain.object.CategoryDO;
import com.raycloud.dmj.account.core.cleancategory.domain.object.CategoryGroupDO;
import com.raycloud.dmj.account.core.cleancategory.domain.object.SubCategoryDO;
import com.raycloud.dmj.account.core.common.PageInfo;
import com.raycloud.dmj.account.core.bill.vo.BillTotalInfo;
import com.raycloud.dmj.account.core.common.exception.SessionException;
import com.raycloud.dmj.account.core.enums.CommonStatusEnum;
import com.raycloud.dmj.account.core.enums.PlatformEnum;
import com.raycloud.dmj.account.core.enums.StandardFundBillSourceEnum;
import com.raycloud.dmj.account.export.core.ExportApplicationService;
import com.raycloud.dmj.account.export.sevice.BillFundExport;
import com.raycloud.dmj.account.infra.common.BusinessException;
import com.raycloud.dmj.account.infra.common.ErrorCodeEnum;
import com.raycloud.dmj.account.infra.session.AccountUser;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import com.raycloud.dmj.account.infra.utils.BeanUtils;
import com.raycloud.dmj.account.infra.utils.DateUtils;
import com.raycloud.dmj.data.chessboard.model.PushResult;
import com.raycloud.dmj.domain.account.Staff;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 标准资金账单流水信息服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class StandardFundBillFlowInfoServiceImpl implements IStandardFundBillFlowInfoService {

    @Resource
    private StandardFundBillFlowInfoDao standardFundBillFlowInfoDao;
    @Resource
    private CategoryDao categoryDao;
    @Resource
    private SubCategoryDao subCategoryDao;
    @Resource
    private CategoryGroupDao categoryGroupDao;
    @Resource
    private ExportApplicationService exportApplicationService;
    @Resource
    private BillFundExport export;

    @Override
    public Long addStandardFundBillFlowInfo(AccountUser accountUser, AddStandardFundBillFlowInfoRequest request) {
        // 参数校验
        AsserUtils.notEmpty(request.getPlatformCode(), "平台类型不能为空");
        AsserUtils.notNull(request.getShopId(), "店铺ID不能为空");
        AsserUtils.notNull(request.getAccountId(), "资金账户ID不能为空");
        AsserUtils.notNull(request.getOccurredAt(), "发生时间不能为空");
        AsserUtils.notNull(request.getRuleId(), "数据清洗规则ID不能为空");
        AsserUtils.notEmpty(request.getCategoryCode(), "分类code不能为空");
        AsserUtils.notNull(request.getCategoryId(), "流水类别ID不能为空");
        AsserUtils.notNull(request.getSubCategoryId(), "流水子类别ID不能为空");
        AsserUtils.notNull(request.getIncomeExpenseDirection(), "收支类型不能为空");
        AsserUtils.notNull(request.getIsOffset(), "是否可抵消类型不能为空");
        AsserUtils.notNull(request.getAmount(), "金额不能为空");
        AsserUtils.notEmpty(request.getBatchNo(), "批次号不能为空");
        AsserUtils.notEmpty(request.getBizKey(), "唯一键不能为空");

        // 获取当前用户信息
        if (accountUser == null || accountUser.getCompanyId() == null) {
            throw new SessionException("无法获取当前用户的公司ID");
        }
        Long companyId = accountUser.getCompanyId();

        StandardFundBillFlowInfoDO flowInfo = new StandardFundBillFlowInfoDO();
        BeanUtils.copyProperties(request, flowInfo);
        // 设置公司ID和创建人
        flowInfo.setCompanyId(companyId);
        flowInfo.setCreator(accountUser.getAccountName());
        flowInfo.setConfirmed(CommonStatusEnum.OFF.getType());
        // 生成唯一键（如果没有提供）
        if (flowInfo.getBizKey() == null) {
            flowInfo.setBizKey(generateBizKey(flowInfo));
        }

        try {
            Long id = standardFundBillFlowInfoDao.addStandardFundBillFlowInfo(flowInfo);
            log.info("新增标准资金账单流水信息成功，ID: {}, 店铺ID: {}, 金额: {}",
                    id, flowInfo.getShopId(), flowInfo.getAmount());
            return id;
        } catch (Exception e) {
            log.error("新增标准资金账单流水信息失败", e);
            throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR,"新增标准资金账单流水信息失败: " + e.getMessage());
        }
    }

    @Override
    public List<StandardFundBillFlowInfoVO> getStandardFundBillFlowInfoList(AccountUser accountUser, StandardFundBillFlowRequest request) {
        // 获取当前用户信息
        if (accountUser == null || accountUser.getCompanyId() == null) {
            throw new SessionException("无法获取当前用户的公司ID");
        }

        //判断时间,默认取最近30天的
        if (request.getStartTime() == null && request.getEndTime() == null) {
            request.setStartTime(DateUtils.getDayStartTime(DateUtils.getOffsetDay(new Date(), -30)));
            request.setEndTime(DateUtils.getDayEndTime(new Date()));
        }

        StandardFundBillFlowParameter parameter = BeanUtils.copy(request, StandardFundBillFlowParameter.class);

        List<StandardFundBillFlowInfoVO> standardFundBillFlowInfoVoList = new ArrayList<>();

        //查询数据
        standardFundBillFlowInfoVoList = standardFundBillFlowInfoDao.getStandardFundBillFlowInfoList(parameter, accountUser.getCompanyId());

        if (CollectionUtils.isEmpty(standardFundBillFlowInfoVoList)) {
            return standardFundBillFlowInfoVoList;
        }

        //查询分类
        Map<String, String> categoryGroupMap = Optional.ofNullable(categoryGroupDao.list(accountUser.getCompanyId()))
                .orElse(Collections.emptyList()) // 如果为null，返回空列表
                .stream()
                .collect(Collectors.toMap(CategoryGroupDO::getCode, CategoryGroupDO::getName));
        //查询类别
        Map<Long, String> categoryMap = Optional.ofNullable(categoryDao.queryByCompanyId(accountUser.getCompanyId()))
                .orElse(Collections.emptyList()) // 如果为null，返回空列表
                .stream()
                .collect(Collectors.toMap(CategoryDO::getId, CategoryDO::getName));
        //查询子类别
        Map<Long, String> subCategoryMap = Optional.ofNullable(subCategoryDao.queryByCompanyId(accountUser.getCompanyId()))
                .orElse(Collections.emptyList()) // 如果为null，返回空列表
                .stream()
                .collect(Collectors.toMap(SubCategoryDO::getId, SubCategoryDO::getName));

        //设置分类名称
        standardFundBillFlowInfoVoList.forEach(flowInfo -> {
            flowInfo.setCategoryCodeName(categoryGroupMap.get(flowInfo.getCategoryCode()));
            flowInfo.setCategoryName(categoryMap.get(flowInfo.getCategoryId()));
            flowInfo.setSubCategoryName(subCategoryMap.get(flowInfo.getSubCategoryId()));
            flowInfo.setAmount(flowInfo.getAmount().stripTrailingZeros());
            flowInfo.setPlatformName(Objects.requireNonNull(PlatformEnum.getPlatformEnumByCode(flowInfo.getPlatformCode())).getTitle());
        });

        return standardFundBillFlowInfoVoList;
    }

    @Override
    public PageInfo<Object> getPageInfo(AccountUser accountUser, StandardFundBillFlowRequest request) {
        // 获取当前用户信息
        if (accountUser == null || accountUser.getCompanyId() == null) {
            throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR,"无法获取当前用户的公司ID");
        }

        //判断时间,默认取最近30天的
        if (request.getStartTime() == null && request.getEndTime() == null) {
            request.setStartTime(DateUtils.getDayStartTime(DateUtils.getOffsetDay(new Date(), -30)));
            request.setEndTime(DateUtils.getDayEndTime(new Date()));
        }

        StandardFundBillFlowParameter parameter = BeanUtils.copy(request, StandardFundBillFlowParameter.class);
        //查询汇总信息
        BillTotalInfo billTotalInfo = standardFundBillFlowInfoDao.getTotalInfo(parameter, accountUser.getCompanyId());
        billTotalInfo.setTotal(billTotalInfo.getTotal() == null ? 0L : billTotalInfo.getTotal());
        billTotalInfo.setTotalAmount(billTotalInfo.getTotalAmount() == null ? BigDecimal.ZERO : billTotalInfo.getTotalAmount().stripTrailingZeros());

        return PageInfo.<Object>builder()
                .pageNo(request.getPageNo())
                .pageSize(request.getPageSize())
                .data(billTotalInfo)
                .total(billTotalInfo.getTotal()).build();
    }

    @Override
    public StandardFundBillFlowInfoVO getStandardFundBillFlowInfoById(AccountUser accountUser,Long id) {
        AsserUtils.notNull(id, "ID不能为空");
        // 获取当前用户信息
        if (accountUser == null || accountUser.getCompanyId() == null) {
            throw new SessionException("无法获取当前用户的公司ID");
        }
        //查询分类
        Map<String, String> categoryGroupMap = Optional.ofNullable(categoryGroupDao.list(accountUser.getCompanyId()))
                .orElse(Collections.emptyList()) // 如果为null，返回空列表
                .stream()
                .collect(Collectors.toMap(CategoryGroupDO::getCode, CategoryGroupDO::getName));
        //查询类别
        Map<Long, String> categoryMap = Optional.ofNullable(categoryDao.queryByCompanyId(accountUser.getCompanyId()))
                .orElse(Collections.emptyList()) // 如果为null，返回空列表
                .stream()
                .collect(Collectors.toMap(CategoryDO::getId, CategoryDO::getName));
        //查询子类别
        Map<Long, String> subCategoryMap = Optional.ofNullable(subCategoryDao.queryByCompanyId(accountUser.getCompanyId()))
                .orElse(Collections.emptyList()) // 如果为null，返回空列表
                .stream()
                .collect(Collectors.toMap(SubCategoryDO::getId, SubCategoryDO::getName));

        StandardFundBillFlowInfoDO flowInfo = standardFundBillFlowInfoDao.getStandardFundBillFlowInfoById(id);
        if (flowInfo == null) {
            throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR.getCode(),"流水信息不存在");
        }
        StandardFundBillFlowInfoVO flowInfoVO = new StandardFundBillFlowInfoVO();
        BeanUtils.copy(flowInfo, StandardFundBillFlowInfoVO.class);
        //设置分类名称
        flowInfoVO.setCategoryName(categoryMap.get(flowInfo.getCategoryId()));
        flowInfoVO.setSubCategoryName(subCategoryMap.get(flowInfo.getSubCategoryId()));
        flowInfoVO.setPlatformName(Objects.requireNonNull(PlatformEnum.getPlatformEnumByCode(flowInfo.getPlatformCode())).getTitle());
        flowInfoVO.setAmount(flowInfo.getAmount().stripTrailingZeros());
        return flowInfoVO;
    }

    @Override
    public void batchAddStandardFundBillFlowInfo(AccountUser accountUser, List<AddStandardFundBillFlowInfoRequest> requestList) {
        // 参数校验
        Assert.notEmpty(requestList, "流水信息列表不能为空");
        // 获取当前用户信息
        if (accountUser == null || accountUser.getCompanyId() == null) {
            throw new SessionException("无法获取当前用户的公司ID");
        }

        List<StandardFundBillFlowInfoDO> flowInfoList = new ArrayList<>();
        // 为每个流水信息设置公司ID和默认值
        for (AddStandardFundBillFlowInfoRequest request : requestList) {
            // 参数校验
            AsserUtils.notEmpty(request.getPlatformCode(), "平台类型不能为空");
            AsserUtils.notNull(request.getShopId(), "店铺ID不能为空");
            AsserUtils.notNull(request.getAccountId(), "资金账户ID不能为空");
            AsserUtils.notNull(request.getOccurredAt(), "发生时间不能为空");
            AsserUtils.notNull(request.getRuleId(), "数据清洗规则ID不能为空");
            AsserUtils.notEmpty(request.getCategoryCode(), "分类code不能为空");
            AsserUtils.notNull(request.getCategoryId(), "流水类别ID不能为空");
            AsserUtils.notNull(request.getSubCategoryId(), "流水子类别ID不能为空");
            AsserUtils.notNull(request.getIncomeExpenseDirection(), "收支类型不能为空");
            AsserUtils.notNull(request.getIsOffset(), "是否可抵消类型不能为空");
            AsserUtils.notNull(request.getAmount(), "金额不能为空");
            AsserUtils.notEmpty(request.getBatchNo(), "批次号不能为空");
            AsserUtils.notEmpty(request.getBizKey(), "唯一键不能为空");

            StandardFundBillFlowInfoDO flowInfo = new StandardFundBillFlowInfoDO();
            BeanUtils.copyProperties(request,flowInfo);
            // 设置公司ID和创建人
            flowInfo.setCompanyId(accountUser.getCompanyId());

            // 设置创建时间
            flowInfo.setCreated(new Date());
            flowInfo.setConfirmed(CommonStatusEnum.OFF.getType());
            if (flowInfo.getSource() == null) {
                flowInfo.setSource(StandardFundBillSourceEnum.BILL.getCode()); // 默认来源账单
            }
        }

        try {
            standardFundBillFlowInfoDao.batchAddStandardFundBillFlowInfo(flowInfoList);
            log.info("批量新增标准资金账单流水信息成功，数量: {}", flowInfoList.size());
        } catch (Exception e) {
            log.error("批量新增标准资金账单流水信息失败", e);
            throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR,"批量新增标准资金账单流水信息失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    public void splitFlow(AccountUser accountUser, StandardFundBillSplitFlowRequest splitFlowRequest) {
        log.info("开始执行流水拆分操作，用户：{}，拆分流水ID：{}",
                accountUser != null ? accountUser.getAccountName() : "unknown",
                splitFlowRequest != null ? splitFlowRequest.getBelongId() : "unknown");
        try {
            // 参数校验
            Assert.notNull(splitFlowRequest, "拆分参数不能为空");
            Assert.notNull(splitFlowRequest.getBelongId(), "请输入拆分流水ID");
            Assert.notNull(splitFlowRequest.getFlowInfoList(), "请输入拆分后的流水信息");
            Assert.notEmpty(splitFlowRequest.getFlowInfoList(), "拆分后的流水信息不能为空");

            // 获取当前用户信息
            if (accountUser == null || accountUser.getCompanyId() == null) {
                throw new SessionException("无法获取当前用户的公司ID");
            }

            //查询流水信息
            StandardFundBillFlowInfoDO billFlowInfo = standardFundBillFlowInfoDao.getStandardFundBillFlowInfoById(splitFlowRequest.getBelongId());

            //校验流水状态
            if (billFlowInfo == null) {
                throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR,"拆分流水不存在");
            }
            if (billFlowInfo.getConfirmed() == 1) {
                throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR,"拆分流水已确认，无法拆分");
            }
            if (billFlowInfo.getDocNo().equals("-1")) {
                throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR,"拆分流水已关联业务单据，无法拆分");
            }
            if (billFlowInfo.getIsSplit() == 1) {
                throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR,"拆分流水已拆分，无法再次拆分");
            }

            //校验拆分前后的金额是否一致
            BigDecimal totalSplitAmount = splitFlowRequest.getFlowInfoList().stream()
                    .map(FundBillSplitFlowRequest::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            if (billFlowInfo.getAmount().compareTo(totalSplitAmount) != 0) {
                throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR,"拆分前后金额不一致，原金额：" + billFlowInfo.getAmount() + "，拆分后总金额：" + totalSplitAmount);
            }

            //拆分流水
            List<StandardFundBillFlowInfoDO> standardFundBillFlowInfoDOList = new ArrayList<>();
            splitFlowRequest.getFlowInfoList().forEach(flowInfo -> {
                StandardFundBillFlowInfoDO fundBillFlowInfo = new StandardFundBillFlowInfoDO();
                BeanUtils.copyProperties(billFlowInfo, fundBillFlowInfo, "id");
                fundBillFlowInfo.setAmount(flowInfo.getAmount());
                fundBillFlowInfo.setBelongId(billFlowInfo.getId());
                fundBillFlowInfo.setCreator(accountUser.getAccountName());
                fundBillFlowInfo.setCategoryCode(flowInfo.getCategoryCode());
                fundBillFlowInfo.setCategoryId(flowInfo.getCategoryId());
                fundBillFlowInfo.setSubCategoryId(flowInfo.getSubCategoryId());
                fundBillFlowInfo.setIncomeExpenseDirection(flowInfo.getAmount().compareTo(BigDecimal.ZERO) > 0 ? 1 : 2);
                fundBillFlowInfo.setBizKey(generateBizKey(fundBillFlowInfo));
                fundBillFlowInfo.setDataRange(billFlowInfo.getDataRange());
                fundBillFlowInfo.setCreator(accountUser.getAccountName());
                fundBillFlowInfo.setModified(new Date());
                fundBillFlowInfo.setCreated(new Date());
                standardFundBillFlowInfoDOList.add(fundBillFlowInfo);
            });

            log.info("开始保存拆分流水数据，拆分流水ID：{}，拆分后流水数量：{}",
                    splitFlowRequest.getBelongId(), standardFundBillFlowInfoDOList.size());

            //保存拆分的流水数据信息
            int insertCount = standardFundBillFlowInfoDao.batchAddStandardFundBillFlowInfo(standardFundBillFlowInfoDOList);
            log.info("成功保存拆分流水数据，插入记录数：{}", insertCount);

            //更新原流水的拆分状态
            int updateCount = standardFundBillFlowInfoDao.updateIsSplitById(splitFlowRequest.getBelongId(), 1);
            if (updateCount != 1) {
                throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR,"更新原流水拆分状态失败，可能已被其他操作修改，流水ID：" + splitFlowRequest.getBelongId());
            }

            log.info("流水拆分操作成功完成，原流水ID：{}，拆分后流水数量：{}",
                    splitFlowRequest.getBelongId(), standardFundBillFlowInfoDOList.size());

        } catch (Exception e) {
            log.error("流水拆分操作失败，原因：{}", e.getMessage(), e);
            throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR, "流水拆分操作失败：" + e.getMessage());
        }
    }

    @Override
    public PushResult export(Staff staff, StandardFundBillFlowRequest request) {
        AsserUtils.notNull(staff, "用户不能为空");
        AsserUtils.notNull(request, "查询参数不能为空");
        AsserUtils.notNull(request.getPageId(), "页面ID不能为空");

        //判断时间,默认取最近30天的
        if (request.getStartTime() == null && request.getEndTime() == null) {
            request.setStartTime(DateUtils.getDayStartTime(DateUtils.getOffsetDay(new Date(), -30)));
            request.setEndTime(DateUtils.getDayEndTime(new Date()));
        }

        StandardFundBillFlowParameter parameter = BeanUtils.copy(request, StandardFundBillFlowParameter.class);
        // 参数转换
        return exportApplicationService.createExportTask(
                staff,
                "bill-fund-export",
                "标准资金账单导出",
                parameter,
                request.getPageId(),
                export
        );

    }



    /**
     * 生成业务唯一键
     *
     * @param flowInfo 流水信息
     * @return 唯一键
     */
    private String generateBizKey(StandardFundBillFlowInfoDO flowInfo) {
        // 使用店铺ID + 批次号 + 时间戳 + UUID后8位生成唯一键
        String timestamp = String.valueOf(System.currentTimeMillis());
        String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
        return flowInfo.getShopId() + "_" + flowInfo.getBatchNo() + "_" + timestamp + "_" + uuid;
    }
}
