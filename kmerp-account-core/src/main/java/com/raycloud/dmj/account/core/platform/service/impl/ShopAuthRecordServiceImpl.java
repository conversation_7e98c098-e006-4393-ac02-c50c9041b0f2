package com.raycloud.dmj.account.core.platform.service.impl;

import com.raycloud.dmj.account.core.platform.base.domain.ShopAuthRecordDO;
import com.raycloud.dmj.account.core.platform.dao.ShopAuthRecordDao;
import com.raycloud.dmj.account.core.platform.service.IShopAuthRecordService;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.LocalDateTime;

@Repository
@Slf4j
public class ShopAuthRecordServiceImpl implements IShopAuthRecordService {

    @Resource
    private ShopAuthRecordDao shopAuthRecordDao;

    @Override
    public Long insertShopAuthRecord(ShopAuthRecordDO record) {
        record.setCreated(LocalDateTime.now());
        record.setModified(LocalDateTime.now());
        return shopAuthRecordDao.insert(record);
    }

    @Override
    public ShopAuthRecordDO queryShopAuthRecordById(Long id) {
       return shopAuthRecordDao.getById(id);
    }

    @Override
    public void updateShopAuthRecord(ShopAuthRecordDO record) {
        shopAuthRecordDao.updateById(record);

    }
}