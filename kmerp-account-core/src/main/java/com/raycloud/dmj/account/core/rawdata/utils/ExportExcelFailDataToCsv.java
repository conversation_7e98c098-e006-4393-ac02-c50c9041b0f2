package com.raycloud.dmj.account.core.rawdata.utils;

import au.com.bytecode.opencsv.CSVWriter;
import com.raycloud.dmj.account.core.rawdata.domains.ImportStaffInfo;
import com.raycloud.readexcel.constant.FailTypeEnum;
import com.raycloud.readexcel.context.ResultFailContext;
import com.raycloud.readexcel.domain.ExcelFailData;

import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * EasyExcel分批次导出数据到同一个Excel文件的工具类
 */
public class ExportExcelFailDataToCsv {



    public static void exportDataToCsv(List<ExcelFailData> excelFailDataList, String outputPath, boolean isFirst,String fileName, ResultFailContext<ImportStaffInfo> resultFailContext) {
        try (CSVWriter writer = new CSVWriter(
                new FileWriter(outputPath, true)
        )) {
            ImportStaffInfo userDefinedData = resultFailContext.getUserDefinedData();
            if (isFirst){
                writer.writeNext(new String[]{"文件名称","当前sheet","表头名称", "列数", "行数", "表名称", "表字段", "错误值","错误类型", "错误信息"});
            }
            List<String[]> dataList = new ArrayList<>();
            for (ExcelFailData datum : excelFailDataList) {
                String[] record = new String[]{};
                record = new String[]{
                        fileName,
                        resultFailContext.getSheetName(),
                        datum.getHeadName(),
                        datum.getColumnIndex(),
                        datum.getLine() + "",
                        datum.getTableName(),
                        datum.getTableField(),
                        datum.getTableField(),
                        datum.getValue(),
                        FailTypeEnum.getFailTypeName(datum.getFailType()),
                        datum.getErrorMessage()
                };
                dataList.add(record);
            }
            // 写入多行数据
            writer.writeAll(dataList);

            System.out.println("数据追加成功！");

        } catch (IOException e) {
            System.err.println("追加数据时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 示例：如何使用该工具类
     */
    public static void main(String[] args) {
        try {
            // 输出文件路径
            String outputPath = "/Users/<USER>/Documents/file/testfile.csv";
            // Sheet名称
            String sheetName = "数据导出";
            // 每批次处理1000条记录

            for (int page = 1; ;) {
                int batchSize = 1000;
                // 模拟从数据库或其他数据源获取数据
                List<DemoData> data = new ArrayList<>(batchSize);

                // 模拟获取10000条数据，每次返回1000条
                if (page * batchSize >= 1000000) {
                    break;
                }

                for (int i = 0; i < batchSize; i++) {
                    int index = page * batchSize + i;
                    if (index >= 1000000) {
                        break;
                    }

                    DemoData item = new DemoData();
                    item.setName("姓名_" + index);
                    item.setAge(20 + (index % 30));
                    item.setAddress("地址_" + UUID.randomUUID().toString().substring(0, 8));
                    data.add(item);
                }


                try (CSVWriter writer = new CSVWriter(
                        new FileWriter(outputPath, true)
                )) {
                    List<String[]> dataList = new ArrayList<>();
                    for (DemoData datum : data) {
                        String[] record = {
                                datum.getName(),
                                String.valueOf(datum.getAge()),
                                datum.getAddress()
                        };
                        dataList.add(record);
                    }
                    // 写入多行数据
                    writer.writeAll(dataList);

                    System.out.println("数据追加成功！");

                } catch (IOException e) {
                    System.err.println("追加数据时出错: " + e.getMessage());
                    e.printStackTrace();
                }
                page++;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }




    /**
     * 示例数据类
     */
    public static class DemoData {
        private String name;
        private Integer age;
        private String address;

        // getter/setter 方法
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public Integer getAge() { return age; }
        public void setAge(Integer age) { this.age = age; }
        public String getAddress() { return address; }
        public void setAddress(String address) { this.address = address; }
    }
}