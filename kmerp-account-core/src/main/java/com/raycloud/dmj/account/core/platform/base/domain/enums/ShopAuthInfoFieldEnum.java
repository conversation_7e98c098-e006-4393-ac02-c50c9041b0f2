package com.raycloud.dmj.account.core.platform.base.domain.enums;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import lombok.Getter;

@Getter
public enum ShopAuthInfoFieldEnum {

    ID("id", "自增主键"),
    SHOP_ID("shop_id", "店铺ID"),
    COMPANY_ID("company_id", "公司ID"),
    PLATFORM_CODE("platform_code", "授权平台id"),
    AUTH_STATUS("auth_status", "授权状态"),
    TOKEN("token", "授权凭证"),
    EXTRA_DATA("extra_data", "额外的授权信息"),
    EXPIRATION_TIME("expiration_time", "授权过期时间"),
    CREATED("created", "创建时间"),
    MODIFIED("modified", "修改时间");

    private final String fieldCode;
    private final String fieldDesc;

    ShopAuthInfoFieldEnum(String fieldCode, String fieldDesc) {
        this.fieldCode = fieldCode;
        this.fieldDesc = fieldDesc;
    }

    public static Set<String> getInsertFields() {
        List<ShopAuthInfoFieldEnum> filterFields = Arrays.asList(ID, CREATED, MODIFIED);
        return Arrays.stream(values())
                .filter(f -> !filterFields.contains(f))
                .map(f -> f.fieldCode)
                .collect(Collectors.toSet());
    }
}