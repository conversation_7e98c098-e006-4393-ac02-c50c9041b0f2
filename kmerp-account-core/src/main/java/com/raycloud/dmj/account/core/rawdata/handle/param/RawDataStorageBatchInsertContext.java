package com.raycloud.dmj.account.core.rawdata.handle.param;

import com.raycloud.dmj.account.core.enums.RawDataSourceEnum;
import com.raycloud.dmj.account.core.rawdata.domains.FileSheetCountInfo;
import com.raycloud.dmj.account.core.rawdata.domains.TableSourceConfig;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 原始数据批量入库上下文
 *
 * <AUTHOR>
 */
@Data
public class RawDataStorageBatchInsertContext {


    /**
     * 公司ID
     */
    private Long companyId;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 同一批文件一组
     */
    private String groupCode;

    /**
     * 基础表名
     */
    private String basicTableName;

    /**
     * 表字段
     */
    Set<String> tableFields;

    /**
     * 表维度的数据
     */
    private List<Map<String, Object>> dataList;

    /**
     * 批次号字段
     */
    private String batchField;

    /**
     * 时间类型
     */
    private Integer dateType;


    /**
     * 需要导入的时间范围-开始时间
     */
    private  Date needStartTime;

    /**
     * 需要导入的时间范围-结束时间
     */
    private Date needEndTime;


    /**
     * sheet索引
     */
    private Integer sheetIndex;


    /**
     * 表数据源配置
     */
    private TableSourceConfig tableSourceConfig;

    /**
     * 监控数据信息
     */
    private MonitorDataInfo monitorDataInfo;

    /**
     * 处理过的批次号上下文
     */
   private Set<String> handleBatchNoList;

    /**
     * 跳过的批次号
     */
    private Set<String> skipBatchNoList;

    /**
     * 文件sheet解析统计数据
     */
    private FileSheetCountInfo fileSheetCountInfo;

    /**
     * 数据来源
     * @see RawDataSourceEnum
     */
    private Integer dataSource;





}
