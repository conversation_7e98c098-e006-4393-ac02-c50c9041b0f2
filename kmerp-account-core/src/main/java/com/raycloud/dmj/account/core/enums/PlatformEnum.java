package com.raycloud.dmj.account.core.enums;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public enum PlatformEnum {

    TM("TM", "天猫","tm"),
    TB("TB", "淘宝","tb");
    /**
     * 平台编码
     */
    private String code;
    /**
     * 平台名称
     */
    private String title;
    /**
     * ERP平台编码
     */
    private String erpCode;

    PlatformEnum(String code, String title, String erpCode) {
        this.code = code;
        this.title = title;
        this.erpCode = erpCode;
    }

    public String getCode() {
        return code;
    }
    public String getTitle() {
        return title;
    }
    public String getErpCode() {
        return erpCode;
    }

    public static PlatformEnum getPlatformEnumByCode(String code) {
        for (PlatformEnum platformEnum : PlatformEnum.values()) {
            if (platformEnum.getCode().equals(code)) {
                return platformEnum;
            }
        }
        return null;
    }

    public static PlatformEnum getPlatformEnumByErpCode(String erpCode) {
        for (PlatformEnum platformEnum : PlatformEnum.values()) {
            if (platformEnum.getErpCode().equals(erpCode)) {
                return platformEnum;
            }
        }
        return null;
    }

    public static List<String> getErpCodeList() {
        List<String> erpCode = new ArrayList<>();
        for (PlatformEnum platformEnum : PlatformEnum.values()) {
            erpCode.add(platformEnum.getErpCode());
        }
        return erpCode;
    }

    public static List<Map<String,Object>> getPlatformCodeList() {
        List<Map<String,Object>> erpCode = new ArrayList<>();
        for (PlatformEnum platformEnum : PlatformEnum.values()) {
            Map<String,Object> map = new HashMap<>();
            map.put("code",platformEnum.getCode());
            map.put("title",platformEnum.getTitle());
            erpCode.add(map);
        }
        return erpCode;
    }
}
