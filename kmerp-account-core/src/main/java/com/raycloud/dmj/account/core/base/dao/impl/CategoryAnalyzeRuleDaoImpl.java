package com.raycloud.dmj.account.core.base.dao.impl;


import com.alibaba.fastjson2.JSON;
import com.mysql.jdbc.Statement;
import com.raycloud.dmj.account.core.base.dao.CategoryAnalyzeRuleDao;
import com.raycloud.dmj.account.core.cleancategory.domain.object.CategoryAnalyzeRuleDO;
import com.raycloud.dmj.account.core.common.Page;
import com.raycloud.dmj.account.core.enums.YesOrNoEnum;
import com.raycloud.dmj.account.core.enums.field.CategoryAnalyzeRuleFieldEnum;
import com.raycloud.dmj.account.infra.common.BusinessException;
import com.raycloud.dmj.account.infra.common.ErrorCodeEnum;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import com.raycloud.dmj.table.api.plus.common.ColumnValues;
import com.raycloud.dmj.table.api.plus.common.base.SQL;
import com.raycloud.dmj.table.api.plus.common.enums.LinkMode;
import com.raycloud.dmj.table.api.plus.component.column.utils.Columns;
import com.raycloud.dmj.table.api.plus.component.condition.utils.Conditions;
import com.raycloud.dmj.table.api.plus.component.order.utils.Orders;
import com.raycloud.dmj.table.api.plus.insert.Inserts;
import com.raycloud.dmj.table.api.plus.query.Queries;
import com.raycloud.dmj.table.api.plus.update.Updates;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
@Slf4j
public class CategoryAnalyzeRuleDaoImpl extends BaseDao implements CategoryAnalyzeRuleDao {

    private final String TABLE_NAME = "category_analyze_rule";

    @Override
    public Long insert(CategoryAnalyzeRuleDO analyzeRuleDO) {
        AsserUtils.notNull(analyzeRuleDO, "参数不能为空！");
        SQL sql = Inserts.insert()
                .into(TABLE_NAME)
                .columns(CategoryAnalyzeRuleFieldEnum.getInsertFields())
                .valueForEntity(analyzeRuleDO)
                .columnNameCamelToUnderline()
                .toSql();

        // 创建KeyHolder用于获取生成的主键
        KeyHolder keyHolder = new GeneratedKeyHolder();
        // 使用带KeyHolder的update方法
        jdbcTemplate.update(
                connection -> {
                    PreparedStatement ps = connection.prepareStatement(
                            sql.getSqlCode(),
                            Statement.RETURN_GENERATED_KEYS
                    );
                    // 设置参数
                    Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
                    for (int i = 0; i < args.length; i++) {
                        ps.setObject(i + 1, args[i]);
                    }
                    return ps;
                },
                keyHolder
        );
        // 获取生成的主键值
        return keyHolder.getKey().longValue();

    }

    @Override
    public CategoryAnalyzeRuleDO queryById(Long companyId, Long id) {
        AsserUtils.notNull(companyId, "公司ID不能为空！");
        AsserUtils.notNull(id, "规则ID不能为空！");
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(CategoryAnalyzeRuleFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId),
                        Conditions.and(Columns.toColumn(CategoryAnalyzeRuleFieldEnum.ID.getFieldCode()), LinkMode.EQUAL, id),
                        Conditions.and(Columns.toColumn(CategoryAnalyzeRuleFieldEnum.DELETED.getFieldCode()), LinkMode.EQUAL, YesOrNoEnum.NO.getBoolean())
                )
                .select(
                ).toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<CategoryAnalyzeRuleDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(CategoryAnalyzeRuleDO.class), args);
        return !query.isEmpty() ? query.get(0) : null;
    }

    @Override
    public void logicDelete(Long companyId, List<Long> ids) {
        AsserUtils.notNull(companyId, "公司ID不能为空！");
        AsserUtils.notEmpty(ids, "规则ID不能为空！");
        // 2. 生成SQL
        SQL sql = Updates.create()
                .table(TABLE_NAME)
                .where(
                        Conditions.and(CategoryAnalyzeRuleFieldEnum.ID.getFieldCode(), LinkMode.IN,ids),
                        Conditions.and(CategoryAnalyzeRuleFieldEnum.COMPANY_ID.getFieldCode(), LinkMode.EQUAL, companyId)
                )
                .update(
                        ColumnValues.create(CategoryAnalyzeRuleFieldEnum.DELETED.getFieldCode(), YesOrNoEnum.YES.getValue())
                ).toSql();
        Object[] updateArgs = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        // 3. 执行更新操作
        int update = jdbcTemplate.update(sql.getSqlCode(), updateArgs);
        if (update <= 0){
            log.error("|FileOriginalDataMonitorDao.updateById error|更新数据库失败！sql={},updateArgs={}",sql.getSqlCode(), JSON.toJSONString(sql.getArgs()));
            throw new BusinessException(ErrorCodeEnum.DB_ERROR);
        }
    }

    @Override
    public void logicDeleteBySubCategoryId(Long companyId, Long subCategoryId) {
        AsserUtils.notNull(companyId, "公司ID不能为空！");
        AsserUtils.notNull(subCategoryId, "子类别ID不能为空！");
        // 2. 生成SQL
        SQL sql = Updates.create()
                .table(TABLE_NAME)
                .where(
                        Conditions.and(CategoryAnalyzeRuleFieldEnum.SUB_CATEGORY_ID.getFieldCode(), LinkMode.EQUAL,subCategoryId),
                        Conditions.and(CategoryAnalyzeRuleFieldEnum.COMPANY_ID.getFieldCode(), LinkMode.EQUAL, companyId),
                        Conditions.and(CategoryAnalyzeRuleFieldEnum.DELETED.getFieldCode(), LinkMode.EQUAL, YesOrNoEnum.NO.getValue())
                )
                .update(
                        ColumnValues.create(CategoryAnalyzeRuleFieldEnum.DELETED.getFieldCode(), YesOrNoEnum.YES.getValue())
                ).toSql();
        Object[] updateArgs = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        // 3. 执行更新操作
        int update = jdbcTemplate.update(sql.getSqlCode(), updateArgs);
        if (update <= 0){
            log.error("|FileOriginalDataMonitorDao.logicDeleteBySubCategoryId error|更新数据库失败！sql={},updateArgs={}",sql.getSqlCode(), JSON.toJSONString(sql.getArgs()));
            throw new BusinessException(ErrorCodeEnum.DB_ERROR);
        }
    }

    @Override
    public Long countBySubCategoryIds(Long companyId,List<Long> subCategoryIds) {
        AsserUtils.notNull(companyId, "公司ID不能为空！");
        AsserUtils.notEmpty(subCategoryIds, "子类别ID列表不能为空！");
        // 组装查询条件
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(CategoryAnalyzeRuleFieldEnum.COMPANY_ID.getFieldCode(), LinkMode.EQUAL, companyId),
                        Conditions.and(CategoryAnalyzeRuleFieldEnum.SUB_CATEGORY_ID.getFieldCode(), LinkMode.IN,subCategoryIds),
                        Conditions.and(CategoryAnalyzeRuleFieldEnum.DELETED.getFieldCode(), LinkMode.IN,YesOrNoEnum.NO.getValue())
                )
                .select(
                        Columns.create("count(*)")
                )
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        return jdbcTemplate.queryForObject(sql.getSqlCode(), Long.class,args);
    }

    @Override
    public   List<CategoryAnalyzeRuleDO> pageQueryBySubCategoryIds(Long companyId, List<Long> subCategoryIds, Page page) {
        AsserUtils.notNull(companyId, "公司ID不能为空！");
        AsserUtils.notEmpty(subCategoryIds, "子类别ID列表不能为空！");
        AsserUtils.notNull(page, "分页参数不能为空！");
        // 组装查询条件
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(CategoryAnalyzeRuleFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId),
                        Conditions.and(CategoryAnalyzeRuleFieldEnum.SUB_CATEGORY_ID.getFieldCode(), LinkMode.IN,subCategoryIds),
                        Conditions.and(CategoryAnalyzeRuleFieldEnum.DELETED.getFieldCode(), LinkMode.IN,YesOrNoEnum.NO.getValue())
                )
                .orderBy(
                        Orders.desc(CategoryAnalyzeRuleFieldEnum.MODIFIED.getFieldCode())
                )
                .select(
                )
                .page(page.getPageNo(), page.getPageSize())
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<CategoryAnalyzeRuleDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(CategoryAnalyzeRuleDO.class), args);
        return !query.isEmpty() ? query : null;

    }

    @Override
    public List<CategoryAnalyzeRuleDO> listBySubCategoryIds(Long companyId, List<Long> subCategoryIds) {
        AsserUtils.notNull(companyId,"公司ID不能为空！");
        AsserUtils.notEmpty(subCategoryIds,"子类别ID列表不能为空！");
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(CategoryAnalyzeRuleFieldEnum.SUB_CATEGORY_ID.getFieldCode(), LinkMode.IN,subCategoryIds),
                        Conditions.and(CategoryAnalyzeRuleFieldEnum.DELETED.getFieldCode(), LinkMode.EQUAL,YesOrNoEnum.NO.getValue()),
                        Conditions.and(CategoryAnalyzeRuleFieldEnum.ENABLE_STATUS.getFieldCode(), LinkMode.EQUAL,YesOrNoEnum.YES.getValue())
                )
                .orderBy(
                        Orders.desc(CategoryAnalyzeRuleFieldEnum.MODIFIED.getFieldCode())
                )
                .select(
                )
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<CategoryAnalyzeRuleDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(CategoryAnalyzeRuleDO.class), args);
        return !query.isEmpty() ? query : null;
    }
}
