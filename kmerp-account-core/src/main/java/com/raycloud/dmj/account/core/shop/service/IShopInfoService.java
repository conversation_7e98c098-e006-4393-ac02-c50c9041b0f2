package com.raycloud.dmj.account.core.shop.service;

import com.raycloud.dmj.account.core.bill.request.StandardFundBillFlowRequest;
import com.raycloud.dmj.account.core.common.PageInfo;
import com.raycloud.dmj.account.core.shop.req.*;
import com.raycloud.dmj.account.core.shop.vo.ErpShopVO;
import com.raycloud.dmj.account.core.shop.vo.ShopInfoVO;
import com.raycloud.dmj.account.core.shop.vo.SimpleShopInfoVO;
import com.raycloud.dmj.account.infra.session.AccountUser;
import com.raycloud.dmj.domain.account.Staff;

import java.util.List;

public interface IShopInfoService {

    /**
     * 获取店铺列表
     * @return 店铺列表
     */
    List<ShopInfoVO> getShopList(AccountUser accountUser, ShopInfoRequest request);

    /**
     * 新增店铺信息
     * @param request 店铺信息
     * @return 新增的ID
     */
    Long addShopInfo(AccountUser accountUser, AddShopInfoRequest request);

    /**
     * 获取ERP店铺列表
     * @return 店铺列表
     */
    List<ErpShopVO> getERPShopList(Staff staff);

    /**
     * 修改店铺期初余额
     * @param accountUser
     * @param request
     * @return
     */
    Long updateShopAmount(AccountUser accountUser, ShopUpdateAmountRequest request);

    /**
     * 修改店铺对帐状态
     * @param accountUser
     * @param request
     * @return
     */
    void  updateShopAmountState(AccountUser accountUser, ShopInfoUpdateStateRequest request);

    /**
     * 批量修改店铺归属公司
     * @param accountUser
     * @param request
     */
    void updateShopCompany(AccountUser accountUser, ShopInfoUpdateAffiliatedCompanyRequest request);

    /**
     * 获取简单的店铺列表
     * @param accountUser 用户信息
     * @param platformCodes 平台编码集合
     * @return
     */
    List<SimpleShopInfoVO> getSimpleShopList(AccountUser accountUser,String platformCodes);

    /**
     * 根据条件查询分页统计信息
     * @param accountUser
     * @param request
     * @return
     */
    PageInfo<Object> getPageInfo(AccountUser accountUser, ShopInfoRequest request);
}
