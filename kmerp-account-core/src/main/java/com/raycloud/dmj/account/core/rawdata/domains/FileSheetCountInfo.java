package com.raycloud.dmj.account.core.rawdata.domains;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

@Data
public class FileSheetCountInfo {
    /**
     * sheet名称
     */
    private String sheetName;
    /**
     * sheet索引
     */
    private Integer sheetIndex;
    /**
     * 解析的总数据
     */
    private AtomicInteger totalCount= new AtomicInteger(0);
    /**
     * 失败的数据
     */
    private AtomicInteger failCount= new AtomicInteger(0);
    /**
     * 成功的数据
     */
    private AtomicInteger successCount= new AtomicInteger(0);
    /**
     * 过滤的数据
     */
    private AtomicInteger filterCount = new AtomicInteger(0);
    /**
     * 文件解析过滤数据
     */
    private AtomicInteger innerFilterCount = new AtomicInteger(0);
    /**
     * table
     */
    private Map<String, TableCountInfo> tableCountInfoList=new HashMap<>();
}
