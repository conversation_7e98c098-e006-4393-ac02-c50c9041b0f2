package com.raycloud.dmj.account.core.platform.service.impl;

import com.raycloud.dmj.account.core.platform.base.domain.ShopAuthInfoDO;
import com.raycloud.dmj.account.core.platform.dao.ShopAuthInfoDao;
import com.raycloud.dmj.account.core.platform.service.IShopAuthInfoService;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.time.LocalDateTime;

@Service
public class ShopAuthInfoServiceImpl implements IShopAuthInfoService {

    @Resource
    private ShopAuthInfoDao shopAuthInfoDao;

    @Override
    public void insertOrUpdateShopAuthInfo(ShopAuthInfoDO shopAuthInfoDO) {
        shopAuthInfoDao.insertOrUpdate(shopAuthInfoDO);
    }

    @Override
    public ShopAuthInfoDO getShopAuthInfoByShopIdAndPlatformCode(Long shopId, Long companyId,String platformCode) {
        return shopAuthInfoDao.selectByShopIdCompanyIdPlatform(shopId, companyId, platformCode);
    }

}