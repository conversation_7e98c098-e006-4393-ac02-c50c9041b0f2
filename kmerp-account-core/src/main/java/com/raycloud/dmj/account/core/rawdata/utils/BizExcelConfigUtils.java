package com.raycloud.dmj.account.core.rawdata.utils;

import com.google.gson.Gson;
import com.raycloud.dmj.account.core.rawdata.domains.TableSourceConfig;
import com.raycloud.readexcel.exception.ExcelConfigException;

public class BizExcelConfigUtils {


    /**
     * 解析多配置
     * @param config
     * @return
     */
    public static TableSourceConfig parseExcelMultipleHeadConfig(String config) {
        try{
            Gson gson = new Gson();
            TableSourceConfig tableSourceConfig = gson.fromJson(config, TableSourceConfig.class);
            return tableSourceConfig;
        }catch (Exception e){
            throw new ExcelConfigException("tableSourceConfig解析异常",e);
        }
    }



}
