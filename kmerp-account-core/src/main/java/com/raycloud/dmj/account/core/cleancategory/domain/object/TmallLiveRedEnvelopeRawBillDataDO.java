package com.raycloud.dmj.account.core.cleancategory.domain.object;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 直播红包明细
 * <AUTHOR>
 */
@Data
public class TmallLiveRedEnvelopeRawBillDataDO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 淘宝主订单号
     */
    private String taobaoMainOrderNo;

    /**
     * 退款操作单号
     */
    private String refundOperationNo;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 主播昵称
     */
    private String anchorNickname;

    /**
     * 充值账号昵称
     */
    private String rechargeAccountNickname;

    /**
     * 订单创建时间
     */
    private Date orderCreateTime;

    /**
     * 订单支付时间
     */
    private Date orderPayTime;

    /**
     * 订单确认收货时间
     */
    private Date orderConfirmReceiveTime;

    /**
     * 订单退款时间
     */
    private Date orderRefundTime;

    /**
     * 订单流水类型
     */
    private String orderFlowType;

    /**
     * 订单退款类型
     */
    private String orderRefundType;

    /**
     * 红包类型
     */
    private String redEnvelopeType;

    /**
     * 红包流水金额
     */
    private BigDecimal redEnvelopeFlowAmount;

    /**
     * 收支类型
     */
    private String incomeExpenseType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 唯一键
     */
    private String bizKey;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 批次时间 格式如********
     */
    private Integer batchTime;

    /**
     * 租户ID
     */
    private Long companyId;

    /**
     * 店铺ID
     */
    private String shopId;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 与汇总表关联字段
     */
    private String serialNo;
}