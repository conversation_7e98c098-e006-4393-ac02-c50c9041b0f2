package com.raycloud.dmj.account.core.cleancategory.domain.object;

import com.raycloud.dmj.account.core.cleancategory.domain.AnalyzeRuleInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 分类解析规则表DO
 * <AUTHOR>
 */
@Data
public class CategoryAnalyzeRuleDO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 修改时间
     */
    private Date modified;

    /**
     * 规则条件（JSON格式）
     * List<List<AnalyzeRuleInfo>>
     * @see AnalyzeRuleInfo
     */
    private String ruleCondition;

    /**
     * 是否可用，1-可用，0-不可用
     */
    private Boolean enableStatus;

    /**
     * 子类别ID
     */
    private Long subCategoryId;


    /**
     * 公司ID
     */
    private Long companyId;

    /**
     * 是否删除，1-已删除，0-未删除
     */
    private Boolean deleted;

}