package com.raycloud.dmj.account.core.cleancategory.domain.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * 分类表VO
 * <AUTHOR>
 */

@Data
public class CategoryGroupVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 分类编码
     */
    private String categoryGroupCode;

    /**
     * 分类名称
     */
    private String categoryGroupName;

    /**
     * 来源（1-系统，2-手动）
     */
    private String typeName;



}