package com.raycloud.dmj.account.core.rawdata.utils.oss;

import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.internal.Mimetypes;
import com.aliyun.oss.model.*;
import com.raycloud.dmj.account.core.common.constant.OssObjectNameType;
import com.raycloud.dmj.account.core.enums.FileTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;

import java.io.*;
import java.net.URL;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;

import static com.raycloud.dmj.account.core.rawdata.utils.oss.OSSClientHelper.closeOSSClient;

@Slf4j
public class OssUtils {

    // 官方文档 https://help.aliyun.com/zh/oss/developer-reference/sdk-code-samples/?spm=a2c4g.********.help-menu-31815.d_5_2.2663a65f0KM8EL
    public static void chunkedUpload(String filePath, String objectName) {
        OSSClient ossClient = null;
        try {
            ossClient = OSSClientHelper.getOSSClient();
            String bucketName = OSSClientHelper.getBucket();
            // 创建InitiateMultipartUploadRequest对象。
            InitiateMultipartUploadRequest request = new InitiateMultipartUploadRequest(bucketName, objectName);

            // 创建ObjectMetadata并设置Content-Type。
            ObjectMetadata metadata = new ObjectMetadata();
            if (metadata.getContentType() == null) {
                metadata.setContentType(Mimetypes.getInstance().getMimetype(new File(filePath), objectName));
            }
            System.out.println("Content-Type: " + metadata.getContentType());

            // 将metadata绑定到上传请求中。
            request.setObjectMetadata(metadata);

            // 初始化分片。
            InitiateMultipartUploadResult upresult = ossClient.initiateMultipartUpload(request);
            // 返回uploadId。
            String uploadId = upresult.getUploadId();

            // partETags是PartETag的集合。PartETag由分片的ETag和分片号组成。
            List<PartETag> partETags = new ArrayList<PartETag>();
            // 每个分片的大小，用于计算文件有多少个分片。单位为字节。
            // 分片最小值为100 KB，最大值为5 GB。最后一个分片的大小允许小于100 KB。
            // 设置分片大小为 1 MB。
            final long partSize = 1 * 1024 * 1024L;

            // 根据上传的数据大小计算分片数。以本地文件为例，说明如何通过File.length()获取上传数据的大小。
            final File sampleFile = new File(filePath);
            long fileLength = sampleFile.length();
            int partCount = (int) (fileLength / partSize);
            if (fileLength % partSize != 0) {
                partCount++;
            }
            // 遍历分片上传。
            for (int i = 0; i < partCount; i++) {
                long startPos = i * partSize;
                long curPartSize = (i + 1 == partCount) ? (fileLength - startPos) : partSize;
                UploadPartRequest uploadPartRequest = new UploadPartRequest();
                uploadPartRequest.setBucketName(bucketName);
                uploadPartRequest.setKey(objectName);
                uploadPartRequest.setUploadId(uploadId);
                // 设置上传的分片流。
                // 以本地文件为例说明如何创建FileInputStream，并通过InputStream.skip()方法跳过指定数据。
                InputStream instream = null;
                try {
                    instream = new FileInputStream(sampleFile);
                    instream.skip(startPos);
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
                uploadPartRequest.setInputStream(instream);
                // 设置分片大小。
                uploadPartRequest.setPartSize(curPartSize);
                // 设置分片号。每一个上传的分片都有一个分片号，取值范围是1~10000，如果超出此范围，OSS将返回InvalidArgument错误码。
                uploadPartRequest.setPartNumber(i + 1);
                // 每个分片不需要按顺序上传，甚至可以在不同客户端上传，OSS会按照分片号排序组成完整的文件。
                UploadPartResult uploadPartResult = ossClient.uploadPart(uploadPartRequest);
                // 每次上传分片之后，OSS的返回结果包含PartETag。PartETag将被保存在partETags中。
                partETags.add(uploadPartResult.getPartETag());

                // 关闭流
                try {
                    instream.close();
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }

            // 创建CompleteMultipartUploadRequest对象。
            // 在执行完成分片上传操作时，需要提供所有有效的partETags。OSS收到提交的partETags后，会逐一验证每个分片的有效性。当所有的数据分片验证通过后，OSS将把这些分片组合成一个完整的文件。
            CompleteMultipartUploadRequest completeMultipartUploadRequest =
                    new CompleteMultipartUploadRequest(bucketName, objectName, uploadId, partETags);

            // 完成分片上传。
            CompleteMultipartUploadResult completeMultipartUploadResult = ossClient.completeMultipartUpload(completeMultipartUploadRequest);
            System.out.println("上传成功，ETag：" + completeMultipartUploadResult.getETag());

        } catch (OSSException oe) {
            System.out.println("Caught an OSSException, which means your request made it to OSS, "
                    + "but was rejected with an error response for some reason.");
            System.out.println("Error Message:" + oe.getErrorMessage());
            System.out.println("Error Code:" + oe.getErrorCode());
            System.out.println("Request ID:" + oe.getRequestId());
            System.out.println("Host ID:" + oe.getHostId());
        } catch (ClientException ce) {
            System.out.println("Caught a ClientException, which means the client encountered "
                    + "a serious internal problem while trying to communicate with OSS, "
                    + "such as not being able to access the network.");
            System.out.println("Error Message:" + ce.getMessage());
        } finally {
            closeOSSClient();
            ossClient.shutdown();
        }
    }

    public static String getOriginalImportBizErrorObjectName(String fileName) {
        return OssObjectNameType.OSS_SYSTEM + OssObjectNameType.ORIGINAL_IMPORT_BIZ_ERROR + fileName + "." + FileTypeEnum.CSV.getFileType();
    }

    public static String getOriginalImportObjectName(String fileName) {
        return OssObjectNameType.OSS_SYSTEM + OssObjectNameType.ORIGINAL_IMPORT + fileName;
    }

    public static String getSharedDataObjectName(String fileName) {
        return OssObjectNameType.OSS_SYSTEM + OssObjectNameType.SHARED_DATA + fileName;
    }

    public static void uploadByUrl(String url, String objectName){
        InputStream inputStream = null;
        try {
            inputStream = new URL(url).openStream();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        upload(objectName, inputStream);
    }

    public static void uploadByFile(File uploadFile, String objectName, boolean isDelete){
        InputStream inputStream = null;
        try {
            inputStream = Files.newInputStream(uploadFile.toPath());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        upload(objectName, inputStream);

        if (isDelete) {
            FileUtils.deleteQuietly(uploadFile);
        }
    }

    public static void upload(String objectName, InputStream inputStream) {
        OSSClient ossClient = null;
        try {
            ossClient = OSSClientHelper.getOSSClient();
            String bucketName = OSSClientHelper.getBucket();
            // 创建PutObjectRequest对象。
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, objectName, inputStream);
            // 如果需要上传时设置存储类型和访问权限，请参考以下示例代码。
            // ObjectMetadata metadata = new ObjectMetadata();
            // metadata.setHeader(OSSHeaders.OSS_STORAGE_CLASS, StorageClass.Standard.toString());
            // metadata.setObjectAcl(CannedAccessControlList.Private);
            // putObjectRequest.setMetadata(metadata);

            // 上传文件。
             ossClient.putObject(putObjectRequest);
        } catch (OSSException oe) {
            log.error("Caught an OSSException, which means your request made it to OSS, but was rejected with an error response for some reason. " +
                    "Error Message: {}, Error Code: {}, Request ID: {}, Host ID: {}",
                    oe.getErrorMessage(), oe.getErrorCode(), oe.getRequestId(), oe.getHostId());
        } catch (ClientException ce) {
            log.error("Caught a ClientException, which means the client encountered a serious internal problem while trying to communicate with OSS, " +
                    "such as not being able to access the network. Error Message: {}", ce.getMessage());

            System.out.println("Error Message:" + ce.getMessage());
        } finally {
            closeOSSClient();
            if (ossClient != null) {
                ossClient.shutdown();
            }
        }
    }

   public static void downloadObject(String objectName, String pathName){
       OSSClient ossClient = null;
       try {
           ossClient = OSSClientHelper.getOSSClient();
           String bucketName = OSSClientHelper.getBucket();
           ossClient.getObject(new GetObjectRequest(bucketName, objectName), new File(pathName));
           System.out.println("File Download Successfully: " + pathName);
       }catch (Exception ce){
            throw new RuntimeException("");
       }
   }



    public static String getObjectName(String objectName) {
        return OssObjectNameType.OSS_SYSTEM + objectName;
    }

    public static void main(String[] args) {
//        chunkedUpload("account_checking_system/original_import_biz_error/导入文件错误日志_82_09de6076-23d7-4d11-92f7-50594fb810bb.csv", "test/test2.csv");
        String test = OSSClientHelper.expireUrl("account_checking_system/original_import_biz_error/导入文件错误日志_7898_5adddf5c-28df-413a-ba35-62204410d091.csv");
        System.out.println(test);
    }
}
