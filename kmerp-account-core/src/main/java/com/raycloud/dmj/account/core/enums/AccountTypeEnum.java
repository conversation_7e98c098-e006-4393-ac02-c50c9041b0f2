package com.raycloud.dmj.account.core.enums;

import lombok.Getter;

/**
 * 资金账户类型枚举
 * 1-系统支付宝、2-系统微信
 * <AUTHOR>
 */
@Getter
public enum AccountTypeEnum {

    TM_ALIPAY(1, "天猫支付宝", PlatformEnum.TM,AccountCategoryEnum.SHOP,AccountBalanceTypeEnum.WRITE_FLOW ),
    TM_WECHAT(2, "天猫微信", PlatformEnum.TM,AccountCategoryEnum.SHOP, AccountBalanceTypeEnum.WRITE_FLOW),
    TM_GUARANTEE(3, "天猫保证金", PlatformEnum.TM,AccountCategoryEnum.SHOP, AccountBalanceTypeEnum.WRITE_FLOW),
    TM_OTHER(4, "天猫保证金", PlatformEnum.TM,AccountCategoryEnum.SHOP, AccountBalanceTypeEnum.WRITE_FLOW),


    OFFLINE(9998, "线下账户", null,AccountCategoryEnum.OFFLINE, AccountBalanceTypeEnum.MANUAL_WRITE),
    CUSTOM_ALIPAY(9999, "自定义支付宝", null,AccountCategoryEnum.CUSTOM_ALIPAY, AccountBalanceTypeEnum.MANUAL_WRITE),
    ;


    /**
     * 类型编码
     */
    private final Integer typeCode;


    /**
     * 类型描述
     */
    private final String typeDesc;
    /**
     * 平台
     */
    private final PlatformEnum platform;

    /**
     * 类别
     */
    private final AccountCategoryEnum category;

    /**
     * 余额类型
     */
    private final AccountBalanceTypeEnum balanceType;



    AccountTypeEnum(Integer typeCode, String typeDesc, PlatformEnum platform, AccountCategoryEnum category, AccountBalanceTypeEnum balanceType) {
        this.typeCode = typeCode;
        this.typeDesc = typeDesc;
        this.platform = platform;
        this.category = category;
        this.balanceType = balanceType;
    }

    /**
     * 根据类型编码获取枚举实例，方便从数据库值转换为枚举
     * @param typeCode 类型编码
     * @return 对应的枚举实例，若未匹配到则返回 null
     */
    public static AccountTypeEnum getByTypeCode(Integer typeCode) {
        for (AccountTypeEnum enumObj : values()) {
            if (enumObj.getTypeCode().equals(typeCode)) {
                return enumObj;
            }
        }
        return null;
    }
}