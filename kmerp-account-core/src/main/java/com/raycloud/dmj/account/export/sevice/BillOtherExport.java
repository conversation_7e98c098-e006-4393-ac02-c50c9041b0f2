package com.raycloud.dmj.account.export.sevice;

import com.raycloud.dmj.account.core.base.dao.StandardOtherBillFlowInfoDao;
import com.raycloud.dmj.account.core.bill.parameter.StandardOtherBillFlowParameter;
import com.raycloud.dmj.account.core.common.constant.Constant;
import com.raycloud.dmj.account.core.enums.*;
import com.raycloud.dmj.account.core.pageconfig.service.IPageColumnConfigService;
import com.raycloud.dmj.account.export.core.parameter.ConsignExpressSQLParameter;
import com.raycloud.dmj.account.export.core.processor.BillOtherExportResultProcessor;
import com.raycloud.dmj.account.export.core.statistics.export.VirtualProgressCallback;
import com.raycloud.dmj.account.export.core.statistics.interf.IExport;
import com.raycloud.dmj.account.infra.repository.base.DbQuery;
import com.raycloud.dmj.data.chessboard.listener.ExportContext;
import com.raycloud.dmj.data.export.core.MultiDataWriter;
import com.raycloud.dmj.data.export.imp.easyexcel.StyleMultiDataWriter;
import com.raycloud.dmj.data.export.utils.DataWriters;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.services.utils.ClueIdUtil;
import com.raycloud.dmj.table.api.plus.common.base.SQL;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Slf4j
@Component
@RequiredArgsConstructor
public class BillOtherExport implements IExport<StandardOtherBillFlowParameter> {

    private final DbQuery dbQuery;

    private final StandardOtherBillFlowInfoDao standardOtherBillFlowInfoDao;

    private final IPageColumnConfigService pageColumnConfigService;


    @Override
    public void doExport(Staff staff, StandardOtherBillFlowParameter parameter, MultiDataWriter writer) throws Exception {
        // 默认为支持样式的excel写入器
        final StyleMultiDataWriter dataWriter = DataWriters.convertToStyleMultiDataWriter(writer);
//        // 参数转换
        ConsignExpressSQLParameter sqlParameter = new ConsignExpressSQLParameter();
        sqlParameter.setStaff(staff);
        sqlParameter.setClueId(ClueIdUtil.getClueId());
        sqlParameter.setPageId(parameter.getPageId());
        sqlParameter.setEffectiveColumnNames(pageColumnConfigService.getEffectiveColumnNames(staff.getCompanyId(), parameter.getPageId()));

        // 构建SQL
        SQL sql = standardOtherBillFlowInfoDao.getExportSQL(parameter, staff.getCompanyId());
        // 创建结果处理器
        BillOtherExportResultProcessor processor = new BillOtherExportResultProcessor();
        // 处理器初始化
        processor.start(staff, sqlParameter);

        try {
            //TODO 这里数据库的connectorId目前先写死
            dbQuery.cursor(Constant.ORIGINAL_DATA_BASE_CONNECTION_KEY, sql, new VirtualProgressCallback() {
                @Override
                public void doAccept(ExportContext context, List<Map<String, Object>> datas) throws Exception {
                    //对数据进行转化
                    convertList(datas, sqlParameter);
                    // 处理数据
                    List<Map<String, Object>> maps = processor.handle(staff, sqlParameter, datas);
                    // 写入文件
                    dataWriter.write(maps);
                }
            });
        } finally {
            processor.finished(staff, sqlParameter);
        }
    }

    //数据转化
    private void convertList(List<Map<String, Object>> datas, ConsignExpressSQLParameter sqlParameter) {
        if (datas == null || datas.isEmpty() || sqlParameter == null) {
            return;
        }
        List<String> effectiveColumnNames = sqlParameter.getEffectiveColumnNames();
        if (effectiveColumnNames == null) {
            return;
        }
        datas.forEach(map -> {
            //平台名称
            if (effectiveColumnNames.contains("platform_name")){
                String platformCode = (String) map.get("platform_code");
                if (platformCode != null) {
                    PlatformEnum platformEnum = PlatformEnum.getPlatformEnumByCode(platformCode);
                    if (platformEnum != null) {
                        map.put("platform_name", platformEnum.getTitle());
                    }
                }
            }
            if (effectiveColumnNames.contains("classify_code_name")){
                map.put("classify_code_name", OtherCategoryGroupEnum.getDescByCode((String) map.get("category_group_code")));
            }
            if (effectiveColumnNames.contains("category_name")){
                map.put("category_name", OtherCategoryEnum.getDescByCode((String) map.get("category_code")));
            }
            if (effectiveColumnNames.contains("sub_category_name")){
                map.put("sub_category_name", OtherSubCategoryEnum.getDescByCode((String) map.get("sub_category_code")));
            }
            //收支方向
            if (effectiveColumnNames.contains("income_expense_direction")){
                map.put("income_expense_direction",IncomeExpenseDirectionEnum.getDescBySourceCode((Integer) map.get("income_expense_direction")));
            }
            //TODO 是否收支对象
            if (effectiveColumnNames.contains("counterparty_name")){
                Long counterpartyId = (Long) map.get("counterparty_id");
                if (counterpartyId != null) {
                    map.put("counterparty_name",null);
                }
            }
            //是否关联业务单据
            if (effectiveColumnNames.contains("is_related_doc")){
                Object docNo = map.get("doc_no");
                if (docNo != null) {
                    map.put("is_related_doc", docNo.equals("-1") ? "否" : "是");
                }
            }
            //账单来源
            if (effectiveColumnNames.contains("source")){
                map.put("source", StandardFundBillSourceEnum.getDesByCode((Integer) map.get("source")));
            }
        });
    }
}
