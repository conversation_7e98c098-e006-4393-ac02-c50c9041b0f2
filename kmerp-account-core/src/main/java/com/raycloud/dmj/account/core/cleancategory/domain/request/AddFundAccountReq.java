package com.raycloud.dmj.account.core.cleancategory.domain.request;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 添加资金账户
 * <AUTHOR>
 */
@Data
public class AddFundAccountReq implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 账户编号
     */
    private String accountCode;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 商家编号
     */
    private String shopId;

    /**
     * 账户类型
     */
    private Integer type;

    /**
     * 账户期初余额
     */
    private BigDecimal startBalance;

    /**
     * 账户期初开始时间
     */
    private Date startDate;


}
