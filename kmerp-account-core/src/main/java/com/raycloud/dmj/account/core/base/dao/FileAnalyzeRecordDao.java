package com.raycloud.dmj.account.core.base.dao;

import com.raycloud.dmj.account.core.rawdata.domains.FileAnalyzeRecordDO;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * Date:  2025/6/16
 *
 * <AUTHOR>
 */
public interface FileAnalyzeRecordDao {
    /**
     * 新增文件解析记录表
     */
    Long addFileAnalyzeRecord(FileAnalyzeRecordDO fileAnalyzeRecordDO);

    void updateFileAnalyzeStatusById(Long recordId, Integer status, String errorMsg);

    void updateFileCountInfo(Long recordId, String fileCountInfo);

    /**
     * 查询一直卡在导入中的批次
     * @param analyzeStatus com.raycloud.dmj.account.core.enums.FileAnalyzeStatusEnum#IMPORTING
     * @param time 截止时间
     * @return 批次监控表
     */
    List<FileAnalyzeRecordDO> queryByStatusAndTime(Integer analyzeStatus, Date time);

    /**
     * 根据ID批量更新文件解析记录表状态和错误信息
     * @param recordIds 记录ID
     * @param status  状态
     * @param errorMsg 错误信息
     */
    void batchUpdateStatusById(Set<Long> recordIds, Integer status, String errorMsg);

    void updateMatchingConfigIdsById(Long recordId, String configIds);

    FileAnalyzeRecordDO getById(Long recordId);

    /**
     * 根据组编码查询
     * @param companyId 公司ID
     * @param groupCode 组编码
     * @return 批次列表
     */
    List<FileAnalyzeRecordDO> listByGroupCode(Long companyId,String groupCode);


}
