package com.raycloud.dmj.account.core.rawdata.domains;

import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 文件分析表配置实体类，对应数据库表 file_analyze_table_config
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileAnalyzeTableConfigDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 配置ID，主键自增
     */
    private Long id;

    /**
     * 文件解析表配置id
     */
    private Long configId;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 表数据类型：1明细表，2去重表
     */
    private Integer tableDataType;

    /**
     * 批次号配置
     */
    private String batchNo;

    /**
     * 唯一键配置
     */
    private String uniqueKey;

    /**
     * 最终的数据映射
     */
    private String tableFields;

    /**
     * 表的入库源信息配置
     */
    private String tableSourceConfig;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 修改时间，自动更新
     */
    private Date modified;

    /**
     * 启用状态：0弃用，1正常
     */
    private Integer enableStatus;
}