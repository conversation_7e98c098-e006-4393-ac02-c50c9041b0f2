package com.raycloud.dmj.account.core.enums.field;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 账单核验规则表字段枚举
 * <AUTHOR>
 */
@Getter
public enum BillVerifyRuleFieldEnum {

    ID("id", "主键ID"),
    SHOP_ID("shop_id", "店铺ID"),
    RULE_NAME("rule_name", "核验规则名称"),
    EFFECTIVE_TIME("effective_time", "生效时间"),
    INVALID_TIME("invalid_time", "失效时间"),
    DATE_TYPE("date_type", "时间类型"),
    RULE_CONTENT("rule_content", "核验规则内容"),
    IS_EXCEPTION_TIP("is_exception_tip", "异常是否提示(1是,0否)"),
    STATUS("status", "是否启用(1开启,0关闭)"),
    JUDGMENT_TYPE("judgment_type", "判断方式(1大于,2等于,3小于)"),
    AMOUNT("amount", "核验金额(元)"),
    CREATED("created", "创建时间"),
    MODIFIED("modified", "修改时间"),
    ENABLE_STATUS("enable_status", "启用状态：0弃用，1正常"),
    COMPANY_ID("company_id", "公司ID");

    /**
     * 字段编码
     */
    private final String fieldCode;

    /**
     * 字段描述
     */
    private final String fieldDesc;

    BillVerifyRuleFieldEnum(String fieldCode, String fieldDesc) {
        this.fieldCode = fieldCode;
        this.fieldDesc = fieldDesc;
    }

    /**
     * 获取插入字段
     * @return 插入字段
     */
    public static Set<String> getInsertFields() {
        List<BillVerifyRuleFieldEnum> filterField = Arrays.asList(
                BillVerifyRuleFieldEnum.ID
        );
        return Arrays.stream(BillVerifyRuleFieldEnum.values()).filter(x -> !filterField.contains(x)).map(
                x -> x.fieldCode
        ).collect(Collectors.toSet());
    }

    /**
     * 获取所有查询字段
     * @return 查询字段数组
     */
    public static String[] getSelectFields() {
        return Arrays.stream(BillVerifyRuleFieldEnum.values())
                .map(BillVerifyRuleFieldEnum::getFieldCode)
                .toArray(String[]::new);
    }
}
