package com.raycloud.dmj.account.core.rawdata.domains;

import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 文件分析记录实体类，对应数据库表 file_analyze_record
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileAnalyzeRecordDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 记录ID
     */
    private Long id;

    /**
     * 数据类型
     */
    private String dataType;

    /**
     * 时间类型，默认为1，1为日，2为月，3为年
     */
    private Integer dateType;

    /**
     * 当前文件的数据开始时间
     */
    private Date fileStartDataRange;

    /**
     * 当前文件的数据结束时间
     */
    private Date fileEndDataRange;

    /**
     * 分析状态：1为原始数据入库成功，2为导入中，3为系统级导入失败，4为业务失败
     */
    private Integer analyzeStatus;

    /**
     * 错误信息
     */
    private String errorMsg;

    /**
     * 当前指定的数据开始时间
     */
    private Date assignStartDataRange;

    /**
     * 当前指定的数据结束时间
     */
    private Date assignEndDataRange;

    /**
     * 数据快照，用于保存config的配置
     */
    private String matchingConfigIds;
    /**
     * importData
     */
    private String importData;

    /**
     * 同一批文件一组
     */
    private String groupCode;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 修改时间
     */
    private Date modified;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 租户ID
     */
    private Long companyId;
    /**
     * 店铺id
     */
    private Long shopId;
    /**
     * 文件解析数量信息
     */
    private String fileCountInfo;

    /**
     * 渠道来源
     */
    private Integer channelSource;
}