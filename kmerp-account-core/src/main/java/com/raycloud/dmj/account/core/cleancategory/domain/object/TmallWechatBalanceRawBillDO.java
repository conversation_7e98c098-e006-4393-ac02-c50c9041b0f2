package com.raycloud.dmj.account.core.cleancategory.domain.object;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class TmallWechatBalanceRawBillDO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID，自增
     */
    private Long id;

    /**
     * 记录日期（格式：YYYYMMDD）
     */
    private Integer recordDate;

    /**
     * 明细总笔数
     */
    private Integer transactionCount;

    /**
     * 收入金额（元）
     */
    private BigDecimal incomeAmount;

    /**
     * 收入笔数
     */
    private Integer incomeCount;

    /**
     * 支出金额（元）
     */
    private BigDecimal expenditureAmount;

    /**
     * 支出笔数
     */
    private Integer expenditureCount;

    /**
     * 期末余额（元）
     */
    private BigDecimal endBalance;

    /**
     * 业务唯一键
     */
    private String bizKey;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 批次时间
     */
    private Integer batchTime;

    /**
     * 公司ID
     */
    private Long companyId;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 创建时间
     */
    private Date created;
}
