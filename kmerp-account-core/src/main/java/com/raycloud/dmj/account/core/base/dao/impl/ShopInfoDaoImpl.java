package com.raycloud.dmj.account.core.base.dao.impl;

import com.raycloud.dmj.account.core.base.dao.ShopInfoDao;
import com.raycloud.dmj.account.core.base.domain.ShopInfoDO;
import com.raycloud.dmj.account.core.enums.CommonStatusEnum;
import com.raycloud.dmj.account.core.enums.ShopStateEnum;
import com.raycloud.dmj.account.core.enums.field.ShopInfoFieldEnum;
import com.raycloud.dmj.account.core.shop.req.ShopInfoRequest;
import com.raycloud.dmj.account.core.shop.req.ShopUpdateAmountRequest;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import com.raycloud.dmj.table.api.plus.common.ColumnValue;
import com.raycloud.dmj.table.api.plus.common.base.SQL;
import com.raycloud.dmj.table.api.plus.common.enums.LinkMode;
import com.raycloud.dmj.table.api.plus.component.$;
import com.raycloud.dmj.table.api.plus.component.column.utils.Columns;
import com.raycloud.dmj.table.api.plus.component.condition.ConditionComponent;
import com.raycloud.dmj.table.api.plus.component.condition.utils.Conditions;
import com.raycloud.dmj.table.api.plus.insert.Inserts;
import com.raycloud.dmj.table.api.plus.query.Queries;
import com.raycloud.dmj.table.api.plus.query.api.QueryFrom;
import com.raycloud.dmj.table.api.plus.update.Updates;
import com.raycloud.dmj.table.api.plus.utils.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Repository
public class ShopInfoDaoImpl extends BaseDao implements ShopInfoDao {

    private final String tableName = "shop_info";

    /**
     * 获取店铺列表

     * @return 店铺列表
     */
    @Override
    public List<ShopInfoDO> getShopList(ShopInfoRequest request, Long companyId) {
        // 构建基础查询
        QueryFrom queryBuilder = Queries.create().from(tableName);
        List<ConditionComponent<?>> conditions = getConditionComponents(companyId,request);

        queryBuilder.where(conditions);
        // 构建SQL并执行查询
        SQL sql = queryBuilder.select()
                .page(request.getPageNo(),request.getPageSize())
                .toSql();

        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        return jdbcTemplate.query(
                sql.getSqlCode(),
                new BeanPropertyRowMapper<>(ShopInfoDO.class),
                args
        );
    }

    @Override
    public Long getPageInfo(ShopInfoRequest request, Long companyId) {
        // 构建基础查询
        QueryFrom queryBuilder = Queries.create().from(tableName);
        List<ConditionComponent<?>> conditions = getConditionComponents(companyId,request);

        queryBuilder.where(conditions);

        // 构建SQL并执行查询
        SQL sql = queryBuilder.select()
                .toSql();

        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        return jdbcTemplate.queryForObject(
                sql.getSqlCode(),
                Long.class,
                args
        );
    }

    /**
     * 新增店铺信息
     * @param shopInfoDO 店铺信息
     * @return 新增记录的主键ID
     */
    @Override
    public Long addShopInfo(ShopInfoDO shopInfoDO) {
        // 设置创建和修改时间
        AsserUtils.notNull(shopInfoDO,"参数不能为空");
        Date now = new Date();
        shopInfoDO.setCreated(now);
        shopInfoDO.setModified(now);

        //默认启用
        shopInfoDO.setEnableStatus(CommonStatusEnum.ON.getType());
        if (shopInfoDO.getAccountStatus() == null) {
            shopInfoDO.setAccountStatus(ShopStateEnum.UN_OPEN.getCode()); // 默认未开启对账
        }

        SQL sql = Inserts.insert()
                .into(tableName)
                .columns(
                        ShopInfoFieldEnum.ID.getFieldCode(),
                        ShopInfoFieldEnum.PLATFORM_CODE.getFieldCode(),
                        ShopInfoFieldEnum.SHOP_CODE.getFieldCode(),
                        ShopInfoFieldEnum.TITLE.getFieldCode(),
                        ShopInfoFieldEnum.SHORT_TITLE.getFieldCode(),
                        ShopInfoFieldEnum.AFFILIATED_COMPANY_ID.getFieldCode(),
                        ShopInfoFieldEnum.AMOUNT.getFieldCode(),
                        ShopInfoFieldEnum.ACCOUNT_STATUS.getFieldCode(),
                        ShopInfoFieldEnum.ENABLE_STATUS.getFieldCode(),
                        ShopInfoFieldEnum.COMPANY_ID.getFieldCode(),
                        ShopInfoFieldEnum.CREATED.getFieldCode(),
                        ShopInfoFieldEnum.MODIFIED.getFieldCode()
                )
                .valueForEntity(shopInfoDO)
                .toSql();

        // 准备参数数组
        Object[] params = new Object[]{
                shopInfoDO.getId(),
                shopInfoDO.getPlatformCode(),
                shopInfoDO.getShopCode(),
                shopInfoDO.getTitle(),
                shopInfoDO.getShortTitle(),
                shopInfoDO.getAffiliatedCompanyId(),
                shopInfoDO.getAmount(),
                shopInfoDO.getAccountStatus(),
                shopInfoDO.getEnableStatus(),
                shopInfoDO.getCompanyId(),
                shopInfoDO.getCreated(),
                shopInfoDO.getModified()
        };

        jdbcTemplate.update(connection -> {
            PreparedStatement ps = connection.prepareStatement(sql.getSqlCode());
            for (int i = 0; i < params.length; i++) {
                ps.setObject(i + 1, params[i]);
            }
            return ps;
        });

        return shopInfoDO.getId();
    }

    /**
     * 更新店铺信息
     * @param shopInfoDO 店铺信息
     * @return 更新的记录数
     */
    @Override
    public Integer updateShopInfo(ShopInfoDO shopInfoDO) {
        // 参数校验
        AsserUtils.notNull(shopInfoDO, "店铺信息不能为空！");
        AsserUtils.notNull(shopInfoDO.getId(), "店铺ID不能为空！");
        // 设置修改时间
        shopInfoDO.setModified(new Date());

        SQL sql = Updates.create()
                .table(tableName)
                .where(Conditions.and(Columns.toColumn(ShopInfoFieldEnum.ID.getFieldCode()), LinkMode.EQUAL, shopInfoDO.getId()))
                .update(getUpdateColumnValueList(shopInfoDO))
                .toSql();

        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        return jdbcTemplate.update(sql.getSqlCode(), args);
    }

    public List<ColumnValue> getUpdateColumnValueList(ShopInfoDO shopInfoDO){
        List<ColumnValue> updateFields = new ArrayList<>();
        if (StringUtils.isNotEmpty(shopInfoDO.getPlatformCode())) {
            updateFields.add($.updateKeyValue(ShopInfoFieldEnum.PLATFORM_CODE.getFieldCode(), shopInfoDO.getPlatformCode()));
        }
        if (StringUtils.isNotEmpty(shopInfoDO.getShopCode())) {
            updateFields.add($.updateKeyValue(ShopInfoFieldEnum.SHOP_CODE.getFieldCode(), shopInfoDO.getShopCode()));
        }
        if (StringUtils.isNotEmpty(shopInfoDO.getTitle())) {
            updateFields.add($.updateKeyValue(ShopInfoFieldEnum.TITLE.getFieldCode(), shopInfoDO.getTitle()));
        }
        if (StringUtils.isNotEmpty(shopInfoDO.getShortTitle())) {
            updateFields.add($.updateKeyValue(ShopInfoFieldEnum.SHORT_TITLE.getFieldCode(), shopInfoDO.getShortTitle()));
        }
        if (shopInfoDO.getAffiliatedCompanyId() != null) {
            updateFields.add($.updateKeyValue(ShopInfoFieldEnum.AFFILIATED_COMPANY_ID.getFieldCode(), shopInfoDO.getAffiliatedCompanyId()));
        }
        if (shopInfoDO.getAmount() != null) {
            updateFields.add($.updateKeyValue(ShopInfoFieldEnum.AMOUNT.getFieldCode(), shopInfoDO.getAmount()));
        }
        if (shopInfoDO.getAccountStatus() != null) {
            updateFields.add($.updateKeyValue(ShopInfoFieldEnum.ACCOUNT_STATUS.getFieldCode(), shopInfoDO.getAmount()));
        }
        updateFields.add($.updateKeyValue(ShopInfoFieldEnum.MODIFIED.getFieldCode(), new Date()));
        return updateFields;
    }

    @Override
    public ShopInfoDO getShopInfoById(Long id) {
        // 参数校验
        AsserUtils.notNull(id, "店铺ID不能为空！");
        SQL sql = Queries.create()
                .from(tableName)
                .where(Conditions.and(Columns.toColumn(ShopInfoFieldEnum.ID.getFieldCode()), LinkMode.EQUAL, id))
                .select()
                .limit(1)
                .toSql();

        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<ShopInfoDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(ShopInfoDO.class), args);
        return !query.isEmpty() ? query.get(0) : null;
    }

    @Override
    public List<ShopInfoDO> listShopInfoByIds(Long companyId, List<Long> ids) {
        AsserUtils.notNull(companyId, "公司ID不能为空！");
        AsserUtils.notEmpty(ids, "店铺ID列表不能为空！");
        SQL sql = Queries.create()
                .from(tableName)
                .where(
                        Conditions.and(Columns.toColumn(ShopInfoFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId),
                        Conditions.and(Columns.toColumn(ShopInfoFieldEnum.ID.getFieldCode()), LinkMode.IN, ids)
                )
                .select()
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<ShopInfoDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(ShopInfoDO.class), args);
        return !query.isEmpty() ? query : null;
    }




    private static List<ConditionComponent<?>> getConditionComponents(Long companyId,ShopInfoRequest request) {
        List<ConditionComponent<?>> conditions = new ArrayList<>();

        // 公司ID条件（必须）
        conditions.add(Conditions.and(Columns.toColumn(ShopInfoFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId));

        if (ObjectUtils.isEmpty(request)){
            return conditions;
        }

        //判断店铺ID
        if (request.getIdList() != null){
            conditions.add(Conditions.and(Columns.toColumn(ShopInfoFieldEnum.ID.getFieldCode()), LinkMode.IN, request.getIdList()));
        }

        //平台编码集合
        if (CollectionUtils.isNotEmpty(request.getPlatformCodeList())){
            conditions.add(Conditions.and(Columns.toColumn(ShopInfoFieldEnum.PLATFORM_CODE.getFieldCode()), LinkMode.IN, request.getPlatformCodeList()));
        }
        //平台编码
        if (StringUtils.isNotEmpty(request.getPlatformCode())){
            conditions.add(Conditions.and(Columns.toColumn(ShopInfoFieldEnum.PLATFORM_CODE.getFieldCode()), LinkMode.EQUAL, request.getPlatformCode()));
        }
        //店铺code
        if (StringUtils.isNotEmpty(request.getShopCode())){
            conditions.add(Conditions.and(Columns.toColumn(ShopInfoFieldEnum.SHOP_CODE.getFieldCode()), LinkMode.LIKE_FULL_MATCH, request.getShopCode().trim()));
        }
        //店铺名称
        if (StringUtils.isNotEmpty(request.getTitle())){
            conditions.add(Conditions.and(Columns.toColumn(ShopInfoFieldEnum.TITLE.getFieldCode()), LinkMode.LIKE_FULL_MATCH, request.getTitle()));
        }
        //对帐状态
        if (Objects.nonNull(request.getAccountStatus())){
            conditions.add(Conditions.and(Columns.toColumn(ShopInfoFieldEnum.ACCOUNT_STATUS.getFieldCode()), LinkMode.EQUAL, request.getAccountStatus()));
        }
        return conditions;
    }

    /**
     * 更新店铺对账状态
     * @param shopIdList 店铺ID集合
     * @param accountStatus 对账状态 0:关闭对帐 1:开启对帐
     * @param companyId 公司ID
     * @return 更新的记录数
     */
    @Override
    public Integer updateShopAmountState(List<Long> shopIdList, Integer accountStatus, Long companyId) {
        // 参数校验
        AsserUtils.notEmpty(shopIdList, "请输入店铺ID");
        AsserUtils.notNull(accountStatus, "请输入期初余额");
        AsserUtils.notNull(companyId, "请输入公司ID");

        SQL sql = Updates.create()
                .table(tableName)
                .where(
                        Conditions.and(Columns.toColumn(ShopInfoFieldEnum.ID.getFieldCode()), LinkMode.IN, shopIdList),
                        Conditions.and(Columns.toColumn(ShopInfoFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId)
                )
                .update(
                        $.updateKeyValue(ShopInfoFieldEnum.ACCOUNT_STATUS.getFieldCode(), accountStatus),
                        $.updateKeyValue(ShopInfoFieldEnum.MODIFIED.getFieldCode(), new Date())
                ).toSql();

        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        return jdbcTemplate.update(sql.getSqlCode(), args);
    }

    /**
     * 更新店铺期初应收余额
     * @param shopId 店铺ID
     * @param request 参数
     * @param companyId 公司ID
     * @return 更新的记录数
     */
    @Override
    public Integer updateShopAmount(Long shopId, ShopUpdateAmountRequest request, Long companyId) {
        // 参数校验
        AsserUtils.notNull(request, "请输入参数");
        AsserUtils.notNull(shopId, "请输入店铺ID");
        AsserUtils.notNull(companyId, "请输入公司ID");

        // 动态构建更新字段列表
        List<ColumnValue> updateFields = new ArrayList<>();

        // 只有当字段不为空时才添加到更新列表中
        if (request.getAmount() != null) {
            updateFields.add($.updateKeyValue(ShopInfoFieldEnum.AMOUNT.getFieldCode(), request.getAmount()));
        }
        if (request.getStartDate() != null) {
            updateFields.add($.updateKeyValue(ShopInfoFieldEnum.START_DATE.getFieldCode(), request.getStartDate()));
        }
        if (request.getReconciliationType() != null) {
            updateFields.add($.updateKeyValue(ShopInfoFieldEnum.RECONCILIATION_TYPE.getFieldCode(), request.getReconciliationType()));
        }
        if (request.getReconciliationDate() != null) {
            updateFields.add($.updateKeyValue(ShopInfoFieldEnum.RECONCILIATION_DATE.getFieldCode(), request.getReconciliationDate()));
        }
        // 修改时间总是更新
        updateFields.add($.updateKeyValue(ShopInfoFieldEnum.MODIFIED.getFieldCode(), new Date()));

        // 如果没有任何字段需要更新，直接返回0
        if (updateFields.size() == 1) { // 只有修改时间字段
            return 0;
        }

        SQL sql = Updates.create()
                .table(tableName)
                .where(
                        Conditions.and(Columns.toColumn(ShopInfoFieldEnum.ID.getFieldCode()), LinkMode.EQUAL, shopId),
                        Conditions.and(Columns.toColumn(ShopInfoFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId)
                )
                .update(updateFields)
                .toSql();

        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        return jdbcTemplate.update(sql.getSqlCode(), args);
    }

    /**
     * 批量更新店铺所属公司
     * @param shopIdList 店铺ID列表
     * @param affiliatedCompanyId 所属公司ID
     * @param companyId 当前用户公司ID
     * @return 更新的记录数
     */
    @Override
    public Integer updateShopCompany(List<Long> shopIdList, Long affiliatedCompanyId, Long companyId) {
        // 参数校验
        AsserUtils.notNull(shopIdList, "店铺ID列表不能为空");
        AsserUtils.notNull(affiliatedCompanyId, "请输入期初余额");
        AsserUtils.notNull(companyId, "公司ID不能为空");

        SQL sql = Updates.create()
                .table(tableName)
                .where(
                        Conditions.and(Columns.toColumn(ShopInfoFieldEnum.ID.getFieldCode()), LinkMode.IN, shopIdList),
                        Conditions.and(Columns.toColumn(ShopInfoFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId)
                )
                .update(
                        $.updateKeyValue(ShopInfoFieldEnum.AFFILIATED_COMPANY_ID.getFieldCode(), affiliatedCompanyId),
                        $.updateKeyValue(ShopInfoFieldEnum.MODIFIED.getFieldCode(), new Date())
                ).toSql();

        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        return jdbcTemplate.update(sql.getSqlCode(), args);
    }
}
