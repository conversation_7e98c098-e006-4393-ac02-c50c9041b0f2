package com.raycloud.dmj.account.core.enums.field;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Getter
public enum MonitorSummaryFieldEnum {

    ID("id", "自增主键"),
    SHOP_ID("shop_id", "店铺ID"),
    COMPANY_ID("company_id", "公司ID"),
    CREATED("created", "创建时间"),
    MODIFIED("modified", "修改时间"),
    PLATFORM_CODE("platform_code", "平台code"),
    DATA_SOURCE("data_source", "数据类型：支付宝、微信、集分宝、淘金币"),
    IMPORT_SUCCESS_TIME("import_success_time", "导入成功的时间"),
    ANALYZE_SUCCESS_TIME("analyze_success_time", "解析成功的时间");

    /**
     * 字段编码
     */
    private final String fieldCode;

    /**
     * 字段描述
     */
    private final String fieldDesc;

    MonitorSummaryFieldEnum(String fieldCode, String fieldDesc) {
        this.fieldCode = fieldCode;
        this.fieldDesc = fieldDesc;
    }

    /**
     * 获取插入字段
     *
     * @return 插入字段
     */
    public static Set<String> getInsertFields() {
        List<MonitorSummaryFieldEnum> filterField = Arrays.asList(
                MonitorSummaryFieldEnum.ID,
                MonitorSummaryFieldEnum.CREATED,
                MonitorSummaryFieldEnum.MODIFIED
        );
        return Arrays.stream(MonitorSummaryFieldEnum.values()).filter(x -> !filterField.contains(x)).map(
                x -> x.fieldCode
        ).collect(Collectors.toSet());
    }

}
