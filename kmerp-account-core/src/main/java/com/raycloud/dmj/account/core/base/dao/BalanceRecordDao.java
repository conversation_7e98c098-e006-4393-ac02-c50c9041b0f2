package com.raycloud.dmj.account.core.base.dao;

import com.raycloud.dmj.account.core.cleancategory.domain.object.BalanceRecordDO;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface BalanceRecordDao {


    /**
     * 插入
     * @param categoryDO 类别
     */
    void insertOnDuplicateKey(BalanceRecordDO categoryDO);

    /**
     * 根据账户ID集合查询期初期末余额信息
     */
    List<BalanceRecordDO> listByFundAccountIds(List<Long> fundAccountIds, Long companyId, Date startTime, Date endTime);

}
