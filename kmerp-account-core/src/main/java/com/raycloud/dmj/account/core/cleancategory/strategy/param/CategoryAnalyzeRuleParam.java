package com.raycloud.dmj.account.core.cleancategory.strategy.param;

import com.raycloud.dmj.account.core.cleancategory.domain.AnalyzeRuleInfo;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class CategoryAnalyzeRuleParam {

    /**
     * 规则ID
     */
    private Long ruleId;

    /**
     * 子类目ID
     */
    private Long subCategoryId;

    /**
     * 规则内容
     */
    private  List<List<AnalyzeRuleInfo>> ruleGroups;

}
