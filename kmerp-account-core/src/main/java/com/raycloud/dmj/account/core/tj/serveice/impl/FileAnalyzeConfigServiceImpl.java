package com.raycloud.dmj.account.core.tj.serveice.impl;

import com.alibaba.fastjson2.JSON;
import com.raycloud.dmj.account.core.base.dao.FileAnalyzeConfigDao;
import com.raycloud.dmj.account.core.base.dao.FileAnalyzeTableConfigDao;
import com.raycloud.dmj.account.core.rawdata.domains.FileAnalyzeConfigDO;
import com.raycloud.dmj.account.core.rawdata.domains.FileAnalyzeTableConfigDO;
import com.raycloud.dmj.account.core.tj.req.*;
import com.raycloud.dmj.account.core.tj.serveice.IFileAnalyzeConfigService;
import com.raycloud.dmj.account.core.tj.vo.*;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import com.raycloud.readexcel.constant.PreprocessorType;
import com.raycloud.readexcel.constant.TranslatorType;
import com.raycloud.readexcel.constant.ValidatorType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class FileAnalyzeConfigServiceImpl implements IFileAnalyzeConfigService {

    private Logger logger = Logger.getLogger(this.getClass());

    @Autowired
    private FileAnalyzeConfigDao fileAnalyzeConfigDao;

    @Autowired
    private FileAnalyzeTableConfigDao fileAnalyzeTableConfigDao;

    @Override
    public List<CheckerVo> getCheckerList() {
        return Arrays.stream(ValidatorType.values()).map(x -> {
            CheckerVo checkerVo = new CheckerVo();
            checkerVo.setCode(x.getValue());
            checkerVo.setTitle(x.getTitle());
            checkerVo.setTips(x.getMessage());
            return checkerVo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<CheckerVo> getTranslatorList() {
        return Arrays.stream(TranslatorType.values()).map(x -> {
            CheckerVo checkerVo = new CheckerVo();
            checkerVo.setCode(x.getValue());
            checkerVo.setTitle(x.getTitle());
            return checkerVo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<CheckerVo> getPreprocessorList() {
        return Arrays.stream(PreprocessorType.values()).map(x -> {
            CheckerVo checkerVo = new CheckerVo();
            checkerVo.setCode(x.getValue());
            checkerVo.setTitle(x.getTitle());
            checkerVo.setTips(x.getDescription());
            return checkerVo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<FileAnalyzeConfigVo> getFileAnalyzeConfigList(String dataType) {
        List<FileAnalyzeConfigVo> configVoList = fileAnalyzeConfigDao.queryFileAnalyzeConfigVo(dataType);
        if (CollectionUtils.isNotEmpty(configVoList)) {
            configVoList.forEach(configVo -> {
                configVo.setSubConfigNum(fileAnalyzeTableConfigDao.countByConfigId(configVo.getId()));
            });
        }
        return configVoList;
    }

    @Override
    public FileAnalyzeConfigVo getFileAnalyzeConfig(Long id) {
        Assert.notNull(id, "ID不能为空");
        FileAnalyzeConfigDO analyzeConfigDO = fileAnalyzeConfigDao.queryFileAnalyzeConfigById(id);
        Assert.notNull(analyzeConfigDO, "配置信息不存在");
        FileAnalyzeConfigVo fileAnalyzeConfigVo = new FileAnalyzeConfigVo();
        fileAnalyzeConfigVo.setId(id);
        fileAnalyzeConfigVo.setDataType(analyzeConfigDO.getDataType());
        fileAnalyzeConfigVo.setSheetType(analyzeConfigDO.getSheetType());
        fileAnalyzeConfigVo.setSheet(analyzeConfigDO.getSheet());
        fileAnalyzeConfigVo.setHeaderStartIndex(analyzeConfigDO.getHeaderStartIndex());
        fileAnalyzeConfigVo.setHeaderEndIndex(analyzeConfigDO.getHeaderEndIndex());
        //将JSON字符串转化成对象list
        fileAnalyzeConfigVo.setHeadConfig(JSON.parseArray(analyzeConfigDO.getHeadConfig(), FileHeadConfigVo.class));
        fileAnalyzeConfigVo.setFilter(JSON.parseArray(analyzeConfigDO.getFilter(), FilterVo.class));
        return fileAnalyzeConfigVo;
    }

    @Override
    public Long addFileAnalyzeConfig(FileAnalyzeConfigRequest addFileAnalyzeConfig) {
        // 参数校验
        AsserUtils.notNull(addFileAnalyzeConfig, "文件解析配置不能为空");
        AsserUtils.hasText(addFileAnalyzeConfig.getDataType(), "数据类型不能为空");
        AsserUtils.notNull(addFileAnalyzeConfig.getSheetType(), "Sheet类型不能为空");
        AsserUtils.hasText(addFileAnalyzeConfig.getSheet(), "Sheet名称不能为空");
        AsserUtils.notNull(addFileAnalyzeConfig.getHeaderEndIndex(), "表头结束索引不能为空");
        Assert.notEmpty(addFileAnalyzeConfig.getHeadConfig(), "文件表头配置不能为空");

        // 校验headConfig和filter中的key不能重复
        List<String> headKeyList = addFileAnalyzeConfig.getHeadConfig().stream().map(FileHeadConfigRequest::getKey).collect(Collectors.toList());
        validateKeyUniqueness(headKeyList,"文件表头配置");

        if (addFileAnalyzeConfig.getFilter() != null && !addFileAnalyzeConfig.getFilter().isEmpty()) {
            List<String> filterKeyList = addFileAnalyzeConfig.getFilter().stream().map(FilterRequest::getKey).collect(Collectors.toList());
            validateKeyUniqueness(filterKeyList,"过滤配置");
        }

        // 校验同一个data_type下的配置约束
        validateDataTypeConstraints(addFileAnalyzeConfig.getDataType(), addFileAnalyzeConfig.getSheetType(),
                addFileAnalyzeConfig.getSheet(), null);

        // 构建DO对象
        FileAnalyzeConfigDO fileAnalyzeConfig = new FileAnalyzeConfigDO();
        fileAnalyzeConfig.setDataType(addFileAnalyzeConfig.getDataType());
        fileAnalyzeConfig.setSheetType(addFileAnalyzeConfig.getSheetType());
        fileAnalyzeConfig.setSheet(addFileAnalyzeConfig.getSheet());
        fileAnalyzeConfig.setHeaderStartIndex(addFileAnalyzeConfig.getHeaderStartIndex());
        fileAnalyzeConfig.setHeaderEndIndex(addFileAnalyzeConfig.getHeaderEndIndex());
        fileAnalyzeConfig.setHeadConfig(JSON.toJSONString(addFileAnalyzeConfig.getHeadConfig()));
        fileAnalyzeConfig.setFilter(JSON.toJSONString(addFileAnalyzeConfig.getFilter()));

        // 设置默认值
        Date now = new Date();
        fileAnalyzeConfig.setVersion(0);
        fileAnalyzeConfig.setEnableStatus(1);
        fileAnalyzeConfig.setCreated(now);
        fileAnalyzeConfig.setModified(now);

        // 调用正确的DAO方法插入数据并返回主键ID
        return fileAnalyzeConfigDao.addFileAnalyzeConfig(fileAnalyzeConfig);
    }



    @Override
    public Long updateFileAnalyzeConfig(FileAnalyzeConfigRequest fileAnalyzeConfigDTO) {
        // 参数校验
        AsserUtils.notNull(fileAnalyzeConfigDTO, "文件解析配置不能为空");
        AsserUtils.notNull(fileAnalyzeConfigDTO.getId(), "配置ID不能为空");
        AsserUtils.hasText(fileAnalyzeConfigDTO.getDataType(), "数据类型不能为空");
        AsserUtils.notNull(fileAnalyzeConfigDTO.getSheetType(), "Sheet类型不能为空");
        AsserUtils.hasText(fileAnalyzeConfigDTO.getSheet(), "Sheet名称不能为空");
        AsserUtils.notNull(fileAnalyzeConfigDTO.getHeaderEndIndex(), "表头结束索引不能为空");
        AsserUtils.notEmpty(fileAnalyzeConfigDTO.getHeadConfig(), "文件表头配置不能为空");

        // 检查配置是否存在
        FileAnalyzeConfigDO existingConfig = fileAnalyzeConfigDao.queryFileAnalyzeConfigById(fileAnalyzeConfigDTO.getId());
        AsserUtils.notNull(existingConfig, "配置信息不存在，无法更新");

        // 校验headConfig和filter中的key不能重复
        List<String> headKeyList = fileAnalyzeConfigDTO.getHeadConfig().stream().map(FileHeadConfigRequest::getKey).collect(Collectors.toList());
        validateKeyUniqueness(headKeyList,"文件表头配置");

        if (fileAnalyzeConfigDTO.getFilter() != null && !fileAnalyzeConfigDTO.getFilter().isEmpty()) {
            List<String> filterKeyList = fileAnalyzeConfigDTO.getFilter().stream().map(FilterRequest::getKey).collect(Collectors.toList());
            validateKeyUniqueness(filterKeyList,"过滤配置");
        }

        // 校验同一个data_type下的配置约束（更新时排除当前配置ID）
        validateDataTypeConstraints(fileAnalyzeConfigDTO.getDataType(), fileAnalyzeConfigDTO.getSheetType(),
                fileAnalyzeConfigDTO.getSheet(), fileAnalyzeConfigDTO.getId());

        // 构建更新对象
        FileAnalyzeConfigDO fileAnalyzeConfig = new FileAnalyzeConfigDO();
        fileAnalyzeConfig.setId(fileAnalyzeConfigDTO.getId());
        fileAnalyzeConfig.setDataType(fileAnalyzeConfigDTO.getDataType());
        fileAnalyzeConfig.setSheetType(fileAnalyzeConfigDTO.getSheetType());
        fileAnalyzeConfig.setSheet(fileAnalyzeConfigDTO.getSheet());
        fileAnalyzeConfig.setHeaderStartIndex(fileAnalyzeConfigDTO.getHeaderStartIndex());
        fileAnalyzeConfig.setHeaderEndIndex(fileAnalyzeConfigDTO.getHeaderEndIndex());
        fileAnalyzeConfig.setHeadConfig(JSON.toJSONString(fileAnalyzeConfigDTO.getHeadConfig()));
        fileAnalyzeConfig.setFilter(JSON.toJSONString(fileAnalyzeConfigDTO.getFilter()));

        // 保持原有的版本号和启用状态
        fileAnalyzeConfig.setVersion(existingConfig.getVersion() + 1); // 版本号递增
        fileAnalyzeConfig.setEnableStatus(existingConfig.getEnableStatus());

        // 执行更新
        int updatedRows = fileAnalyzeConfigDao.updateFileAnalyzeConfig(fileAnalyzeConfig);
        if (updatedRows <= 0) {
            throw new RuntimeException("更新文件解析配置失败");
        }

        return fileAnalyzeConfigDTO.getId();
    }

    @Override
    public List<FileSheetConfigVo> getFileAnalyzeTableConfigList(Long configId) {
        Assert.notNull(configId, "配置ID不能为空");

        List<FileAnalyzeTableConfigDO> tableConfigList = fileAnalyzeTableConfigDao.queryByConfigId(configId);
        if (CollectionUtils.isEmpty(tableConfigList)) {
            return Collections.emptyList();
        }

        return tableConfigList.stream().map(this::convertToVo).collect(Collectors.toList());
    }

    @Override
    public Long addFileAnalyzeTableConfig(FileSheetConfigRequest fileSheetConfigRequest) {
        // 参数校验
        Assert.notNull(fileSheetConfigRequest, "表配置不能为空");
        Assert.notNull(fileSheetConfigRequest.getConfigId(), "文件解析配置ID不能为空");
        Assert.hasText(fileSheetConfigRequest.getTableName(), "数据表名称不能为空");
        Assert.hasText(fileSheetConfigRequest.getDataType(), "数据表类型不能为空");

        List<String> fieldsList = fileSheetConfigRequest.getTableFields().stream().map(FileSheetTableFieldRequest::getTableField).collect(Collectors.toList());
        validateKeyUniqueness(fieldsList,"字段配置");

        // 检查父配置是否存在
        FileAnalyzeConfigDO parentConfig = fileAnalyzeConfigDao.queryFileAnalyzeConfigById(fileSheetConfigRequest.getConfigId());
        Assert.notNull(parentConfig, "文件解析配置不存在");

        // 构建DO对象
        FileAnalyzeTableConfigDO tableConfig = new FileAnalyzeTableConfigDO();
        tableConfig.setConfigId(fileSheetConfigRequest.getConfigId());
        tableConfig.setTableName(fileSheetConfigRequest.getTableName());
        tableConfig.setTableDataType(Integer.valueOf(fileSheetConfigRequest.getDataType()));
        tableConfig.setBatchNo(JSON.toJSONString(fileSheetConfigRequest.getBatchNo()));
        tableConfig.setUniqueKey(JSON.toJSONString(fileSheetConfigRequest.getUniqueKey()));
        tableConfig.setTableFields(JSON.toJSONString(fileSheetConfigRequest.getTableFields()));
        tableConfig.setTableSourceConfig(fileSheetConfigRequest.getTableSourceConfig());

        // 设置默认值
        Date now = new Date();
        tableConfig.setCreated(now);
        tableConfig.setModified(now);
        tableConfig.setEnableStatus(1);

        // 插入数据并返回主键ID
        return fileAnalyzeTableConfigDao.addFileAnalyzeTableConfig(tableConfig);
    }

    @Override
    public Long updateFileAnalyzeTableConfig(FileSheetConfigRequest fileSheetConfigRequest) {
        // 参数校验
        Assert.notNull(fileSheetConfigRequest, "表配置不能为空");
        Assert.notNull(fileSheetConfigRequest.getId(), "表配置ID不能为空");
        Assert.notNull(fileSheetConfigRequest.getConfigId(), "文件解析配置ID不能为空");
        Assert.hasText(fileSheetConfigRequest.getTableName(), "数据表名称不能为空");
        Assert.hasText(fileSheetConfigRequest.getDataType(), "数据表类型不能为空");

        List<String> fieldsList = fileSheetConfigRequest.getTableFields().stream().map(FileSheetTableFieldRequest::getTableField).collect(Collectors.toList());
        validateKeyUniqueness(fieldsList,"字段配置");

        // 检查表配置是否存在
        FileAnalyzeTableConfigDO existingTableConfig = fileAnalyzeTableConfigDao.queryById(fileSheetConfigRequest.getId());
        Assert.notNull(existingTableConfig, "表配置信息不存在，无法更新");

        // 检查父配置是否存在
        FileAnalyzeConfigDO parentConfig = fileAnalyzeConfigDao.queryFileAnalyzeConfigById(fileSheetConfigRequest.getConfigId());
        Assert.notNull(parentConfig, "文件解析配置不存在");

        // 构建更新对象
        FileAnalyzeTableConfigDO tableConfig = new FileAnalyzeTableConfigDO();
        tableConfig.setId(fileSheetConfigRequest.getId());
        tableConfig.setConfigId(fileSheetConfigRequest.getConfigId());
        tableConfig.setTableName(fileSheetConfigRequest.getTableName());
        tableConfig.setTableDataType(Integer.valueOf(fileSheetConfigRequest.getDataType()));
        tableConfig.setBatchNo(JSON.toJSONString(fileSheetConfigRequest.getBatchNo()));
        tableConfig.setUniqueKey(JSON.toJSONString(fileSheetConfigRequest.getUniqueKey()));
        tableConfig.setTableFields(JSON.toJSONString(fileSheetConfigRequest.getTableFields()));
        tableConfig.setTableSourceConfig(fileSheetConfigRequest.getTableSourceConfig());
        tableConfig.setEnableStatus(existingTableConfig.getEnableStatus());

        // 执行更新
        int updatedRows = fileAnalyzeTableConfigDao.updateFileAnalyzeTableConfig(tableConfig);
        if (updatedRows <= 0) {
            throw new RuntimeException("更新表配置失败");
        }

        return fileSheetConfigRequest.getId();
    }

    /**
     * 将DO对象转换为VO对象
     * @param tableConfigDO DO对象
     * @return VO对象
     */
    private FileSheetConfigVo convertToVo(FileAnalyzeTableConfigDO tableConfigDO) {
        FileSheetConfigVo vo = new FileSheetConfigVo();
        vo.setId(tableConfigDO.getId());
        vo.setConfigId(tableConfigDO.getConfigId());
        vo.setTableName(tableConfigDO.getTableName());
        vo.setTableType(String.valueOf(tableConfigDO.getTableDataType()));
        vo.setTableSourceConfig(tableConfigDO.getTableSourceConfig());

        // 将JSON字符串转换为对象列表
        if (tableConfigDO.getBatchNo() != null) {
            vo.setBatchNo(JSON.parseArray(tableConfigDO.getBatchNo(), FileSheetTableConfigVo.class));
        }
        if (tableConfigDO.getUniqueKey() != null) {
            vo.setUniqueKey(JSON.parseArray(tableConfigDO.getUniqueKey(), FileSheetTableConfigVo.class));
        }
        if (tableConfigDO.getTableFields() != null) {
            vo.setTableFields(JSON.parseArray(tableConfigDO.getTableFields(), FileSheetTableFieldVo.class));
        }

        return vo;
    }

    /**
     * 校验headConfig和filter中的key不能重复
     * @param keyList
     */
    private void validateKeyUniqueness(List<String> keyList,String type) {
        Set<String> allKeys = new HashSet<>();
        List<String> duplicateKeys = new ArrayList<>();
        // 校验headConfig中的key
        if (CollectionUtils.isNotEmpty(keyList)) {
            for (String key : keyList) {
                if (key != null && !key.trim().isEmpty()) {
                    key = key.trim();
                    if (!allKeys.add(key)) {
                        duplicateKeys.add(key);
                    }
                }
            }
        }
        // 如果有重复的key，抛出异常
        if (!duplicateKeys.isEmpty()) {
            String duplicateKeyStr = String.join(", ", duplicateKeys);
            throw new IllegalArgumentException(type + "中存在重复的key: " + duplicateKeyStr);
        }
    }

    /**
     * 校验同一个data_type下的配置约束
     * @param dataType 数据类型
     * @param sheetType Sheet类型
     * @param sheet Sheet名称
     * @param excludeId 排除的配置ID（更新时使用，新增时传null）
     */
    private void validateDataTypeConstraints(String dataType, Integer sheetType, String sheet, Long excludeId) {
        // 查询同一个data_type下的所有启用配置
        List<FileAnalyzeConfigDO> existingConfigs = fileAnalyzeConfigDao.queryEnabledConfigsByDataType(dataType);

        if (CollectionUtils.isEmpty(existingConfigs)) {
            return; // 如果没有现有配置，直接通过
        }

        // 过滤掉当前更新的配置（如果是更新操作）
        if (excludeId != null) {
            existingConfigs = existingConfigs.stream()
                    .filter(config -> !excludeId.equals(config.getId()))
                    .collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(existingConfigs)) {
            return; // 过滤后如果没有配置，直接通过
        }

        // 校验1：sheetType必须一致
        Integer existingSheetType = existingConfigs.get(0).getSheetType();
        if (!sheetType.equals(existingSheetType)) {
            String sheetTypeDesc = sheetType == 1 ? "下标" : "名称";
            String existingSheetTypeDesc = existingSheetType == 1 ? "下标" : "名称";
            throw new IllegalArgumentException(
                String.format("数据类型[%s]下已存在SheetType为[%s]的配置，不能添加SheetType为[%s]的配置。同一数据类型下的SheetType必须保持一致。",
                    dataType, existingSheetTypeDesc, sheetTypeDesc)
            );
        }

        // 校验2：sheet值不能重复
        List<String> existingSheets = existingConfigs.stream()
                .map(FileAnalyzeConfigDO::getSheet)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (existingSheets.contains(sheet)) {
            String sheetDesc = sheetType == 1 ? "下标" : "名称";
            throw new IllegalArgumentException(
                String.format("数据类型[%s]下已存在Sheet%s为[%s]的配置，不能重复添加。", dataType, sheetDesc, sheet)
            );
        }
    }
}
