package com.raycloud.dmj.account.core.cleancategory.domain.object;

import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 余额记录表
 */
@Data
public class BalanceRecordDO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 修改时间
     */
    private Date modified;

    /**
     * 资金账户ID
     */
    private Long fundAccountId;

    /**
     * 公司ID
     */
    private Long companyId;

    /**
     * 期初余额
     */
    private BigDecimal startBalance;

    /**
     * 期末余额
     */
    private BigDecimal endBalance;


    /**
     * 标准资金流水变化的金额
     */
    private BigDecimal changeAmount;

    /**
     * 日期
     */
    private Date date;


    /**
     * 店铺ID
     */
    private Long shopId;
}