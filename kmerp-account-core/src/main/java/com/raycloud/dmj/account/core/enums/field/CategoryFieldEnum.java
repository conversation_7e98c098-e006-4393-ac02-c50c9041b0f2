package com.raycloud.dmj.account.core.enums.field;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 分类表字段枚举
 */
@Getter
public enum CategoryFieldEnum {

    ID("id", "主键ID"),
    CREATED("created", "创建时间"),
    MODIFIED("modified", "修改时间"),
    CATEGORY_GROUP_CODE("category_group_code", "分类编码"),
    SOURCE("source", "来源（1-系统，2-手动）"),
    NAME("name", "类别名称"),
    FUND_ACCOUNT_ID("fund_account_id", "资金账户ID"),
    PLATFORM_CODE("platform_code", "平台code"),
    COMPANY_ID("company_id", "公司ID");

    /**
     * 字段编码
     */
    private final String fieldCode;

    /**
     * 字段描述
     */
    private final String fieldDesc;

    CategoryFieldEnum(String fieldCode, String fieldDesc) {
        this.fieldCode = fieldCode;
        this.fieldDesc = fieldDesc;
    }

    /**
     * 获取插入字段
     * @return 插入字段
     */
    public static Set<String> getInsertFields() {
        List<CategoryFieldEnum> filterField = Arrays.asList(
                CategoryFieldEnum.ID
        );
        return Arrays.stream(CategoryFieldEnum.values()).filter(x -> !filterField.contains(x)).map(
                x -> x.fieldCode
        ).collect(Collectors.toSet());

    }
}