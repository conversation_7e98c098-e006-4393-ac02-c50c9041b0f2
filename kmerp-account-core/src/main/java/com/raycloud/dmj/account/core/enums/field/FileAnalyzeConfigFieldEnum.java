package com.raycloud.dmj.account.core.enums.field;

import lombok.Getter;

/**
 * Date:  2025/6/16
 * <AUTHOR>
 */
@Getter
public enum FileAnalyzeConfigFieldEnum {


    ID("id", "主键ID"),
    DATA_TYPE("data_type", "数据类型"),
    SHEET_TYPE("sheet_type", "sheet的类型，1为下标，2为名称"),
    SHEET("sheet", "配置对应的sheet"),
    FILTER("filter", "数据合法性过滤、清洗规则"),
    HEADER_START_INDEX("header_start_index", "表头从第几行开始"),
    HEADER_END_INDEX("header_end_index", "表头从第几行结束"),
    HEAD_CONFIG("head_config", "表头-业务字段配置"),
    CREATED("created", "创建时间"),
    MODIFIED("modified", "修改时间"),
    ENABLE_STATUS("enable_status", "0:弃用 1：正常"),
    VERSION("version", "版本号");

    /**
     * 字段编码
     */
    private final String fieldCode;

    /**
     * 字段描述
     */
    private final String fieldDesc;

    FileAnalyzeConfigFieldEnum(String fieldCode, String fieldDesc){
        this.fieldCode = fieldCode;
        this.fieldDesc = fieldDesc;
    }


}
