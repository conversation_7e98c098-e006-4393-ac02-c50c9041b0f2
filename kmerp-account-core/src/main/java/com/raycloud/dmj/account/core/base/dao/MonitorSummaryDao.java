package com.raycloud.dmj.account.core.base.dao;

import com.raycloud.dmj.account.core.base.domain.MonitorSummaryDO;
import com.raycloud.dmj.account.core.common.Page;
import com.raycloud.dmj.account.core.common.PageInfo;
import com.raycloud.dmj.account.core.common.PageListBase;

import java.util.List;

public interface MonitorSummaryDao {

    Long insert(MonitorSummaryDO monitorSummaryDO);

    List<MonitorSummaryDO> pageDataSummaryByPlatformCode(Long companyId, String platformCode, Long shopId, Page page);

    PageInfo<Void> pageInfoSummaryByPlatformCode(Long companyId, String platformCode, Long shopId, Page page);


    /**
     * 更新导入成功时间
     * @param companyId 公司ID
     * @param shopId 店铺ID
     * @param source 数据源
     */
    void updateAnalyzeTimeByShopAndSource(Long companyId,Long shopId, Integer source);
    /**
     * 插入数据，如果数据已存在则更新
     * @param monitorSummaryDO  数据
     */
    void insertOnDuplicateKey(MonitorSummaryDO monitorSummaryDO);
}
