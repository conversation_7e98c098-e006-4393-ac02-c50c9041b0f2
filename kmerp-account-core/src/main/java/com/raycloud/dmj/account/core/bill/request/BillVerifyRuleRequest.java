package com.raycloud.dmj.account.core.bill.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 账单核验规则查询请求对象
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BillVerifyRuleRequest{

    /**
     * 平台code
     */
    private String platformCode;

    /**
     * 店铺ID集合
     */
    private List<Long> shopIdList;

    /**
     * 核验规则名称
     */
    private String ruleName;

}
