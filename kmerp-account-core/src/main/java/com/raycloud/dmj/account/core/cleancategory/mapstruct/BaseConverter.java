package com.raycloud.dmj.account.core.cleancategory.mapstruct;

import com.alibaba.fastjson2.JSON;

/**
 * <AUTHOR>
 */
public class BaseConverter {


    /**
     * 对象转Map
     * @param obj 对象
     * @return  Map
     */
    public static String convertToString(Object obj) {
        // 将对象转换为JSON字符串
        return JSON.toJSONString(obj);

    }
    

    /**
     * Map转对象方法
     * @param json  Map
     * @param clazz  类
     * @return 对象
     * @param <T> 泛型
     */
    public static <T> T convertToObject(String json, Class<T> clazz) {
        // 将JSON字符串转换为指定类型的对象
        return JSON.parseObject(json, clazz);
    }
}