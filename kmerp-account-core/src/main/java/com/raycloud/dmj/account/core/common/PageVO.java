package com.raycloud.dmj.account.core.common;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
public class PageVO<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 总数
     */
    private Long total;

    /**
     * 数据列表
     */
    private List<T> list;




}
