package com.raycloud.dmj.account.core.base.dao.impl;

import com.raycloud.dmj.account.core.base.dao.StandardFundBillFlowInfoDao;
import com.raycloud.dmj.account.core.base.domain.BillSummaryRecordDO;
import com.raycloud.dmj.account.core.base.domain.StandardFundBillFlowInfoDO;
import com.raycloud.dmj.account.core.bill.parameter.StandardFundBillFlowParameter;
import com.raycloud.dmj.account.core.bill.vo.BillTotalInfo;
import com.raycloud.dmj.account.core.bill.vo.StandardFundBillFlowInfoVO;
import com.raycloud.dmj.account.core.enums.StandardFundBillSourceEnum;
import com.raycloud.dmj.account.core.enums.YesOrNoEnum;
import com.raycloud.dmj.account.core.enums.field.FundAccountFieldEnum;
import com.raycloud.dmj.account.core.enums.field.ShopInfoFieldEnum;
import com.raycloud.dmj.account.core.enums.field.StandardFundBillFlowInfoFieldEnum;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import com.raycloud.dmj.table.api.plus.common.ColumnValues;
import com.raycloud.dmj.table.api.plus.common.base.SQL;
import com.raycloud.dmj.table.api.plus.common.enums.LinkMode;
import com.raycloud.dmj.table.api.plus.common.enums.OrderType;
import com.raycloud.dmj.table.api.plus.component.$;
import com.raycloud.dmj.table.api.plus.component.column.utils.Columns;
import com.raycloud.dmj.table.api.plus.component.column.utils.Functions;
import com.raycloud.dmj.table.api.plus.component.condition.ConditionComponent;
import com.raycloud.dmj.table.api.plus.component.condition.utils.Conditions;
import com.raycloud.dmj.table.api.plus.delete.Deletes;
import com.raycloud.dmj.table.api.plus.insert.Inserts;
import com.raycloud.dmj.table.api.plus.insert.batch.InsertBatchSQL;
import com.raycloud.dmj.table.api.plus.insert.core.InsertMode;
import com.raycloud.dmj.table.api.plus.query.Queries;
import com.raycloud.dmj.table.api.plus.query.api.QueryFrom;
import com.raycloud.dmj.table.api.plus.update.Updates;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 标准资金账单流水信息表 DAO
 * 
 * <AUTHOR>
 */
@Repository
public class StandardFundBillFlowInfoDaoImpl extends BaseDao implements StandardFundBillFlowInfoDao {

    private final static String tableName = "standard_fund_bill_flow_info";
    private final static String shopTableName = "shop_info";
    private final static String fundAccountTableName = "fund_account";
    private final String DOC_NO = "-1";

    /**
     * 新增标准资金账单流水信息
     * @param flowInfo 流水信息
     * @return 新增的ID
     */
    @Override
    public Long addStandardFundBillFlowInfo(StandardFundBillFlowInfoDO flowInfo) {
        Date now = new Date();
        // 设置默认值
        if (flowInfo.getSource() == null) {
            flowInfo.setSource(StandardFundBillSourceEnum.BILL.getCode()); // 默认来源账单
        }
        if (flowInfo.getDocNo() == null) {
            flowInfo.setDocNo(DOC_NO); // 默认值
        }
        //设置创建时间
        flowInfo.setCreated(now);
        if (flowInfo.getModified() == null) {
            flowInfo.setModified(now);
        }

        // 构建插入SQL
        SQL sql = Inserts.insert()
                .into(tableName)
                .columns(
                        StandardFundBillFlowInfoFieldEnum.PLATFORM_CODE.getFieldCode(),
                        StandardFundBillFlowInfoFieldEnum.SHOP_ID.getFieldCode(),
                        StandardFundBillFlowInfoFieldEnum.ACCOUNT_ID.getFieldCode(),
                        StandardFundBillFlowInfoFieldEnum.OCCURRED_AT.getFieldCode(),
                        StandardFundBillFlowInfoFieldEnum.RULE_ID.getFieldCode(),
                        StandardFundBillFlowInfoFieldEnum.CATEGORY_CODE.getFieldCode(),
                        StandardFundBillFlowInfoFieldEnum.CATEGORY_ID.getFieldCode(),
                        StandardFundBillFlowInfoFieldEnum.SUB_CATEGORY_ID.getFieldCode(),
                        StandardFundBillFlowInfoFieldEnum.AMOUNT.getFieldCode(),
                        StandardFundBillFlowInfoFieldEnum.INCOME_EXPENSE_DIRECTION.getFieldCode(),
                        StandardFundBillFlowInfoFieldEnum.BILL_NO.getFieldCode(),
                        StandardFundBillFlowInfoFieldEnum.ORDER_NO.getFieldCode(),
                        StandardFundBillFlowInfoFieldEnum.DOC_NO.getFieldCode(),
                        StandardFundBillFlowInfoFieldEnum.REMARK.getFieldCode(),
                        StandardFundBillFlowInfoFieldEnum.IS_OFFSET.getFieldCode(),
                        StandardFundBillFlowInfoFieldEnum.SOURCE.getFieldCode(),
                        StandardFundBillFlowInfoFieldEnum.CREATOR.getFieldCode(),
                        StandardFundBillFlowInfoFieldEnum.CREATED.getFieldCode(),
                        StandardFundBillFlowInfoFieldEnum.BATCH_NO.getFieldCode(),
                        StandardFundBillFlowInfoFieldEnum.BIZ_KEY.getFieldCode(),
                        StandardFundBillFlowInfoFieldEnum.COMPANY_ID.getFieldCode(),
                        StandardFundBillFlowInfoFieldEnum.MODIFIED.getFieldCode(),
                        StandardFundBillFlowInfoFieldEnum.DATA_RANGE.getFieldCode()
                ).valueForEntity(flowInfo).toSql();

        // 使用KeyHolder捕获自增主键
        KeyHolder keyHolder = new GeneratedKeyHolder();
        Object[] params = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();

        jdbcTemplate.update(connection -> {
            PreparedStatement ps = connection.prepareStatement(sql.getSqlCode(), Statement.RETURN_GENERATED_KEYS);
            for (int i = 0; i < params.length; i++) {
                ps.setObject(i + 1, params[i]);
            }
            return ps;
        }, keyHolder);

        // 返回生成的主键ID
        Number key = keyHolder.getKey();
        return key != null ? key.longValue() : null;
    }

    /**
     * 根据条件查询标准资金账单流水信息列表
     * @param parameter 查询条件
     * @param companyId 公司ID
     * @return 流水信息列表
     */
    @Override
    public List<StandardFundBillFlowInfoVO> getStandardFundBillFlowInfoList(StandardFundBillFlowParameter parameter, Long companyId) {
        // 构建基础查询
        QueryFrom queryBuilder = getQueryFrom();

        // 添加查询条件（多表查询，需要指定表别名）
        List<ConditionComponent<?>> conditions = getConditionComponentsWithAlias(companyId, parameter);

        // 构建SQL并执行查询
        SQL sql = queryBuilder.where(conditions)
                .orderBy($.create("bill." + StandardFundBillFlowInfoFieldEnum.OCCURRED_AT.getFieldCode(), OrderType.find("desc")))
                .select("bill.*,s.short_title as shop_short_title,f.account_name")
                .page( parameter.getPageNo(),parameter.getPageSize())
                .toSql();

        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        return jdbcTemplate.query(
                sql.getSqlCode(),
                new BeanPropertyRowMapper<>(StandardFundBillFlowInfoVO.class),
                args
        );
    }

    @Override
    public SQL getExportSQL(StandardFundBillFlowParameter parameter, Long companyId) {
        // 构建基础查询
        QueryFrom queryBuilder = getQueryFrom();

        // 添加查询条件（多表查询，需要指定表别名）
        List<ConditionComponent<?>> conditions = getConditionComponentsWithAlias(companyId, parameter);

        // 构建SQL并执行查询
        SQL sql = queryBuilder.where(conditions)
                .orderBy($.create("bill." + StandardFundBillFlowInfoFieldEnum.OCCURRED_AT.getFieldCode(), OrderType.find("desc")))
                .select("bill.*,s.short_title as shop_short_title,f.account_name")
                .toSql();
        return sql;
    }

    private static QueryFrom getQueryFrom() {
        // 构建基础查询
        QueryFrom queryBuilder = Queries.create().from(tableName, "bill")
                .join(shopTableName,"s")
                .on(
                        Conditions.and(
                                Columns.create(StandardFundBillFlowInfoFieldEnum.SHOP_ID.getFieldCode()).referenceTableAlias("bill"),
                                LinkMode.EQUAL,
                                Columns.create(ShopInfoFieldEnum.ID.getFieldCode()).referenceTableAlias("s")
                        )
                )
                .join(fundAccountTableName,"f")
                .on(
                        Conditions.and(
                                Columns.create(StandardFundBillFlowInfoFieldEnum.ACCOUNT_ID.getFieldCode()).referenceTableAlias("bill"),
                                LinkMode.EQUAL,
                                Columns.create(FundAccountFieldEnum.ID.getFieldCode()).referenceTableAlias("f")
                        )
                );
        return queryBuilder;
    }

    /**
     * 统计符合条件的数据条数
     * @param parameter 查询条件
     * @param companyId 公司ID
     * @return 符合条件的数据条数
     */
    @Override
    public Long getCount(StandardFundBillFlowParameter parameter, Long companyId) {
        // 构建基础查询
        QueryFrom queryBuilder = getQueryFrom();

        // 添加查询条件
        List<ConditionComponent<?>> conditions = getConditionComponentsWithAlias(companyId, parameter);

        // 构建统计SQL
        SQL sql = queryBuilder.where(conditions)
                .select(Columns.create("COUNT(*)"))
                .toSql();

        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();

        // 执行统计查询
        return jdbcTemplate.queryForObject(sql.getSqlCode(), Long.class, args);
    }

    /**
     * 统计符合条件的数据汇总信息
     * @param parameter 查询条件
     * @param companyId 公司ID
     * @return 符合条件的数据条数
     */
    @Override
    public BillTotalInfo getTotalInfo(StandardFundBillFlowParameter parameter, Long companyId) {
        // 构建基础查询
        QueryFrom queryBuilder = getQueryFrom();

        // 添加查询条件
        List<ConditionComponent<?>> conditions = getConditionComponentsWithAlias(companyId, parameter);

        // 构建统计SQL
        SQL sql = queryBuilder.where(conditions)
                .select(Columns.create("sum(bill.amount) as totalAmount,count(*) as total"))
                .toSql();

        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();

        List<BillTotalInfo> list = jdbcTemplate.query(
                sql.getSqlCode(),
                new BeanPropertyRowMapper<>(BillTotalInfo.class),
                args
        );
        // 执行统计查询
        return list.isEmpty() ? null : list.get(0);
    }

    /**
     * 根据ID查询标准资金账单流水信息
     * @param id 主键ID
     * @return 流水信息
     */
    @Override
    public StandardFundBillFlowInfoDO getStandardFundBillFlowInfoById(Long id) {
        SQL sql = Queries.create()
                .from(tableName)
                .where(Conditions.and(Columns.toColumn(StandardFundBillFlowInfoFieldEnum.ID.getFieldCode()),
                                     LinkMode.EQUAL, id))
                .select()
                .limit(1)
                .toSql();

        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<StandardFundBillFlowInfoDO> results = jdbcTemplate.query(
                sql.getSqlCode(),
                new BeanPropertyRowMapper<>(StandardFundBillFlowInfoDO.class),
                args
        );

        return results.isEmpty() ? null : results.get(0);
    }

    /**
     * 批量新增标准资金账单流水信息
     * @param flowInfoList 流水信息列表
     */
    @Override
    public Integer batchAddStandardFundBillFlowInfo(List<StandardFundBillFlowInfoDO> flowInfoList) {
        if (flowInfoList == null || flowInfoList.isEmpty()) {
            return 0;
        }

        // 设置默认值
        Date now = new Date();
        for (StandardFundBillFlowInfoDO flowInfo : flowInfoList) {
            // 设置默认值
            if (flowInfo.getSource() == null) {
                flowInfo.setSource(StandardFundBillSourceEnum.BILL.getCode());
            }
            if (flowInfo.getDocNo() == null) {
                flowInfo.setDocNo(DOC_NO);
            }
            if (flowInfo.getCreated() == null) {
                flowInfo.setCreated(now);
            }
            if (flowInfo.getModified() == null) {
                flowInfo.setModified(now);
            }

        }

        List<Object> objectList = flowInfoList.stream()
                .map(item -> (Object) item)
                .collect(Collectors.toList());

        InsertBatchSQL sql = Inserts.create(InsertMode.DEFAULT)
                .into(tableName)
                .columns(StandardFundBillFlowInfoFieldEnum.getInsertFields())
                .valueForEntities(objectList)
                .batch()
                .toSql();

        jdbcTemplate.batchUpdate(sql.getSqlPattern(), sql.getArguments());
        return flowInfoList.size();

        // 构建批量插入SQL
//        StringBuilder sqlBuilder = new StringBuilder();
//        sqlBuilder.append("INSERT INTO ").append(tableName)
//                .append(" (platform_code, shop_id, account_id, occurred_at, rule_id, category_code, ")
//                .append("category_id, sub_category_id, amount, income_expense_direction, bill_no, ")
//                .append("order_no, doc_no, remark, is_offset , source, ")
//                .append("creator, created, batch_no, biz_key, company_id)")
//                .append(" VALUES ");
//
//        List<Object> params = new ArrayList<>();
//        Date now = new Date();
//
//        for (int i = 0; i < flowInfoList.size(); i++) {
//            StandardFundBillFlowInfoDO flowInfo = flowInfoList.get(i);
//
//            // 设置默认值
//            if (flowInfo.getSource() == null) {
//                flowInfo.setSource(1);
//            }
//            if (flowInfo.getDocNo() == null) {
//                flowInfo.setDocNo(DOC_NO);
//            }
//            if (flowInfo.getCreated() == null) {
//                flowInfo.setCreated(now);
//            }
//
//            if (i > 0) {
//                sqlBuilder.append(", ");
//            }
//            sqlBuilder.append("(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
//
//            // 添加参数
//            params.add(flowInfo.getPlatformCode());
//            params.add(flowInfo.getShopId());
//            params.add(flowInfo.getAccountId());
//            params.add(flowInfo.getOccurredAt());
//            params.add(flowInfo.getRuleId());
//            params.add(flowInfo.getCategoryCode());
//            params.add(flowInfo.getCategoryId());
//            params.add(flowInfo.getSubCategoryId());
//            params.add(flowInfo.getAmount());
//            params.add(flowInfo.getIncomeExpenseDirection());
//            params.add(flowInfo.getBillNo());
//            params.add(flowInfo.getOrderNo());
//            params.add(flowInfo.getDocNo());
//            params.add(flowInfo.getRemark());
//            params.add(flowInfo.getIsOffset());
//            params.add(flowInfo.getSource());
//            params.add(flowInfo.getCreator());
//            params.add(flowInfo.getCreated());
//            params.add(flowInfo.getBatchNo());
//            params.add(flowInfo.getBizKey());
//            params.add(flowInfo.getCompanyId());
//        }
//
//        // 执行批量插入
//        return jdbcTemplate.update(sqlBuilder.toString(), params.toArray());
    }
    
    @Override
    public Integer updateIsSplitById(Long belongId, int isSplit) {
        SQL sql = Updates.create()
                .table(tableName)
                .where(
                        Conditions.and(Columns.toColumn(StandardFundBillFlowInfoFieldEnum.ID.getFieldCode()), LinkMode.EQUAL, belongId)
                )
                .update(
                        ColumnValues.create(StandardFundBillFlowInfoFieldEnum.IS_SPLIT.getFieldCode(), isSplit),
                        ColumnValues.create(StandardFundBillFlowInfoFieldEnum.MODIFIED.getFieldCode(), new Date())
                ).toSql();

        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        return jdbcTemplate.update(sql.getSqlCode(), args);
    }

    @Override
    public void deleteByAccountIdAndDataRange(Long companyId, Long accountId, Integer dataRange) {
        AsserUtils.notNull(companyId, "公司ID不能为空！");
        AsserUtils.notNull(accountId, "资金账户ID不能为空！");
        AsserUtils.notNull(dataRange, "账期不能为空！");

        SQL sql = Deletes.create()
                .from(tableName)
                .where(
                        Conditions.and(StandardFundBillFlowInfoFieldEnum.COMPANY_ID.getFieldCode(), LinkMode.EQUAL, companyId),
                        Conditions.and(StandardFundBillFlowInfoFieldEnum.ACCOUNT_ID.getFieldCode(), LinkMode.EQUAL, accountId),
                        Conditions.and(StandardFundBillFlowInfoFieldEnum.DATA_RANGE.getFieldCode(), LinkMode.EQUAL, dataRange)
                ).toSql();
        Object[] updateArgs = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        jdbcTemplate.update(sql.getSqlCode(),updateArgs);

    }

    @Override
    public Integer batchInsert(List<StandardFundBillFlowInfoDO> flowInfoList) {
        AsserUtils.notEmpty(flowInfoList, "流水信息列表不能为空");

        // 设置默认值
        Date now = new Date();
        for (StandardFundBillFlowInfoDO flowInfo : flowInfoList) {
            if (flowInfo.getCreated() == null) {
                flowInfo.setCreated(now);
            }
            if (flowInfo.getModified() == null) {
                flowInfo.setModified(now);
            }
        }

        List<Object> objectList = flowInfoList.stream()
                .map(item -> (Object) item)
                .collect(Collectors.toList());
        InsertBatchSQL sql = Inserts.create(InsertMode.DEFAULT)
                .into(tableName)
                .columns(StandardFundBillFlowInfoFieldEnum.getInsertFields())
                .valueForEntities(objectList)
                .batch()
                .toSql();
        jdbcTemplate.batchUpdate(sql.getSqlPattern(), sql.getArguments());
        return flowInfoList.size();

    }

    @Override
    public Integer confirmByAccountIdAndDataRange(Long companyId, Long accountId, Integer dataRange) {
        AsserUtils.notNull(companyId, "companyId不能为空");
        AsserUtils.notNull(accountId, "accountId不能为空");
        AsserUtils.notNull(dataRange, "dataRange不能为空");
        SQL sql = Updates.create()
                .table(tableName)
                .where(
                        Conditions.and(Columns.toColumn(StandardFundBillFlowInfoFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId),
                        Conditions.and(Columns.toColumn(StandardFundBillFlowInfoFieldEnum.ACCOUNT_ID.getFieldCode()), LinkMode.EQUAL, accountId),
                        Conditions.and(Columns.toColumn(StandardFundBillFlowInfoFieldEnum.DATA_RANGE.getFieldCode()), LinkMode.EQUAL, dataRange)
                )
                .update(
                        ColumnValues.create(StandardFundBillFlowInfoFieldEnum.CONFIRMED.getFieldCode(), YesOrNoEnum.YES.getValue()),
                        ColumnValues.create(StandardFundBillFlowInfoFieldEnum.MODIFIED.getFieldCode(), new Date())
                ).toSql();

        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        return jdbcTemplate.update(sql.getSqlCode(), args);

    }

    @Override
    public List<BillSummaryRecordDO> getBillSummary(Long accountId, Pair<Date, Date> dateDatePair, Long companyId) {
        if (accountId == null || dateDatePair == null || companyId == null) {
            return Collections.emptyList();
        }

        SQL sql = Queries.create()
                .from(tableName)
                .where(
                        Conditions.and(Columns.toColumn(StandardFundBillFlowInfoFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, 1),
                        Conditions.and(Columns.toColumn(StandardFundBillFlowInfoFieldEnum.OCCURRED_AT.getFieldCode()), LinkMode.GREATER_THAN_EQUAL, "2025-07-01 00:00:00"),
                        Conditions.and(Columns.toColumn(StandardFundBillFlowInfoFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.LESS_THAN_EQUAL, "2025-07-01 00:00:00"),
                        Conditions.and(Columns.toColumn(StandardFundBillFlowInfoFieldEnum.ACCOUNT_ID.getFieldCode()), LinkMode.EQUAL, "4")
                )
                .groupBy(
                        Columns.toColumn(StandardFundBillFlowInfoFieldEnum.CATEGORY_CODE.getFieldCode()),
                        Columns.toColumn(StandardFundBillFlowInfoFieldEnum.CATEGORY_ID.getFieldCode()),
                        Columns.toColumn(StandardFundBillFlowInfoFieldEnum.SUB_CATEGORY_ID.getFieldCode()),
                        Columns.toColumn(StandardFundBillFlowInfoFieldEnum.SHOP_ID.getFieldCode())
                )
                .select(
                        Functions.sum(StandardFundBillFlowInfoFieldEnum.AMOUNT.getFieldCode()).alias("amount"),
                        Columns.toColumn(StandardFundBillFlowInfoFieldEnum.CATEGORY_CODE.getFieldCode()),
                        Columns.toColumn(StandardFundBillFlowInfoFieldEnum.CATEGORY_ID.getFieldCode()),
                        Columns.toColumn(StandardFundBillFlowInfoFieldEnum.SUB_CATEGORY_ID.getFieldCode()),
                        Columns.toColumn(StandardFundBillFlowInfoFieldEnum.SHOP_ID.getFieldCode()),
                        Columns.toColumn(StandardFundBillFlowInfoFieldEnum.ACCOUNT_ID.getFieldCode())
                )
                .toSql();

        // 构建SQL并执行查询
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        return jdbcTemplate.query(
                sql.getSqlCode(),
                new BeanPropertyRowMapper<>(BillSummaryRecordDO.class),
                args
        );
    }

    /**
     * 构建多表查询条件（带表别名）
     * @param companyId 公司ID
     * @param parameter 查询条件
     * @return 条件列表
     */
    private List<ConditionComponent<?>> getConditionComponentsWithAlias(Long companyId, StandardFundBillFlowParameter parameter) {
        List<ConditionComponent<?>> conditions = new ArrayList<>();

        // 公司ID条件（必须）- 指定使用主表的company_id
        conditions.add(Conditions.and(
                Columns.create(StandardFundBillFlowInfoFieldEnum.COMPANY_ID.getFieldCode()).referenceTableAlias("bill"),
                LinkMode.EQUAL,
                companyId));

        if (ObjectUtils.isEmpty(parameter)){
            return conditions;
        }

        //发生时间
        if (ObjectUtils.isNotEmpty(parameter.getStartTime()) && ObjectUtils.isNotEmpty(parameter.getEndTime())) {
            conditions.add(Conditions.and(
                    Columns.create(StandardFundBillFlowInfoFieldEnum.OCCURRED_AT.getFieldCode()).referenceTableAlias("bill"),
                    LinkMode.GREATER_THAN_EQUAL,
                    parameter.getStartTime()));
            conditions.add(Conditions.and(
                    Columns.create(StandardFundBillFlowInfoFieldEnum.OCCURRED_AT.getFieldCode()).referenceTableAlias("bill"),
                    LinkMode.LESS_THAN_EQUAL,
                    parameter.getEndTime()));
        }
        //平台
        if (ObjectUtils.isNotEmpty(parameter.getPlatformTypeList())) {
            conditions.add(Conditions.and(
                    Columns.create(StandardFundBillFlowInfoFieldEnum.PLATFORM_CODE.getFieldCode()).referenceTableAlias("bill"),
                    LinkMode.IN,
                    parameter.getPlatformTypeList()));
        }
        // 店铺ID条件（可选）
        if (ObjectUtils.isNotEmpty(parameter.getShopIdList())) {
            conditions.add(Conditions.and(
                    Columns.create(StandardFundBillFlowInfoFieldEnum.SHOP_ID.getFieldCode()).referenceTableAlias("bill"),
                    LinkMode.IN,
                    parameter.getShopIdList()));
        }
        // 资金账户ID条件（可选）
        if (ObjectUtils.isNotEmpty(parameter.getAccountIdList())) {
            conditions.add(Conditions.and(
                    Columns.create(StandardFundBillFlowInfoFieldEnum.ACCOUNT_ID.getFieldCode()).referenceTableAlias("bill"),
                    LinkMode.IN,
                    parameter.getAccountIdList()));
        }
        // 子类别ID条件（可选）
        if (ObjectUtils.isNotEmpty(parameter.getSubCategoryIdList())) {
            conditions.add(Conditions.and(
                    Columns.create(StandardFundBillFlowInfoFieldEnum.SUB_CATEGORY_ID.getFieldCode()).referenceTableAlias("bill"),
                    LinkMode.IN,
                    parameter.getSubCategoryIdList()));
        }
        //收支方向
        if (ObjectUtils.isNotEmpty(parameter.getIncomeExpenseDirection())) {
            conditions.add(Conditions.and(
                    Columns.create(StandardFundBillFlowInfoFieldEnum.INCOME_EXPENSE_DIRECTION.getFieldCode()).referenceTableAlias("bill"),
                    LinkMode.EQUAL,
                    parameter.getIncomeExpenseDirection()));
        }
        //关联订单流水号
        if (ObjectUtils.isNotEmpty(parameter.getBillNoList())) {
            conditions.add(Conditions.and(
                    Columns.create(StandardFundBillFlowInfoFieldEnum.BILL_NO.getFieldCode()).referenceTableAlias("bill"),
                    LinkMode.IN,
                    parameter.getBillNoList()));
        }
        //关联账户订单号
        if (ObjectUtils.isNotEmpty(parameter.getOrderNoList())) {
            conditions.add(Conditions.and(
                    Columns.create(StandardFundBillFlowInfoFieldEnum.ORDER_NO.getFieldCode()).referenceTableAlias("bill"),
                    LinkMode.IN,
                    parameter.getOrderNoList()));
        }
        //关联业务单据号
        if (StringUtils.isNotEmpty(parameter.getDocNo())) {
            conditions.add(Conditions.and(
                    Columns.create(StandardFundBillFlowInfoFieldEnum.DOC_NO.getFieldCode()).referenceTableAlias("bill"),
                    LinkMode.EQUAL,
                    parameter.getDocNo()));
        }
        //备注
        if (StringUtils.isNotEmpty(parameter.getRemark())) {
            conditions.add(Conditions.and(
                    Columns.create(StandardFundBillFlowInfoFieldEnum.REMARK.getFieldCode()).referenceTableAlias("bill"),
                    LinkMode.LIKE_FULL_MATCH,
                    parameter.getRemark()));
        }
        //是否可抵消
        if (ObjectUtils.isNotEmpty(parameter.getIsOffset())) {
            conditions.add(Conditions.and(
                    Columns.create(StandardFundBillFlowInfoFieldEnum.IS_OFFSET.getFieldCode()).referenceTableAlias("bill"),
                    LinkMode.EQUAL,
                    parameter.getIsOffset()));
        }
        //是否手动拆分
        if (ObjectUtils.isNotEmpty(parameter.getManualSplit())) {
            if (parameter.getManualSplit() == 1) {
                conditions.add(Conditions.and(
                        Columns.create(StandardFundBillFlowInfoFieldEnum.BELONG_ID.getFieldCode()).referenceTableAlias("bill"),
                        LinkMode.IS_NOT_NULL,
                        null));
            }else {
                conditions.add(Conditions.and(
                        Columns.create(StandardFundBillFlowInfoFieldEnum.BELONG_ID.getFieldCode()).referenceTableAlias("bill"),
                        LinkMode.IS_NULL,
                        null));
            }
        }
        //是否关联业务单据
        if (ObjectUtils.isNotEmpty(parameter.getIsRelatedDoc())) {
            if (parameter.getIsRelatedDoc() == 1) {
                conditions.add(Conditions.and(
                        Columns.create(StandardFundBillFlowInfoFieldEnum.DOC_NO.getFieldCode()).referenceTableAlias("bill"),
                        LinkMode.NOT_EQUAL,
                        DOC_NO));
            }else {
                conditions.add(Conditions.and(
                        Columns.create(StandardFundBillFlowInfoFieldEnum.DOC_NO.getFieldCode()).referenceTableAlias("bill"),
                        LinkMode.EQUAL,
                        DOC_NO));
            }
        }
        //确认状态
        if (ObjectUtils.isNotEmpty(parameter.getConfirmed())) {
            conditions.add(Conditions.and(
                    Columns.create(StandardFundBillFlowInfoFieldEnum.CONFIRMED.getFieldCode()).referenceTableAlias("bill"),
                    LinkMode.EQUAL,
                    parameter.getConfirmed()));
        }
        //来源方式
        if (ObjectUtils.isNotEmpty(parameter.getSource())) {
            conditions.add(Conditions.and(
                    Columns.create(StandardFundBillFlowInfoFieldEnum.SOURCE.getFieldCode()).referenceTableAlias("bill"),
                    LinkMode.EQUAL,
                    parameter.getSource()));
        }
        return conditions;
    }
}
