package com.raycloud.dmj.account.core.pageconfig.request;

import lombok.Data;

import java.util.List;

/**
 * 批量更新页面列配置请求
 * <AUTHOR>
 */
@Data
public class BatchUpdatePageColumnConfigRequest {

    /**
     * 配置列表
     */
    private List<PageColumnConfigUpdateItem> configList;

    @Data
    public static class PageColumnConfigUpdateItem {
        
        /**
         * 配置ID
         */
        private Long id;

        /**
         * 列编码
         */
        private String colCode;

        /**
         * 列名称
         */
        private String colTitle;

        /**
         * 列宽
         */
        private Integer width;

        /**
         * 排序
         */
        private Integer sort;

        /**
         * 是否可见(0 不可见 1可见)
         */
        private Integer visible;
    }
}
