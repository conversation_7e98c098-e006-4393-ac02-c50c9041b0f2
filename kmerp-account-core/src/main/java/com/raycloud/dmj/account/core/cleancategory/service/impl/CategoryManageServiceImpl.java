package com.raycloud.dmj.account.core.cleancategory.service.impl;

import com.google.common.collect.Lists;
import com.raycloud.dmj.account.core.base.dao.CategoryDao;
import com.raycloud.dmj.account.core.base.dao.CategoryGroupDao;
import com.raycloud.dmj.account.core.base.dao.FundAccountDao;
import com.raycloud.dmj.account.core.base.dao.SubCategoryDao;
import com.raycloud.dmj.account.core.cleancategory.domain.QueryCategoryParam;
import com.raycloud.dmj.account.core.cleancategory.domain.QuerySubCategoryParam;
import com.raycloud.dmj.account.core.cleancategory.domain.SubCategoryDTO;
import com.raycloud.dmj.account.core.cleancategory.domain.object.CategoryDO;
import com.raycloud.dmj.account.core.cleancategory.domain.object.CategoryGroupDO;
import com.raycloud.dmj.account.core.cleancategory.domain.object.FundAccountDO;
import com.raycloud.dmj.account.core.cleancategory.domain.object.SubCategoryDO;
import com.raycloud.dmj.account.core.cleancategory.domain.request.*;
import com.raycloud.dmj.account.core.cleancategory.domain.vo.CategoryGroupVO;
import com.raycloud.dmj.account.core.cleancategory.domain.vo.SimpleSubCategoryVo;
import com.raycloud.dmj.account.core.cleancategory.domain.vo.SubCategoryVO;
import com.raycloud.dmj.account.core.cleancategory.mapstruct.CategoryMapStruct;
import com.raycloud.dmj.account.core.cleancategory.service.CategoryManageService;
import com.raycloud.dmj.account.core.common.Page;
import com.raycloud.dmj.account.core.common.PageListBase;
import com.raycloud.dmj.account.core.common.TreeVO;
import com.raycloud.dmj.account.infra.common.BusinessException;
import com.raycloud.dmj.account.infra.common.ErrorCodeEnum;
import com.raycloud.dmj.account.infra.session.AccountUser;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import com.raycloud.dmj.account.infra.utils.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 分类管理服务实现
 *
 * <AUTHOR>
 */
@Service
public class CategoryManageServiceImpl implements CategoryManageService {

    CategoryMapStruct convert = CategoryMapStruct.INSTANCE;

    @Resource
    private CategoryDao categoryDao;

    @Resource
    private SubCategoryDao subCategoryDao;

    @Resource
    private CategoryGroupDao categoryGroupDao;

    @Resource
    private FundAccountDao fundAccountDao;


    @Override
    public Long addCategory(AccountUser accountUser, AddCategoryReq req) {
        AsserUtils.notNull(accountUser, "用户不能为空！");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空！");
        AsserUtils.notNull(req, "参数不能为空！");
        AsserUtils.notEmpty(req.getPlatformCode(), "平台编码不能为空！");
        AsserUtils.notEmpty(req.getCategoryName(), "分类名称不能为空！");
        AsserUtils.notNull(req.getFundAccountId(), "资金账户ID不能为空！");
        AsserUtils.notNull(req.getIncomeExpenseObjectId(), "收支对象ID不能为空！");
        AsserUtils.notNull(req.getOffset(), "是否抵消不能为空！");
        //同一个资金账户下只有一个相同的类别名称
        CategoryDO categoryDO = categoryDao.getByPlatformAndFundIdAndCategoryAndName(
                accountUser.getCompanyId(),
                req.getFundAccountId(),
                req.getPlatformCode(),
                req.getCategoryName()
        );
        Long categoryId;
        if (Objects.isNull(categoryDO)) {
            categoryId = categoryDao.insert(convert.toCategoryDO(accountUser, req));
        } else {
            categoryId = categoryDO.getId();
        }
        SubCategoryDO subCategory = subCategoryDao.queryByCategoryIdAndName(accountUser.getCompanyId(), categoryId, req.getSubCategoryName());
        if (Objects.nonNull(subCategory)) {
            //同一资金账户，子类别名称不能重复
            throw new BusinessException(ErrorCodeEnum.SUB_CATEGORY_NAME_EXISTS);
        }
        SubCategoryDO subCategoryDO = convert.toSubCategoryDO(accountUser, req);
        subCategoryDO.setCategoryId(categoryId);
        return subCategoryDao.insert(subCategoryDO);
    }

    @Override
    public void editCategoryGroup(AccountUser user, EditCategoryGroupReq req) {
        AsserUtils.notNull(user, "用户不能为空！");
        AsserUtils.notNull(user.getCompanyId(), "公司ID不能为空！");
        AsserUtils.notNull(req, "参数不能为空！");
        AsserUtils.notNull(req.getCategoryGroupCode(), "分类编码不能为空");
        AsserUtils.notNull(req.getCategoryGroupName(), "分类名称不能为空");
        CategoryGroupDO categoryGroupDO = convert.toCategoryGroupDO(user, req);
        categoryGroupDao.updateByCode(categoryGroupDO);
    }

    @Override
    public PageListBase<SubCategoryVO> pageQueryCategoryList(AccountUser accountUser, QueryCategoryReq req) {
        // 1. 前置参数校验
        validateQueryParams(accountUser, req);

        // 2. 构建查询参数
        QuerySubCategoryParam queryParam = buildQuerySubCategoryParam(req);

        // 3. 查询总数
        Long total = subCategoryDao.countByParam(accountUser.getCompanyId(), queryParam);
        Page page = new Page(req.getPageNo(), req.getPageSize());
        if (total <= 0) {
            return new PageListBase<>(Collections.emptyList(), page, 0L);
        }

        // 4. 分页查询核心数据
        List<SubCategoryDTO> subCategoryDTOList = subCategoryDao.pageQueryByParam(
                accountUser.getCompanyId(), queryParam, page
        );
        if (CollectionUtils.isEmpty(subCategoryDTOList)) {
            return new PageListBase<>(Collections.emptyList(), page, 0L);
        }

        // 5. 构建关联映射（拆分映射构建逻辑，复用通用转换逻辑）
        Map<String, String> categoryGroupCodeMap = buildCategoryGroupCodeMap(accountUser, subCategoryDTOList);
        Map<Long, String> fundAccountIdMap = buildFundAccountIdMap(accountUser, subCategoryDTOList);

        // 6. 转换VO（提取转换逻辑，主流程更简洁）
        List<SubCategoryVO> subCategoryVOList = convertToSubCategoryVOList(
                subCategoryDTOList, categoryGroupCodeMap, fundAccountIdMap
        );

        // 7. 返回结果
        return new PageListBase<>(subCategoryVOList, page, total);
    }

    /**
     * 校验入参合法性
     */
    private void validateQueryParams(AccountUser accountUser, QueryCategoryReq req) {
        AsserUtils.notNull(accountUser, "用户不能为空！");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空！");
        AsserUtils.notNull(req, "参数不能为空！");
        AsserUtils.notEmpty(req.getPlatformCode(), "平台编码不能为空！");
    }

    /**
     * 构建子类别查询参数
     */
    private QuerySubCategoryParam buildQuerySubCategoryParam(QueryCategoryReq req) {
        QuerySubCategoryParam param = new QuerySubCategoryParam();
        param.setPlatformCode(req.getPlatformCode());
        param.setCategoryGroupCodes(req.getCategoryGroupCodes());
        param.setCategoryIds(req.getCategoryIds());
        param.setOffset(req.getOffset());
        param.setFundAccountIds(req.getFundAccountIds());
        return param;
    }

    /**
     * 构建 类别组编码 -> 名称 映射
     */
    private Map<String, String> buildCategoryGroupCodeMap(
            AccountUser accountUser, List<SubCategoryDTO> subCategoryDTOList) {
        Set<String> categoryGroupCodes = subCategoryDTOList.stream()
                .map(SubCategoryDTO::getCategoryGroupCode)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(categoryGroupCodes)) {
            return Collections.emptyMap();
        }

        List<CategoryGroupDO> categoryGroupDOList = categoryGroupDao.queryByCodes(
                accountUser.getCompanyId(), categoryGroupCodes
        );
        return Optional.ofNullable(categoryGroupDOList)
                .orElse(Collections.emptyList())
                .stream()
                .collect(Collectors.toMap(
                        CategoryGroupDO::getCode,
                        CategoryGroupDO::getName,
                        (v1, v2) -> v1,
                        HashMap::new
                ));
    }

    /**
     * 构建 资金账户ID -> 名称 映射
     */
    private Map<Long, String> buildFundAccountIdMap(
            AccountUser accountUser, List<SubCategoryDTO> subCategoryDTOList
    ) {
        Set<Long> fundAccountIds = subCategoryDTOList.stream()
                .map(SubCategoryDTO::getFundAccountId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(fundAccountIds)) {
            return Collections.emptyMap();
        }

        List<FundAccountDO> fundAccountDOList = fundAccountDao.queryByIds(
                accountUser.getCompanyId(), fundAccountIds
        );
        return Optional.ofNullable(fundAccountDOList)
                .orElse(Collections.emptyList())
                .stream()
                .collect(Collectors.toMap(
                        FundAccountDO::getId,
                        FundAccountDO::getAccountName,
                        (v1, v2) -> v1,
                        HashMap::new
                ));
    }

    /**
     * 将DTO列表转换为VO列表
     */
    private List<SubCategoryVO> convertToSubCategoryVOList(
            List<SubCategoryDTO> subCategoryDTOList,
            Map<String, String> categoryGroupCodeMap,
            Map<Long, String> fundAccountIdMap
    ) {
        return subCategoryDTOList.stream().map(dto -> {
            SubCategoryVO vo = new SubCategoryVO();
            vo.setId(dto.getId());
            vo.setCreated(dto.getCreated());
            vo.setModified(dto.getModified());
            vo.setCategoryGroupCode(dto.getCategoryGroupCode());
            vo.setCategoryGroupName(categoryGroupCodeMap.get(dto.getCategoryGroupCode()));
            vo.setFundAccountId(dto.getFundAccountId());
            vo.setFundAccountName(fundAccountIdMap.get(dto.getFundAccountId()));
            vo.setCategoryId(dto.getCategoryId());
            vo.setCategoryName(dto.getCategoryName());
            vo.setSubCategoryId(dto.getId());
            vo.setSubCategoryName(dto.getSubCategoryName());
            vo.setOffset(dto.getOffset());
            vo.setIncomeExpenseObjectId(dto.getIncomeExpenseObjectId());
            // TODO: 收支对象名称映射待实现（建议补充类似 buildIncomeExpenseObjectMap 方法）
            // vo.setIncomeExpenseObjectName(incomeExpenseObjectIdToNameMap.get(dto.getIncomeExpenseObjectId()));
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public void editCategory(AccountUser user, EditCategoryReq req) {
        AsserUtils.notNull(user, "用户不能为空！");
        AsserUtils.notNull(user.getCompanyId(), "公司ID不能为空！");
        AsserUtils.notNull(req, "参数不能为空！");
        AsserUtils.notNull(req.getId(), "ID不能为空！");
        AsserUtils.notEmpty(req.getPlatformCode(), "平台编码不能为空！");
        AsserUtils.notEmpty(req.getCategoryName(), "分类名称不能为空！");
        AsserUtils.notNull(req.getFundAccountId(), "资金账户ID不能为空！");
        AsserUtils.notNull(req.getIncomeExpenseObjectId(), "收支对象ID不能为空！");
        AsserUtils.notNull(req.getOffset(), "是否抵消不能为空！");

        //同一个公司和平台和分类下只有一个相同的类别名称
        CategoryDO categoryDO = categoryDao.getByPlatformAndFundIdAndCategoryAndName(
                user.getCompanyId(),
                req.getFundAccountId(),
                req.getPlatformCode(),
                req.getCategoryName());
        Long categoryId;
        if (Objects.isNull(categoryDO)) {
            categoryId = categoryDao.insert(convert.toCategoryDO(user, req));
        } else {
            categoryId = categoryDO.getId();
        }
        SubCategoryDO subCategory = subCategoryDao.queryByCategoryIdAndName(user.getCompanyId(), categoryId, req.getSubCategoryName());
        if (Objects.nonNull(subCategory) && !Objects.equals(req.getId(), subCategory.getId())) {
            //同一资金账户，子类别名称不能重复
            throw new BusinessException(ErrorCodeEnum.SUB_CATEGORY_NAME_EXISTS.getCode(), "该资金账户下，子类别名称已存在！");
        }
        SubCategoryDO subCategoryDO = convert.toSubCategoryDO(user, req);
        subCategoryDO.setCategoryId(categoryId);
        subCategoryDao.updateById(subCategoryDO);

    }

    @Override
    public void deleteCategory(AccountUser accountUser, DeleteCategoryReq req) {
        AsserUtils.notNull(accountUser, "用户不能为空！");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空！");
        AsserUtils.notNull(req, "参数不能为空！");
        AsserUtils.notEmpty(req.getIds(), "ID集合不能为空！");
        subCategoryDao.deleteByIds(accountUser.getCompanyId(), req.getIds());
    }

    @Override
    public List<TreeVO> queryCategoryGroupTree(AccountUser accountUser, QueryCategoryGroupTreeReq req) {
        AsserUtils.notNull(accountUser, "用户不能为空！");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空！");
        List<CategoryGroupDO> categoryGroupDOList = categoryGroupDao.list(accountUser.getCompanyId());
        if (CollectionUtils.isEmpty(categoryGroupDOList)) {
            return Lists.newArrayList();
        }
        return categoryGroupDOList.stream()
                .map(x -> convert.toTreeVO(x))
                .collect(Collectors.toList());
    }

    @Override
    public List<TreeVO> queryCategoryTree(AccountUser accountUser, QueryCategoryTreeReq req) {
        AsserUtils.notNull(accountUser, "用户不能为空！");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空！");
        AsserUtils.notNull(req, "参数不能为空！");
        AsserUtils.notNull(req.getPlatformCode(), "平台编码不能为空");
        QueryCategoryParam queryCategoryParam = new QueryCategoryParam();
        queryCategoryParam.setPlatformCode(req.getPlatformCode());
        queryCategoryParam.setCategoryGroupCodes(req.getCategoryGroupCodes());
        List<CategoryDO> categoryDOList = categoryDao.queryByParam(accountUser.getCompanyId(), queryCategoryParam);
        if (CollectionUtils.isEmpty(categoryDOList)) {
            return Lists.newArrayList();
        }
        Set<Long> categoryIds = categoryDOList.stream().map(CategoryDO::getId).collect(Collectors.toSet());
        List<SubCategoryDO> subCategoryDOList = subCategoryDao.listByCategoryIds(accountUser.getCompanyId(), categoryIds);
        //根据类别ID分组
        List<TreeVO> treeVOList = new ArrayList<>();
        for (CategoryDO categoryDO : categoryDOList) {
            TreeVO treeVO = convert.toTreeVO(categoryDO);
            treeVOList.add(treeVO);
        }
        if (CollectionUtils.isEmpty(subCategoryDOList)) {
            return treeVOList;
        }
        Map<Long, List<SubCategoryDO>> categoryIdToSubCategoryDOListMap = subCategoryDOList.stream()
                .collect(Collectors.groupingBy(SubCategoryDO::getCategoryId));

        for (TreeVO treeVO : treeVOList) {
            List<TreeVO> children = new ArrayList<>();
            List<SubCategoryDO> categoryIdToSubCategoryDOList = categoryIdToSubCategoryDOListMap.get((Long) treeVO.getValue());
            if (CollectionUtils.isEmpty(categoryIdToSubCategoryDOList)) {
                //跳过本次循环
                continue;
            }
            for (SubCategoryDO subCategoryDO : categoryIdToSubCategoryDOList) {
                TreeVO subCategoryTreeVO = convert.toTreeVO(subCategoryDO);
                children.add(subCategoryTreeVO);
            }
            treeVO.setChildren(children);
        }
        return treeVOList;
    }

    @Override
    public List<TreeVO> querySubCategoryTree(AccountUser accountUser, QuerySubCategoryTreeReq req) {
        AsserUtils.notNull(accountUser, "用户不能为空！");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空！");
        AsserUtils.notNull(req, "参数不能为空！");
        AsserUtils.notNull(req.getPlatformCode(), "平台编码不能为空");
        QuerySubCategoryParam querySubCategoryParam = new QuerySubCategoryParam();
        List<SubCategoryDO> subCategoryDOList = subCategoryDao.queryByParam(accountUser.getCompanyId(), querySubCategoryParam);
        if (CollectionUtils.isEmpty(subCategoryDOList)) {
            return Lists.newArrayList();
        }
        return subCategoryDOList.stream()
                .map(x -> convert.toTreeVO(x))
                .collect(Collectors.toList());
    }

    @Override
    public List<CategoryGroupVO> listCategoryGroup(AccountUser accountUser, QueryCategoryGroupReq req) {
        List<CategoryGroupDO> categoryGroupDOList = categoryGroupDao.list(accountUser.getCompanyId());
        if (CollectionUtils.isEmpty(categoryGroupDOList)) {
            return Lists.newArrayList();
        }
        return categoryGroupDOList.stream()
                .map(x -> convert.toCategoryGroupVO(x))
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, CategoryGroupDO> getCategoryGroupMap(AccountUser accountUser) {
        //查询分类
        // 如果为null，返回空列表
        return Optional.ofNullable(categoryGroupDao.list(accountUser.getCompanyId()))
                .orElse(Collections.emptyList()) // 如果为null，返回空列表
                .stream()
                .collect(Collectors.toMap(CategoryGroupDO::getCode, Function.identity(), (a, b) -> a));
    }

    @Override
    public Map<Long, CategoryDO> getCategoryMap(AccountUser accountUser) {
        //查询类别
        // 如果为null，返回空列表
        // 如果为null，返回空列表
        return Optional.ofNullable(categoryDao.queryByCompanyId(accountUser.getCompanyId()))
                .orElse(Collections.emptyList()) // 如果为null，返回空列表
                .stream()
                .collect(Collectors.toMap(CategoryDO::getId, Function.identity(), (a, b) -> a));
    }

    @Override
    public Map<Long, SubCategoryDO> getSubCategoryMap(AccountUser accountUser) {
        //查询子类别
        // 如果为null，返回空列表
        return Optional.ofNullable(subCategoryDao.queryByCompanyId(accountUser.getCompanyId()))
                .orElse(Collections.emptyList()) // 如果为null，返回空列表
                .stream()
                .collect(Collectors.toMap(SubCategoryDO::getId, Function.identity(), (a, b) -> a));
    }

    @Override
    public Map<String, String> getCategoryGroupMap(Long companyId) {
        //查询分类
        // 如果为null，返回空列表
        return Optional.ofNullable(categoryGroupDao.list(companyId))
                .orElse(Collections.emptyList()) // 如果为null，返回空列表
                .stream()
                .collect(Collectors.toMap(CategoryGroupDO::getCode, CategoryGroupDO::getName));
    }

    @Override
    public Map<Long, String> getCategoryMap(Long companyId) {
        //查询类别
        // 如果为null，返回空列表
        // 如果为null，返回空列表
        return Optional.ofNullable(categoryDao.queryByCompanyId(companyId))
                .orElse(Collections.emptyList()) // 如果为null，返回空列表
                .stream()
                .collect(Collectors.toMap(CategoryDO::getId, CategoryDO::getName));
    }

    @Override
    public Map<Long, String> getSubCategoryMap(Long companyId) {
        //查询子类别
        // 如果为null，返回空列表
        return Optional.ofNullable(subCategoryDao.queryByCompanyId(companyId))
                .orElse(Collections.emptyList()) // 如果为null，返回空列表
                .stream()
                .collect(Collectors.toMap(SubCategoryDO::getId, SubCategoryDO::getName));
    }

    @Override
    public List<SimpleSubCategoryVo> getSimpleSubCategoryVoByAccountId(AccountUser accountUser, QuerySubCategoryReq req) {
        AsserUtils.notNull(accountUser, "用户不能为空！");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空！");
        AsserUtils.notNull(req, "参数不能为空！");
        AsserUtils.notEmpty(req.getPlatformCodeList(), "平台code集合不能为空！");

        QuerySubCategoryParam querySubCategoryParam = new QuerySubCategoryParam();
        querySubCategoryParam.setPlatformCodeList(req.getPlatformCodeList());
        querySubCategoryParam.setFundAccountIds(req.getAccountIdList());

        List<Long> categoryIdList = req.getCategoryIdList();
        if(CollectionUtils.isEmpty(categoryIdList)){
            List<CategoryDO> categoryDOList = categoryDao.queryByParam(accountUser.getCompanyId(), QueryCategoryParam.builder().platformCodeList(req.getPlatformCodeList()).fundAccountIds(req.getAccountIdList()).build());
            categoryIdList = categoryDOList.stream().map(CategoryDO::getId).collect(Collectors.toList());
        }

        querySubCategoryParam.setCategoryIds(categoryIdList);
        List<SubCategoryDO> subCategoryDOList = subCategoryDao.queryByParam(accountUser.getCompanyId(), querySubCategoryParam);
        return BeanUtils.copyList(subCategoryDOList, SimpleSubCategoryVo.class);
    }
}
