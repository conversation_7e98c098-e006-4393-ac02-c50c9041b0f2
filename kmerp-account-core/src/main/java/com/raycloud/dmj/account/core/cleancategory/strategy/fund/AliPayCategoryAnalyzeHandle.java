package com.raycloud.dmj.account.core.cleancategory.strategy.fund;

import com.raycloud.dmj.account.core.base.dao.FundAccountDao;
import com.raycloud.dmj.account.core.base.dao.TmallAlipayRawBillDao;
import com.raycloud.dmj.account.core.base.domain.StandardFundBillFlowInfoDO;
import com.raycloud.dmj.account.core.cleancategory.domain.object.FundAccountDO;
import com.raycloud.dmj.account.core.cleancategory.domain.object.TmallAlipayRawBillDataDO;
import com.raycloud.dmj.account.core.cleancategory.strategy.param.CategoryAnalyzeParam;
import com.raycloud.dmj.account.core.common.Page;
import com.raycloud.dmj.account.core.enums.AccountTypeEnum;
import com.raycloud.dmj.account.core.enums.IncomeExpenseDirectionEnum;
import com.raycloud.dmj.account.core.enums.RawDataSourceEnum;
import com.raycloud.dmj.account.core.enums.StandardFundBillSourceEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * 支付宝分类解析
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class AliPayCategoryAnalyzeHandle extends StandardFundCategoryAnalyzeHandle<TmallAlipayRawBillDataDO> {


    @Resource
    private TmallAlipayRawBillDao tmallAlipayRawBillDao;

    @Resource
    private FundAccountDao fundAccountDao;


    @Override
    protected FundAccountDO getFundAccount(CategoryAnalyzeParam param) {
        return fundAccountDao.getByShopIdAndType(param.getCompanyId(), param.getShopId(),
                AccountTypeEnum.TM_ALIPAY.getTypeCode());
    }

    @Override
    protected Pair<BigDecimal, BigDecimal> calculateBalance(CategoryAnalyzeParam param) {

        //查询期初余额
        TmallAlipayRawBillDataDO earliestTmallAlipayRawBillDataDO = tmallAlipayRawBillDao.getEarliestByDataRange(param.getCompanyId(),
                param.getShopId(),
                param.getDataRange());
        BigDecimal accountBalance = earliestTmallAlipayRawBillDataDO.getAccountBalance();
        BigDecimal incomeAmount = earliestTmallAlipayRawBillDataDO.getIncomeAmount();
        BigDecimal expenseAmount = earliestTmallAlipayRawBillDataDO.getExpenseAmount();
        BigDecimal startBalance = accountBalance.subtract(incomeAmount.add(expenseAmount));
        //查询期末余额
        TmallAlipayRawBillDataDO latestTmallAlipayRawBillDataDO = tmallAlipayRawBillDao.getLatestByDataRange(param.getCompanyId(), param.getShopId(), param.getDataRange());
        BigDecimal endBalance = latestTmallAlipayRawBillDataDO.getAccountBalance();
        return Pair.of(startBalance, endBalance);
    }

    @Override
    protected List<TmallAlipayRawBillDataDO> listPageRawData(CategoryAnalyzeParam param, Page page) {
        return tmallAlipayRawBillDao.listPageByDataRange(
                param.getCompanyId(),
                param.getShopId(),
                param.getDataRange(),
                page
        );
    }


    @Override
    protected void setStandardFundBillFlowInfoDO(StandardFundBillFlowInfoDO standardFundBillFlowInfoDO, TmallAlipayRawBillDataDO tmallAlipayRawBillDataDO) {
        standardFundBillFlowInfoDO.setOccurredAt(tmallAlipayRawBillDataDO.getOccurTime());
        //金额：原始数据表中有收入金额和支出金额字段，标准资金流水表中正数为收入，负数为支出
        BigDecimal amount;
        Integer incomeExpenseDirection;
        if (tmallAlipayRawBillDataDO.getIncomeAmount().compareTo(BigDecimal.ZERO) > 0){
            amount = tmallAlipayRawBillDataDO.getIncomeAmount();
            incomeExpenseDirection = IncomeExpenseDirectionEnum.INCOME.getCode();
        }else {
            amount = tmallAlipayRawBillDataDO.getExpenseAmount();
            incomeExpenseDirection = IncomeExpenseDirectionEnum.EXPENSE.getCode();
        }
        standardFundBillFlowInfoDO.setAmount(amount);
        standardFundBillFlowInfoDO.setIncomeExpenseDirection(incomeExpenseDirection);
        standardFundBillFlowInfoDO.setBillNo(tmallAlipayRawBillDataDO.getAccountFlowNo());
        standardFundBillFlowInfoDO.setOrderNo(tmallAlipayRawBillDataDO.getBizBaseOrderNo());
//                standardFundBillFlowInfoDO.setDocNo(x.getBizOrderNo());
        standardFundBillFlowInfoDO.setRemark(tmallAlipayRawBillDataDO.getRemark());
        standardFundBillFlowInfoDO.setDataRange(tmallAlipayRawBillDataDO.getBatchTime());
        standardFundBillFlowInfoDO.setSource(StandardFundBillSourceEnum.BILL.getCode());
        standardFundBillFlowInfoDO.setCreator(CREATOR);
        standardFundBillFlowInfoDO.setBatchNo(tmallAlipayRawBillDataDO.getBatchNo());
        standardFundBillFlowInfoDO.setBizKey(tmallAlipayRawBillDataDO.getBizKey());
    }


    @Override
    public boolean accord(RawDataSourceEnum source) {
        return RawDataSourceEnum.ALIPAY.equals(source);
    }
}
