package com.raycloud.dmj.account.core.base.domain;

import com.raycloud.dmj.account.core.common.BaseInfo;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 资金流水合计记录表实体类，对应数据库表 bill_summary_record
 * <AUTHOR>
 */
@Data
public class BillSummaryRecordDO extends BaseInfo {

    /**
     * 主键ID，自增
     */
    private Long id;

    /**
     * 资金账户ID
     */
    private Long accountId;

    /**
     * 流水类别ID
     */
    private Long categoryId;

    /**
     * 流水子类别ID
     */
    private Long subCategoryId;

    /**
     * 帐期
     */
    private Date billingCycle;

    /**
     * 金额(元)（正数收入，负数支出）
     */
    private BigDecimal amount;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 启用状态：0弃用，1正常
     */
    private Integer enableStatus;
}
