package com.raycloud.dmj.account.core.cleancategory.domain.object;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 集分宝原始交易数据表
 * <AUTHOR>
 */
@Data
public class TmallAlipayPointsRawBillDataDO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    private Long id;

    /**
     * 店铺简称
     */
    private String sortTitle;

    /**
     * 交易发生的月份，格式如202503
     */
    private Integer month;

    /**
     * 支付宝交易对应的流水号
     */
    private String alipayTransactionId;

    /**
     * 商家系统内的订单编号
     */
    private String merchantOrderId;

    /**
     * 所交易商品的名称
     */
    private String productName;

    /**
     * 订单的金额，单位为元
     */
    private BigDecimal orderAmount;

    /**
     * 集分宝等抵扣的金额，单位为元
     */
    private BigDecimal deductionAmount;

    /**
     * 业务实际发生的日期
     */
    private Date businessDate;

    /**
     * 收款的账户名称
     */
    private String receivingAccount;

    /**
     * 付款的账户名称
     */
    private String payingAccount;

    /**
     * 数据下载的时间
     */
    private Date downloadTime;

    /**
     * 执行下载操作的账户
     */
    private String downloadAccount;

    /**
     * 唯一键
     */
    private String bizKey;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 批次时间 格式如********
     */
    private Integer batchTime;

    /**
     * 租户ID
     */
    private Long companyId;

    /**
     * 店铺ID
     */
    private String shopId;

    /**
     * 创建时间
     */
    private Date created;
}