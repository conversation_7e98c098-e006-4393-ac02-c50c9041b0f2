package com.raycloud.dmj.account.core.rawdata.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;

import lombok.Data;
import lombok.extern.log4j.Log4j;

import java.io.File;
import java.util.*;

/**
 * easyexcel写入工具类
 * <AUTHOR>
 */

@Log4j
public class EasyExcelWriteUtil {


    /**
     * 写入excel
     *
     * @param file     保存的路径名
     * @param headList 表头
     * @param dataList 数据
     */
    public static void write(File file,String sheetName,  List<ExcelHead> headList, List<Map<String, Object>> dataList) {

        File parentDir = file.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            boolean dirsCreated = parentDir.mkdirs();
            if (!dirsCreated) {
                log.error("创建目录失败");
            }
        }
        ExcelWriterBuilder writerBuilder = EasyExcel.write();
        writerBuilder.file(file);
        writerBuilder.excelType(ExcelTypeEnum.XLSX);
        //自动关闭流
        writerBuilder.autoCloseStream(true);

        // 判断文件是否存在
        boolean fileExistsFlag = file.exists();

        // 如果文件已存在，则使用模板方式写入
        if (fileExistsFlag) {
            String tempFilepath = file.getParent() + File.separator + UUID.randomUUID() + ".xlsx";
            boolean sheetExists = false;
            try (ExcelReader excelReader = EasyExcel.read(file).build()) {
                List<ReadSheet> sheets = excelReader.excelExecutor().sheetList();
                for (ReadSheet sheet : sheets) {
                    if (sheetName.equals(sheet.getSheetName())) {
                        sheetExists = true;
                        break;
                    }
                }
            }
            // 如果sheet不存在则生成head
            if (!sheetExists){
                writerBuilder.head(convertHead(headList));
            }

            writerBuilder.withTemplate(file).file(tempFilepath)
                    .sheet(sheetName)
                    .doWrite(convertData(headList, dataList));

            if (file.delete()) {
                File tempFile = new File(tempFilepath);
                boolean renameSuccess = tempFile.renameTo(file);
                if (!renameSuccess) {
                    log.error("重命名失败");
                }
            } else {
                log.error("删除原文件失败");
            }
        } else {
            // 如果文件不存在，则使用普通方式写入
            writerBuilder.head(convertHead(headList)).sheet(sheetName)
                    .doWrite(convertData(headList, dataList));
        }
    }


    /**
     * 获取Excel的表头信息
     *
     * @param headList 表头
     * @return 表头
     */
    private static List<List<String>> convertHead(List<ExcelHead> headList) {
        List<List<String>> list = new ArrayList<>();
        for (ExcelHead head : headList) {
            list.add(Collections.singletonList(head.getTitle()));
        }
        return list;
    }

    /**
     * 封装数据
     *
     * @param headList 表头
     * @param dataList 数据
     * @return 数据
     */
    private static List<List<Object>> convertData(List<ExcelHead> headList, List<Map<String, Object>> dataList) {
        List<List<Object>> result = new ArrayList<>();
        // 数据转换为EasyExcel的格式
        for (Map<String, Object> data : dataList) {
            List<Object> row = new ArrayList<>();
            for (ExcelHead head : headList) {
                Object value = data.get(head.getFieldName());
                // null值的处理
                row.add(Optional.ofNullable(value).orElse(head.getNullValue()));
            }
            result.add(row);
        }
        return result;
    }


    @Data
    public static class ExcelHead {

        // 字段名称
        private String fieldName;
        // Excel表头显示值
        private String title;
        // 如果数据为null时，显示的值
        private Object nullValue;

    }
}
