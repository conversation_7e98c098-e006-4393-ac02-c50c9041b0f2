package com.raycloud.dmj.account.core.enums.field;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 分类组表字段枚举
 * <AUTHOR>
 */
@Getter
public enum CategoryGroupFieldEnum {

    ID("id", "主键ID"),
    CREATED("created", "创建时间"),
    MODIFIED("modified", "修改时间"),
    SOURCE("source", "来源（1-系统，2-手动）"),
    CODE("code", "类型编码"),
    NAME("name", "分类名称"),
    COMPANY_ID("company_id", "公司ID");

    /**
     * 字段编码
     */
    private final String fieldCode;

    /**
     * 字段描述
     */
    private final String fieldDesc;

    CategoryGroupFieldEnum(String fieldCode, String fieldDesc) {
        this.fieldCode = fieldCode;
        this.fieldDesc = fieldDesc;
    }

    /**
     * 获取插入字段
     * @return 插入字段
     */
    public static Set<String> getInsertFields() {
        List<CategoryGroupFieldEnum> filterField = Arrays.asList(
                CategoryGroupFieldEnum.ID
        );
        return Arrays.stream(CategoryGroupFieldEnum.values()).filter(x -> !filterField.contains(x)).map(
                x -> x.fieldCode
        ).collect(Collectors.toSet());

    }
}