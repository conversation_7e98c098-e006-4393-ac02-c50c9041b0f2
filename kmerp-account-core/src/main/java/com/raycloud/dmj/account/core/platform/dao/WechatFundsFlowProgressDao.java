package com.raycloud.dmj.account.core.platform.dao;

import com.raycloud.dmj.account.core.platform.base.domain.ShopAuthInfoDO;
import com.raycloud.dmj.account.core.platform.base.domain.WechatFundsFlowProgressDO;

public interface WechatFundsFlowProgressDao {

    WechatFundsFlowProgressDO selectByShopIdCompanyId(Long shopId, Long companyId);

    int insertOrUpdate(WechatFundsFlowProgressDO record);
}
