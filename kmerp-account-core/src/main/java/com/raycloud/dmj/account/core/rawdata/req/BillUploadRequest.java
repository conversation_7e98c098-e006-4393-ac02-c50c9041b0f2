package com.raycloud.dmj.account.core.rawdata.req;

import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;


/**
 * 账单上传请求实体类，用于接收表单数据
 *
 * <AUTHOR>
 */
@Data
public class BillUploadRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 账单类型：1-日账单，2-月账单，3-年账单
     */
    private Integer billType;

    /**
     * 开始时间，格式根据账单类型不同：
     * - 日账单：YYYY-MM-DD
     * - 月账单：YYYY-MM
     * - 年账单：YYYY
     */
    private String startTime;

    /**
     * 结束时间，格式根据账单类型不同：
     * - 日账单：YYYY-MM-DD
     * - 月账单：YYYY-MM
     * - 年账单：YYYY
     */
    private String endTime;

    /**
     * 文件类型：xlsx, csv, xls, zip
     */
    private String fileType;

    /**
     * 数据类型描述
     */
    private String dataType;

    /**
     * 公司ID
     */
    private Long companyId;


    /**
     * 当前店铺id
     */
    private Long shopId;

    /**
     * 上传的文件
     */
    private MultipartFile file;
}