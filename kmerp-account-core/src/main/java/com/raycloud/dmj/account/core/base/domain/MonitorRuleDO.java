package com.raycloud.dmj.account.core.base.domain;

import com.raycloud.dmj.account.core.enums.DateTypeEnum;
import com.raycloud.dmj.account.core.enums.RawDataSourceEnum;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
@Data
public class MonitorRuleDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    private Long id;

    /**
     * 创建时间
     */
    private LocalDateTime created;

    /**
     * 更新时间
     */
    private LocalDateTime modified;

    /**
     * 监控规则
     */
    private String monitorRule;

    /**
     * 公司ID
     */
    private Long companyId;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 数据类型：支付宝、微信、集分宝等
     */
    private Integer dataSource;

    /**
     * 账单类型：1=日，2=月，3=年
     */
    private Integer dateType;

}
