package com.raycloud.dmj.account.export.core.statistics.interf;

import com.raycloud.dmj.data.export.core.IMultiDataLoader;
import com.raycloud.dmj.data.export.core.MultiDataWriter;
import com.raycloud.dmj.domain.account.Staff;

public interface IExport<T> extends IMultiDataLoader<T> {

     default String getExportCode(){
        return this.getClass().getName().replaceAll("\\.","-");
    }

    void doExport(Staff staff, T parameter, MultiDataWriter writer) throws Exception;

    @Override
    default void execute(Staff staff, T param, MultiDataWriter writer) throws Exception{
        doExport(staff, param, writer);
    };
}
