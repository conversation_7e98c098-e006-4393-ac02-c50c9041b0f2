package com.raycloud.dmj.account.core.bill.service;

import com.raycloud.dmj.account.core.bill.request.AddStandardFundBillFlowInfoRequest;
import com.raycloud.dmj.account.core.bill.request.StandardFundBillFlowRequest;
import com.raycloud.dmj.account.core.bill.request.StandardFundBillSplitFlowRequest;
import com.raycloud.dmj.account.core.bill.vo.StandardFundBillFlowInfoVO;
import com.raycloud.dmj.account.core.common.PageInfo;
import com.raycloud.dmj.account.infra.session.AccountUser;
import com.raycloud.dmj.data.chessboard.model.PushResult;
import com.raycloud.dmj.domain.account.Staff;

import java.util.List;

/**
 * 标准资金账单流水信息服务接口
 * 
 * <AUTHOR>
 */
public interface IStandardFundBillFlowInfoService {

    /**
     * 新增标准资金账单流水信息
     * @param request 流水信息
     * @return 新增的ID
     */
    Long addStandardFundBillFlowInfo(AccountUser accountUser, AddStandardFundBillFlowInfoRequest request);

    /**
     * 批量新增标准资金账单流水信息
     * @param requestList 流水信息列表
     */
    void batchAddStandardFundBillFlowInfo(AccountUser accountUser,List<AddStandardFundBillFlowInfoRequest> requestList);

    /**
     * 根据条件查询标准资金账单流水信息列表
     * @return 流水信息列表
     */
    List<StandardFundBillFlowInfoVO> getStandardFundBillFlowInfoList(AccountUser accountUser, StandardFundBillFlowRequest request);

    /**
     * 根据ID查询标准资金账单流水信息
     * @param id 主键ID
     * @return 流水信息
     */
    StandardFundBillFlowInfoVO getStandardFundBillFlowInfoById(AccountUser accountUser,Long id);

    /**
     * 拆分流水
     * @param accountUser
     * @param splitFlowRequest
     */
    void splitFlow(AccountUser accountUser, StandardFundBillSplitFlowRequest splitFlowRequest);

    PageInfo<Object>  getPageInfo(AccountUser accountUser, StandardFundBillFlowRequest request);

    /**
     * 导出数据
     * @param staff 当前用户
     * @param request 查询条件
     */
    PushResult export(Staff staff, StandardFundBillFlowRequest request);
}
