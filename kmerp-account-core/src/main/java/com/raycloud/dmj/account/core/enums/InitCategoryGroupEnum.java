package com.raycloud.dmj.account.core.enums;

import lombok.Getter;

/**
 * 初始化分类枚举
 * 仅初始化使用，请勿在业务中使用
 * 业务中使用分类表
 * <AUTHOR>
 */

@Getter
public enum InitCategoryGroupEnum {

    HK("HK", "货款"),
    TX("TX", "提现"),
    KF("KF", "扣费"),
    QT("QT", "其他"),
    BT("BT", "补贴"),
    ZZ("ZZ", "转账"),
    BZJ("BZJ", "保证金");

    private final String code;
    private final String desc;

    InitCategoryGroupEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
