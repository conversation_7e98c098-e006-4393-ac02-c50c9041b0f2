package com.raycloud.dmj.account.core.rawdata.domains;

import lombok.Data;


/**
 * <AUTHOR>
 */
@Data
public class TableSourceConfig {

    /**
     * 过滤配置
     */
    private FilterConfig filterConfig;

    /**
     * 数据源标识
     */
    private String dataSourceCode;
    /**
     * 数据源类型
     */
    private String dataSourceType;
    /**
     * 时间字段
     */
    private String dateField;
    /**
     *  是否需要分表，0为不需要，1为需要
     */
    private Integer tableSplitFlag;
    /**
     * 分表配置
     */
    private TableSplitConfig tableSplitConfig;
}
