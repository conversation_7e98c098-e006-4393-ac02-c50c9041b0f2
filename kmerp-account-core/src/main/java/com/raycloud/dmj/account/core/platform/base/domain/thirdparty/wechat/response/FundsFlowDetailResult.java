
package com.raycloud.dmj.account.core.platform.base.domain.thirdparty.wechat.response;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

import java.util.List;

/**
 * @Description: 资金流水列表
 * <AUTHOR>
 * @Date 2023/10/24 3:54 下午
 */
@Data
public class FundsFlowDetailResult extends WxBaseResult {

    @JSONField(name = "funds_flow")
    private FundsFlow fundsFlow;

    @Data
    public static class FundsFlow {

        /**
         * 流水id
         */
        @JSONField(name = "flow_id")
        private String flowId;

        /**
         * 资金类型,见FundsType
         */
        @JSONField(name = "funds_type")
        private Integer fundsType;

        private String fundsTypeDesc;

        /**
         * 流水类型, 1 收入，2 支出
         */
        @JSONField(name = "flow_type")
        private Integer flowType;

        /**
         * 流水金额
         */
        @JSONField(name = "amount")
        private Double amount;

        /**
         * 余额
         */
        @JSONField(name = "balance")
        private Double balance;

        /**
         * 流水关联信息
         */
        @JSONField(name = "related_info_list")
        private List<RelatedInfo> relatedInfoList;

        /**
         * 记账时间
         */
        @JSONField(name = "bookkeeping_time")
        private String bookkeepingTime;

        /**
         * 备注
         */
        @JSONField(name = "remark")
        private String remark;

    }

    @Data
    public static class RelatedInfo {

        /**
         * 关联类型, 1 订单， 2售后，3 提现，4 运费险
         */
        @JSONField(name = "related_type")
        private Integer relatedType;

        /**
         * 关联订单号
         */
        @JSONField(name = "order_id")
        private String orderId;

        /**
         * 	关联售后单号
         */
        @JSONField(name = "aftersale_id")
        private String aftersaleId;

        /**
         * 	关联提现单号
         */
        @JSONField(name = "withdraw_id")
        private String withdrawId;

        /**
         * 	记账时间
         */
        @JSONField(name = "bookkeeping_time")
        private String bookkeepingTime;

        /**
         * 关联运费险单号
         */
        @JSONField(name = "insurance_id")
        private String insuranceId;

        /**
         * 	关联支付单号
         */
        @JSONField(name = "transaction_id")
        private String transactionId;

    }

    @Getter
    @AllArgsConstructor
    public enum FundsType {

        ORDER_PAYMENT(1, "订单结算"),
        ORDER_CHARGE(2, "订单手续费"),
        REFUND(3, "订单退款"),
        WITHDRAW(4, "提现"),
        WITHDRAW_REFUND(5, "提现失败退票"),
        AUTHOR_COMMISSION(10, "达人佣金"),
        JS_SERVICE_FEE(11, "技术服务费"),

        TEAM_COMMISSION(12, "团长抽佣"),
        COMMISSION_RETURN(13, "返佣人气卡"),
        FREIGHT_INSURANCE(16, "运费险"),
        SUB_ACCOUNT(99, "分账"),

        ;

        private Integer type;

        private String desc;


    }

    @Getter
    @AllArgsConstructor
    public enum RelatedType {

        ORDER(1, "订单"),
        AFTERSALE(2, "售后"),
        WITHDRAW(3, "提现"),
        INSURANCE(4, "运费险"),

        ;

        private Integer type;

        private String desc;
    }

}
