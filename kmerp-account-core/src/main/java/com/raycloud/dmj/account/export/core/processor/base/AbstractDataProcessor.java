package com.raycloud.dmj.account.export.core.processor.base;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.raycloud.dmj.account.infra.logger.AdvanceLogger;
import com.raycloud.dmj.account.infra.utils.StringFormatUtils;
import com.raycloud.dmj.domain.account.Staff;
import lombok.Data;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Data
public abstract class AbstractDataProcessor<T> implements IDataProcessor<List<T>>, ILifeCycle<ProcessorContext> {

    private AdvanceLogger logger = AdvanceLogger.log(this.getClass());

    public String processorName() {
        return this.getClass().getSimpleName();
    }

    public String cacheName() {
        return processorName() + "Cache";
    }

    @Override
    public void start(Staff staff, ProcessorContext context) {
        context.setStatus(1);
        context.getProcessorStartTime().put(processorName(), System.currentTimeMillis());
        boolean checked = check(staff, context);
        context.getProcessorChecked().put(processorName(), checked);

        if (checked) {
            // 注册缓存
            context.getProcessorCache().registerCache(
                    // 每个数据处理器都有自己的缓存名称
                    cacheName(),
                    Caffeine.newBuilder()
                            .initialCapacity(16)
                            // 理论上缓存的填充数据（维度数据）,数据量不会超过2W，超过2W的情况大部分是业务异常场景。
                            .maximumSize(20000)
                            // 数据处理器的缓存数据有效期10分钟，每次访问
                            .expireAfterAccess(10, TimeUnit.MINUTES)
            );
        }
    }


    @Override
    public void exception(Staff staff, ProcessorContext context, Throwable throwable) {
        context.setStatus(3);
        String name = context.getName();
        logger.error(StringFormatUtils.format("[{} -> {}] 执行异常", name, processorName()), throwable);
    }

    @Override
    public void finished(Staff staff, ProcessorContext context) {
        context.setStatus(2);
        // 完成后要关闭缓存
        context.getProcessorCache().close();
        long endtime = System.currentTimeMillis();
        Long startTime = context.getProcessorStartTime().get(processorName());
        logger.info("[{}]完成,耗时 {}ms", processorName(), endtime - startTime);
    }

    @Override
    public List<T> doProcess(Staff staff, ProcessorContext context, List<T> data) {
        if (ObjectUtils.isEmpty(data)) {
            return data;
        }
        if (context.getProcessorChecked().get(processorName())) {
            if (ready(staff, context, data)) {
                return process(staff, context, data);
            }
            return data;
        }
        return data;
    }

    /**
     * 检查阶段，如果当前处理器没有被检查通过，则直接返回数据，不进行任何处理。
     * @param staff
     * @param context
     * @return
     */
    protected abstract boolean check(Staff staff, ProcessorContext context);

    /**
     * 准备阶段，预处理/预加载填充数据，如果没有数据，则后续会跳过这个填充，减少数据处理时间。
     * @param staff
     * @param context
     * @param data
     * @return
     */
    protected abstract boolean ready(Staff staff, ProcessorContext context, List<T> data);

    /**
     * 处理阶段，执行数据填充，进到这里代表一定通过了check，和 ready
     * @param staff
     * @param context
     * @param data
     * @return
     */
    protected abstract List<T> process(Staff staff, ProcessorContext context, List<T> data);

    /**
     * 重置MAP值
     * @param row
     * @param key
     * @param value
     */
    protected void resetValue(Map<String, Object> row, String key, Object value) {
        if (ObjectUtils.isEmpty(row)) {
            return;
        }
        // 保存旧值快照
        if (row.containsKey(key)) {
            Object oldVal = row.get(key);
            // 只有当旧值不为空，并且旧值与新值不相同时才保存旧值快照
            if (oldVal != null && !oldVal.equals(value)) {
                row.put(key + "_snapshot", oldVal);
            }
        }
        row.put(key, value);
    }
}
