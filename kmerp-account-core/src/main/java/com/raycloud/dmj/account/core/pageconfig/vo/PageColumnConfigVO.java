package com.raycloud.dmj.account.core.pageconfig.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 页面列配置信息VO
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PageColumnConfigVO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 页面ID
     */
    private Long pageId;

    /**
     * 列编码
     */
    private String colCode;

    /**
     * 列名称
     */
    private String colTitle;

    /**
     * 列宽
     */
    private Integer width;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否可见(0 不可见 1可见)
     */
    private Integer visible;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 更新时间
     */
    private Date modified;

    /**
     * 公司ID
     */
    private Long companyId;
}
