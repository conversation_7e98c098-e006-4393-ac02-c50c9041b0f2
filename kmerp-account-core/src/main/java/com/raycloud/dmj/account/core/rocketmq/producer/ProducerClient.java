package com.raycloud.dmj.account.core.rocketmq.producer;

import com.alibaba.fastjson2.JSON;
import com.raycloud.dmj.account.core.rocketmq.TopicConstant;
import com.raycloud.dmj.account.core.rocketmq.dto.BillSummaryMsg;
import com.raycloud.dmj.account.core.rocketmq.dto.RawDataStatusChangeMsg;
import com.raycloud.dmj.account.core.rocketmq.dto.RawDataStorageMsg;
import com.raycloud.dmj.account.core.rocketmq.dto.RuleVerifyMsg;
import org.apache.rocketmq.client.exception.MQBrokerException;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.remoting.exception.RemotingException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;


/**
 * 生产者客户端
 * <AUTHOR>
 */

@Component
public class ProducerClient {


    @Resource
    private DefaultMQProducer DefaultMQProducer;


    /**
     * 发送原始数据入库消息
     * @param rawDataStorageMsg 原始数据入库消息
     * @return 发送结果
     */
    public boolean sendRawDataStorageMsg(RawDataStorageMsg rawDataStorageMsg) throws MQBrokerException, RemotingException, InterruptedException, MQClientException {

        Message msg = new Message(TopicConstant.RAW_DATA_STORAGE_TOPIC, "*",
                JSON.toJSONString(rawDataStorageMsg).getBytes(StandardCharsets.UTF_8));
        SendResult send = DefaultMQProducer.send(msg);
        return SendStatus.SEND_OK.equals(send.getSendStatus());

    }

    /**
     * 发送原始数据批次入库状态消息
     * @param rawDataStorageMsg 原始数据入库消息
     * @return 发送结果
     */
    public boolean sendRawDataStatusChangeMsg(RawDataStatusChangeMsg rawDataStorageMsg) throws MQBrokerException, RemotingException, InterruptedException, MQClientException {
        Message msg = new Message(TopicConstant.BATCH_RAW_DATA_STATUS_CHANGE_TOPIC, String.valueOf(rawDataStorageMsg.getStatus()),
                JSON.toJSONString(rawDataStorageMsg).getBytes(StandardCharsets.UTF_8));
        SendResult send = DefaultMQProducer.send(msg);
        return SendStatus.SEND_OK.equals(send.getSendStatus());

    }


    /**
     * 发送规则校验消息通知
     * @param ruleVerifyMsg 规则校验消息
     * @return
     * @throws MQBrokerException
     * @throws RemotingException
     * @throws InterruptedException
     * @throws MQClientException
     */
    public boolean sendRuleVerifyLMsg(RuleVerifyMsg ruleVerifyMsg) throws MQBrokerException, RemotingException, InterruptedException, MQClientException {
        Message msg = new Message(TopicConstant.RULE_VERIFY_TOPIC, JSON.toJSONString(ruleVerifyMsg).getBytes(StandardCharsets.UTF_8));
        SendResult send = DefaultMQProducer.send(msg);
        return SendStatus.SEND_OK.equals(send.getSendStatus());

    }

    /**
     * 发送资金统计消息
     * @param billSummaryMsg 原始数据入库消息
     * @return 发送结果
     */
    public boolean sendBillSummaryMsg(BillSummaryMsg billSummaryMsg) throws MQBrokerException, RemotingException, InterruptedException, MQClientException {
        Message msg = new Message(TopicConstant.FOUND_BILL_SUMMARY_TOPIC,
                JSON.toJSONString(billSummaryMsg).getBytes(StandardCharsets.UTF_8));
        SendResult send = DefaultMQProducer.send(msg);
        return SendStatus.SEND_OK.equals(send.getSendStatus());

    }
}