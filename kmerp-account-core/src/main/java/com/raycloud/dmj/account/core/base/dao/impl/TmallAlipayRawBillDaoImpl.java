package com.raycloud.dmj.account.core.base.dao.impl;

import com.raycloud.dmj.account.core.base.dao.TmallAlipayRawBillDao;
import com.raycloud.dmj.account.core.cleancategory.domain.object.TmallAlipayRawBillDataDO;
import com.raycloud.dmj.account.core.common.Page;
import com.raycloud.dmj.account.core.enums.field.TmallAlipayRawBillFieldEnum;
import com.raycloud.dmj.account.core.rawdata.utils.HashTableShardingUtil;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import com.raycloud.dmj.table.api.plus.common.base.SQL;
import com.raycloud.dmj.table.api.plus.common.enums.LinkMode;
import com.raycloud.dmj.table.api.plus.component.column.utils.Columns;
import com.raycloud.dmj.table.api.plus.component.condition.utils.Conditions;
import com.raycloud.dmj.table.api.plus.component.order.utils.Orders;
import com.raycloud.dmj.table.api.plus.query.Queries;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 文件原始数据监控表
 *
 * <AUTHOR>
 */

@Slf4j
@Repository
public class TmallAlipayRawBillDaoImpl extends BaseDao implements TmallAlipayRawBillDao {



    private final String TABLE_NAME = "tmall_alipay_raw_bill_data";

    @Override
    public List<TmallAlipayRawBillDataDO> listPageByDataRange(Long companyId, Long shopId, Integer dataRange, Page page) {
        AsserUtils.notNull(companyId, "公司ID不能为空！");
        AsserUtils.notNull(shopId, "店铺ID不能为空！");
        AsserUtils.notNull(dataRange, "账期不能为空！");
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(TmallAlipayRawBillFieldEnum.BATCH_TIME.getFieldCode()), LinkMode.EQUAL, dataRange),
                        Conditions.and(Columns.toColumn(TmallAlipayRawBillFieldEnum.SHOP_ID.getFieldCode()), LinkMode.EQUAL, shopId),
                        Conditions.and(Columns.toColumn(TmallAlipayRawBillFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId)
                )
                .orderBy(
                        Orders.desc(TmallAlipayRawBillFieldEnum.CREATED.getFieldCode())
                )
                .select(
                )
                .page(page.getPageNo(), page.getPageSize())
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<TmallAlipayRawBillDataDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(TmallAlipayRawBillDataDO.class), args);
        return !query.isEmpty() ? query : null;
    }

    @Override
    public TmallAlipayRawBillDataDO getEarliestByDataRange(Long companyId, Long shopId, Integer dataRange) {
        AsserUtils.notNull(companyId, "公司ID不能为空！");
        AsserUtils.notNull(shopId, "店铺ID不能为空！");
        AsserUtils.notNull(dataRange, "批次号不能为空！");
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(TmallAlipayRawBillFieldEnum.BATCH_TIME.getFieldCode()), LinkMode.EQUAL, dataRange),
                        Conditions.and(Columns.toColumn(TmallAlipayRawBillFieldEnum.SHOP_ID.getFieldCode()), LinkMode.EQUAL, shopId),
                        Conditions.and(Columns.toColumn(TmallAlipayRawBillFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId)
                ).orderBy(
                        Orders.asc(TmallAlipayRawBillFieldEnum.OCCUR_TIME.getFieldCode())
                )
                .select(
                )
                .limit(1)
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<TmallAlipayRawBillDataDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(TmallAlipayRawBillDataDO.class), args);
        return !query.isEmpty() ? query.get(0) : null;

    }

    @Override
    public TmallAlipayRawBillDataDO getLatestByDataRange(Long companyId, Long shopId,Integer dataRange) {
        AsserUtils.notNull(companyId, "公司ID不能为空！");
        AsserUtils.notNull(shopId, "店铺ID不能为空！");
        AsserUtils.notNull(dataRange, "批次号不能为空！");
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(TmallAlipayRawBillFieldEnum.BATCH_TIME.getFieldCode()), LinkMode.EQUAL, dataRange),
                        Conditions.and(Columns.toColumn(TmallAlipayRawBillFieldEnum.SHOP_ID.getFieldCode()), LinkMode.EQUAL, shopId),
                        Conditions.and(Columns.toColumn(TmallAlipayRawBillFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId)
                ).orderBy(
                        Orders.desc(TmallAlipayRawBillFieldEnum.OCCUR_TIME.getFieldCode())
                )
                .select(
                )
                .limit(1)
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<TmallAlipayRawBillDataDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(TmallAlipayRawBillDataDO.class), args);
        return !query.isEmpty() ? query.get(0) : null;
    }


    /**
     * 获取表名
     * @param companyId 公司ID
     * @return 表名
     */
    private String getTableName(Long companyId){
        int shardTableNo = HashTableShardingUtil.getShardTableNo(companyId);
        return TABLE_NAME+"_"+shardTableNo;
    }
}
