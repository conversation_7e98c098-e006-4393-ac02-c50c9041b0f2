package com.raycloud.dmj.account.core.rawdata.callback.param;


import com.raycloud.dmj.account.core.enums.RawDataSourceEnum;
import com.raycloud.dmj.account.core.rawdata.domains.FileAnalyzeCountInfo;
import com.raycloud.dmj.account.core.enums.DateTypeEnum;
import lombok.Data;

import java.util.*;

/**
 * 批量回调上下文
 *
 * <AUTHOR>
 */

@Data
public class BatchCallbackContext {


    /**
     * 公司ID
     */
    private Long companyId;


    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 需要导入的时间范围-开始时间
     */
    private Date needStartTime;

    /**
     * 需要导入的时间范围-结束时间
     */
    private Date needEndTime;


    /**
     * 时间类型
     *
     * @see DateTypeEnum
     */
    private Integer dateType;


    /**
     * 当前数据来源的记录ID
     */
    private Long lastRecordId;

    /**
     * sheetId -> 配置ID
     */
    private Map<String, Long> sheetConfigIdMap;


    /**
     * 处理的批次号
     */
    private Set<String> handleBatchNoSet;

    /**
     * 跳过的批次号
     */
    private Set<String> skipBatchNoList;


    /**
     * 是否是第一批失败
     */
    private Boolean isFailFirst = true;

    /**
     * 文件解析统计数据
     */
    private HashMap<String,FileAnalyzeCountInfo> fileAnalyzeCountInfoHashMap = new HashMap<>();

    /**
     * failOutputFilePath，失败的临时文件
     *
     * @return
     */
    private String failOutputFilePath;

    /**
     * 数据来源
     * @see RawDataSourceEnum
     */
    private Integer dataSource;


    /**
     * overallErrorFlag
     */
    private Boolean bizErrorFlag = false;

    /**
     * 当前文件名称
     * @param sheetName
     * @param sheetType
     * @return
     */
    private String fileName;

    public BatchCallbackContext() {
        this.handleBatchNoSet = new HashSet<>();
        this.skipBatchNoList = new HashSet<>();
    }

    public static String getSheetKey(String sheetName, Integer sheetType) {
        return sheetName + "_" + sheetType;
    }
}
