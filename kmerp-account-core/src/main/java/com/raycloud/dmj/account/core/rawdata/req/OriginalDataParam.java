package com.raycloud.dmj.account.core.rawdata.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.raycloud.dmj.account.core.enums.DateTypeEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
public class OriginalDataParam implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 店铺ID
     */
    private Long shopUniId;

    /**
     * 文件类型 ID
     */
    private String blockId;

    /**
     * OSS 的 URL 地址
     */
    private String content;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * 真实的开始数据范围
     */
    private Date realStartDataRange;

    /**
     * 真实的结束数据范围
     */
    private Date realEndDataRange;
    /**
     * 时间类型
     * @see DateTypeEnum
     */
    private Integer dateType;

    /**
     * 当前数据范围
     * 格式根据 date_type 决定：
     * - 如果是日：yyyy-MM-dd
     * - 如果是月：yyyy-MM
     * - 如果是年：yyyy
     */
    private String dataRange;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 扩展参数
     */
    private Map<String, String> extendParam;
}