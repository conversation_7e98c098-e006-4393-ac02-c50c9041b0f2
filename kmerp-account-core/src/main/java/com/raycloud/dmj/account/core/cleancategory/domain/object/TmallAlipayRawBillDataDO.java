package com.raycloud.dmj.account.core.cleancategory.domain.object;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 支付宝原始数据表
 * <AUTHOR>
 */
@Data
public class TmallAlipayRawBillDataDO implements Serializable {
    private static final long serialVersionUID = -1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 账务流水号
     */
    private String accountFlowNo;

    /**
     * 业务流水号
     */
    private String bizFlowNo;

    /**
     * 商户订单号
     */
    private String merchantOrderNo;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 发生时间
     */
    private Date occurTime;

    /**
     * 对方账号
     */
    private String counterpartyAccount;

    /**
     * 收入金额(+元)
     */
    private BigDecimal incomeAmount;

    /**
     * 支出金额(-元)
     */
    private BigDecimal expenseAmount;

    /**
     * 账户余额(元)
     */
    private BigDecimal accountBalance;

    /**
     * 交易渠道
     */
    private String transactionChannel;

    /**
     * 业务类型
     */
    private String bizType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 业务描述
     */
    private String bizDescription;

    /**
     * 业务账单来源
     */
    private String bizBillSource;

    /**
     * 业务基础订单号
     */
    private String bizBaseOrderNo;

    /**
     * 业务订单号
     */
    private String bizOrderNo;

    /**
     * 下载时间
     */
    private Date downloadTime;

    /**
     * 下载账户
     */
    private String downloadAccount;

    /**
     * 唯一键
     */
    private String bizKey;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 批次时间
     */
    private Integer batchTime;

    /**
     * 公司ID
     */
    private Long companyId;

    /**
     * 店铺ID
     */
    private String shopId;

    /**
     * 创建时间
     */
    private Date created;
}    