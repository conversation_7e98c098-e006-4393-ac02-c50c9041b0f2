package com.raycloud.dmj.account.core.rawdata.handle.filter;


import com.raycloud.dmj.account.core.enums.FilterTypeEnum;
import com.raycloud.dmj.account.core.rawdata.handle.filter.handle.TimeFilterHandler;

import java.util.HashMap;
import java.util.Map;

/**
 * 过滤处理工厂
 * <AUTHOR>
 */
public class FilterFactory {

    private static final Map<String, FilterHandler<?>> HANDLER_MAP = new HashMap<>();

    static {
        HANDLER_MAP.put(FilterTypeEnum.TIME.getCode(), new TimeFilterHandler());
    }

    /**
     * 获取指定类型的处理器
     * @param type 过滤类型
     * @return 处理器
     * @param <P> 参数泛型
     */
    public static <P> FilterHandler<P> getHandler(String type) {
        FilterHandler<?> handler = HANDLER_MAP.get(type);
        return (FilterHandler<P>) handler;
    }


    /**
     * 判断是否支持
     * @param type 过滤类型
     * @return 是否支持
     */
    public static boolean hasSupport(String type){
        return HANDLER_MAP.containsKey(type);
    }

}
