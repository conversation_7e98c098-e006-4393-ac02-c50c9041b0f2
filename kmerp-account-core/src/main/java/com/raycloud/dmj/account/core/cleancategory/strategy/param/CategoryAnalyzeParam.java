package com.raycloud.dmj.account.core.cleancategory.strategy.param;

import com.raycloud.dmj.account.core.enums.DateTypeEnum;
import com.raycloud.dmj.account.core.enums.RawDataSourceEnum;
import lombok.Data;

/**
 * 类别解析参数
 * <AUTHOR>
 */

@Data
public class CategoryAnalyzeParam {

    /**
     * 公司id
     */
    private Long companyId;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 账期
     */
    private Integer dataRange;

    /**
     * 账期类型
     */
    private DateTypeEnum dateType;

    /**
     * 原始数据来源
     */
    private RawDataSourceEnum source;
}
