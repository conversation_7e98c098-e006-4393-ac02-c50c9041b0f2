package com.raycloud.dmj.account.core.enums;

import lombok.Getter;

/**
 * 重洗任务状态表 0-清洗中，30-清洗失败，50-清洗成功
 * <AUTHOR>
 */
@Getter
public enum AgainAnalyzeStatusEnum {

    //0-执行中，30-执行失败，50-执行成功
    ANALYSIS(0, "执行中"),
    ANALYZE_FAIL(30, "执行失败"),
    ANALYZE_SUCCESS(50, "执行成功"),
    ;

    private final Integer status;

    private final String desc;

    AgainAnalyzeStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }


    public static AgainAnalyzeStatusEnum of(Integer status) {
        for (AgainAnalyzeStatusEnum value : AgainAnalyzeStatusEnum.values()) {
            if (value.status.equals(status)) {
                return value;
            }
        }
        return null;
    }
}
