package com.raycloud.dmj.account.core.cleancategory.strategy.other;

import com.raycloud.dmj.account.core.base.dao.TmallHuiYingBaoRawBillDataDao;
import com.raycloud.dmj.account.core.base.domain.StandardOtherBillFlowInfoDO;
import com.raycloud.dmj.account.core.cleancategory.domain.object.TmallHuiYingBaoRawBillDataDO;
import com.raycloud.dmj.account.core.cleancategory.strategy.param.CategoryAnalyzeParam;
import com.raycloud.dmj.account.core.common.Page;
import com.raycloud.dmj.account.core.enums.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;


/**
 * 集分宝类目解析处理
 * <AUTHOR>
 */
@Component
public class HuiYingBaoAnalyzeHandle extends StandardOtherCategoryAnalyzeHandle<TmallHuiYingBaoRawBillDataDO> {


    @Resource
    private TmallHuiYingBaoRawBillDataDao tmallHuiYingBaoRawBillDataDao;

    @Override
    protected List<TmallHuiYingBaoRawBillDataDO> listPageRawData(CategoryAnalyzeParam param, Page page) {
        return tmallHuiYingBaoRawBillDataDao.listPageByDataRange(
                param.getCompanyId(),
                param.getShopId(),
                param.getDataRange(),
                page
        );
    }

    @Override
    protected void setStandardFundBillFlowInfoDO(StandardOtherBillFlowInfoDO standardOtherBillFlowInfoDO, TmallHuiYingBaoRawBillDataDO rawDataDO) {
        standardOtherBillFlowInfoDO.setCategoryGroupCode(OtherCategoryGroupEnum.BT.getCode());
        standardOtherBillFlowInfoDO.setCategoryCode(OtherCategoryEnum.HYB.getCode());
        standardOtherBillFlowInfoDO.setSubCategoryCode(OtherSubCategoryEnum.HYB.getCode());
        standardOtherBillFlowInfoDO.setBillingCycle(rawDataDO.getMonth());
        standardOtherBillFlowInfoDO.setOccurredAt(rawDataDO.getBusinessOccurrenceTime());
        BigDecimal amount = rawDataDO.getDeductionAmount();
        standardOtherBillFlowInfoDO.setAmount(amount);
        if (amount.compareTo(BigDecimal.ZERO)>0){
            standardOtherBillFlowInfoDO.setIncomeExpenseDirection(IncomeExpenseDirectionEnum.INCOME.getCode());
        }else {
            standardOtherBillFlowInfoDO.setIncomeExpenseDirection(IncomeExpenseDirectionEnum.EXPENSE.getCode());
        }
        standardOtherBillFlowInfoDO.setOrderNo(rawDataDO.getMerchantOrderNo());
        standardOtherBillFlowInfoDO.setBatchNo(rawDataDO.getBatchNo());
        standardOtherBillFlowInfoDO.setBizKey(rawDataDO.getBizKey());


    }

    @Override
    protected void deleteByShopAndDataRangeAndCategoryGroup(CategoryAnalyzeParam param) {
        standardOtherBillFlowInfoDao.deleteByShopAndDataRangeAndCategory(param.getCompanyId(),
                param.getShopId(),param.getDataRange() , OtherCategoryEnum.HYB.getCode());
    }

    @Override
    public boolean accord(RawDataSourceEnum source) {
        return RawDataSourceEnum.HUI_YING_BAO.equals(source);
    }
}
