package com.raycloud.dmj.account.export.utils;

import org.apache.commons.lang3.time.DateUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Objects;

public class DateTimeUtils {

    /**
     * 2000-01-01 00:00:00
     */
    public final static Long DEFAULT_TIMESTAMP = 946656000000L;

    /**
     * 2000-01-01 00:00:00
     */
    public final static Date DEFAULT_TIME = new Date(DEFAULT_TIMESTAMP);


    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");


    public synchronized static java.sql.Date convertStringToDate(String sMt){
        java.sql.Date result = null;
        try
        {
            Date utilDate=null;
            if(sMt!=null&&sMt.length()>0){
                int length=sMt.length();

                if(length>17){
                    utilDate=new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(sMt);
                }else if(length>13){
                    utilDate=new SimpleDateFormat("yyyy-MM-dd HH:mm").parse(sMt);
                }else if(length>=10){
                    utilDate=new SimpleDateFormat("yyyy-MM-dd").parse(sMt);
                }else if(length>=8){
                    utilDate=new SimpleDateFormat("HH:mm:ss").parse(sMt);
                }else if(length==7){
                    utilDate=new SimpleDateFormat("yyyy-MM").parse(sMt);
                }else if(length==5){
                    utilDate=new SimpleDateFormat("HH:mm").parse(sMt);
                }

                result = new java.sql.Date( utilDate.getTime());

            }
            //result=sdf.parse(sMt);
            //result = Date.valueOf(sMt);
        }
        catch(Exception e) {
            //e.printStackTrace();
        }

        return result;
    }

    /**
     * 今日0点
     */
    public static Date atStartOfToday() {
        LocalDate today = LocalDate.now();
        return Date.from(today.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }


    public static LocalDate parseDate(String source) {
        return LocalDate.parse(source, DATE_FORMATTER);
    }

    public static Date localDateToDate(LocalDate localDate) {
        if(null == localDate){
            return null;
        }
        return Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 查询两个时间点差了多少天
     * @param start 开始
     * @param end 结束
     * @return
     */
    public static int daysBetween(Long start, Long end) {
        long diff = end +1000 - start;
        return (int) (diff / (1000 * 60 * 60 * 24));
    }

    /**
     * 返回两个日期中较大值
     * @param date1
     * @param date2
     * @return
     */
    public static Date max(Long date1, Long date2) {
        if (Objects.isNull(date1) && Objects.isNull(date2)) {
            return new Date();
        }
        if (Objects.isNull(date1)) {
            return new Date(date2);
        }
        if (Objects.isNull(date2)) {
            return new Date(date1);
        }

        return date1 >= date2 ? new Date(date1) : new Date(date2);
    }

    /**
     * 返回两个日期中较小值
     * @param date1
     * @param date2
     * @return
     */
    public static Date min(Long date1, Long date2) {
        if (Objects.isNull(date1) && Objects.isNull(date2)) {
            return new Date();
        }
        if (Objects.isNull(date1)) {
            return new Date(date2);
        }
        if (Objects.isNull(date2)) {
            return new Date(date1);
        }

        return date1 <= date2 ? new Date(date1) : new Date(date2);
    }


    /**
     * 获取某日的0点时间戳
     * @param days
     * @return
     */
    public static Long getTimestamp(int days) {
        Date date = atStartOfToday();
        return DateUtils.addDays(date, days).getTime();
    }


    public static String format(Date date,String pattern){
        SimpleDateFormat format = new SimpleDateFormat(pattern);
        return format.format(date);
    }


    public static String format(Date date){
        return format(date,"yyyy-MM-dd HH:mm:ss");
    }
}
