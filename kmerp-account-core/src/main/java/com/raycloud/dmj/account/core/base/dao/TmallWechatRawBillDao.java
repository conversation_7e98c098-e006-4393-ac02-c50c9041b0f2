package com.raycloud.dmj.account.core.base.dao;

import com.raycloud.dmj.account.core.common.Page;
import com.raycloud.dmj.account.core.cleancategory.domain.object.TmallWechatRawBillDataDO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface TmallWechatRawBillDao {

    /**
     * 根据账期分页查询微信支付宝账单
     *
     * @param dataRange 账期
     * @return 微信支付宝账单
     */
    List<TmallWechatRawBillDataDO> listPageByDataRange(Long companyId, Long shopId, Integer dataRange, Page page);


}
