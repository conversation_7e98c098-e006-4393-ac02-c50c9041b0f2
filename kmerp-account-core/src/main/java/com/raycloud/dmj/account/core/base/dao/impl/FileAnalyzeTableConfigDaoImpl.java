package com.raycloud.dmj.account.core.base.dao.impl;

import com.raycloud.dmj.account.core.base.dao.FileAnalyzeTableConfigDao;
import com.raycloud.dmj.account.core.enums.field.FileAnalyzeConfigFieldEnum;
import com.raycloud.dmj.account.core.enums.field.FileAnalyzeTableConfigFieldEnum;
import com.raycloud.dmj.account.core.rawdata.domains.FileAnalyzeTableConfigDO;
import com.raycloud.dmj.table.api.plus.common.base.SQL;
import com.raycloud.dmj.table.api.plus.common.enums.LinkMode;
import com.raycloud.dmj.table.api.plus.component.column.utils.Columns;
import com.raycloud.dmj.table.api.plus.component.condition.utils.Conditions;
import com.raycloud.dmj.table.api.plus.insert.Inserts;
import com.raycloud.dmj.table.api.plus.query.Queries;
import com.raycloud.dmj.table.api.plus.update.Updates;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.Date;
import java.util.List;

/**
 * Date:  2025/6/16
 *
 * <AUTHOR>
 */
@Repository
public class FileAnalyzeTableConfigDaoImpl extends BaseDao implements FileAnalyzeTableConfigDao {

    private final String tableName = "file_analyze_table_config";

    /**
     * 查询文件解析配置表
     */
    @Override
    public List<FileAnalyzeTableConfigDO> queryFileAnalyzeTableConfig(List<Long> configIds) {
        SQL sql = Queries.create()
                .from(tableName)
                .where(
                        Conditions.and(Columns.toColumn(FileAnalyzeTableConfigFieldEnum.CONFIG_ID.getFieldCode()), LinkMode.IN,configIds),
                        Conditions.and(Columns.toColumn(FileAnalyzeConfigFieldEnum.ENABLE_STATUS.getFieldCode()), LinkMode.EQUAL, 1)
                )
                .select(
                        FileAnalyzeTableConfigFieldEnum.ID.getFieldCode(),
                        FileAnalyzeTableConfigFieldEnum.CONFIG_ID.getFieldCode(),
                        FileAnalyzeTableConfigFieldEnum.TABLE_NAME.getFieldCode(),
                        FileAnalyzeTableConfigFieldEnum.TABLE_DATA_TYPE.getFieldCode(),
                        FileAnalyzeTableConfigFieldEnum.BATCH_NO.getFieldCode(),
                        FileAnalyzeTableConfigFieldEnum.UNIQUE_KEY.getFieldCode(),
                        FileAnalyzeTableConfigFieldEnum.TABLE_FIELDS.getFieldCode(),
                        FileAnalyzeTableConfigFieldEnum.TABLE_SOURCE_CONFIG.getFieldCode()
                )
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<FileAnalyzeTableConfigDO> doList = jdbcTemplate.query(
                sql.getSqlCode(),
                new BeanPropertyRowMapper<>(FileAnalyzeTableConfigDO.class),
                args
        );
        return doList;
    }
    /**
     * 查询指定configId下的FileAnalyzeTableConfig记录数量
     * @param configId 配置ID
     * @return 记录数量
     */
    @Override
    public int countByConfigId(Long configId) {
        SQL sql = Queries.create()
                .from(tableName)
                .where(
                        Conditions.and(Columns.toColumn(FileAnalyzeTableConfigFieldEnum.CONFIG_ID.getFieldCode()), LinkMode.EQUAL, configId),
                        Conditions.and(Columns.toColumn(FileAnalyzeTableConfigFieldEnum.ENABLE_STATUS.getFieldCode()), LinkMode.EQUAL, 1)
                )
                .select("COUNT(*)")
                .toSql();

        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();

        Integer count = jdbcTemplate.queryForObject(
                sql.getSqlCode(),
                Integer.class,
                args
        );

        return count != null ? count : 0;
    }

    /**
     * 根据配置ID查询表配置列表
     * @param configId 配置ID
     * @return 表配置列表
     */
    @Override
    public List<FileAnalyzeTableConfigDO> queryByConfigId(Long configId) {
        SQL sql = Queries.create()
                .from(tableName)
                .where(
                        Conditions.and(Columns.toColumn(FileAnalyzeTableConfigFieldEnum.CONFIG_ID.getFieldCode()), LinkMode.EQUAL, configId),
                        Conditions.and(Columns.toColumn(FileAnalyzeTableConfigFieldEnum.ENABLE_STATUS.getFieldCode()), LinkMode.EQUAL, 1)
                )
                .select(
                        FileAnalyzeTableConfigFieldEnum.ID.getFieldCode(),
                        FileAnalyzeTableConfigFieldEnum.CONFIG_ID.getFieldCode(),
                        FileAnalyzeTableConfigFieldEnum.TABLE_NAME.getFieldCode(),
                        FileAnalyzeTableConfigFieldEnum.TABLE_DATA_TYPE.getFieldCode(),
                        FileAnalyzeTableConfigFieldEnum.BATCH_NO.getFieldCode(),
                        FileAnalyzeTableConfigFieldEnum.UNIQUE_KEY.getFieldCode(),
                        FileAnalyzeTableConfigFieldEnum.TABLE_FIELDS.getFieldCode(),
                        FileAnalyzeTableConfigFieldEnum.TABLE_SOURCE_CONFIG.getFieldCode(),
                        FileAnalyzeTableConfigFieldEnum.CREATED.getFieldCode(),
                        FileAnalyzeTableConfigFieldEnum.MODIFIED.getFieldCode(),
                        FileAnalyzeTableConfigFieldEnum.ENABLE_STATUS.getFieldCode()
                )
                .toSql();

        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        return jdbcTemplate.query(
                sql.getSqlCode(),
                new BeanPropertyRowMapper<>(FileAnalyzeTableConfigDO.class),
                args
        );
    }

    /**
     * 根据ID查询单个表配置
     * @param id 表配置ID
     * @return 表配置对象
     */
    @Override
    public FileAnalyzeTableConfigDO queryById(Long id) {
        SQL sql = Queries.create()
                .from(tableName)
                .where(
                        Conditions.and(Columns.toColumn(FileAnalyzeTableConfigFieldEnum.ID.getFieldCode()), LinkMode.EQUAL, id),
                        Conditions.and(Columns.toColumn(FileAnalyzeTableConfigFieldEnum.ENABLE_STATUS.getFieldCode()), LinkMode.EQUAL, 1)
                )
                .select(
                        FileAnalyzeTableConfigFieldEnum.ID.getFieldCode(),
                        FileAnalyzeTableConfigFieldEnum.CONFIG_ID.getFieldCode(),
                        FileAnalyzeTableConfigFieldEnum.TABLE_NAME.getFieldCode(),
                        FileAnalyzeTableConfigFieldEnum.TABLE_DATA_TYPE.getFieldCode(),
                        FileAnalyzeTableConfigFieldEnum.BATCH_NO.getFieldCode(),
                        FileAnalyzeTableConfigFieldEnum.UNIQUE_KEY.getFieldCode(),
                        FileAnalyzeTableConfigFieldEnum.TABLE_FIELDS.getFieldCode(),
                        FileAnalyzeTableConfigFieldEnum.TABLE_SOURCE_CONFIG.getFieldCode(),
                        FileAnalyzeTableConfigFieldEnum.CREATED.getFieldCode(),
                        FileAnalyzeTableConfigFieldEnum.MODIFIED.getFieldCode(),
                        FileAnalyzeTableConfigFieldEnum.ENABLE_STATUS.getFieldCode()
                )
                .toSql();

        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<FileAnalyzeTableConfigDO> results = jdbcTemplate.query(
                sql.getSqlCode(),
                new BeanPropertyRowMapper<>(FileAnalyzeTableConfigDO.class),
                args
        );
        return results.isEmpty() ? null : results.get(0);
    }

    /**
     * 新增表配置
     * @param tableConfig 表配置对象
     * @return 新增记录的主键ID
     */
    @Override
    public Long addFileAnalyzeTableConfig(FileAnalyzeTableConfigDO tableConfig) {
        SQL sql = Inserts.insert()
                .into(tableName)
                .columns(
                        FileAnalyzeTableConfigFieldEnum.CONFIG_ID.getFieldCode(),
                        FileAnalyzeTableConfigFieldEnum.TABLE_NAME.getFieldCode(),
                        FileAnalyzeTableConfigFieldEnum.TABLE_DATA_TYPE.getFieldCode(),
                        FileAnalyzeTableConfigFieldEnum.BATCH_NO.getFieldCode(),
                        FileAnalyzeTableConfigFieldEnum.UNIQUE_KEY.getFieldCode(),
                        FileAnalyzeTableConfigFieldEnum.TABLE_FIELDS.getFieldCode(),
                        FileAnalyzeTableConfigFieldEnum.TABLE_SOURCE_CONFIG.getFieldCode(),
                        FileAnalyzeTableConfigFieldEnum.CREATED.getFieldCode(),
                        FileAnalyzeTableConfigFieldEnum.MODIFIED.getFieldCode(),
                        FileAnalyzeTableConfigFieldEnum.ENABLE_STATUS.getFieldCode()
                )
                .valueForEntity(tableConfig)
                .toSql();

        // 使用KeyHolder捕获自增主键
        KeyHolder keyHolder = new GeneratedKeyHolder();

        // 准备参数数组
        Object[] params = new Object[]{
                tableConfig.getConfigId(),
                tableConfig.getTableName(),
                tableConfig.getTableDataType(),
                tableConfig.getBatchNo(),
                tableConfig.getUniqueKey(),
                tableConfig.getTableFields(),
                tableConfig.getTableSourceConfig(),
                tableConfig.getCreated(),
                tableConfig.getModified(),
                tableConfig.getEnableStatus()
        };

        jdbcTemplate.update(connection -> {
            PreparedStatement ps = connection.prepareStatement(sql.getSqlCode(), Statement.RETURN_GENERATED_KEYS);
            for (int i = 0; i < params.length; i++) {
                ps.setObject(i + 1, params[i]);
            }
            return ps;
        }, keyHolder);

        // 返回生成的主键ID
        Number key = keyHolder.getKey();
        return key != null ? key.longValue() : null;
    }

    /**
     * 更新表配置
     * @param tableConfig 表配置对象
     * @return 更新影响的行数
     */
    @Override
    public int updateFileAnalyzeTableConfig(FileAnalyzeTableConfigDO tableConfig) {
        SQL sql = Updates.create()
                .table(tableName)
                .where(
                        Conditions.and(Columns.toColumn(FileAnalyzeTableConfigFieldEnum.ID.getFieldCode()), LinkMode.EQUAL, tableConfig.getId())
                )
                .update(
                        com.raycloud.dmj.table.api.plus.component.$.updateKeyValue(FileAnalyzeTableConfigFieldEnum.CONFIG_ID.getFieldCode(), tableConfig.getConfigId()),
                        com.raycloud.dmj.table.api.plus.component.$.updateKeyValue(FileAnalyzeTableConfigFieldEnum.TABLE_NAME.getFieldCode(), tableConfig.getTableName()),
                        com.raycloud.dmj.table.api.plus.component.$.updateKeyValue(FileAnalyzeTableConfigFieldEnum.TABLE_DATA_TYPE.getFieldCode(), tableConfig.getTableDataType()),
                        com.raycloud.dmj.table.api.plus.component.$.updateKeyValue(FileAnalyzeTableConfigFieldEnum.BATCH_NO.getFieldCode(), tableConfig.getBatchNo()),
                        com.raycloud.dmj.table.api.plus.component.$.updateKeyValue(FileAnalyzeTableConfigFieldEnum.UNIQUE_KEY.getFieldCode(), tableConfig.getUniqueKey()),
                        com.raycloud.dmj.table.api.plus.component.$.updateKeyValue(FileAnalyzeTableConfigFieldEnum.TABLE_FIELDS.getFieldCode(), tableConfig.getTableFields()),
                        com.raycloud.dmj.table.api.plus.component.$.updateKeyValue(FileAnalyzeTableConfigFieldEnum.TABLE_SOURCE_CONFIG.getFieldCode(), tableConfig.getTableSourceConfig()),
                        com.raycloud.dmj.table.api.plus.component.$.updateKeyValue(FileAnalyzeTableConfigFieldEnum.ENABLE_STATUS.getFieldCode(), tableConfig.getEnableStatus()),
                        com.raycloud.dmj.table.api.plus.component.$.updateKeyValue(FileAnalyzeTableConfigFieldEnum.MODIFIED.getFieldCode(), new Date())
                )
                .toSql();

        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        return jdbcTemplate.update(sql.getSqlCode(), args);
    }
}
