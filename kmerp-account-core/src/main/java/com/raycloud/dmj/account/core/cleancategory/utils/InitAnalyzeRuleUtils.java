package com.raycloud.dmj.account.core.cleancategory.utils;

import com.google.common.collect.Lists;
import com.raycloud.dmj.account.core.cleancategory.domain.AnalyzeRuleInfo;
import com.raycloud.dmj.account.core.enums.*;

import com.raycloud.dmj.account.infra.common.BusinessException;
import com.raycloud.dmj.account.infra.common.ErrorCodeEnum;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 初始化解析规则工具类
 */
public class InitAnalyzeRuleUtils {

    public static void main(String[] args) {
        List<InitRule> rules1 =listAliPayInitAnalyzeRule();
        //根据分类code进行分组
        Map<InitCategoryGroupEnum, List<InitRule>> collect1 = rules1.stream().collect(Collectors.groupingBy(InitRule::getCategoryGroupCode));
        List<InitRule> rules2 = listWechatInitAnalyzeRule();
        Map<InitCategoryGroupEnum, List<InitRule>> collect2 = rules2.stream().collect(Collectors.groupingBy(InitRule::getCategoryGroupCode));
        List<InitRule> rules3 = listGuaranteeInitAnalyzeRule();
        Map<InitCategoryGroupEnum, List<InitRule>> collect3 = rules3.stream().collect(Collectors.groupingBy(InitRule::getCategoryGroupCode));
    }


    /**
     * 根据账户类型获取初始化规则
     * @param accountTypeCode 账户类型
     * @return 初始化规则
     */
    public static List<InitRule> listInitRuleByTypeCode(Integer accountTypeCode) {
        AsserUtils.notNull(accountTypeCode, "账户类型不能为空！");
        AccountTypeEnum accountType = AccountTypeEnum.getByTypeCode(accountTypeCode);
        AsserUtils.notNull(accountType, "账户类型不存在！");
        switch (accountType){
            case TM_ALIPAY:
                return listAliPayInitAnalyzeRule();
            case TM_WECHAT:
                return listWechatInitAnalyzeRule();
            case TM_GUARANTEE:
                return listGuaranteeInitAnalyzeRule();
            default:
                throw new BusinessException(ErrorCodeEnum.PARAM_ERROR, "不支持初始化的类型！");
        }
    }



    @Data
    public static class InitRule implements Serializable {

        private static final long serialVersionUID = 1212121L;

        /**
         * 分类
         */
        private InitCategoryGroupEnum categoryGroupCode;

        /**
         * 类别名称
         */
        private String categoryName;

        /**
         * 子类别名称
         */
        private String subCategoryName;


        /**
         * 规则
         */
        private List<List<AnalyzeRuleInfo>> analyzeRuleContent;

    }


    private static InitRule builderRule(InitCategoryGroupEnum categoryGroup, String categoryName, String subCategoryName, List<AnalyzeRuleInfo> ... ruleInfos){
        InitRule initRule = new InitRule();
        initRule.setCategoryGroupCode(categoryGroup);
        initRule.setCategoryName(categoryName);
        initRule.setSubCategoryName(subCategoryName);
        List<List<AnalyzeRuleInfo>> ruleInfoLists = Arrays.asList(ruleInfos);
        initRule.setAnalyzeRuleContent(ruleInfoLists);
        return initRule;
    }



    private static AnalyzeRuleInfo builderRule(String fieldCode, AnalyzeRuleOperatorEnum operator, String value){
        AnalyzeRuleInfo ruleIfo = new AnalyzeRuleInfo();
        ruleIfo.setFieldCode(fieldCode);
        ruleIfo.setOperator(operator.getOperatorCode());
        ArrayList<String> valueList = Lists.newArrayList(value);
        ruleIfo.setValueList(valueList);
        return ruleIfo;
    }



    /**
     * 保证金
     * @return List<Rule>
     */
    public static List<InitRule> listGuaranteeInitAnalyzeRule() {
        List<InitRule> dataList = new ArrayList<>();

        // 保证金转账相关规则
        dataList.add(
                builderRule(InitCategoryGroupEnum.ZZ, "支付宝账户-充值", "支付宝账户-充值",
                        Lists.newArrayList(
                                builderRule(GuaranteeMatchFieldEnum.REASON.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "充值/系统划扣充值")
                        )
                )
        );
        dataList.add(
                builderRule(InitCategoryGroupEnum.ZZ, "支付宝账户-提现转移", "支付宝账户-提现转移",
                        Lists.newArrayList(
                                builderRule(GuaranteeMatchFieldEnum.REASON.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "提现")
                        )
                )
        );
        dataList.add(
                builderRule(InitCategoryGroupEnum.ZZ, "微信账户-充值", "微信账户-充值",
                        Lists.newArrayList(
                                builderRule(GuaranteeMatchFieldEnum.BUSINESS_DESCRIPTION.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "008002800008|保证金-淘宝-缴存（维权锁定）")
                        )
                )
        );

        // 保证金扣费相关规则
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "交易赔付", "交易赔付-保证金扣款",
                        Lists.newArrayList(
                                builderRule(GuaranteeMatchFieldEnum.BUSINESS_DESCRIPTION.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "0070002|其他支出-交易赔付（保证金扣款）")
                        )
                )
        );
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "支付宝账户-运费赔付", "支付宝账户-运费赔付",
                        Lists.newArrayList(
                                builderRule(GuaranteeMatchFieldEnum.REASON.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "欠费划扣/平台垫资欠费/运费垫资欠费")
                        )
                )
        );
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "支付宝账户-账扣转移", "支付宝账户-账扣转移",
                        Lists.newArrayList(
                                builderRule(GuaranteeMatchFieldEnum.REASON.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "欠费划扣")
                        )
                )
        );

        // 交易赔付相关规则
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "交易赔付", "交易赔付-拒绝换货",
                        Lists.newArrayList(
                                builderRule(GuaranteeMatchFieldEnum.REASON.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "交易赔付/违背承诺/违背换货承诺/拒绝换货")
                        )
                )
        );
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "交易赔付", "交易赔付-缺货赔付",
                        Lists.newArrayList(
                                builderRule(GuaranteeMatchFieldEnum.REASON.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "交易赔付/违背承诺/违背发货承诺/缺货")
                        )
                )
        );
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "交易赔付", "交易赔付-物流轨迹异常",
                        Lists.newArrayList(
                                builderRule(GuaranteeMatchFieldEnum.REASON.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "交易赔付/违背承诺/违背发货承诺/物流轨迹异常")
                        )
                )
        );
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "交易赔付", "交易赔付-虚假发货",
                        Lists.newArrayList(
                                builderRule(GuaranteeMatchFieldEnum.REASON.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "交易赔付/违背承诺/违背发货承诺/虚假发货")
                        )
                )
        );
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "交易赔付", "交易赔付-延迟发货",
                        Lists.newArrayList(
                                builderRule(GuaranteeMatchFieldEnum.REASON.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "交易赔付/违背承诺/违背发货承诺/延迟发货")
                        )
                )
        );
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "交易赔付", "交易赔付-延迟换货",
                        Lists.newArrayList(
                                builderRule(GuaranteeMatchFieldEnum.REASON.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "交易赔付/违背承诺/违背换货承诺/延迟换货")
                        )
                )
        );
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "交易赔付", "交易赔付-运费",
                        Lists.newArrayList(
                                builderRule(GuaranteeMatchFieldEnum.REASON.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "交易赔付/争议处理/运费争议"),
                                builderRule(GuaranteeMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "卖家责任")
                        ),
                        Lists.newArrayList(
                                builderRule(GuaranteeMatchFieldEnum.REASON.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "交易赔付/争议处理/运费争议"),
                                builderRule(GuaranteeMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "退货邮费")
                        ),
                        Lists.newArrayList(
                                builderRule(GuaranteeMatchFieldEnum.REASON.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "交易赔付/争议赔付/额外赔付/运费")
                        )
                )
        );
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "交易赔付", "交易赔付-违背赠品承诺",
                        Lists.newArrayList(
                                builderRule(GuaranteeMatchFieldEnum.REASON.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "交易赔付/违背承诺/违背赠品承诺")
                        )
                )
        );
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "交易赔付", "交易赔付-未按时开具发票",
                        Lists.newArrayList(
                                builderRule(GuaranteeMatchFieldEnum.REASON.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "交易赔付")
                        )
                )
        );

        // 交易客诉 - 商家赔付相关规则
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "交易客诉-商家赔付", "交易客诉-商家赔付_骚扰他人-恶意骚扰",
                        Lists.newArrayList(
                                builderRule(GuaranteeMatchFieldEnum.REASON.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "交易赔付/骚扰他人/恶意骚扰")
                        )
                )
        );
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "交易客诉-商家赔付", "交易客诉-商家赔付_投诉赔付",
                        Lists.newArrayList(
                                builderRule(GuaranteeMatchFieldEnum.REASON.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "交易赔付/投诉赔付")
                        )
                )
        );

        // 微信货款 - 售后退款相关规则
        dataList.add(
                builderRule(InitCategoryGroupEnum.HK, "售后退款", "售后退款",
                        Lists.newArrayList(
                                builderRule(GuaranteeMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "售后退款")
                        )
                )
        );
        dataList.add(
                builderRule(InitCategoryGroupEnum.HK, "其他售后-客服", "其他售后-客服",
                        Lists.newArrayList(
                                builderRule(GuaranteeMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "refundplatform系统转移卖家保证金")
                        ),
                        Lists.newArrayList(
                                builderRule(GuaranteeMatchFieldEnum.REASON.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "交易赔付/争议处理/运费争议"),
                                builderRule(GuaranteeMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "-")
                        )
                )
        );
        return dataList;
}

        public static List<InitRule> listWechatInitAnalyzeRule() {
        List<InitRule> dataList = new ArrayList<>();

        // 微信货款相关规则
        dataList.add(
                builderRule(InitCategoryGroupEnum.HK, "货款收入", "货款收入",
                        Lists.newArrayList(
                                builderRule(WeChatMatchFieldEnum.ENTRY_TYPE.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "交易收款")
                        )
                )
        );
        dataList.add(
                builderRule(InitCategoryGroupEnum.HK, "退款", "交易退款",
                        Lists.newArrayList(
                                builderRule(WeChatMatchFieldEnum.ENTRY_TYPE.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "交易退款(售后)")
                        )
                )
        );

        // 微信扣费相关规则
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "基础软件服务费", "基础软件服务费",
                        Lists.newArrayList(
                                builderRule(WeChatMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "基础软件服务费")
                        )
                )
        );
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "淘宝内容推广服务费", "淘宝内容推广服务费",
                        Lists.newArrayList(
                                builderRule(WeChatMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "淘宝内容推广服务费")
                        )
                )
        );
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "光合平台软件服务费", "光合平台软件服务费",
                        Lists.newArrayList(
                                builderRule(WeChatMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "光合平台软件服务费")
                        )
                )
        );

        // 微信保证金相关规则
        dataList.add(
                builderRule(InitCategoryGroupEnum.BZJ, "保证金扣款", "保证金扣款",
                        Lists.newArrayList(
                                builderRule(WeChatMatchFieldEnum.ENTRY_TYPE.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "保证金扣款")
                        )
                )
        );

        // 微信提现相关规则
        dataList.add(
                builderRule(InitCategoryGroupEnum.TX, "提现", "提现",
                        Lists.newArrayList(
                                builderRule(WeChatMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "提现")
                        )
                )
        );

        return dataList;
    }

        public static List<InitRule> listAliPayInitAnalyzeRule(){
        List<InitRule> dataList = new ArrayList<>();

        // 支付宝货款相关规则
        dataList.add(
                builderRule(InitCategoryGroupEnum.HK, "货款收入", "货款收入",
                        Lists.newArrayList(builderRule(AlipayMatchFieldEnum.BIZ_DESCRIPTION.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "0010001|交易收款-交易收款")),
                        Lists.newArrayList(builderRule(AlipayMatchFieldEnum.BIZ_DESCRIPTION.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "0010002|交易收款-预售定金（买家责任不退还）"))
                )
        );
        dataList.add(
                builderRule(InitCategoryGroupEnum.HK, "退款", "货款退款",
                        Lists.newArrayList(builderRule(AlipayMatchFieldEnum.BIZ_DESCRIPTION.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "0020001|交易退款-余额退款"))
                )
        );
        dataList.add(
                builderRule(InitCategoryGroupEnum.HK, "国际卡拒付", "国际卡拒付",
                        Lists.newArrayList(builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "国际卡拒付"))
                )
        );

        // 支付宝保证金相关规则
        dataList.add(
                builderRule(InitCategoryGroupEnum.BZJ, "支付宝_保证金缴存", "支付宝_保证金_未按时发货赔付",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.BIZ_DESCRIPTION.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "008002800014|保证金-天猫-出账缴存"),
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "天猫保证金-充值（代扣）-延迟发货")
                        ),
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.BIZ_DESCRIPTION.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "008002800014|保证金-天猫-出账缴存"),
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "天猫保证金-充值（代扣）-承诺未履约_延迟换货")
                        ),
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.BIZ_DESCRIPTION.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "008002800014|保证金-天猫-出账缴存"),
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "天猫保证金-充值（代扣）-延迟发赠品")
                        ),
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.BIZ_DESCRIPTION.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "008002800014|保证金-天猫-出账缴存"),
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "天猫保证金-充值（代扣）-当天/24小时内未发货")
                        )
                )
        );

        dataList.add(
                builderRule(InitCategoryGroupEnum.BZJ, "支付宝_保证金缴存", "支付宝_保证金_未按时开具发票",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.BIZ_DESCRIPTION.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "008002800014|保证金-天猫-出账缴存"),
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "天猫保证金-充值（代扣）-未按时开具发票")
                        )
                )
        );

        dataList.add(
                builderRule(InitCategoryGroupEnum.BZJ, "支付宝_保证金缴存", "支付宝_保证金_承担退货运费",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.BIZ_DESCRIPTION.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "008002800014|保证金-天猫-出账缴存"),
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "天猫保证金-充值（代扣）-退货邮费")
                        ),
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.BIZ_DESCRIPTION.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "008002800014|保证金-天猫-出账缴存"),
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "天猫保证金-充值（代扣）-运费赔付")
                        )
                )
        );

        dataList.add(
                builderRule(InitCategoryGroupEnum.BZJ, "支付宝_保证金缴存", "支付宝_保证金_售后退款",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.BIZ_DESCRIPTION.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "008002800014|保证金-天猫-出账缴存"),
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "天猫保证金-充值（代扣）")
                        ),
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.BIZ_DESCRIPTION.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "008002800014|保证金-天猫-出账缴存"),
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "天猫保证金-充值（代扣）-售后退款")
                        ),
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.BIZ_DESCRIPTION.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "008002800014|保证金-天猫-出账缴存"),
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "天猫保证金-充值（代扣）-refundplatform系统转移卖家保证金")
                        )
                )
        );

        dataList.add(
                builderRule(InitCategoryGroupEnum.BZJ, "支付宝_保证金缴存", "支付宝_保证金_拒绝履行发赠品承诺",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.BIZ_DESCRIPTION.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "008002800014|保证金-天猫-出账缴存"),
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "天猫保证金-充值（代扣）-拒绝履行发赠品承诺")
                        )
                )
        );

        dataList.add(
                builderRule(InitCategoryGroupEnum.BZJ, "支付宝_保证金缴存", "支付宝_保证金_拒绝履行换货承诺",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.BIZ_DESCRIPTION.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "008002800014|保证金-天猫-出账缴存"),
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "天猫保证金-充值（代扣）-拒绝履行换货承诺")
                        )
                )
        );

        dataList.add(
                builderRule(InitCategoryGroupEnum.BZJ, "支付宝_保证金缴存", "支付宝_保证金_其他交易客诉",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.BIZ_DESCRIPTION.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "008002800014|保证金-天猫-出账缴存"),
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "天猫保证金-充值（代扣）-商家态度问题_态度差")
                        ),
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.BIZ_DESCRIPTION.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "008002800014|保证金-天猫-出账缴存"),
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "天猫保证金-充值（代扣）-双方协商一致")
                        ),
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.BIZ_DESCRIPTION.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "008002800014|保证金-天猫-出账缴存"),
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "天猫保证金-充值（代扣）-卖家责任")
                        ),
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.BIZ_DESCRIPTION.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "008002800014|保证金-天猫-出账缴存"),
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "天猫保证金-充值（代扣）-物流轨迹异常")
                        ),
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.BIZ_DESCRIPTION.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "008002800014|保证金-天猫-出账缴存"),
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "天猫保证金-充值（代扣）-承诺物流方式未履行")
                        ),
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.BIZ_DESCRIPTION.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "008002800014|保证金-天猫-出账缴存"),
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "天猫保证金-充值（代扣）-商家承诺未履约_承诺未履行")
                        )
                )
        );

        dataList.add(
                builderRule(InitCategoryGroupEnum.BZJ, "支付宝_保证金_追缴", "支付宝_保证金_追缴",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "天猫保证金-充值（代扣）-保证金追缴")
                        )
                )
        );


        //扣款相关
        // 返点积分
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "返点积分", "返点积分",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.BIZ_DESCRIPTION.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "0030001|软件服务费-天猫返点积分")
                        ),
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.BIZ_DESCRIPTION.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "0030018|软件服务费-天猫返点积分（退款）")
                        )
                )
        );

        // 信用卡支付服务费
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "信用卡支付服务费", "信用卡支付服务费",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.BIZ_DESCRIPTION.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "0030038|软件服务费-支付宝服务费"),
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "信用卡支付服务费")
                        )
                )
        );

        // 花呗支付服务费
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "花呗支付服务费", "花呗支付服务费",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.BIZ_DESCRIPTION.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "0030038|软件服务费-支付宝服务费"),
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "花呗支付服务费")
                        )
                )
        );

            // 光合平台软件服务费
            dataList.add(
                    builderRule(InitCategoryGroupEnum.KF, "光合平台软件服务费", "光合平台软件服务费",
                            Lists.newArrayList(
                                    builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "光合平台软件服务费")
                            )
                    )
            );

        // 花呗分期服务费
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "花呗分期服务费", "花呗分期服务费",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.BIZ_DESCRIPTION.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "003003800001|软件服务费-支付宝服务费-花呗分期")
                        )
                )
        );

        // 聚划算佣金
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "聚划算佣金", "聚划算佣金",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.BIZ_DESCRIPTION.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "0030007|软件服务费-聚划算")
                        )
                )
        );

        // 淘宝客佣金
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "淘宝客佣金", "淘宝客佣金",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "淘宝客佣金代扣款")
                        ),
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "淘宝联盟推广佣金")
                        ),
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "淘宝联盟佣金代扣")
                        )
                )
        );

        // 基础软件服务费
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "基础软件服务费", "基础软件服务费",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.BIZ_DESCRIPTION.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "0030130|软件服务费-基础软件服务费")
                        )
                )
        );

        // 商家集运物流服务费
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "商家集运物流服务费", "商家集运物流服务费",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "代扣款（扣款用途：商家集运物流服务费")
                        )
                )
        );

        // 商家集运中转操作费
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "商家集运中转操作费", "商家集运中转操作费",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "代扣款（扣款用途：商家集运中转操作费")
                        )
                )
        );

        // 淘宝内容推广服务费
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "淘宝内容推广服务费", "淘宝内容推广服务费",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.BIZ_DESCRIPTION.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "0030129|软件服务费-热浪引擎第三方服务商服务费（平台开票）")
                        )
                )
        );

        // 天猫佣金
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "天猫佣金", "天猫佣金",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.BIZ_DESCRIPTION.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "0030003|软件服务费-类目软件服务费（原天猫佣金）")
                        )
                )
        );

        // 佣金返还
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "佣金返还", "佣金返还",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "代扣款（扣款用途：天猫先收后返结算（佣金返还）")
                        )
                )
        );

        // 消费者体验提升计划服务费
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "消费者体验提升计划服务费", "消费者体验提升计划服务费",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.BIZ_DESCRIPTION.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "0170125T|服务费-消费者体验提升计划服务费")
                        )
                )
        );

        // 首单拉新计划
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "首单拉新计划", "首单拉新计划",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "品牌新享-首单拉新计划")
                        )
                )
        );

        // 创作者平台软件服务费
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "创作者平台软件服务费", "创作者平台软件服务费",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "创作者平台软件服务费")
                        )
                )
        );

        // 百亿补贴软件服务费
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "百亿补贴软件服务费", "百亿补贴软件服务费",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.BIZ_DESCRIPTION.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "0030088|软件服务费-百亿补贴软件服务费（渠道）")
                        )
                )
        );

        // 88会员专享折扣服务费
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "88会员专享折扣服务费", "88会员专享折扣服务费",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "88会员专享折扣服务费")
                        )
                )
        );

        // 88VIP淘宝消费券技术服务费
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "88VIP淘宝消费券技术服务费", "88VIP淘宝消费券技术服务费",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "88VIP淘宝消费券技术服务费")
                        )
                )
        );

        // 生意参谋退款
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "生意参谋退款", "生意参谋退款",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "代扣款（扣款用途：2024淘宝惠商计划退款_生意参谋")
                        )
                )
        );

        // 未知软件费返还
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "未知软件费返还", "未知软件费返还",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "代扣款（扣款用途：明星店铺退款 ")
                        )
                )
        );

        // 运费险 - 卖家版 - 天安
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "运费险", "运费险-卖家版-天安",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "保险承保-卖家版运费险保费收取"),
                                builderRule(AlipayMatchFieldEnum.COUNTERPARTY_ACCOUNT.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "天安财产保险股份有限公司")
                        )
                )
        );

        // 运费险 - 聚划算版 - 国泰
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "运费险", "运费险-聚划算版-国泰",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "保险承保-聚划算运费险保费收取"),
                                builderRule(AlipayMatchFieldEnum.COUNTERPARTY_ACCOUNT.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "国泰财产保险有限责任公司")
                        )
                )
        );

        // 运费险 - 卖家版 - 国泰
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "运费险", "运费险-卖家版-国泰",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "保险承保-卖家版运费险保费收取"),
                                builderRule(AlipayMatchFieldEnum.COUNTERPARTY_ACCOUNT.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "国泰财产保险有限责任公司")
                        )
                )
        );

        // 运费险 - 严选直播 - 国泰
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "运费险", "运费险-严选直播-国泰",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "保险承保-严选直播运费险保费收取"),
                                builderRule(AlipayMatchFieldEnum.COUNTERPARTY_ACCOUNT.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "国泰财产保险有限责任公司")
                        )
                )
        );

        // 运费险 - 百亿补贴 - 国泰
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "运费险", "运费险-百亿补贴-国泰",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "保险承保-百亿补贴运费险保费收取"),
                                builderRule(AlipayMatchFieldEnum.COUNTERPARTY_ACCOUNT.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "国泰财产保险有限责任公司")
                        )
                )
        );

        // 运费险 - 卖家版 - 人民财产
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "运费险", "运费险-卖家版-人民财产",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "保险承保-卖家版运费险保费收取"),
                                builderRule(AlipayMatchFieldEnum.COUNTERPARTY_ACCOUNT.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "中国人民财产保险股份有限公司")
                        )
                )
        );

        // 运费险 - 聚划算版 - 人民财产
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "运费险", "运费险-聚划算版-人民财产",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "保险承保-聚划算运费险保费收取"),
                                builderRule(AlipayMatchFieldEnum.COUNTERPARTY_ACCOUNT.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "中国人民财产保险股份有限公司")
                        )
                )
        );

        // 运费险 - 聚划算版 - 中国人寿
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "运费险", "运费险-聚划算版-中国人寿",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "保险承保-聚划算运费险保费收取"),
                                builderRule(AlipayMatchFieldEnum.COUNTERPARTY_ACCOUNT.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "中国人寿财产保险股份有限公司")
                        )
                )
        );

        // 运费险 - 聚划算版 - 太平洋
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "运费险", "运费险-聚划算版-太平洋",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "保险承保-聚划算运费险保费收取"),
                                builderRule(AlipayMatchFieldEnum.COUNTERPARTY_ACCOUNT.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "中国太平洋财产保险股份有限公司")
                        )
                )
        );

        // 运费险 - 卖家版 - 太平洋
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "运费险", "运费险-卖家版-太平洋",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "保险承保-卖家版运费险保费收取"),
                                builderRule(AlipayMatchFieldEnum.COUNTERPARTY_ACCOUNT.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "中国太平洋财产保险股份有限公司")
                        )
                )
        );

        // 拍卖一口价商品交易软件服务费
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "拍卖一口价商品交易软件服务费", "拍卖一口价商品交易软件服务费",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "拍卖一口价商品交易软件服务费")
                        )
                )
        );

        // 拍卖服务费
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "拍卖服务费", "拍卖服务费",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "拍卖服务费")
                        )
                )
        );

        // 天猫极速提现服务费
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "天猫极速提现服务费", "天猫极速提现服务费",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "天猫极速提现服务费")
                        )
                )
        );

        // 直播运费险
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "直播运费险", "直播运费险",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "直播运费险")
                        )
                )
        );

        // 惠商计划退款
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "惠商计划退款", "惠商计划退款",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "惠商计划退款")
                        )
                )
        );

        // 品牌新享会员礼金
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "品牌新享会员礼金", "品牌新享会员礼金",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "品牌新享会员礼金")
                        )
                )
        );

        // 先用后付技术服务费
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "先用后付技术服务费", "先用后付技术服务费",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "先用后付技术服务费")
                        )
                )
        );

        // 直播软件服务费
        dataList.add(
                builderRule(InitCategoryGroupEnum.KF, "直播软件服务费", "直播软件服务费",
                        Lists.newArrayList(
                                builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "直播软件服务费")
                        )
                )
        );




            // 支付宝提现相关规则
            dataList.add(
                    builderRule(InitCategoryGroupEnum.TX, "提现", "提现",
                            Lists.newArrayList(
                                    builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "提现"),
                                    builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "退代收代付")
                            )
                    )
            );



            // 支付宝其他相关规则
            dataList.add(
                    builderRule(InitCategoryGroupEnum.QT, "年费返还", "年费返还",
                            Lists.newArrayList(
                                    builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "技术年费年度结算")
                            )
                    )
            );

            // 业务充值规则
            dataList.add(
                    builderRule(InitCategoryGroupEnum.QT, "业务充值", "业务充值",
                            Lists.newArrayList(
                                    builderRule(WeChatMatchFieldEnum.ENTRY_TYPE.getFieldCode(), AnalyzeRuleOperatorEnum.EQUAL, "充值（大额充值）")
                            )
                    )
            );

            // 基金代发任务规则
            dataList.add(
                    builderRule(InitCategoryGroupEnum.QT, "基金代发任务", "基金代发任务",
                            Lists.newArrayList(
                                    builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "代扣款（扣款用途：基金代发任务")
                            )
                    )
            );

            // 商家权益红包规则
            dataList.add(
                    builderRule(InitCategoryGroupEnum.QT, "商家权益红包", "商家权益红包",
                            Lists.newArrayList(
                                    builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "商家权益红包")
                            ),
                            Lists.newArrayList(
                                    builderRule(AlipayMatchFieldEnum.REMARK.getFieldCode(), AnalyzeRuleOperatorEnum.CONTAINS, "商家权益红包-红包退回")
                            )
                    )
            );


            // 依次按照表格添加其他保证金转账相关规则
        return dataList;
    }
}
