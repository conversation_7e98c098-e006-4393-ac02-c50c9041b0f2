package com.raycloud.dmj.account.export.sevice;

import com.raycloud.dmj.account.core.base.dao.StandardFundBillFlowInfoDao;
import com.raycloud.dmj.account.core.bill.parameter.StandardFundBillFlowParameter;
import com.raycloud.dmj.account.core.cleancategory.service.CategoryManageService;
import com.raycloud.dmj.account.core.common.constant.Constant;
import com.raycloud.dmj.account.core.enums.CategoryCodeEnum;
import com.raycloud.dmj.account.core.enums.IncomeExpenseDirectionEnum;
import com.raycloud.dmj.account.core.enums.PlatformEnum;
import com.raycloud.dmj.account.core.enums.StandardFundBillSourceEnum;
import com.raycloud.dmj.account.core.pageconfig.service.IPageColumnConfigService;
import com.raycloud.dmj.account.export.core.parameter.ConsignExpressSQLParameter;
import com.raycloud.dmj.account.export.core.processor.BillFundExportResultProcessor;
import com.raycloud.dmj.account.export.core.statistics.export.VirtualProgressCallback;
import com.raycloud.dmj.account.export.core.statistics.interf.IExport;
import com.raycloud.dmj.account.infra.repository.base.DbQuery;
import com.raycloud.dmj.data.chessboard.listener.ExportContext;
import com.raycloud.dmj.data.export.core.MultiDataWriter;
import com.raycloud.dmj.data.export.imp.easyexcel.StyleMultiDataWriter;
import com.raycloud.dmj.data.export.utils.DataWriters;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.services.utils.ClueIdUtil;
import com.raycloud.dmj.table.api.plus.common.base.SQL;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Slf4j
@Component
@RequiredArgsConstructor
public class BillFundExport implements IExport<StandardFundBillFlowParameter> {

    private final DbQuery dbQuery;

    private final StandardFundBillFlowInfoDao standardFundBillFlowInfoDao;

    private final IPageColumnConfigService pageColumnConfigService;

    private final CategoryManageService categoryManageService;

    @Override
    public void doExport(Staff staff, StandardFundBillFlowParameter parameter, MultiDataWriter writer) throws Exception {
        // 默认为支持样式的excel写入器
        final StyleMultiDataWriter dataWriter = DataWriters.convertToStyleMultiDataWriter(writer);
//        // 参数转换
        ConsignExpressSQLParameter sqlParameter = new ConsignExpressSQLParameter();
        sqlParameter.setStaff(staff);
        sqlParameter.setClueId(ClueIdUtil.getClueId());
        sqlParameter.setPageId(parameter.getPageId());
        sqlParameter.setEffectiveColumnNames(pageColumnConfigService.getEffectiveColumnNames(staff.getCompanyId(), parameter.getPageId()));
        //设置分类名称
        sqlParameter.setCategoryGroupCodeNames(categoryManageService.getCategoryGroupMap(staff.getCompanyId()));
        sqlParameter.setCategoryNames(categoryManageService.getCategoryMap(staff.getCompanyId()));
        sqlParameter.setSubCategoryNames(categoryManageService.getSubCategoryMap(staff.getCompanyId()));

        // 构建SQL
        SQL sql = standardFundBillFlowInfoDao.getExportSQL(parameter, staff.getCompanyId());
        // 创建结果处理器
        BillFundExportResultProcessor processor = new BillFundExportResultProcessor();
        // 处理器初始化
        processor.start(staff, sqlParameter);

        try {
            //TODO 这里数据库的connectorId目前先写死
            dbQuery.cursor(Constant.ORIGINAL_DATA_BASE_CONNECTION_KEY, sql, new VirtualProgressCallback() {
                @Override
                public void doAccept(ExportContext context, List<Map<String, Object>> datas) throws Exception {
                    //对数据进行转化
                    convertList(datas, sqlParameter);
                    // 处理数据
                    List<Map<String, Object>> maps = processor.handle(staff, sqlParameter, datas);
                    // 写入文件
                    dataWriter.write(maps);
                }
            });
        } finally {
            processor.finished(staff, sqlParameter);
        }
    }

    //数据转化
    private void convertList(List<Map<String, Object>> datas, ConsignExpressSQLParameter sqlParameter) {
        if (datas == null || datas.isEmpty() || sqlParameter == null) {
            return;
        }
        List<String> effectiveColumnNames = sqlParameter.getEffectiveColumnNames();
        if (effectiveColumnNames == null) {
            return;
        }
        datas.forEach(map -> {
            //平台名称
            if (effectiveColumnNames.contains("platform_name")){
                String platformCode = (String) map.get("platform_code");
                if (platformCode != null) {
                    PlatformEnum platformEnum = PlatformEnum.getPlatformEnumByCode(platformCode);
                    if (platformEnum != null) {
                        map.put("platform_name", platformEnum.getTitle());
                    }
                }
            }
            if (effectiveColumnNames.contains("category_code_name")){
                Object categoryCodeName = map.get("category_code_name");
                if (categoryCodeName != null && categoryCodeName.equals(CategoryCodeEnum.UNKNOWN.getCode())){
                    map.put("category_code_name",CategoryCodeEnum.getByDesc((String) categoryCodeName) );
                }else {
                    if (effectiveColumnNames.contains("category_name")){
                        Long categoryId = (Long) map.get("category_id");
                        if (categoryId != null && sqlParameter.getCategoryNames() != null) {
                            map.put("category_name", sqlParameter.getCategoryNames().get(categoryId));
                        }
                    }
                    if (effectiveColumnNames.contains("sub_category_name")){
                        Long subCategoryId = (Long) map.get("sub_category_id");
                        if (subCategoryId != null && sqlParameter.getSubCategoryNames() != null) {
                            map.put("sub_category_name", sqlParameter.getSubCategoryNames().get(subCategoryId));
                        }
                    }
                    String categoryCode = (String) map.get("category_code");
                    if (categoryCode != null && sqlParameter.getCategoryGroupCodeNames() != null) {
                        map.put("category_code_name", sqlParameter.getCategoryGroupCodeNames().get(categoryCode));
                    }
                }
            }
            //收支方向
            if (effectiveColumnNames.contains("income_expense_direction")){
                map.put("income_expense_direction",IncomeExpenseDirectionEnum.getDescBySourceCode((Integer) map.get("income_expense_direction")));
            }
            //是否可抵消
            if (effectiveColumnNames.contains("is_offset")){
                Integer isOffset = (Integer) map.get("is_offset");
                if (isOffset != null) {
                    map.put("is_offset", isOffset == 1 ? "是" : "否");
                }
            }
            //是否手动拆分
            if (effectiveColumnNames.contains("is_split")){
                Integer isSplit = (Integer) map.get("is_split");
                if (isSplit != null) {
                    map.put("is_split", isSplit == 1 ? "是" : "否");
                }
            }
            //是否关联业务单据
            if (effectiveColumnNames.contains("is_related_doc")){
                Object docNo = map.get("doc_no");
                if (docNo != null) {
                    map.put("is_related_doc", docNo.equals("-1") ? "否" : "是");
                }
            }
            //账单来源
            if (effectiveColumnNames.contains("source")){
                map.put("source", StandardFundBillSourceEnum.getDesByCode((Integer) map.get("source")));
            }
            //是否确认
            if (effectiveColumnNames.contains("confirmed")){
                if (map.get("confirmed") != null){
                    map.put("confirmed", (Integer) map.get("confirmed") == 1 ? "是" : "否");
                }else {
                    map.putIfAbsent("confirmed", "否");
                }
            }
        });
    }
}
