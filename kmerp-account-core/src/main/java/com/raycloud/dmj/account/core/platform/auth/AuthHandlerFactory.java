package com.raycloud.dmj.account.core.platform.auth;

import com.google.common.collect.Maps;
import org.springframework.lang.NonNull;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.Map;
@Component
public class AuthHandlerFactory implements InitializingBean, ApplicationContextAware {

    private static final Map<String, AbstractPlatformHandler> HANDLER_MAP = Maps.newHashMap();

    private ApplicationContext appContext;

    public AbstractPlatformHandler getHandler(String jobType) {
        AbstractPlatformHandler handler = HANDLER_MAP.get(jobType);
        if (handler == null) {
            throw new RuntimeException("无对应平台处理类");
        }
        return handler;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        appContext.getBeansOfType(AbstractPlatformHandler.class).values()
                .forEach(handler -> HANDLER_MAP.put(handler.getPlatformType().getValue(), handler));
    }

    @Override
    public void setApplicationContext(@NonNull ApplicationContext applicationContext) throws BeansException {
        this.appContext = applicationContext;
    }
}