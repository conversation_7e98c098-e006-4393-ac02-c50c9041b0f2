package com.raycloud.dmj.account.core.common.response;


import lombok.Getter;
import lombok.Setter;
import org.springframework.http.HttpStatus;

@Getter
@Setter
public class FailResponse<T> extends BaseResponse<T> {

    public FailResponse(String message){
        this.setResult(HttpStatus.EXPECTATION_FAILED.value());
        this.setMessage(message);
    }

    public FailResponse(Integer result, String message){
        this.setResult(result);
        this.setMessage(message);
    }

    /**
     * 错误信息描述
     */
    private String message;

}
