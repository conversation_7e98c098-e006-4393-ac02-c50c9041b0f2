package com.raycloud.dmj.account.core.shop.vo;

import com.raycloud.dmj.account.core.common.BaseInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 店铺信息表实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShopInfoVO extends BaseInfo {

    /**
     * 店铺ID，主键自增
     */
    private Long id;

    /**
     * 平台编码
     */
    private String platformCode;

    /**
     * 平台名称
     */
    private String platformName;

    /**
     * 店铺编码
     */
    private String shopCode;

    /**
     * 店铺名称
     */
    private String title;

    /**
     * 店铺简称
     */
    private String shortTitle;

    /**
     * 所属公司ID
     */
    private Long affiliatedCompanyId;

    /**
     * 所属公司名称 不入库字段
     */
    private Long affiliatedCompanyName;

    /**
     * 期初应收余额
     */
    private BigDecimal amount;

    /**
     * 对帐状态 0:关闭对帐 1:暂停对帐 2:开启对帐
     */
    private Integer accountStatus;

    /**
     * 期初时间
     */
    private Date startDate;

    /**
     * 对帐维度类型 1：自然月 2:财务月
     */
    private Integer reconciliationType;

    /**
     * 财务月截止时间(每个月多少号) 示例：1 代表每个月1号，最大28
     */
    private Integer reconciliationDate;

    /**
     * 启用状态 0:弃用 1:正常
     */
    private Integer enableStatus;

}
