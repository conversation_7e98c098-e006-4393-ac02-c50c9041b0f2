
package com.raycloud.dmj.account.core.enums.field;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 监控规则表字段枚举
 *
 * <AUTHOR>
 */
@Getter
public enum MonitorRuleFieldEnum {

    ID("id", "自增主键"),
    CREATED("created", "创建时间"),
    MODIFIED("modified", "更新时间"),
    MONITOR_RULE("monitor_rule", "监控规则"),
    COMPANY_ID("company_id", "公司ID"),
    SHOP_ID("shop_id", "店铺ID"),
    DATA_SOURCE("data_source", "数据类型：支付宝、微信、集分宝等"),
    DATE_TYPE("date_type", "账单类型：1=日，2=月，3=年");

    /**
     * 字段编码
     */
    private final String fieldCode;

    /**
     * 字段描述
     */
    private final String fieldDesc;

    MonitorRuleFieldEnum(String fieldCode, String fieldDesc) {
        this.fieldCode = fieldCode;
        this.fieldDesc = fieldDesc;
    }

    /**
     * 获取插入字段
     *
     * @return 插入字段
     */
    public static Set<String> getInsertFields() {
        List<MonitorRuleFieldEnum> filterField = Arrays.asList(
                MonitorRuleFieldEnum.ID
        );
        return Arrays.stream(MonitorRuleFieldEnum.values()).filter(x -> !filterField.contains(x)).map(
                x -> x.fieldCode
        ).collect(Collectors.toSet());
    }
}

