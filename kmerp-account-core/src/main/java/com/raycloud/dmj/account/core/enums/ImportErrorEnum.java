package com.raycloud.dmj.account.core.enums;

public enum ImportErrorEnum {
    FILE_NOT_EXIST(1, "未匹配到当前的配置信息"),
    NOT_CONFIG_DATE_FIELD(2, "没有配置日期字段"),
    NOT_FILE_DATE_PARAM_ERROR(3, "文件日期不存在的情况下，日期传入参数必须为一个日期"),
    FILE_TYPE_ERROR(4, "文件类型错误,不支持当前文件类型"),
    BIZ_ERROR(5, "业务报错"),
    ;

    private final Integer code;
    private final String desc;
    ImportErrorEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    public Integer getCode() {
        return code;
    }
    public String getDesc() {
        return desc;
    }
    public static ImportErrorEnum getByCode(Integer code) {
        for (ImportErrorEnum value : ImportErrorEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
