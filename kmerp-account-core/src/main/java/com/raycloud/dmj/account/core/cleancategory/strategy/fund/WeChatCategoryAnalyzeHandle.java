package com.raycloud.dmj.account.core.cleancategory.strategy.fund;

import com.raycloud.dmj.account.core.base.dao.FundAccountDao;
import com.raycloud.dmj.account.core.base.dao.TmallWechatBalanceRawBillDao;
import com.raycloud.dmj.account.core.base.dao.TmallWechatRawBillDao;
import com.raycloud.dmj.account.core.base.domain.StandardFundBillFlowInfoDO;
import com.raycloud.dmj.account.core.cleancategory.domain.object.FundAccountDO;
import com.raycloud.dmj.account.core.cleancategory.domain.object.TmallWechatBalanceRawBillDO;
import com.raycloud.dmj.account.core.cleancategory.domain.object.TmallWechatRawBillDataDO;
import com.raycloud.dmj.account.core.cleancategory.strategy.param.CategoryAnalyzeParam;
import com.raycloud.dmj.account.core.common.Page;
import com.raycloud.dmj.account.core.enums.AccountTypeEnum;
import com.raycloud.dmj.account.core.enums.IncomeExpenseDirectionEnum;
import com.raycloud.dmj.account.core.enums.RawDataSourceEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * 微信分类解析
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class WeChatCategoryAnalyzeHandle extends StandardFundCategoryAnalyzeHandle<TmallWechatRawBillDataDO> {


    @Resource
    private TmallWechatRawBillDao tmallWechatRawBillDao;

    @Resource
    private TmallWechatBalanceRawBillDao tmallWechatBalanceRawBillDao;

    @Resource
    private FundAccountDao fundAccountDao;


    @Override
    protected FundAccountDO getFundAccount(CategoryAnalyzeParam param) {
        //根据查询资金账号类型为支付宝资金账户
        return fundAccountDao.getByShopIdAndType(param.getCompanyId(), param.getShopId(),
                AccountTypeEnum.TM_WECHAT.getTypeCode());
    }

    @Override
    protected Pair<BigDecimal, BigDecimal> calculateBalance(CategoryAnalyzeParam param) {
        //查询期初余额
        TmallWechatBalanceRawBillDO wechatBalanceRawBillDaoByDataRange = tmallWechatBalanceRawBillDao.getByDataRange(param.getCompanyId(), param.getShopId(), param.getDataRange());
        BigDecimal startBalance = wechatBalanceRawBillDaoByDataRange.getEndBalance()
                .add(wechatBalanceRawBillDaoByDataRange.getExpenditureAmount())
                .subtract(wechatBalanceRawBillDaoByDataRange.getIncomeAmount());

        //查询期末余额
        BigDecimal endBalance = wechatBalanceRawBillDaoByDataRange.getEndBalance();
        return Pair.of(startBalance, endBalance);
    }

    @Override
    protected List<TmallWechatRawBillDataDO> listPageRawData(CategoryAnalyzeParam param, Page page) {
        return tmallWechatRawBillDao.listPageByDataRange(
                param.getCompanyId(),
                param.getShopId(),
                param.getDataRange(),
                page
        );
    }



    @Override
    protected void setStandardFundBillFlowInfoDO(StandardFundBillFlowInfoDO standardFundBillFlowInfoDO, TmallWechatRawBillDataDO tmallWechatRawBillDataDO) {
        standardFundBillFlowInfoDO.setOccurredAt(tmallWechatRawBillDataDO.getEntryTime());
        //金额：原始数据表中有收入金额和支出金额字段，标准资金流水表中正数为收入，负数为支出
        BigDecimal amount;
        Integer incomeExpenseDirection;
        if (tmallWechatRawBillDataDO.getIncomeAmount().compareTo(BigDecimal.ZERO) > 0){
            amount = tmallWechatRawBillDataDO.getIncomeAmount();
            incomeExpenseDirection = IncomeExpenseDirectionEnum.INCOME.getCode();
        }else {
            //微信中支出金额为正数 故取反
            amount = tmallWechatRawBillDataDO.getExpenseAmount().negate();
            incomeExpenseDirection = IncomeExpenseDirectionEnum.EXPENSE.getCode();
        }
        standardFundBillFlowInfoDO.setAmount(amount);
        standardFundBillFlowInfoDO.setIncomeExpenseDirection(incomeExpenseDirection);
        standardFundBillFlowInfoDO.setBillNo(tmallWechatRawBillDataDO.getPaymentFlowNo());
        standardFundBillFlowInfoDO.setOrderNo(tmallWechatRawBillDataDO.getTaobaoOrderNo());
//                standardFundBillFlowInfoDO.setDocNo(x.getBizOrderNo());
        standardFundBillFlowInfoDO.setRemark(tmallWechatRawBillDataDO.getRemark());
        standardFundBillFlowInfoDO.setDataRange(tmallWechatRawBillDataDO.getBatchTime());
        standardFundBillFlowInfoDO.setBatchNo(tmallWechatRawBillDataDO.getBatchNo());
        standardFundBillFlowInfoDO.setBizKey(tmallWechatRawBillDataDO.getBizKey());
    }


    @Override
    public boolean accord(RawDataSourceEnum source) {
        return RawDataSourceEnum.WECHAT.equals(source);
    }
}
