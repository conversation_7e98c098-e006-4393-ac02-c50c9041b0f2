package com.raycloud.dmj.account.core.rawdata.callback;

import com.alibaba.nacos.client.naming.utils.CollectionUtils;
import com.raycloud.dmj.account.core.rawdata.callback.param.BatchCallbackContext;
import com.raycloud.dmj.account.core.common.constant.TempFilePathBizType;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import com.raycloud.dmj.account.core.rawdata.utils.BizExcelConfigUtils;
import com.raycloud.dmj.account.core.rawdata.utils.ExportExcelFailDataToCsv;
import com.raycloud.dmj.account.core.rawdata.utils.OutputFilePathUtil;
import com.raycloud.dmj.account.core.rawdata.domains.*;
import com.raycloud.dmj.account.core.enums.FileTypeEnum;
import com.raycloud.dmj.account.core.rawdata.handle.RawDataStorageHandle;
import com.raycloud.dmj.account.core.rawdata.handle.param.MonitorDataInfo;
import com.raycloud.dmj.account.core.rawdata.handle.param.RawDataStorageBatchInsertContext;
import com.raycloud.dmj.account.infra.common.BusinessException;
import com.raycloud.dmj.account.infra.common.ErrorCodeEnum;
import com.raycloud.readexcel.callback.BatchCallback;
import com.raycloud.readexcel.constant.SheetTypeEnum;
import com.raycloud.readexcel.context.CompleteContext;
import com.raycloud.readexcel.context.ResultFailContext;
import com.raycloud.readexcel.context.ResultHeadContext;
import com.raycloud.readexcel.context.ResultSuccessContext;
import com.raycloud.readexcel.domain.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Date:  2025/6/13 表格
 *
 * <AUTHOR>
 */
@Slf4j
public class RawDataStorageBatchCallback implements BatchCallback<ImportStaffInfo> {

    private final RawDataStorageHandle rawDataStorageHandle;
    /**
     * 上下文参数
     */
    private final BatchCallbackContext batchCallbackContext;

    public RawDataStorageBatchCallback(RawDataStorageHandle rawDataStorageHandle, BatchCallbackContext batchCallbackContext) {
        AsserUtils.notNull(rawDataStorageHandle, "rawDataStorageHandle is null");
        AsserUtils.notNull(batchCallbackContext, "batchCallbackContext is null");
        AsserUtils.notNull(batchCallbackContext.getCompanyId(), "companyId is null");
        AsserUtils.notNull(batchCallbackContext.getShopId(), "shopId is null");
        AsserUtils.notNull(batchCallbackContext.getSheetConfigIdMap(), "batchCallbackContext.getSheetConfigIdMap() is null");
        AsserUtils.notNull(batchCallbackContext.getLastRecordId(), "batchCallbackContext.getLastRecordId() is null");
        AsserUtils.notNull(batchCallbackContext.getDateType(), "batchCallbackContext.getDateType() is null");
        AsserUtils.notNull(batchCallbackContext.getHandleBatchNoSet(), "batchCallbackContext.getHandleBatchNoList() is null");
        AsserUtils.notNull(batchCallbackContext.getFileAnalyzeCountInfoHashMap(), "batchCallbackContext.getFileAnalyzeCountInfo() is null");
        AsserUtils.notNull(batchCallbackContext.getDataSource(), "batchCallbackContext.getDataSource() is null");
        this.rawDataStorageHandle = rawDataStorageHandle;
        this.batchCallbackContext = batchCallbackContext;
    }


    @Override
    public Integer callbackSize() {
        return 10000;
    }

    @Override
    public void headResult(ResultHeadContext<ImportStaffInfo> resultHeadContext) {
        HashMap<String, FileAnalyzeCountInfo> fileAnalyzeCountInfoHashMap = batchCallbackContext.getFileAnalyzeCountInfoHashMap();
        FileAnalyzeCountInfo fileAnalyzeCountInfo1 = fileAnalyzeCountInfoHashMap.get(resultHeadContext.getExcelInfo().getFileName());
        if (fileAnalyzeCountInfo1 == null) {
            fileAnalyzeCountInfo1 = new FileAnalyzeCountInfo();
            fileAnalyzeCountInfo1.setFileName(resultHeadContext.getExcelInfo().getFileName());
            Integer sheetIndex = resultHeadContext.getSheetIndex();
            Map<Integer, FileSheetCountInfo> fileSheetCountInfoMap = new HashMap<>();
            FileSheetCountInfo fileSheetCountInfo = new FileSheetCountInfo();
            fileSheetCountInfo.setSheetIndex(sheetIndex);
            fileSheetCountInfo.setSheetName(resultHeadContext.getSheetName());
            fileSheetCountInfoMap.put(sheetIndex,fileSheetCountInfo);
            fileAnalyzeCountInfo1.setFileSheetCountInfoMap(fileSheetCountInfoMap);
            fileAnalyzeCountInfoHashMap.put(resultHeadContext.getExcelInfo().getFileName(),fileAnalyzeCountInfo1);
        }else {
            Map<Integer, FileSheetCountInfo> fileSheetCountInfoMap = fileAnalyzeCountInfo1.getFileSheetCountInfoMap();
            FileSheetCountInfo fileSheetCountInfo = fileSheetCountInfoMap.get(resultHeadContext.getSheetIndex());
            if (fileSheetCountInfo == null){
                fileSheetCountInfo = new FileSheetCountInfo();
                fileSheetCountInfo.setSheetIndex(resultHeadContext.getSheetIndex());
                fileSheetCountInfo.setSheetName(resultHeadContext.getSheetName());
                fileSheetCountInfoMap.put(resultHeadContext.getSheetIndex(),fileSheetCountInfo);
            }
        }
    }


    @Override
    public void successResult(ResultSuccessContext<ImportStaffInfo> resultSuccessContext) {
        try {
            ImportStaffInfo userDefinedData = resultSuccessContext.getUserDefinedData();
            String sheetName = resultSuccessContext.getSheetName();
            Integer sheetIndex = resultSuccessContext.getSheetIndex();
            String fileName = resultSuccessContext.getExcelInfo().getFileName();
            Map<String, List<Map<String, Object>>> result = resultSuccessContext.getResult();
            List<ExcelConfig> excelConfigList = resultSuccessContext.getExcelConfig();
            ExcelConfig excelConfig = findExcelConfig(sheetIndex, sheetName, excelConfigList);
            if (excelConfig == null) {
                throw new BusinessException(ErrorCodeEnum.CONFIG_ERROR.getCode(),
                        String.format("未找到对应的表配置,sheetName=%s", sheetName));
            }
            handleResultData(userDefinedData, result, excelConfig,fileName,sheetIndex);
            handleSuccessAndFilterCount(resultSuccessContext,batchCallbackContext);
        } catch (BusinessException e) {
            //发生异常，则将业务异常标修改为失败 在外侧将处理过的批次处理为失败
            batchCallbackContext.setBizErrorFlag(true);
            log.error("|RawDataStorageBatchCallback.successResult systemError|errorCode={},errorMsg={}", e.getErrorCode(), e.getErrorMessage(), e);
            throw e;
        } catch (Exception e) {
            //发生异常，则将业务异常标修改为失败 在外侧将处理过的批次处理为失败
            batchCallbackContext.setBizErrorFlag(true);
            log.error("|RawDataStorageBatchCallback.successResult error|errorMsg={}", e.getMessage(), e);
            throw e;
        }

    }

    private void handleSuccessAndFilterCount(ResultSuccessContext<ImportStaffInfo> resultSuccessContext, BatchCallbackContext batchCallbackContext) {
        HashMap<String, FileAnalyzeCountInfo> fileAnalyzeCountInfoHashMap = batchCallbackContext.getFileAnalyzeCountInfoHashMap();
        FileAnalyzeCountInfo fileAnalyzeCountInfo = fileAnalyzeCountInfoHashMap.get(resultSuccessContext.getExcelInfo().getFileName());
        Map<Integer, FileSheetCountInfo> fileSheetCountInfoMap = fileAnalyzeCountInfo.getFileSheetCountInfoMap();
        FileSheetCountInfo fileSheetCountInfo = fileSheetCountInfoMap.get(resultSuccessContext.getSheetIndex());
        List<ExcelFilterData> filterResult = resultSuccessContext.getFilterResult();
        if (filterResult != null && !filterResult.isEmpty()) {
            List<Integer> filterLineList = filterResult.stream().map(s -> s.getLine()).distinct().collect(Collectors.toList());
            fileSheetCountInfo.getFilterCount().addAndGet(filterLineList.size());
            fileSheetCountInfo.getInnerFilterCount().addAndGet(filterLineList.size());
        }
        fileSheetCountInfo.getSuccessCount().addAndGet(resultSuccessContext.getSuccessCount());
    }


    /**
     * 根据sheetName查找对应的表配置
     *
     * @param sheetIndex      sheetIndex
     * @param sheetName       sheetName
     * @param excelConfigList 表配置列表
     * @return 表配置
     */
    private ExcelConfig findExcelConfig(Integer sheetIndex, String sheetName, List<ExcelConfig> excelConfigList) {
        return excelConfigList.stream()
                .filter(x -> {
                    if (SheetTypeEnum.SHEET_INDEX.getSheetType().equals(x.getSheetType())) {
                        return StringUtils.equals(x.getSheet(), String.valueOf(sheetIndex));
                    } else if (SheetTypeEnum.SHEET_NAME.getSheetType().equals(x.getSheetType())) {
                        return StringUtils.equals(x.getSheet(), sheetName);
                    } else {
                        return false;
                    }
                })
                .findFirst()
                .orElseThrow(() -> new BusinessException(ErrorCodeEnum.CONFIG_ERROR.getCode(),
                        String.format("未找到对应的表配置,sheetName=%s", sheetName)));
    }

    /**
     * 处理结果数据
     *
     * @param result      结果数据
     * @param excelConfig 表配置
     */
    private void handleResultData(ImportStaffInfo userDefinedData, Map<String, List<Map<String, Object>>> result, ExcelConfig excelConfig,String fileName,Integer sheetIndex) {
        //获取sheetKey，用于获取导入数据配置ID
        String sheetKey = BatchCallbackContext.getSheetKey(excelConfig.getSheet(), excelConfig.getSheetType());
        result.forEach((basicTableName, dataList) -> {
            //获取数据映射配置
            DataMapping dataMapping = findDataMapping(basicTableName, excelConfig);
            //构建批量插入上下文
            RawDataStorageBatchInsertContext context = buildInsertContext(userDefinedData, basicTableName, sheetKey, dataMapping, dataList,fileName,sheetIndex);
            //批量插入数据
            rawDataStorageHandle.batchInsert(context);
        });
    }

    /**
     * 获取数据映射配置
     *
     * @param tableName   表名
     * @param excelConfig 表配置
     * @return 数据映射配置
     */
    private DataMapping findDataMapping(String tableName, ExcelConfig excelConfig) {
        return excelConfig.getDataMapping().stream()
                .filter(x -> StringUtils.equals(x.getTableName(), tableName))
                .findFirst()
                .orElseGet(() -> {
                    // 抛出异常前记录错误日志
                    log.error("未找到数据表映射配置，表名: {}", tableName);
                    throw new BusinessException(ErrorCodeEnum.CONFIG_ERROR.getCode(),
                            String.format("未找到对应的表映射,tableName=%s", tableName));
                });
    }


    /**
     * 构建批量插入上下文
     *
     * @param basicTableName 表名
     * @param sheetKey       sheetKey
     * @param dataMapping    表映射
     * @param dataList       数据列表
     * @return 批量插入上下文
     */
    private RawDataStorageBatchInsertContext buildInsertContext(ImportStaffInfo userDefinedData, String basicTableName, String sheetKey, DataMapping dataMapping,
                                                                List<Map<String, Object>> dataList,String fileName,Integer sheetIndex) {
        String batchCodeField = Optional.ofNullable(dataMapping.getBatchNo())
                .map(TableField::getTableField)
                .orElseGet(() -> {
                    log.error("未找到批次号字段，表名: {}", basicTableName);
                    throw new BusinessException(ErrorCodeEnum.CONFIG_ERROR.getCode(),
                            String.format("未找到对应的批次号字段,basicTableName=%s", basicTableName));
                });

        Set<String> tableFields = dataMapping.getTableFields().stream()
                .filter(x -> Objects.nonNull(x) && x.getBePutStorage())
                .map(TableField::getTableField)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(tableFields)) {
            log.error("未找到表字段，表名: {}", basicTableName);
            throw new BusinessException(ErrorCodeEnum.CONFIG_ERROR.getCode(),
                    String.format("未找到对应的表字段,basicTableName=%s", basicTableName));
        }
        RawDataStorageBatchInsertContext context = new RawDataStorageBatchInsertContext();
        context.setBasicTableName(basicTableName);
        context.setTableFields(tableFields);
        context.setCompanyId(userDefinedData.getCompanyId());
        MonitorDataInfo monitorDataInfo = new MonitorDataInfo();
        monitorDataInfo.setLastRecordId(batchCallbackContext.getLastRecordId());
        monitorDataInfo.setLastConfigId(batchCallbackContext.getSheetConfigIdMap().get(sheetKey));
        context.setDateType(batchCallbackContext.getDateType());
        context.setNeedStartTime(batchCallbackContext.getNeedStartTime());
        context.setNeedEndTime(batchCallbackContext.getNeedEndTime());
        context.setMonitorDataInfo(monitorDataInfo);
        context.setShopId(batchCallbackContext.getShopId());
        HashMap<String, FileAnalyzeCountInfo> fileAnalyzeCountInfoHashMap = batchCallbackContext.getFileAnalyzeCountInfoHashMap();
        FileAnalyzeCountInfo fileAnalyzeCountInfo = fileAnalyzeCountInfoHashMap.get(fileName);
        Map<Integer, FileSheetCountInfo> fileSheetCountInfoMap = fileAnalyzeCountInfo.getFileSheetCountInfoMap();
        FileSheetCountInfo fileSheetCountInfo = fileSheetCountInfoMap.get(sheetIndex);
        if (fileSheetCountInfo == null) {
            throw new BusinessException(ErrorCodeEnum.CONFIG_ERROR.getCode(),
                    String.format("FileSheetCountInfo未初始化,sheetIndex=%s", sheetIndex));
        }
        //初始化
        fileSheetCountInfo.getTableCountInfoList().putIfAbsent(basicTableName, new TableCountInfo(basicTableName));
        context.setFileSheetCountInfo(fileSheetCountInfo);
        String tableSourceConfigStr = dataMapping.getTableSourceConfig();
        TableSourceConfig tableSourceConfig = BizExcelConfigUtils.parseExcelMultipleHeadConfig(tableSourceConfigStr);
        context.setTableSourceConfig(tableSourceConfig);
        context.setBatchField(batchCodeField);
        context.setDataList(dataList);
        context.setSheetIndex(sheetIndex);
        context.setDataSource(batchCallbackContext.getDataSource());
        context.setHandleBatchNoList(batchCallbackContext.getHandleBatchNoSet());
        context.setSkipBatchNoList(batchCallbackContext.getSkipBatchNoList());
        return context;
    }


    @Override
    public boolean errorResult(ResultFailContext<ImportStaffInfo> resultFailContext) {
        boolean isFailFirst;
        //配置第一次报错数据初始化
        if (batchCallbackContext.getIsFailFirst()) {
            isFailFirst = true;
            batchCallbackContext.setFailOutputFilePath(OutputFilePathUtil.getOutputFilePath(TempFilePathBizType.EXPORT_FAIL_DATA, FileTypeEnum.CSV.getFileType()));
            batchCallbackContext.setIsFailFirst(false);
        } else {
            isFailFirst = false;
        }
        ExportExcelFailDataToCsv.exportDataToCsv(resultFailContext.getErrorResult(),batchCallbackContext.getFailOutputFilePath(),  isFailFirst,resultFailContext.getExcelInfo().getFileName(),resultFailContext);
        handleErrorCount(resultFailContext, batchCallbackContext);
        return true;
    }

    private void handleErrorCount(ResultFailContext<ImportStaffInfo> resultFailContext, BatchCallbackContext batchCallbackContext) {
        HashMap<String, FileAnalyzeCountInfo> fileAnalyzeCountInfoHashMap = batchCallbackContext.getFileAnalyzeCountInfoHashMap();
        FileAnalyzeCountInfo fileAnalyzeCountInfo = fileAnalyzeCountInfoHashMap.get(resultFailContext.getExcelInfo().getFileName());
        Map<Integer, FileSheetCountInfo> fileSheetCountInfoMap = fileAnalyzeCountInfo.getFileSheetCountInfoMap();
        FileSheetCountInfo fileSheetCountInfo = fileSheetCountInfoMap.get(resultFailContext.getSheetIndex());
        List<ExcelFailData> errorResult = resultFailContext.getErrorResult();
        if (CollectionUtils.isEmpty(errorResult)){
            return;
        }
        List<Integer> errorLineList = errorResult.stream().map(s -> s.getLine()).distinct().collect(Collectors.toList());
        fileSheetCountInfo.getFailCount().addAndGet(errorLineList.size());
    }

    @Override
    public void onComplete(CompleteContext<ImportStaffInfo> completeContext) {
        //获取失败行数
        Integer failLineCount = completeContext.getFailLineCount();
        //如果失败行数大于0，则更新当前批的文件标为失败
        if (failLineCount > 0) {
            batchCallbackContext.setBizErrorFlag(true);
        }
    }
}
