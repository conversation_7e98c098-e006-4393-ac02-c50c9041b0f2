package com.raycloud.dmj.account.core.base.dao.impl;

import com.raycloud.dmj.account.core.base.dao.FileAnalyzeConfigDao;
import com.raycloud.dmj.account.core.enums.field.FileAnalyzeConfigFieldEnum;
import com.raycloud.dmj.account.core.rawdata.domains.FileAnalyzeConfigDO;
import com.raycloud.dmj.account.core.tj.vo.FileAnalyzeConfigVo;
import com.raycloud.dmj.table.api.plus.common.base.SQL;
import com.raycloud.dmj.table.api.plus.common.enums.LinkMode;
import com.raycloud.dmj.table.api.plus.component.$;
import com.raycloud.dmj.table.api.plus.component.column.utils.Columns;
import com.raycloud.dmj.table.api.plus.component.condition.utils.Conditions;
import com.raycloud.dmj.table.api.plus.insert.Inserts;
import com.raycloud.dmj.table.api.plus.query.Queries;
import com.raycloud.dmj.table.api.plus.query.api.QueryFrom;
import com.raycloud.dmj.table.api.plus.update.Updates;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.Date;
import java.util.List;

/**
 * Date:  2025/6/16
 *
 * <AUTHOR>
 */
@Repository
public class FileAnalyzeConfigDaoImpl extends BaseDao implements FileAnalyzeConfigDao {

    private final String tableName = "file_analyze_config";

    /**
     * 查询文件解析配置表
     */
    @Override
    public List<FileAnalyzeConfigDO> queryFileAnalyzeConfig(String dataType) {
        SQL sql = Queries.create()
                .from(tableName)
                .where(
                        Conditions.and(Columns.toColumn(FileAnalyzeConfigFieldEnum.DATA_TYPE.getFieldCode()), LinkMode.EQUAL,dataType),
                        Conditions.and(Columns.toColumn(FileAnalyzeConfigFieldEnum.ENABLE_STATUS.getFieldCode()), LinkMode.EQUAL, 1)
                )
                .select()
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        // 应该使用 query() 方法，而不是 queryForList()
        List<FileAnalyzeConfigDO> doList = jdbcTemplate.query(
                sql.getSqlCode(),
                new BeanPropertyRowMapper<>(FileAnalyzeConfigDO.class),
                args
        );
        return doList;
    }

    /**
     * 查询文件解析配置VO列表
     * @param dataType 数据类型，为空时查询所有启用的配置
     * @return 配置列表
     */
    @Override
    public List<FileAnalyzeConfigVo> queryFileAnalyzeConfigVo(String dataType) {
        // 构建基础查询
        QueryFrom queryBuilder = Queries.create().from(tableName);

        // 动态构建WHERE条件 - 始终包含启用状态条件
        if (isNotEmpty(dataType)) {
            queryBuilder.where(
                    Conditions.and(Columns.toColumn(FileAnalyzeConfigFieldEnum.DATA_TYPE.getFieldCode()), LinkMode.EQUAL, dataType.trim()),
                    Conditions.and(Columns.toColumn(FileAnalyzeConfigFieldEnum.ENABLE_STATUS.getFieldCode()), LinkMode.EQUAL, 1)
            );
        } else {
            queryBuilder.where(
                    Conditions.and(Columns.toColumn(FileAnalyzeConfigFieldEnum.ENABLE_STATUS.getFieldCode()), LinkMode.EQUAL, 1)
            );
        }

        // 构建SQL并执行查询
        SQL sql = queryBuilder.select(
                        Columns.toColumn(FileAnalyzeConfigFieldEnum.ID.getFieldCode()),
                        Columns.toColumn(FileAnalyzeConfigFieldEnum.DATA_TYPE.getFieldCode()),
                        Columns.toColumn(FileAnalyzeConfigFieldEnum.SHEET_TYPE.getFieldCode()),
                        Columns.toColumn(FileAnalyzeConfigFieldEnum.SHEET.getFieldCode()),
                        Columns.toColumn(FileAnalyzeConfigFieldEnum.HEADER_START_INDEX.getFieldCode()),
                        Columns.toColumn(FileAnalyzeConfigFieldEnum.HEADER_END_INDEX.getFieldCode())
                )
                .toSql();

        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        return jdbcTemplate.query(
                sql.getSqlCode(),
                new BeanPropertyRowMapper<>(FileAnalyzeConfigVo.class),
                args
        );
    }

    /**
     * 检查字符串是否不为空（包含null、空字符串、空白字符串的检查）
     * @param str 待检查的字符串
     * @return true-不为空，false-为空
     */
    @Override
    public boolean isNotEmpty(String str) {
        return str != null && !str.trim().isEmpty();
    }

    @Override
    public FileAnalyzeConfigDO queryFileAnalyzeConfigById(Long id) {
        SQL sql = Queries.create()
                .from(tableName)
                .where(
                        Conditions.and(Columns.toColumn(FileAnalyzeConfigFieldEnum.ID.getFieldCode()), LinkMode.EQUAL,id)
                        ,Conditions.and(Columns.toColumn(FileAnalyzeConfigFieldEnum.ENABLE_STATUS.getFieldCode()), LinkMode.EQUAL, 1)
                )
                .select(
                        Columns.toColumn(FileAnalyzeConfigFieldEnum.ID.getFieldCode()),
                        Columns.toColumn(FileAnalyzeConfigFieldEnum.DATA_TYPE.getFieldCode()),
                        Columns.toColumn(FileAnalyzeConfigFieldEnum.SHEET_TYPE.getFieldCode()),
                        Columns.toColumn(FileAnalyzeConfigFieldEnum.SHEET.getFieldCode()),
                        Columns.toColumn(FileAnalyzeConfigFieldEnum.FILTER.getFieldCode()),
                        Columns.toColumn(FileAnalyzeConfigFieldEnum.HEADER_START_INDEX.getFieldCode()),
                        Columns.toColumn(FileAnalyzeConfigFieldEnum.HEADER_END_INDEX.getFieldCode()),
                        Columns.toColumn(FileAnalyzeConfigFieldEnum.HEAD_CONFIG.getFieldCode()),
                        Columns.toColumn(FileAnalyzeConfigFieldEnum.VERSION.getFieldCode()),
                        Columns.toColumn(FileAnalyzeConfigFieldEnum.CREATED.getFieldCode()),
                        Columns.toColumn(FileAnalyzeConfigFieldEnum.MODIFIED.getFieldCode())
                )
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();

        try {
            return jdbcTemplate.queryForObject(
                    sql.getSqlCode(),
                    new BeanPropertyRowMapper<>(FileAnalyzeConfigDO.class),
                    args
            );
        } catch (EmptyResultDataAccessException e) {
            // 如果没有找到记录，返回null
            return null;
        }
    }

    /**
     * 新增文件解析配置
     * @param fileAnalyzeConfig 文件解析配置对象
     * @return 新增记录的主键ID
     */
    @Override
    public Long addFileAnalyzeConfig(FileAnalyzeConfigDO fileAnalyzeConfig) {
        SQL sql = Inserts.insert()
                .into(tableName)
                .columns(
                        FileAnalyzeConfigFieldEnum.DATA_TYPE.getFieldCode(),
                        FileAnalyzeConfigFieldEnum.SHEET_TYPE.getFieldCode(),
                        FileAnalyzeConfigFieldEnum.SHEET.getFieldCode(),
                        FileAnalyzeConfigFieldEnum.FILTER.getFieldCode(),
                        FileAnalyzeConfigFieldEnum.HEADER_START_INDEX.getFieldCode(),
                        FileAnalyzeConfigFieldEnum.HEADER_END_INDEX.getFieldCode(),
                        FileAnalyzeConfigFieldEnum.HEAD_CONFIG.getFieldCode(),
                        FileAnalyzeConfigFieldEnum.VERSION.getFieldCode(),
                        FileAnalyzeConfigFieldEnum.ENABLE_STATUS.getFieldCode(),
                        FileAnalyzeConfigFieldEnum.CREATED.getFieldCode(),
                        FileAnalyzeConfigFieldEnum.MODIFIED.getFieldCode()
                )
                .valueForEntity(fileAnalyzeConfig)
                .toSql();

        // 使用KeyHolder捕获自增主键
        KeyHolder keyHolder = new GeneratedKeyHolder();

        // 准备参数数组
        Object[] params = new Object[]{
                fileAnalyzeConfig.getDataType(),
                fileAnalyzeConfig.getSheetType(),
                fileAnalyzeConfig.getSheet(),
                fileAnalyzeConfig.getFilter(),
                fileAnalyzeConfig.getHeaderStartIndex(),
                fileAnalyzeConfig.getHeaderEndIndex(),
                fileAnalyzeConfig.getHeadConfig(),
                fileAnalyzeConfig.getVersion(),
                fileAnalyzeConfig.getEnableStatus(),
                fileAnalyzeConfig.getCreated(),
                fileAnalyzeConfig.getModified()
        };

        jdbcTemplate.update(connection -> {
            PreparedStatement ps = connection.prepareStatement(sql.getSqlCode(), Statement.RETURN_GENERATED_KEYS);
            for (int i = 0; i < params.length; i++) {
                ps.setObject(i + 1, params[i]);
            }
            return ps;
        }, keyHolder);

        // 返回生成的主键ID
        Number key = keyHolder.getKey();
        return key != null ? key.longValue() : null;
    }

    /**
     * 更新文件解析配置
     * @param fileAnalyzeConfig 文件解析配置对象
     * @return 更新影响的行数
     */
    @Override
    public int updateFileAnalyzeConfig(FileAnalyzeConfigDO fileAnalyzeConfig) {
        SQL sql = Updates.create()
                .table(tableName)
                .where(
                        Conditions.and(Columns.toColumn(FileAnalyzeConfigFieldEnum.ID.getFieldCode()), LinkMode.EQUAL, fileAnalyzeConfig.getId())
                )
                .update(
                        $.updateKeyValue(FileAnalyzeConfigFieldEnum.DATA_TYPE.getFieldCode(), fileAnalyzeConfig.getDataType()),
                        $.updateKeyValue(FileAnalyzeConfigFieldEnum.SHEET_TYPE.getFieldCode(), fileAnalyzeConfig.getSheetType()),
                        $.updateKeyValue(FileAnalyzeConfigFieldEnum.SHEET.getFieldCode(), fileAnalyzeConfig.getSheet()),
                        $.updateKeyValue(FileAnalyzeConfigFieldEnum.FILTER.getFieldCode(), fileAnalyzeConfig.getFilter()),
                        $.updateKeyValue(FileAnalyzeConfigFieldEnum.HEADER_START_INDEX.getFieldCode(), fileAnalyzeConfig.getHeaderStartIndex()),
                        $.updateKeyValue(FileAnalyzeConfigFieldEnum.HEADER_END_INDEX.getFieldCode(), fileAnalyzeConfig.getHeaderEndIndex()),
                        $.updateKeyValue(FileAnalyzeConfigFieldEnum.HEAD_CONFIG.getFieldCode(), fileAnalyzeConfig.getHeadConfig()),
                        $.updateKeyValue(FileAnalyzeConfigFieldEnum.VERSION.getFieldCode(), fileAnalyzeConfig.getVersion()),
                        $.updateKeyValue(FileAnalyzeConfigFieldEnum.MODIFIED.getFieldCode(), new Date())
                )
                .toSql();

        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        return jdbcTemplate.update(sql.getSqlCode(), args);
    }

    /**
     * 根据数据类型查询所有启用的配置（用于业务校验）
     * @param dataType 数据类型
     * @return 配置列表
     */
    @Override
    public List<FileAnalyzeConfigDO> queryEnabledConfigsByDataType(String dataType) {
        SQL sql = Queries.create()
                .from(tableName)
                .where(
                        Conditions.and(Columns.toColumn(FileAnalyzeConfigFieldEnum.DATA_TYPE.getFieldCode()), LinkMode.EQUAL, dataType),
                        Conditions.and(Columns.toColumn(FileAnalyzeConfigFieldEnum.ENABLE_STATUS.getFieldCode()), LinkMode.EQUAL, 1)
                )
                .select(
                        Columns.toColumn(FileAnalyzeConfigFieldEnum.ID.getFieldCode()),
                        Columns.toColumn(FileAnalyzeConfigFieldEnum.DATA_TYPE.getFieldCode()),
                        Columns.toColumn(FileAnalyzeConfigFieldEnum.SHEET_TYPE.getFieldCode()),
                        Columns.toColumn(FileAnalyzeConfigFieldEnum.SHEET.getFieldCode()),
                        Columns.toColumn(FileAnalyzeConfigFieldEnum.ENABLE_STATUS.getFieldCode())
                )
                .toSql();

        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        return jdbcTemplate.query(
                sql.getSqlCode(),
                new BeanPropertyRowMapper<>(FileAnalyzeConfigDO.class),
                args
        );
    }
}
