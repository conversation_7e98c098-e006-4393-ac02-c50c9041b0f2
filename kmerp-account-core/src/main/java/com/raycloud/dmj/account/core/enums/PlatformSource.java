package com.raycloud.dmj.account.core.enums;

/**
 * 数据来源
 */
public enum PlatformSource {
    RPA(1, "RPA"),
    MANUAL(2, "人工");
    /**
     * 数据来源
     */
    private Integer dataSource;
    /**
     * 数据来源描述
     */
    private String dataSourceDesc;

    PlatformSource(Integer dataSource, String dataSourceDesc) {
        this.dataSource = dataSource;
        this.dataSourceDesc = dataSourceDesc;
    }
    public Integer getDataSource() {
        return dataSource;
    }
    public String getDataSourceDesc() {
        return dataSourceDesc;
    }
    public static PlatformSource getByDataSource(Integer dataSource) {
        for (PlatformSource value : values()) {
            if (value.getDataSource().equals(dataSource)) {
                return value;
            }
        }
        return null;
    }
}
