package com.raycloud.dmj.account.core.enums;

import lombok.Getter;

@Getter
public enum CategoryCodeEnum {

    UNKNOWN("UNKNOWN", "未识别");
    /**
     * 数据来源
     */
    private String code;
    /**
     * 数据来源描述
     */
    private String desc;

    CategoryCodeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getByDesc(String dataSource) {
        for (CategoryCodeEnum value : values()) {
            if (value.getCode().equals(dataSource)) {
                return value.getDesc();
            }
        }
        return null;
    }
}
