package com.raycloud.dmj.account.core.cleancategory.service.impl;

import com.google.common.collect.Lists;
import com.raycloud.dmj.account.core.base.dao.FundAccountDao;
import com.raycloud.dmj.account.core.base.dao.ShopInfoDao;
import com.raycloud.dmj.account.core.base.domain.ShopInfoDO;
import com.raycloud.dmj.account.core.cleancategory.domain.object.FundAccountDO;
import com.raycloud.dmj.account.core.cleancategory.domain.request.AddFundAccountReq;
import com.raycloud.dmj.account.core.cleancategory.domain.request.EditFundAccountReq;
import com.raycloud.dmj.account.core.cleancategory.domain.request.QueryFundAccountReq;
import com.raycloud.dmj.account.core.cleancategory.domain.vo.FundAccountVO;
import com.raycloud.dmj.account.core.cleancategory.domain.vo.SimpleFundAccountVO;
import com.raycloud.dmj.account.core.cleancategory.mapstruct.FundAccountMapStruct;
import com.raycloud.dmj.account.core.cleancategory.service.FundAccountService;
import com.raycloud.dmj.account.core.common.Log;
import com.raycloud.dmj.account.core.common.TreeVO;
import com.raycloud.dmj.account.core.enums.AccountTypeEnum;
import com.raycloud.dmj.account.core.enums.YesOrNoEnum;
import com.raycloud.dmj.account.infra.common.BusinessException;
import com.raycloud.dmj.account.infra.common.ErrorCodeEnum;
import com.raycloud.dmj.account.infra.session.AccountUser;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import com.raycloud.dmj.account.infra.utils.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 账户服务实现类
 *
 * <AUTHOR>
 */

@Service
public class FundAccountServiceImpl implements FundAccountService {

    @Resource
    private FundAccountDao fundAccountDao;

    @Resource
    private ShopInfoDao shopInfoDao;

    FundAccountMapStruct convert = FundAccountMapStruct.INSTANCE;

    @Override
    @Log
    public Long addFundAccount(AccountUser accountUser, AddFundAccountReq req) {
        AsserUtils.notNull(accountUser, "用户不能为空！");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空！");
        AsserUtils.notNull(req, "参数不能为空！");
        AsserUtils.notEmpty(req.getAccountCode(), "账户编码不能为空！");
        AsserUtils.notEmpty(req.getAccountName(), "账户名称不能为空！");
        AsserUtils.notNull(req.getShopId(), "店铺ID不能为空！");
        AsserUtils.notNull(req.getType(), "账户类型不能为空！");
        AsserUtils.notNull(req.getStartBalance(), "期初余额不能为空！");
        AsserUtils.notNull(req.getStartDate(), "期初日期不能为空！");
        // 账户编码唯一性校验
        if (Objects.nonNull(fundAccountDao.queryByAccountCode(accountUser.getCompanyId(),req.getAccountCode()))){
            throw new BusinessException(ErrorCodeEnum.FUND_ACCOUNT_CODE_EXISTS);
        }
        //名称唯一性校验
        if (Objects.nonNull(fundAccountDao.queryByAccountName(accountUser.getCompanyId(),req.getAccountName()))){
            throw new BusinessException(ErrorCodeEnum.FUND_ACCOUNT_NAME_EXISTS);
        }
        //添加资金账户
        FundAccountDO fundAccountDO = convert.toDo(accountUser,req);
        fundAccountDO.setCompanyId(accountUser.getCompanyId());
        return fundAccountDao.insert(fundAccountDO);
    }

    @Override
    public void editFundAccount(AccountUser accountUser, EditFundAccountReq req) {
        AsserUtils.notNull(accountUser, "用户不能为空！");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空！");
        AsserUtils.notNull(req, "参数不能为空！");
        AsserUtils.notNull(req.getId(), "ID不能为空！");
        AsserUtils.notEmpty(req.getAccountName(), "账户名称不能为空！");
        AsserUtils.notNull(req.getShopId(), "店铺ID不能为空！");
        AsserUtils.notNull(req.getType(), "账户类型不能为空！");
        AsserUtils.notNull(req.getStartBalance(), "期初余额不能为空！");
        AsserUtils.notNull(req.getStartDate(), "期初日期不能为空！");
        //修改资金账户
        FundAccountDO fundAccountDO = convert.toDo(req);
        fundAccountDao.updateById(fundAccountDO);
    }

    @Override
    public List<FundAccountVO> queryFundAccountList(AccountUser accountUser) {
        AsserUtils.notNull(accountUser, "用户不能为空！");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空！");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空！");
        List<FundAccountDO> fundAccountDOList = fundAccountDao.queryByCompanyId(accountUser.getCompanyId());
        List<Long> shopIds = fundAccountDOList.stream().map(FundAccountDO::getShopId).map(Long::valueOf).distinct().collect(Collectors.toList());
        List<ShopInfoDO> shopInfoDOList = shopInfoDao.listShopInfoByIds(accountUser.getCompanyId(), shopIds);
        Map<Long, ShopInfoDO> shopIdToObjMap = Optional.ofNullable(shopInfoDOList)
                .orElse(Collections.emptyList())
                .stream()
                .collect(Collectors.toMap(
                        ShopInfoDO::getId,
                        x -> x,
                        (v1, v2) -> v1,
                        HashMap::new
                ));

        return fundAccountDOList.stream()
                .map(x -> convert.toVO(shopIdToObjMap, x))
                .collect(Collectors.toList());


    }

    @Override
    public void authorizeFundAccount(AccountUser accountUser, Long id) {
        AsserUtils.notNull(accountUser, "用户不能为空！");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空！");
        AsserUtils.notNull(id, "ID不能为空！");
        fundAccountDao.updateAuthorizeById(YesOrNoEnum.YES.getValue(), id);

    }

    @Override
    public void confirmStartPeriod(AccountUser accountUser, Long id) {
        AsserUtils.notNull(accountUser, "用户不能为空！");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空！");
        AsserUtils.notNull(id, "ID不能为空！");
        fundAccountDao.updateConfirmStartPeriodById(YesOrNoEnum.YES.getValue(), id);
    }

    @Override
    public List<TreeVO> queryFundTypeTree(AccountUser accountUser) {
        return Arrays.stream(AccountTypeEnum.values()).map(x -> {
            TreeVO treeVO = new TreeVO();
            treeVO.setLabel(x.getTypeDesc());
            treeVO.setValue(x.getTypeCode());
            return treeVO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<TreeVO> queryFundAccountTree(AccountUser accountUser) {
        AsserUtils.notNull(accountUser, "用户不能为空！");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空！");
        List<FundAccountDO> fundAccountDOList = fundAccountDao.queryByCompanyId(accountUser.getCompanyId());
        if (CollectionUtils.isEmpty(fundAccountDOList)){
            return Lists.newArrayList();
        }
        return fundAccountDOList.stream()
                .map(x -> convert.toTreeVO(x))
                .collect(Collectors.toList());
    }

    @Override
    public List<SimpleFundAccountVO> getSimpleFundAccountList(AccountUser accountUser, QueryFundAccountReq req) {
        AsserUtils.notNull(accountUser, "用户不能为空！");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空！");
        AsserUtils.notNull(req, "参数不能为空！");

        //账户信息集合
        List<FundAccountDO> accountDOList;
        //根据平台code获取账户信息
        if (CollectionUtils.isEmpty(req.getShopIdList())){
            AsserUtils.notNull(req.getPlatformCode(), "平台code不能为空！");
            accountDOList = fundAccountDao.queryByPlatformCode(req.getPlatformCode(), accountUser.getCompanyId());
        }else {
            //根据店铺ID获取账户信息
            AsserUtils.notNull(req.getShopIdList(), "店铺ID集合不能为空！");
            accountDOList = fundAccountDao.queryByShopIdList(req.getShopIdList(), accountUser.getCompanyId());
        }
        return BeanUtils.copyList(accountDOList, SimpleFundAccountVO.class);
    }
}
