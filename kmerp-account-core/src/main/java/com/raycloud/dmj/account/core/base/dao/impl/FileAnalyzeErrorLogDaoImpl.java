package com.raycloud.dmj.account.core.base.dao.impl;

import com.raycloud.dmj.account.core.base.dao.FileAnalyzeErrorLogDao;
import com.raycloud.dmj.account.core.enums.field.FileAnalyzeErrorLogEnum;
import com.raycloud.dmj.account.core.rawdata.domains.FileAnalyzeErrorLogDO;
import com.raycloud.dmj.table.api.plus.common.base.SQL;
import com.raycloud.dmj.table.api.plus.insert.Inserts;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.stream.IntStream;
@Slf4j
@Repository
public class FileAnalyzeErrorLogDaoImpl extends BaseDao implements FileAnalyzeErrorLogDao {


    private final String TABLE_NAME = "file_analyze_error_log";


    /**
     * 新增批次错误日志表
     */
    @Override
    public Long add(FileAnalyzeErrorLogDO fileAnalyzeErrorLogDO) {
        SQL sql = Inserts.insert()
                .into(TABLE_NAME)
                .columns(
                        FileAnalyzeErrorLogEnum.RECORD_ID.getFieldCode(),
                        FileAnalyzeErrorLogEnum.ERROR_LOG_FILE.getFieldCode(),
                        FileAnalyzeErrorLogEnum.CREATED.getFieldCode(),
                        FileAnalyzeErrorLogEnum.MODIFIED.getFieldCode(),
                        FileAnalyzeErrorLogEnum.CREATED_BY.getFieldCode(),
                        FileAnalyzeErrorLogEnum.COMPANY_ID.getFieldCode(),
                        FileAnalyzeErrorLogEnum.SHOP_ID.getFieldCode()
                )
                .valueForEntity(fileAnalyzeErrorLogDO)
                // 因为通常情况 数据库都是下划线的命名方式，而代码上是驼峰命名，所以需要开启这个配置，将实体字段名字映射成下划线
                .toSql();
        // 使用KeyHolder捕获主键
        KeyHolder keyHolder = new GeneratedKeyHolder();
        // 准备参数数组
        Object[] params = new Object[]{
                fileAnalyzeErrorLogDO.getRecordId(),
                fileAnalyzeErrorLogDO.getErrorLogFile(),
                fileAnalyzeErrorLogDO.getCreated(),
                fileAnalyzeErrorLogDO.getModified(),
                fileAnalyzeErrorLogDO.getCreatedBy(),
                fileAnalyzeErrorLogDO.getTenantId(),
                fileAnalyzeErrorLogDO.getShopUniId()
        };
        // 使用update方法并传入PreparedStatementCreator和KeyHolder
        jdbcTemplate.update(
                connection -> {
                    // 创建PreparedStatement时指定RETURN_GENERATED_KEYS
                    PreparedStatement ps = connection.prepareStatement(sql.getSqlCode(), new String[]{"id"});
                    // 使用IntStream设置参数
                    IntStream.range(0, params.length)
                            .forEach(i -> {
                                try {
                                    ps.setObject(i + 1, params[i]);
                                } catch (SQLException e) {
                                    throw new RuntimeException(e);
                                }
                            });
                    return ps;
                },
                keyHolder
        );

        // 从keyHolder中获取主键，而非通过SQL参数
        return keyHolder.getKey().longValue();
    }
}
