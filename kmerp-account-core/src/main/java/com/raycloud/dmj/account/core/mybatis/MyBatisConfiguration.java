package com.raycloud.dmj.account.core.mybatis;

import com.raycloud.dmj.account.core.common.constant.Constant;
import com.raycloud.dmj.account.infra.datasource.KmerpDatasourceConfiguration;
import com.raycloud.dmj.account.infra.repository.Connectors;
import com.raycloud.dmj.kmbi.connector.jdbc.JdbcConnector;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;

import javax.sql.DataSource;

/**
 * MyBatis配置类
 * 
 * <AUTHOR>
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
@MapperScan(basePackages = "com.raycloud.dmj.account.core.mapper")
public class MyBatisConfiguration {

    private final KmerpDatasourceConfiguration kmerpDatasourceConfiguration;

    /**
     * 主数据源SqlSessionFactory
     */
    @Bean
    @Primary
    public SqlSessionFactory sqlSessionFactory() throws Exception {
        SqlSessionFactoryBean factoryBean = new SqlSessionFactoryBean();
        
        // 获取主数据源
        DataSource dataSource = getIndexDataSource();
        factoryBean.setDataSource(dataSource);
        
        // 设置MyBatis配置
        org.apache.ibatis.session.Configuration configuration = new org.apache.ibatis.session.Configuration();
        configuration.setMapUnderscoreToCamelCase(true);
        configuration.setUseGeneratedKeys(true);
        configuration.setDefaultExecutorType(org.apache.ibatis.session.ExecutorType.REUSE);
        configuration.setDefaultStatementTimeout(30);
        factoryBean.setConfiguration(configuration);
        
        // 设置mapper xml文件位置
        factoryBean.setMapperLocations(
            new PathMatchingResourcePatternResolver()
                .getResources("classpath*:mapper/**/*.xml")
        );
        
        // 设置类型别名包
        factoryBean.setTypeAliasesPackage("com.raycloud.dmj.account.core.domain");
        
        return factoryBean.getObject();
    }

    /**
     * 主SqlSessionTemplate
     */
    @Bean
    @Primary
    public SqlSessionTemplate sqlSessionTemplate() throws Exception {
        return new SqlSessionTemplate(sqlSessionFactory());
    }

    /**
     * 安全地获取数据源
     */
    private DataSource getIndexDataSource() {
        try {
            String connectorId = Constant.ORIGINAL_DATA_BASE_CONNECTION_KEY;
            JdbcConnector connector = kmerpDatasourceConfiguration.getConnector(connectorId);
            if (connector != null) {
                return connector.getDataSource();
            }
        } catch (Exception e) {
            log.warn("无法获取索引数据源，尝试使用本地开发数据源: {}", e.getMessage());
        }

        try {
            // 尝试使用本地开发数据源
            JdbcConnector connector = kmerpDatasourceConfiguration.getConnector("LOCAL:KMERP:MYSQL:DEV");
            if (connector != null) {
                return connector.getDataSource();
            }
        } catch (Exception e) {
            log.warn("无法获取本地开发数据源: {}", e.getMessage());
        }

        // 如果都失败了，创建一个内存数据库
        log.warn("所有数据源都不可用，使用内存数据库作为备用");
        return createInMemoryDataSource();
    }

    /**
     * 创建内存数据库作为备用数据源
     */
    private DataSource createInMemoryDataSource() {
        try {
            // 使用 H2 内存数据库
            org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseBuilder builder =
                new org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseBuilder();
            return builder
                .setType(org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType.H2)
                .build();
        } catch (Exception e) {
            log.error("无法创建内存数据库: {}", e.getMessage());
            throw new RuntimeException("无法创建任何数据源", e);
        }
    }
}
