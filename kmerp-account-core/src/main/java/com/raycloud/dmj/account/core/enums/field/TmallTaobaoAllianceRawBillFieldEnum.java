package com.raycloud.dmj.account.core.enums.field;

import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.Set;
import java.util.stream.Collectors;

@Getter
public enum TmallTaobaoAllianceRawBillFieldEnum {
    ID("id", "自增主键"),
    SORT_TITLE("sort_title", "店铺简称"),
    BILLING_CYCLE("billing_cycle", "账期，格式如202503"),
    FUND_DIRECTION("fund_direction", "资金方向"),
    MERCHANT_CATEGORY("merchant_category", "账单大类"),
    BUSINESS_CATEGORY("business_category", "业务大类"),
    BUSINESS_SUB_CATEGORY("business_sub_category", "业务小类"),
    BUSINESS_TIME("business_time", "时间"),
    ORDER_SN("order_sn", "订单号"),
    POINTS_COUPON_AMOUNT("points_coupon_amount", "积分类服务金额"),
    ALIPAY_ORDER_SN("alipay_order_sn", "支付宝订单号"),
    REMARK("remark", "备注"),
    ALIPAY_TRANSACTION_ID("alipay_transaction_id", "支付宝流水号"),
    CONFIRMATION_TIME("confirmation_time", "确认收货时间"),
    TRANSACTION_SUB_ORDER_SN("transaction_sub_order_sn", "交易子订单号"),
    ALIPAY_ACCOUNT("alipay_account", "支付宝账号"),
    SETTLEMENT_CHANNEL("settlement_channel", "结算渠道"),
    DOWNLOAD_TIME("download_time", "下载时间"),
    DOWNLOAD_ACCOUNT("download_account", "下载账户"),
    BIZ_KEY("biz_key", "唯一键"),
    BATCH_NO("batch_no", "批次号"),
    BATCH_TIME("batch_time", "批次时间 格式如********"),
    COMPANY_ID("company_id", "租户ID"),
    SHOP_ID("shop_id", "店铺ID"),
    CREATED("created", "创建时间");

    /**
     * 字段编码
     */
    private final String fieldCode;

    /**
     * 字段描述
     */
    private final String fieldDesc;

    TmallTaobaoAllianceRawBillFieldEnum(String fieldCode, String fieldDesc) {
        this.fieldCode = fieldCode;
        this.fieldDesc = fieldDesc;
    }

    /**
     * 获取插入字段
     * @return 插入字段
     */
    public static Set<String> getInsertFields() {
        Set<TmallTaobaoAllianceRawBillFieldEnum> filterSet = Collections.singleton(TmallTaobaoAllianceRawBillFieldEnum.ID);
        return Arrays.stream(TmallTaobaoAllianceRawBillFieldEnum.values())
               .filter(x ->!filterSet.contains(x))
               .map(x -> x.fieldCode)
               .collect(Collectors.toSet());
    }
}