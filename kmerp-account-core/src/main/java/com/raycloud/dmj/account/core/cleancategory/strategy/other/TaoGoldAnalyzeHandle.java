package com.raycloud.dmj.account.core.cleancategory.strategy.other;

import com.raycloud.dmj.account.core.base.dao.TmallTaojinbiRawBillDataDao;
import com.raycloud.dmj.account.core.base.domain.StandardOtherBillFlowInfoDO;
import com.raycloud.dmj.account.core.cleancategory.domain.object.TmallTaojinbiRawBillDataDO;
import com.raycloud.dmj.account.core.cleancategory.strategy.param.CategoryAnalyzeParam;
import com.raycloud.dmj.account.core.common.Page;
import com.raycloud.dmj.account.core.enums.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;


/**
 * 淘金币类目解析处理
 * <AUTHOR>
 */
@Component
public class TaoGoldAnalyzeHandle extends StandardOtherCategoryAnalyzeHandle<TmallTaojinbiRawBillDataDO> {


    @Resource
    private TmallTaojinbiRawBillDataDao tmallTaojinbiRawBillDataDao;

    @Override
    protected List<TmallTaojinbiRawBillDataDO> listPageRawData(CategoryAnalyzeParam param, Page page) {
        return tmallTaojinbiRawBillDataDao.listPageByDataRange(
                param.getCompanyId(),
                param.getShopId(),
                param.getDataRange(),
                page
        );
    }

    @Override
    protected void setStandardFundBillFlowInfoDO(StandardOtherBillFlowInfoDO standardOtherBillFlowInfoDO, TmallTaojinbiRawBillDataDO rawDataDO) {
        standardOtherBillFlowInfoDO.setCategoryGroupCode(OtherCategoryGroupEnum.BT.getCode());
        standardOtherBillFlowInfoDO.setCategoryCode(OtherCategoryEnum.TJJ.getCode());
        standardOtherBillFlowInfoDO.setSubCategoryCode(OtherSubCategoryEnum.TJJ.getCode());
        standardOtherBillFlowInfoDO.setBillingCycle(rawDataDO.getBillingCycle());
        standardOtherBillFlowInfoDO.setOccurredAt(rawDataDO.getBusinessTime());
        BigDecimal amount = rawDataDO.getPointsCouponAmount();
        standardOtherBillFlowInfoDO.setAmount(amount);
        if (amount.compareTo(BigDecimal.ZERO)>0){
            standardOtherBillFlowInfoDO.setIncomeExpenseDirection(IncomeExpenseDirectionEnum.INCOME.getCode());
        }else {
            standardOtherBillFlowInfoDO.setIncomeExpenseDirection(IncomeExpenseDirectionEnum.EXPENSE.getCode());
        }
        standardOtherBillFlowInfoDO.setRemark(rawDataDO.getRemark());
        standardOtherBillFlowInfoDO.setOrderNo(rawDataDO.getOrderSn());
        standardOtherBillFlowInfoDO.setBatchNo(rawDataDO.getBatchNo());
        standardOtherBillFlowInfoDO.setBizKey(rawDataDO.getBizKey());

    }

    @Override
    protected void deleteByShopAndDataRangeAndCategoryGroup(CategoryAnalyzeParam param) {
        standardOtherBillFlowInfoDao.deleteByShopAndDataRangeAndCategory(param.getCompanyId(),
                param.getShopId(),param.getDataRange() , OtherCategoryEnum.TJJ.getCode());
    }

    @Override
    public boolean accord(RawDataSourceEnum source) {
        return RawDataSourceEnum.TAO_GOLD.equals(source);
    }
}
