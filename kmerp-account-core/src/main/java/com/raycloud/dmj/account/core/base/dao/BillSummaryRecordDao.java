package com.raycloud.dmj.account.core.base.dao;

import com.raycloud.dmj.account.core.base.domain.BillSummaryRecordDO;
import com.raycloud.dmj.account.core.bill.params.BillSummaryRecordParam;
import com.raycloud.dmj.account.core.bill.request.BillSummaryRecordRequest;

import java.util.Date;
import java.util.List;

/**
 * 资金流水合计记录表DAO
 * <AUTHOR>
 */
public interface BillSummaryRecordDao {

    /**
     * 新增资金流水合计记录
     * @param billSummaryRecordDO 资金流水合计记录对象
     * @return 新增记录的主键ID
     */
    Long insert(BillSummaryRecordDO billSummaryRecordDO);

    /**
     * 根据ID查询资金流水合计记录
     * @param id 主键ID
     * @param companyId 公司ID
     * @return 资金流水合计记录
     */
    BillSummaryRecordDO queryById(Long id, Long companyId);

    /**
     * 根据参数查询资金流水合计记录列表
     * @param request 查询参数
     * @param companyId 公司ID
     * @return 资金流水合计记录列表
     */
    List<BillSummaryRecordDO> queryByParam(BillSummaryRecordParam request, Long companyId);

    /**
     * 根据店铺ID和帐期查询资金流水合计记录列表
     * @param shopId 店铺ID
     * @param billingCycle 帐期
     * @param companyId 公司ID
     * @return 资金流水合计记录列表
     */
    List<BillSummaryRecordDO> queryByShopAndCycle(Long shopId, Date billingCycle, Long companyId);

    Integer batchInsert(List<BillSummaryRecordDO> recordDOList);
}
