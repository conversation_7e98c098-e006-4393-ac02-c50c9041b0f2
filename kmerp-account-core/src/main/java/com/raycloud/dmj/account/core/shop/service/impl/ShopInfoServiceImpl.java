package com.raycloud.dmj.account.core.shop.service.impl;

import com.raycloud.dmj.account.core.base.dao.ShopInfoDao;
import com.raycloud.dmj.account.core.base.domain.ShopInfoDO;
import com.raycloud.dmj.account.core.bill.request.StandardFundBillFlowRequest;
import com.raycloud.dmj.account.core.common.PageInfo;
import com.raycloud.dmj.account.core.common.exception.SessionException;
import com.raycloud.dmj.account.core.enums.PlatformEnum;
import com.raycloud.dmj.account.core.mapper.shop.ShopInfoMapper;
import com.raycloud.dmj.account.core.shop.req.*;
import com.raycloud.dmj.account.core.shop.service.IShopInfoService;
import com.raycloud.dmj.account.core.shop.vo.ErpShopVO;
import com.raycloud.dmj.account.core.shop.vo.ShopInfoVO;
import com.raycloud.dmj.account.core.shop.vo.SimpleShopInfoVO;
import com.raycloud.dmj.account.infra.common.BusinessException;
import com.raycloud.dmj.account.infra.common.ErrorCodeEnum;
import com.raycloud.dmj.account.infra.dubbo.KmerpDubboConfiguration;
import com.raycloud.dmj.account.infra.session.AccountUser;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import com.raycloud.dmj.account.infra.utils.BeanUtils;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.user.Shop;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ShopInfoServiceImpl implements IShopInfoService {

    @Autowired
    private ShopInfoDao shopInfoDao;

    @Resource
    protected KmerpDubboConfiguration kmerpDubboConfiguration;


    @Override
    public List<ShopInfoVO> getShopList(AccountUser accountUser, ShopInfoRequest request) {
        // 获取当前用户信息
        if (accountUser == null || accountUser.getCompanyId() == null) {
            // 如果无法获取公司ID，可以考虑抛出异常或使用默认值
            throw new SessionException("无法获取当前用户的公司ID");
        }
        List<ShopInfoDO> infoDaoShopList = shopInfoDao.getShopList(request, accountUser.getCompanyId());
        List<ShopInfoVO> shopInfoVOList = new ArrayList<>();

        //TODO 归属公司名称

        //设置平台名称
        infoDaoShopList.forEach(shopInfo -> {
            ShopInfoVO shopInfoVO = new ShopInfoVO();
            BeanUtils.copyProperties(shopInfo,shopInfoVO);
            shopInfoVO.setPlatformName(Objects.requireNonNull(PlatformEnum.getPlatformEnumByCode(shopInfo.getPlatformCode())).getTitle());
        });
        return shopInfoVOList;
    }

    @Override
    public PageInfo<Object> getPageInfo(AccountUser accountUser, ShopInfoRequest request) {
        //获取数据条数
        Long count = shopInfoDao.getPageInfo(request, accountUser.getCompanyId());

        return PageInfo.<Object>builder()
                .pageNo(request.getPageNo())
                .pageSize(request.getPageSize())
                .total(count).build();
    }

    @Override
    public Long addShopInfo(AccountUser accountUser, AddShopInfoRequest request) {
        // 参数校验
        AsserUtils.notNull(request, "店铺信息不能为空");
        AsserUtils.notNull(request.getId(), "店铺ID不能为空");
        AsserUtils.notEmpty(request.getShopCode(), "店铺编码不能为空");
        AsserUtils.notEmpty(request.getTitle(), "店铺名称不能为空");
        AsserUtils.notNull(request.getAffiliatedCompanyId(), "归属公司不能为空");
        AsserUtils.notEmpty(request.getPlatformCode(), "平台编码不能为空");
        // 获取当前用户信息
        if (accountUser == null || accountUser.getCompanyId() == null) {
            throw new SessionException("无法获取当前用户的公司ID");
        }

        ShopInfoDO shopInfoDO = new ShopInfoDO();
        BeanUtils.copyProperties(request,shopInfoDO);
        // 设置公司ID
        shopInfoDO.setCompanyId(accountUser.getCompanyId());

        // 执行新增操作
        return shopInfoDao.addShopInfo(shopInfoDO);
    }

    @Override
    public void updateShopAmountState(AccountUser accountUser, ShopInfoUpdateStateRequest request) {
        AsserUtils.notNull(request, "请输入参数");
        AsserUtils.notNull(request.getShopIdList(), "请输入店铺ID");
        AsserUtils.notNull(request.getAccountStatus(), "请输入对账状态");
        // 获取当前用户信息
        if (accountUser == null || accountUser.getCompanyId() == null) {
            throw new SessionException("无法获取当前用户的公司ID");
        }
        // 执行更新操作
        int updateCount = shopInfoDao.updateShopAmountState(request.getShopIdList(), request.getAccountStatus(), accountUser.getCompanyId());

        if (updateCount < 0) {
            throw new RuntimeException("更新店铺信息失败");
        }
    }

    @Override
    public Long updateShopAmount(AccountUser accountUser, ShopUpdateAmountRequest request) {
        AsserUtils.notNull(request, "请输入参数");
        AsserUtils.notNull(request.getShopId(), "请输入店铺ID");
        AsserUtils.notNull(request.getAmount(), "请输入期初余额");
        AsserUtils.notNull(request.getStartDate(), "请输入期初时间");
        AsserUtils.notNull(request.getReconciliationType(), "请输入对账维度类型");
        // 获取当前用户信息
        if (accountUser == null || accountUser.getCompanyId() == null) {
            throw new SessionException("无法获取当前用户的公司ID");
        }
        ShopInfoDO shopInfoDO = shopInfoDao.getShopInfoById(request.getShopId());
        if (shopInfoDO == null) {
            throw new BusinessException(ErrorCodeEnum.SHOP_NOT_EXISTS,"店铺不存在!");
        }
        // 执行更新操作
        int updateCount = shopInfoDao.updateShopAmount(request.getShopId(), request, accountUser.getCompanyId());

        if (updateCount > 0) {
            return shopInfoDO.getId();
        } else {
            throw new RuntimeException("更新店铺信息失败");
        }
    }

    @Override
    public void updateShopCompany(AccountUser accountUser, ShopInfoUpdateAffiliatedCompanyRequest request) {
        AsserUtils.notNull(request, "请输入参数");
        AsserUtils.notNull(request.getShopIdList(), "请输入需要修改的店铺ID");
        AsserUtils.notNull(request.getAffiliatedCompanyId(), "请输入归属公司");
        // 获取当前用户信息
        if (accountUser == null || accountUser.getCompanyId() == null) {
            throw new SessionException("无法获取当前用户的公司ID");
        }
        // 执行更新操作
        int updateCount = shopInfoDao.updateShopCompany(request.getShopIdList(), request.getAffiliatedCompanyId(), accountUser.getCompanyId());

        if (updateCount < 0) {
            throw new RuntimeException("更新店铺信息失败");
        }
    }

    @Override
    public List<ErpShopVO> getERPShopList(Staff staff) {
        Long companyId = staff.getCompanyId();
        ShopInfoRequest request = new ShopInfoRequest();
        //获取系统内用户的店铺信息
        List<ShopInfoDO> shopInfoDOList = shopInfoDao.getShopList(request, companyId);
        Set<Long> shopIdList = shopInfoDOList.stream().map(ShopInfoDO::getId).collect(Collectors.toSet());
        List<Shop> shopList = kmerpDubboConfiguration.getShopService().queryByCompanyId(staff);
        //过滤平台
        shopList = shopList.stream().filter(shop -> PlatformEnum.getErpCodeList().contains(shop.getSource())).collect(Collectors.toList());

        List<ErpShopVO> erpShopVOList = new ArrayList<>();
        shopList.forEach(shop -> {
            ErpShopVO erpShopVo = new ErpShopVO();
            erpShopVo.setShopId(shop.getId());
            erpShopVo.setShopCode(shop.getUserId().toString());
            erpShopVo.setTitle(shop.getTitle());
            erpShopVo.setShortTitle(shop.getShortTitle());
            erpShopVo.setSource(shop.getSource());
            erpShopVo.setSourceName(shop.getSourceName());
            erpShopVo.setIsSelect(0);
            if (shopIdList.contains(erpShopVo.getShopId())) {
                erpShopVo.setIsSelect(1);
            }
            erpShopVo.setPlatformCode(Objects.requireNonNull(PlatformEnum.getPlatformEnumByErpCode(shop.getSource())).getCode());
            erpShopVOList.add(erpShopVo);
        });

        return erpShopVOList;
    }

    @Override
    public List<SimpleShopInfoVO> getSimpleShopList(AccountUser accountUser,String platformCodes) {
        // 获取当前用户信息
        if (accountUser == null || accountUser.getCompanyId() == null) {
            throw new SessionException("无法获取当前用户的公司ID");
        }

        //设置查询条件
        List<String> platformCodeList = new ArrayList<>();
        if (StringUtils.isNotEmpty(platformCodes)) {
            platformCodeList = Arrays.asList(platformCodes.split(","));
        }

        List<SimpleShopInfoVO> simpleShopInfoVOList = new ArrayList<>();
        List<ShopInfoDO> shopInfoDOList = shopInfoDao.getShopList(ShopInfoRequest.builder().platformCodeList(platformCodeList).build(), accountUser.getCompanyId());
        if (shopInfoDOList == null || shopInfoDOList.isEmpty()) {
            return simpleShopInfoVOList;
        }
        shopInfoDOList.forEach(shopInfoDO -> {
            SimpleShopInfoVO simpleShopInfoVo = new SimpleShopInfoVO();
            simpleShopInfoVo.setId(shopInfoDO.getId());
            simpleShopInfoVo.setTitle(shopInfoDO.getTitle());
            simpleShopInfoVo.setShortTitle(shopInfoDO.getShortTitle());
            simpleShopInfoVOList.add(simpleShopInfoVo);
        });
        return simpleShopInfoVOList;
    }
}
