package com.raycloud.dmj.account.core.cleancategory.domain.vo;

import com.raycloud.dmj.account.core.enums.AgainAnalyzeStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class BillAgainAnalyzeTaskVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 资金账户名称
     */
    private String fundAccountName;


    /**
     * 重洗开始时间
     */
    private Date reprocessStartTime;

    /**
     * 重洗结束时间
     */
    private Date reprocessEndTime;


    /**
     * 任务状态名称，0-清洗中，30-清洗失败，50-清洗成功
     * @see AgainAnalyzeStatusEnum
     */
    private String statusDesc;


    /**
     * 创建时间
     */
    private Date created;


    /**
     * 完成日期
     */
    private Date completionDate;

}
