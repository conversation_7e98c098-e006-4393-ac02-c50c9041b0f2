package com.raycloud.dmj.account.core.cleancategory.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 直播红包明细
 * <AUTHOR>
 */
@Data
public class TmallLiveRedEnvelopeRawBillDataDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 淘宝主订单号
     */
    private String taobaoMainOrderNo;


    /**
     * 订单确认收货时间
     */
    private Date orderConfirmReceiveTime;


    /**
     * 红包类型
     */
    private String redEnvelopeType;

    /**
     * 红包流水金额
     */
    private BigDecimal redEnvelopeFlowAmount;


    /**
     * 备注
     */
    private String remark;

    /**
     * 唯一键
     */
    private String bizKey;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 账期
     */
    private Integer billingMonth;
}