package com.raycloud.dmj.account.core.platform.base.domain;

import lombok.Data;
import java.time.LocalDateTime;

@Data
public class ShopAuthInfoDO {

    /**
     * 自增主键
     */
    private Long id;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 公司ID
     */
    private Long companyId;

    /**
     * 授权平台id
     */
    private String platformCode;

    /**
     * 授权状态
     */
    private String authStatus;

    /**
     * 授权凭证
     */
    private String token;

    /**
     * 额外的授权信息
     */
    private String extraData;

    /**
     * 授权过期时间
     */
    private LocalDateTime expirationTime;

    /**
     * 创建时间
     */
    private LocalDateTime created;

    /**
     * 修改时间
     */
    private LocalDateTime modified;
}