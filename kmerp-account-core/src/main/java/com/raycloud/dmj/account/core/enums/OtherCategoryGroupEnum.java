package com.raycloud.dmj.account.core.enums;

import lombok.Getter;

/**
 * 其他类目组枚举
 * <AUTHOR>
 */
@Getter
public enum OtherCategoryGroupEnum {

    //补贴
    BT("BT", "补贴"),
    ;

    private final String code;

    private final String desc;

    OtherCategoryGroupEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(String code) {
        for (OtherCategoryGroupEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return "未知类型";
    }
}
