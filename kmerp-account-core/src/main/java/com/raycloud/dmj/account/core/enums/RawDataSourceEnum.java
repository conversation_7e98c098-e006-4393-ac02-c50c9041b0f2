package com.raycloud.dmj.account.core.enums;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 原始数据来源枚举
 * <AUTHOR>
 */
@Getter
public enum RawDataSourceEnum {
    //RPA数据来源ID ： 55-微信，58-天猫保证金账单，59-天猫消费积分，60-天猫集分宝，61-天猫惠营宝，62-天猫直播红包，63-天猫淘宝联盟，64-天猫淘金币
    //数据来源：1-支付宝， 2-微信， 3-保证金，4-消费积分，5-集分宝，6-惠营宝，7-直播红包，8-淘宝联盟，9-淘金币
    ALIPAY(1, "支付宝",-1L),
    WECHAT(2, "微信",55L),
    GUARANTEE(3, "保证金",58L),
    CONSUME_INTEGRAL(4, "消费积分",59L),
    SET_POINTS(5, "集分宝",60L),
    HUI_YING_BAO(6, "惠营宝",61L),
    LIVE_RED_PACKET(7, "直播红包",62L),
    TAOBAO_UNION(8, "淘宝联盟",63L),
    TAO_GOLD(9, "淘金币",64L);



    private final Integer code;

    private final String desc;

    private final Long rpaSourceId;

    RawDataSourceEnum(Integer code, String desc, Long rpaSourceId) {
        this.code = code;
        this.desc = desc;
        this.rpaSourceId = rpaSourceId;
    }


    /**
     * 根据code返回枚举
     * @param code 枚举code
     * @return 枚举
     */
    public static RawDataSourceEnum getByCode(Integer code) {
        return Arrays.stream(RawDataSourceEnum.values())
                .filter(x -> code.equals(x.getCode()))
                .findFirst().orElse(null);
    }

    /**
     * 根据RPA数据源ID返回枚举
     * @param rpaSourceId  RPA数据源ID
     * @return 枚举
     */
    public static RawDataSourceEnum  getByRpaSourceId(Long rpaSourceId){
        return Arrays.stream(RawDataSourceEnum.values())
                .filter(x -> rpaSourceId.equals(x.getRpaSourceId()))
                .findFirst().orElse(null);






    }

    public static List<RawDataSourceEnum> getEnumList() {
        return Lists.newArrayList(RawDataSourceEnum.values());
    }


    /**
     * 根据来源获取账户类型
     * @param source  来源
     * @return 账户类型
     */
    public static AccountTypeEnum getAccountTypeByCode(Integer source) {

        if (Objects.isNull( source)){
            return null;
        }
        if (Objects.equals(ALIPAY.getCode(), source)){
            return AccountTypeEnum.TM_ALIPAY;
        }else if (Objects.equals(WECHAT.getCode(), source)){
            return AccountTypeEnum.TM_WECHAT;
        }else if (Objects.equals(GUARANTEE.getCode(), source)){
            return AccountTypeEnum.TM_GUARANTEE;
        }else if (Arrays.asList(CONSUME_INTEGRAL.getCode(),SET_POINTS.getCode(),HUI_YING_BAO.getCode()
                ,LIVE_RED_PACKET.getCode(),TAOBAO_UNION.getCode(),TAO_GOLD.getCode()).contains(source)){
            return AccountTypeEnum.TM_OTHER;
        }else {
            return null;
        }

    }
    /**
     * 根据来源获取账户类型
     * @param accountTypeCode  资金账户类型
     * @return 账户类型
     */
    public static RawDataSourceEnum getDataSourceByAccountTypeCode(Integer accountTypeCode) {

        if (Objects.isNull( accountTypeCode)){
            return null;
        }
        if (Objects.equals(AccountTypeEnum.TM_ALIPAY.getTypeCode(), accountTypeCode)){
            return ALIPAY;
        }else if (Objects.equals(AccountTypeEnum.TM_WECHAT.getTypeCode(), accountTypeCode)){
            return WECHAT;
        }else if (Objects.equals(AccountTypeEnum.TM_GUARANTEE.getTypeCode(), accountTypeCode)){
            return GUARANTEE;
        }else {
            return null;
        }

    }
}
