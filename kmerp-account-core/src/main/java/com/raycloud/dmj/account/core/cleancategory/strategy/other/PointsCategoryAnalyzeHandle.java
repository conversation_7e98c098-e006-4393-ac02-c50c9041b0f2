package com.raycloud.dmj.account.core.cleancategory.strategy.other;

import com.raycloud.dmj.account.core.base.dao.TmallAlipayPointsRawBillDataDao;
import com.raycloud.dmj.account.core.base.domain.StandardOtherBillFlowInfoDO;
import com.raycloud.dmj.account.core.cleancategory.domain.object.TmallAlipayPointsRawBillDataDO;
import com.raycloud.dmj.account.core.cleancategory.strategy.param.CategoryAnalyzeParam;
import com.raycloud.dmj.account.core.common.Page;
import com.raycloud.dmj.account.core.enums.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;


/**
 * 集分宝类目解析处理
 * <AUTHOR>
 */
@Component
public class PointsCategoryAnalyzeHandle extends StandardOtherCategoryAnalyzeHandle<TmallAlipayPointsRawBillDataDO> {


    @Resource
    private TmallAlipayPointsRawBillDataDao tmallAlipayPointsRawBillDataDao;

    @Override
    protected List<TmallAlipayPointsRawBillDataDO> listPageRawData(CategoryAnalyzeParam param, Page page) {
        return tmallAlipayPointsRawBillDataDao.listPageByDataRange(
                param.getCompanyId(),
                param.getShopId(),
                param.getDataRange(),
                page
        );
    }

    @Override
    protected void setStandardFundBillFlowInfoDO(StandardOtherBillFlowInfoDO standardOtherBillFlowInfoDO, TmallAlipayPointsRawBillDataDO rawDataDO) {
        standardOtherBillFlowInfoDO.setCategoryGroupCode(OtherCategoryGroupEnum.BT.getCode());
        standardOtherBillFlowInfoDO.setCategoryCode(OtherCategoryEnum.JFB.getCode());
        standardOtherBillFlowInfoDO.setSubCategoryCode(OtherSubCategoryEnum.JFB.getCode());
        standardOtherBillFlowInfoDO.setBillingCycle(rawDataDO.getMonth());
        standardOtherBillFlowInfoDO.setOccurredAt(rawDataDO.getBusinessDate());

        BigDecimal deductionAmount = rawDataDO.getDeductionAmount();
        standardOtherBillFlowInfoDO.setAmount(deductionAmount);
        if (deductionAmount.compareTo(BigDecimal.ZERO)>0){
            standardOtherBillFlowInfoDO.setIncomeExpenseDirection(IncomeExpenseDirectionEnum.INCOME.getCode());
        }else {
            standardOtherBillFlowInfoDO.setIncomeExpenseDirection(IncomeExpenseDirectionEnum.EXPENSE.getCode());
        }
        standardOtherBillFlowInfoDO.setOrderNo(rawDataDO.getMerchantOrderId());
        standardOtherBillFlowInfoDO.setBatchNo(rawDataDO.getBatchNo());
        standardOtherBillFlowInfoDO.setBizKey(rawDataDO.getBizKey());

    }

    @Override
    protected void deleteByShopAndDataRangeAndCategoryGroup(CategoryAnalyzeParam param) {
        standardOtherBillFlowInfoDao.deleteByShopAndDataRangeAndCategory(param.getCompanyId(),
                param.getShopId(),param.getDataRange() , OtherCategoryEnum.JFB.getCode());
    }

    @Override
    public boolean accord(RawDataSourceEnum source) {
        return RawDataSourceEnum.SET_POINTS.equals(source);
    }
}
