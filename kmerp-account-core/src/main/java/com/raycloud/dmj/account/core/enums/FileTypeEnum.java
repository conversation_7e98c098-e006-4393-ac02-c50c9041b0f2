package com.raycloud.dmj.account.core.enums;

public enum FileTypeEnum {
    XLSX("xlsx"),
    CSV("csv"),
    XLS("xls"),
    ZIP("zip");

    private String fileType;

    FileTypeEnum(String fileType) {
        this.fileType = fileType;
    }

    public String getFileType() {
        return fileType;
    }

    public static FileTypeEnum getFileType(String fileType) {
        for (FileTypeEnum fileTypeEnum : FileTypeEnum.values()) {
            if (fileTypeEnum.getFileType().equals(fileType)) {
                return fileTypeEnum;
            }
        }
        return null;
    }

}
