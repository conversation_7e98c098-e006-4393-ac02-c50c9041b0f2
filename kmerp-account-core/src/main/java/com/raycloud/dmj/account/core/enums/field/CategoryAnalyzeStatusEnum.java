package com.raycloud.dmj.account.core.enums.field;

import lombok.Getter;

/**
 * 原始数据解析状态枚举
 * <AUTHOR>
 */
@Getter
public enum CategoryAnalyzeStatusEnum {

    //数据状态， 0-解析中， 30-解析失败  50-解析成功
    ANALYZING(0, "解析中"),
    ANALYZE_FAIL(30, "解析失败"),
    ANALYZE_SUCCESS(50, "解析成功");


    private final Integer status;

    private final String desc;

    CategoryAnalyzeStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
