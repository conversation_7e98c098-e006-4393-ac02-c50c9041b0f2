package com.raycloud.dmj.account.core.platform.auth.impl;

import com.raycloud.dmj.domain.user.User;
import com.raycloud.dmj.services.user.IUserService;
import com.raycloud.middle.gateway.dubbo.MiddleGatewayDubbo;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class DubboUserService {

    @DubboReference(registry = "erpZk", version = "erp-prod-0.0.1", check = false)
    private IUserService userService;

    public User getUser(Long shopId) {
        return userService.queryById(shopId);
    }


}
