package com.raycloud.dmj.account.core.platform.dao.impl;

import com.raycloud.dmj.account.core.base.dao.impl.BaseDao;
import com.raycloud.dmj.account.core.platform.base.domain.ShopAuthInfoDO;
import com.raycloud.dmj.account.core.platform.base.domain.WechatFundsFlowProgressDO;
import com.raycloud.dmj.account.core.platform.base.domain.enums.ShopAuthInfoFieldEnum;
import com.raycloud.dmj.account.core.platform.base.domain.enums.WechatFundsFlowProgressFieldEnum;
import com.raycloud.dmj.account.core.platform.dao.WechatFundsFlowProgressDao;
import com.raycloud.dmj.table.api.plus.common.base.SQL;
import com.raycloud.dmj.table.api.plus.common.enums.LinkMode;
import com.raycloud.dmj.table.api.plus.component.$;
import com.raycloud.dmj.table.api.plus.component.column.utils.Columns;
import com.raycloud.dmj.table.api.plus.component.condition.utils.Conditions;
import com.raycloud.dmj.table.api.plus.insert.Inserts;
import com.raycloud.dmj.table.api.plus.query.Queries;
import com.raycloud.dmj.table.api.plus.update.Updates;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class WechatFundsFlowProgressDaoImpl extends BaseDao implements WechatFundsFlowProgressDao {

    private static final String TABLE_NAME = "wechat_funds_flow_progress";

    @Override
    public WechatFundsFlowProgressDO selectByShopIdCompanyId(Long shopId, Long companyId) {
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(Conditions.and(Columns.toColumn(WechatFundsFlowProgressFieldEnum.SHOP_ID.getFieldCode()), LinkMode.EQUAL, shopId),
                        Conditions.and(Columns.toColumn(WechatFundsFlowProgressFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId))
                .select()
                .toSql();
        List<WechatFundsFlowProgressDO> records = jdbcTemplate.query(
                sql.getSqlCode(),
                new BeanPropertyRowMapper<>(WechatFundsFlowProgressDO.class),
                sql.getArgs().toArray()
        );
        return records.isEmpty() ? null : records.get(0);
    }

    @Override
    public int insertOrUpdate(WechatFundsFlowProgressDO record) {
        WechatFundsFlowProgressDO existing = selectByShopIdCompanyId(
                record.getShopId(), record.getCompanyId());
        if (existing == null) {
            // 新增
            record.setCreated(LocalDateTime.now());
            record.setModified(LocalDateTime.now());
            SQL sql = Inserts.insert()
                    .into(TABLE_NAME)
                    .columns(WechatFundsFlowProgressFieldEnum.getInsertFields())
                    .valueForEntity(record)
                    .toSql();
            return jdbcTemplate.update(sql.getSqlCode(), sql.getArgs().toArray());
        } else {
            // 更新
            record.setId(existing.getId());
            record.setCreated(existing.getCreated());
            record.setModified(LocalDateTime.now());
            SQL sql = Updates.create()
                    .table(TABLE_NAME)
                    .where(
                            Conditions.and(
                                    Columns.toColumn(WechatFundsFlowProgressFieldEnum.ID.getFieldCode()),
                                    LinkMode.EQUAL,
                                    record.getId()
                            )
                    )
                    .update(
                            $.updateKeyValue(WechatFundsFlowProgressFieldEnum.NEXT_KEY.getFieldCode(), record.getNextKey()),
                            $.updateKeyValue(WechatFundsFlowProgressFieldEnum.PAGE_NO.getFieldCode(), record.getPageNo()),
                            $.updateKeyValue(WechatFundsFlowProgressFieldEnum.OVER_TIME.getFieldCode(), record.getOverTime()),
                            $.updateKeyValue(WechatFundsFlowProgressFieldEnum.OBJECT_NAME.getFieldCode(), record.getObjectName()),
                            $.updateKeyValue(WechatFundsFlowProgressFieldEnum.MODIFIED.getFieldCode(), record.getModified())
                    )
                    .toSql();
            return jdbcTemplate.update(sql.getSqlCode(), sql.getArgs().toArray());
        }
    }

}
