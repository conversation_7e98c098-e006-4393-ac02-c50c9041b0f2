package com.raycloud.dmj.account.core.enums.field;

import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.Set;
import java.util.stream.Collectors;

@Getter
public enum AnalyzeCategoryRecordFieldEnum {
    ID("id", "自增主键"),
    CREATED("created", "创建时间"),
    MODIFIED("modified", "更新时间"),
    DATE_TYPE("date_type", "时间类型，默认为1，1为日，2为月，3为年"),
    DATA_RANGE("data_range", "当前数据范围，如果date_type为日，则是yyyyMMdd，如果是月，则是yyyyMM，年则是yyyy"),
    COMPANY_ID("company_id", "公司ID"),
    SHOP_ID("shop_id", "店铺ID"),
    DATA_SOURCE("data_source", "数据源"),
    ANALYZE_STATUS("analyze_status", "解析状态：0-解析中， 30-解析失败  50-解析成功"),
    FEATURE("feature", "扩展字段"),
    VERSION("version", "版本号");

    /**
     * 字段编码
     */
    private final String fieldCode;

    /**
     * 字段描述
     */
    private final String fieldDesc;

    AnalyzeCategoryRecordFieldEnum(String fieldCode, String fieldDesc) {
        this.fieldCode = fieldCode;
        this.fieldDesc = fieldDesc;
    }

    /**
     * 获取插入字段
     * @return 插入字段
     */
    public static Set<String> getInsertFields() {
        Set<AnalyzeCategoryRecordFieldEnum> filterSet = Collections.singleton(AnalyzeCategoryRecordFieldEnum.ID);
        return Arrays.stream(AnalyzeCategoryRecordFieldEnum.values())
               .filter(x ->!filterSet.contains(x))
               .map(x -> x.fieldCode)
               .collect(Collectors.toSet());
    }
}