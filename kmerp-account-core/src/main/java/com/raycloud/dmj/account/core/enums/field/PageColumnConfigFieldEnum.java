package com.raycloud.dmj.account.core.enums.field;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 页面列配置信息表字段枚举
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum PageColumnConfigFieldEnum {

    ID("id", "主键ID"),
    PAGE_ID("page_id", "页面ID"),
    COL_CODE("col_code", "列编码"),
    COL_TITLE("col_title", "列名称"),
    WIDTH("width", "列宽"),
    SORT("sort", "排序"),
    VISIBLE("visible", "是否可见"),
    CREATED("created", "创建时间"),
    MODIFIED("modified", "更新时间"),
    COMPANY_ID("company_id", "公司ID");

    private final String fieldCode;
    private final String fieldDesc;

    /**
     * 获取插入字段列表（排除主键ID）
     * @return 插入字段列表
     */
    public static List<String> getInsertFields() {
        return Arrays.stream(values())
                .filter(field -> !field.equals(ID))
                .map(PageColumnConfigFieldEnum::getFieldCode)
                .collect(Collectors.toList());
    }

    /**
     * 获取所有字段列表
     * @return 所有字段列表
     */
    public static List<String> getAllFields() {
        return Arrays.stream(values())
                .map(PageColumnConfigFieldEnum::getFieldCode)
                .collect(Collectors.toList());
    }
}
