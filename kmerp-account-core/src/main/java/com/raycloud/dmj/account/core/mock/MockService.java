package com.raycloud.dmj.account.core.mock;

import com.google.common.collect.Lists;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.table.api.plus.common.enums.LinkMode;
import com.raycloud.dmj.table.api.plus.component.$;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@RequiredArgsConstructor
@Service
public class MockService {

    private final MockRepository mockRepository;

    public Object get(Staff staff, String name) {
        return mockRepository.getReportMySQL(staff).getList(
                Lists.newArrayList(
                        $.and("name", LinkMode.LIKE_FULL_MATCH, name)
                )
        );
    }
}
