package com.raycloud.dmj.account.core.cleancategory.domain.request;

import lombok.Data;

import java.io.Serializable;

/**
 * 重新分析请求
 * <AUTHOR>
 */
@Data
public class AgainAnalyzeReq implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 资金账户ID
     */
    private Long fundAccountId;

    /**
     * 重洗开始时间
     * yyyy-MM-dd
     */
    private String reprocessStartTime;

    /**
     * 重洗结束时间
     * yyyy-MM-dd
     */
    private String reprocessEndTime;
}
