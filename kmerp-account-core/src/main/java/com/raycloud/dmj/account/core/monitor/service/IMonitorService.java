package com.raycloud.dmj.account.core.monitor.service;

import com.raycloud.dmj.account.core.common.PageInfo;
import com.raycloud.dmj.account.core.monitor.request.QueryShopMonitorReq;
import com.raycloud.dmj.account.core.monitor.vo.MonitorSummaryVO;

import java.util.List;

public interface IMonitorService {

    /**
     * 店铺数据推送监控
     * @return
     */
    List<MonitorSummaryVO> queryShopDataMonitor(QueryShopMonitorReq req);


    /**
     * 店铺数据推送监控详情
     * @return
     */
    PageInfo<Void> queryShopDataMonitorPageInfo(QueryShopMonitorReq req);


}
