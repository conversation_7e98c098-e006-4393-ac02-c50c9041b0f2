package com.raycloud.dmj.account.core.enums.field;

import lombok.Getter;

/**
 * 保证金原始数据字段枚举
 * <AUTHOR>
 */
@Getter
public enum TmallGuaranteeRawBillFieldEnum {

    ID("id", "自增主键"),
    STORE_SHORT_NAME("store_short_name", "店铺简称"),
    COMPLETION_TIME("completion_time", "完成时间"),
    FUND_TYPE("fund_type", "资金类型"),
    OPERATION_TYPE("operation_type", "操作类型"),
    REASON("reason", "原因"),
    CURRENCY("currency", "币种"),
    RECEIPT_AMOUNT("receipt_amount", "收支金额"),
    CASH_BALANCE("cash_balance", "现金总余额"),
    SOURCE_ACCOUNT("source_account", "来源账户"),
    DESTINATION_ACCOUNT("destination_account", "去向账户"),
    CONTRIBUTION_TYPE("contribution_type", "出资类型"),
    BUSINESS_DESCRIPTION("business_description", "业务描述"),
    BUSINESS_NUMBER("business_number", "业务编号"),
    ORDER_NUMBER("order_number", "订单编号"),
    REMARK("remark", "备注"),
    DOWNLOAD_TIME("download_time", "下载时间"),
    DOWNLOAD_ACCOUNT("download_account", "下载账户"),
    BIZ_KEY("biz_key", "唯一键"),
    BATCH_NO("batch_no", "批次号"),
    BATCH_TIME("batch_time", "批次时间"),
    COMPANY_ID("company_id", "公司ID"),
    SHOP_ID("shop_id", "店铺ID"),
    CREATED("created", "创建时间");

    /**
     * 字段编码
     */
    private final String fieldCode;

    /**
     * 字段描述
     */
    private final String fieldDesc;

    TmallGuaranteeRawBillFieldEnum(String fieldCode, String fieldDesc) {
        this.fieldCode = fieldCode;
        this.fieldDesc = fieldDesc;
    }
}    