package com.raycloud.dmj.account.core.platform.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.raycloud.dmj.account.core.platform.base.domain.SharedDataInfoDO;
import com.raycloud.dmj.account.core.platform.dao.SharedDataInfoDao;
import com.raycloud.dmj.account.core.platform.service.ISharedDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;

@Slf4j
@Service
public class SharedDataServiceImpl implements ISharedDataService {

    @Resource
    private SharedDataInfoDao sharedDataInfoDao;


    @Override
    public SharedDataInfoDO getSharedDataInfo(Long shopId, Long companyId, String type, LocalDate date) {
        return sharedDataInfoDao.getByType(shopId, companyId, type, date);
    }

    @Override
    public void insertSharedDataInfo(SharedDataInfoDO sharedDataInfoDO) {
        sharedDataInfoDao.insert(sharedDataInfoDO);
    }
}
