package com.raycloud.dmj.account.core.bill.service.impl;

import com.alibaba.fastjson2.JSON;
import com.raycloud.dmj.account.core.base.dao.BillVerifyRuleDao;
import com.raycloud.dmj.account.core.base.dao.FundAccountDao;
import com.raycloud.dmj.account.core.base.domain.BillVerifyRuleDO;
import com.raycloud.dmj.account.core.bill.request.AddBillVerifyRuleRequest;
import com.raycloud.dmj.account.core.bill.request.BillVerifyRuleRequest;
import com.raycloud.dmj.account.core.bill.service.IBillVerifyRuleService;
import com.raycloud.dmj.account.core.bill.utils.VerifyRuleUtils;
import com.raycloud.dmj.account.core.bill.vo.BillVerifyRuleVo;
import com.raycloud.dmj.account.core.bill.vo.VerifyRuleVo;
import com.raycloud.dmj.account.core.cleancategory.domain.object.FundAccountDO;
import com.raycloud.dmj.account.core.enums.CommonStatusEnum;
import com.raycloud.dmj.account.core.enums.DateTypeEnum;
import com.raycloud.dmj.account.infra.common.BusinessException;
import com.raycloud.dmj.account.infra.common.ErrorCodeEnum;
import com.raycloud.dmj.account.infra.session.AccountUser;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import com.raycloud.dmj.account.infra.utils.BeanUtils;
import com.raycloud.dmj.account.infra.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 账单核验规则服务实现类
 * <AUTHOR>
 */
@Slf4j
@Service
public class BillVerifyRuleServiceImpl implements IBillVerifyRuleService {

    @Resource
    private BillVerifyRuleDao billVerifyRuleDao;
    @Resource
    private FundAccountDao fundAccountDao;

    @Override
    public Long addBillVerifyRule(AddBillVerifyRuleRequest request, AccountUser accountUser) {
        // 参数校验
        AsserUtils.notNull(accountUser, "用户不能为空");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空");
        AsserUtils.notNull(request, "账单核验规则不能为空");
        AsserUtils.notNull(request.getShopId(), "所属店铺ID不能为空");
        AsserUtils.hasText(request.getRuleName(), "核验规则名称不能为空");
        AsserUtils.notNull(request.getEffectiveTime(), "生效时间不能为空");
        AsserUtils.notNull(request.getInvalidTime(), "失效时间不能为空");
        AsserUtils.notNull(request.getDateType(), "时间类型不能为空");
        AsserUtils.notEmpty(request.getRuleContent(), "核验规则内容不能为空");
        AsserUtils.notNull(request.getJudgmentType(), "判断方式不能为空");
        AsserUtils.notNull(request.getAmount(), "核验金额不能为空");

        //校验核验规则是否符合要求
        VerifyRuleUtils.validateExpression(request.getRuleContent());

        BillVerifyRuleDO billVerifyRuleDO = new BillVerifyRuleDO();
        BeanUtils.copyProperties(request, billVerifyRuleDO,"id","effectiveTime","invalidTime");

        DateTypeEnum dateTypeEnum = DateTypeEnum.of(request.getDateType());
        if (dateTypeEnum == null) {
            throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR,"时间类型异常");
        }
        //设置生效时间
        billVerifyRuleDO.setEffectiveTime(DateUtils.parse(request.getEffectiveTime(),dateTypeEnum.getPatter()));
        //设置失效时间
        billVerifyRuleDO.setInvalidTime(DateUtils.getEndDayByDateType(DateUtils.parse(request.getInvalidTime(),dateTypeEnum.getPatter()),dateTypeEnum.getCode()));

        //设置校验规则内容
        billVerifyRuleDO.setRuleContent(JSON.toJSONString(request.getRuleContent()));
        // 设置公司ID
        billVerifyRuleDO.setCompanyId(accountUser.getCompanyId());
        // 设置创建时间和修改时间
        Date now = new Date();
        billVerifyRuleDO.setCreated(now);
        billVerifyRuleDO.setModified(now);
        // 设置默认值
        if (billVerifyRuleDO.getIsExceptionTip() == null) {
            billVerifyRuleDO.setIsExceptionTip(CommonStatusEnum.ON.getType());
        }
        if (billVerifyRuleDO.getStatus() == null) {
            billVerifyRuleDO.setStatus(CommonStatusEnum.OFF.getType());
        }
        if (billVerifyRuleDO.getEnableStatus() == null) {
            billVerifyRuleDO.setEnableStatus(CommonStatusEnum.ON.getType());
        }
        // 执行新增操作
        return billVerifyRuleDao.insert(billVerifyRuleDO);
    }

    @Override
    public BillVerifyRuleVo getBillVerifyRuleById(Long id, AccountUser accountUser) {
        AsserUtils.notNull(id, "ID不能为空");
        AsserUtils.notNull(accountUser, "用户不能为空");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空");

        BillVerifyRuleDO billVerifyRuleDO = billVerifyRuleDao.queryById(id, accountUser.getCompanyId());

        BillVerifyRuleVo ruleVo = new BillVerifyRuleVo();
        BeanUtils.copyProperties(billVerifyRuleDO, ruleVo,"effectiveTime","invalidTime");

        DateTypeEnum typeEnum = DateTypeEnum.of(billVerifyRuleDO.getDateType());
        if (typeEnum == null) {
            throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR,"时间类型异常");
        }
        ruleVo.setEffectiveTime(DateUtils.formatDate(billVerifyRuleDO.getEffectiveTime(), typeEnum.getPatter()));
        ruleVo.setInvalidTime(DateUtils.formatDate(billVerifyRuleDO.getInvalidTime(), typeEnum.getPatter()));

        //设置校验规则表达式信息
        ruleVo.setRuleContent(JSON.parseArray(billVerifyRuleDO.getRuleContent(), VerifyRuleVo.class));
        return ruleVo;
    }

    @Override
    public void updateBillVerifyRule(AddBillVerifyRuleRequest request, AccountUser accountUser) {
        // 参数校验
        AsserUtils.notNull(accountUser, "用户不能为空");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空");
        AsserUtils.notNull(request, "账单核验规则不能为空");
        AsserUtils.notNull(request.getId(), "ID不能为空");
        AsserUtils.notNull(request.getShopId(), "所属店铺ID不能为空");
        AsserUtils.hasText(request.getRuleName(), "核验规则名称不能为空");
        AsserUtils.notNull(request.getEffectiveTime(), "生效时间不能为空");
        AsserUtils.notNull(request.getInvalidTime(), "失效时间不能为空");
        AsserUtils.notNull(request.getDateType(), "时间类型不能为空");
        AsserUtils.notEmpty(request.getRuleContent(), "核验规则内容不能为空");
        AsserUtils.notNull(request.getJudgmentType(), "判断方式不能为空");
        AsserUtils.notNull(request.getAmount(), "核验金额不能为空");

        //校验核验规则是否符合要求
        VerifyRuleUtils.validateExpression(request.getRuleContent());

        BillVerifyRuleDO billVerifyRuleDO = new BillVerifyRuleDO();
        BeanUtils.copyProperties(request, billVerifyRuleDO,"id","effectiveTime","invalidTime");

        //获取时间类型
        DateTypeEnum dateTypeEnum = DateTypeEnum.of(request.getDateType());
        if (dateTypeEnum == null) {
            throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR,"时间类型异常");
        }
        //设置生效时间
        billVerifyRuleDO.setEffectiveTime(DateUtils.parse(request.getEffectiveTime(),dateTypeEnum.getPatter()));
        //设置失效时间
        billVerifyRuleDO.setInvalidTime(DateUtils.getEndDayByDateType(DateUtils.parse(request.getInvalidTime(),dateTypeEnum.getPatter()),dateTypeEnum.getCode()));

        // 设置公司ID
        billVerifyRuleDO.setCompanyId(accountUser.getCompanyId());
        // 执行更新操作
        int result = billVerifyRuleDao.updateById(billVerifyRuleDO);
        if (result <= 0) {
            throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR,"更新账单核验规则失败");
        }

    }

    @Override
    public void deleteBillVerifyRule(Long id, AccountUser accountUser) {
        AsserUtils.notNull(id, "ID不能为空");
        AsserUtils.notNull(accountUser, "用户不能为空");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空");

        // 执行删除操作（逻辑删除）
        int result = billVerifyRuleDao.deleteById(id, accountUser.getCompanyId());
        if (result <= 0) {
            throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR,"删除账单核验规则失败");
        }
    }

    @Override
    public void bulkDeletionBillVerifyRule(List<Long> idList, AccountUser accountUser) {
        AsserUtils.notNull(idList, "ID集合不能为空");
        AsserUtils.notNull(accountUser, "用户不能为空");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空");

        // 执行删除操作（逻辑删除）
        int result = billVerifyRuleDao.bulkDeletion(idList, accountUser.getCompanyId());
        if (result <= 0) {
            throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR,"删除账单核验规则失败");
        }
    }

    @Override
    public List<BillVerifyRuleVo> getBillVerifyRuleList(BillVerifyRuleRequest request, AccountUser accountUser) {
        // 参数校验
        AsserUtils.notNull(accountUser, "用户不能为空");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空");
        AsserUtils.notNull(request, "查询参数不能为空");
        AsserUtils.notNull(request.getPlatformCode(), "平台code不能为空");

        // 查询数据
        List<BillVerifyRuleDO> ruleDOList = billVerifyRuleDao.queryByParam(request, accountUser.getCompanyId());
        List<BillVerifyRuleVo> voList = new ArrayList<>();
        ruleDOList.forEach(item -> {
            BillVerifyRuleVo ruleVo = new BillVerifyRuleVo();
            BeanUtils.copyProperties(item, ruleVo,"invalidTime","effectiveTime");

            DateTypeEnum typeEnum = DateTypeEnum.of(item.getDateType());
            if (typeEnum == null) {
                throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR,"时间类型异常");
            }
            ruleVo.setEffectiveTime(DateUtils.formatDate(item.getEffectiveTime(), typeEnum.getPatter()));
            ruleVo.setInvalidTime(DateUtils.formatDate(item.getInvalidTime(), typeEnum.getPatter()));

            //设置校验规则表达式信息
            ruleVo.setRuleContent(JSON.parseArray(item.getRuleContent(), VerifyRuleVo.class));
            voList.add(ruleVo);
        });
        return voList;
    }

    @Override
    public void enableRule(List<Long> idList, AccountUser accountUser) {
        // 参数校验
        AsserUtils.notNull(accountUser, "用户不能为空");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空");
        AsserUtils.notNull(idList, "请输入规则ID");

        int result = billVerifyRuleDao.enableRule(idList,accountUser.getCompanyId());
        if (result <= 0) {
            throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR,"开启账单核验规则失败");
        }
    }

    @Override
    public void shutDownRule(List<Long> idList, AccountUser accountUser) {
        // 参数校验
        AsserUtils.notNull(accountUser, "用户不能为空");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空");
        AsserUtils.notNull(idList, "请输入规则ID");

        int result = billVerifyRuleDao.shutDownRule(idList,accountUser.getCompanyId());
        if (result <= 0) {
            throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR,"关闭账单核验规则失败");
        }
    }

}
