package com.raycloud.dmj.account.core.bill.service;

import com.raycloud.dmj.account.core.bill.request.AddBillVerifyRuleRequest;
import com.raycloud.dmj.account.core.bill.request.BillVerifyRuleRequest;
import com.raycloud.dmj.account.core.bill.vo.BillVerifyRuleVo;
import com.raycloud.dmj.account.infra.session.AccountUser;

import java.util.List;

/**
 * 账单核验规则服务接口
 * <AUTHOR>
 */
public interface IBillVerifyRuleService {

    /**
     * 新增账单核验规则
     * @param request 账单核验规则对象
     * @param accountUser 当前用户
     * @return 新增记录的主键ID
     */
    Long addBillVerifyRule(AddBillVerifyRuleRequest request, AccountUser accountUser);

    /**
     * 根据ID查询账单核验规则
     * @param id 主键ID
     * @param accountUser 当前用户
     * @return 账单核验规则VO
     */
    BillVerifyRuleVo getBillVerifyRuleById(Long id, AccountUser accountUser);

    /**
     * 更新账单核验规则
     *
     * @param request 账单核验规则对象
     * @param accountUser      当前用户
     */
    void updateBillVerifyRule(AddBillVerifyRuleRequest request, AccountUser accountUser);

    /**
     * 根据ID删除账单核验规则
     *
     * @param id          主键ID
     * @param accountUser 当前用户
     */
    void deleteBillVerifyRule(Long id, AccountUser accountUser);

    /**
     * 分页查询账单核验规则列表
     * @param request 查询参数
     * @param accountUser 当前用户
     * @return 分页结果
     */
    List<BillVerifyRuleVo> getBillVerifyRuleList(BillVerifyRuleRequest request, AccountUser accountUser);
    /**
     * 启用核验规则
     * @param idList   主键ID集合
     * @param accountUser  当前用户
     */
    void enableRule(List<Long> idList, AccountUser accountUser);

    /**
     * 关闭核验规则
     * @param idList  主键ID集合
     * @param accountUser  当前用户
     */
    void shutDownRule(List<Long> idList, AccountUser accountUser);

    /**
     * 批量删除核验规则
     * @param idList 规则ID集合
     * @param accountUser 用户信息
     */
    void bulkDeletionBillVerifyRule(List<Long> idList, AccountUser accountUser);
}
