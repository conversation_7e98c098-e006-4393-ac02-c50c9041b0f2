package com.raycloud.dmj.account.core.cleancategory.domain.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * 查询
 * <AUTHOR>
 */
@Data
public class QueryCategoryTreeReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 平台code
     */
    private String platformCode;

    /**
     * 分类组code
     */
    private List<String> categoryGroupCodes;

    /**
     * 资金账户ID
     */
    private List<Long> fundAccountIds;



}
