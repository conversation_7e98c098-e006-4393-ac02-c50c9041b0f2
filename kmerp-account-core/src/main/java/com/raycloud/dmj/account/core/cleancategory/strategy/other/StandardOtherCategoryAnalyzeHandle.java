package com.raycloud.dmj.account.core.cleancategory.strategy.other;


import com.alibaba.fastjson2.JSON;
import com.raycloud.dmj.account.core.base.dao.*;
import com.raycloud.dmj.account.core.base.domain.ShopInfoDO;
import com.raycloud.dmj.account.core.base.domain.StandardOtherBillFlowInfoDO;
import com.raycloud.dmj.account.core.cleancategory.domain.object.AnalyzeCategoryRecordDO;
import com.raycloud.dmj.account.core.cleancategory.domain.object.FundAccountDO;
import com.raycloud.dmj.account.core.cleancategory.strategy.CategoryAnalyzeHandle;
import com.raycloud.dmj.account.core.cleancategory.strategy.param.CategoryAnalyzeParam;
import com.raycloud.dmj.account.core.common.Page;
import com.raycloud.dmj.account.core.enums.AccountTypeEnum;
import com.raycloud.dmj.account.core.enums.StandardFundBillSourceEnum;
import com.raycloud.dmj.account.core.enums.feature.AnalyzeCategoryRecordFeatureEnum;
import com.raycloud.dmj.account.core.enums.field.CategoryAnalyzeStatusEnum;
import com.raycloud.dmj.account.core.rawdata.manage.RawDataStorageManage;
import com.raycloud.dmj.account.infra.common.BusinessException;
import com.raycloud.dmj.account.infra.common.ErrorCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 标准其他类目解析
 *
 * <AUTHOR>
 */

@Slf4j
public abstract class StandardOtherCategoryAnalyzeHandle<T> implements CategoryAnalyzeHandle {


    /**
     * 标准资金流水创建人
     */
    protected static final String CREATOR = "admin";


    @Resource
    private FundAccountDao fundAccountDao;

    @Resource
    private ShopInfoDao shopInfoDao;

    @Resource
    private MonitorSummaryDao monitorSummaryDao;

    @Resource
    private RawDataStorageManage rawDataStorageManage;

    @Resource
    private AnalyzeCategoryRecordDao analyzeCategoryRecordDao;

    @Resource
    protected StandardOtherBillFlowInfoDao standardOtherBillFlowInfoDao;


    /**
     * 分页查询原始数据
     *
     * @param param 参数
     * @param page  分页参数
     * @return 结果
     */
    protected abstract List<T> listPageRawData(CategoryAnalyzeParam param, Page page);


    /**
     * 设置其他账单流水
     *
     * @param standardOtherBillFlowInfoDO 其他账单流水
     * @param rawDataDO                   原始数据
     */
    protected abstract void setStandardFundBillFlowInfoDO(StandardOtherBillFlowInfoDO standardOtherBillFlowInfoDO, T rawDataDO);

    /**
     * 根据账期和店铺删除其他账单流水
     *
     * @param param 参数
     */
    protected abstract void deleteByShopAndDataRangeAndCategoryGroup(CategoryAnalyzeParam param);


    @Override
    public void handle(CategoryAnalyzeParam param) {
        //参数检验
        paramVerify(param);
        AnalyzeCategoryRecordDO analyzeCategoryRecordDO = analyzeCategoryRecordDao.getByShopAndSourceAndDataRange(param.getCompanyId(), param.getShopId(), param.getSource().getCode(), param.getDataRange());
        //幂等性检验
        if (idempotentVerify(analyzeCategoryRecordDO)){
            log.info("|StandardFundCategoryAnalyzeHandle.handle|幂等性检验通过，param:{}", JSON.toJSONString(param));
            return;
        }
        //新增或更新解析记录为解析中
        AnalyzeCategoryRecordDO categoryRecordDO = insertOrUpdateAnalyzeCategoryRecord(analyzeCategoryRecordDao, param, analyzeCategoryRecordDO);
        //解析数据
        try {
            analyzeData(param);
        } catch (Exception e) {
            log.error("|StandardOtherCategoryAnalyzeHandle.handle error|原始数据解析失败！，param:{}", JSON.toJSONString(param), e);
            //更新监控表状态,更新解析状态 解析失败
            categoryRecordDO.setAnalyzeStatus(CategoryAnalyzeStatusEnum.ANALYZE_FAIL.getStatus());
            categoryRecordDO.putFeature(AnalyzeCategoryRecordFeatureEnum.ERROR_MESSAGE, e.getMessage());
            analyzeCategoryRecordDao.updateAnalyzeStatusById(categoryRecordDO);
            throw e;
        }
        //更新监控表状态,更新解析状态 解析完成
        categoryRecordDO.setAnalyzeStatus(CategoryAnalyzeStatusEnum.ANALYZE_SUCCESS.getStatus());
        analyzeCategoryRecordDao.updateAnalyzeStatusById(categoryRecordDO);

    }

    /**
     * 获取资金账户
     *
     * @param param 参数
     * @return 资金账户
     */
    private FundAccountDO getFundAccount(CategoryAnalyzeParam param) {
        return fundAccountDao.getByShopIdAndType(param.getCompanyId(), param.getShopId(),
                AccountTypeEnum.TM_OTHER.getTypeCode());
    }


    /**
     * 解析数据
     *
     * @param param 解析参数
     */
    private void analyzeData(CategoryAnalyzeParam param) {

        //查询店铺信息
        ShopInfoDO shopInfoDO = shopInfoDao.getShopInfoById(param.getShopId());
        if (Objects.isNull(shopInfoDO)) {
            log.error("|StandardFundCategoryAnalyzeHandle.analyzeData error|店铺不存在,param:{}", JSON.toJSONString(param));
            throw new BusinessException(ErrorCodeEnum.SHOP_NOT_EXISTS);
        }
        FundAccountDO fundAccountDO = getFundAccount(param);
        //校验数据是否导入成功
        verifyDataIsImport(rawDataStorageManage,param);
        //校验上期数据是否成功解析
        verifyPreviousDataRange(analyzeCategoryRecordDao,param, fundAccountDO);
        //先删除该店铺下该账期的同一类型的其他流水
        deleteByShopAndDataRangeAndCategoryGroup(param);
        int pageNo = 1;
        AtomicInteger batchInsertCount = new AtomicInteger(0);
        while (true) {
            Page page = new Page(pageNo, DEFAULT_ANALYZE_SIZE);
            List<T> currentPage = listPageRawData(param, page);
            if (CollectionUtils.isEmpty(currentPage)) {
                break;
            }
            //构建标准资金账单流水信息
            List<StandardOtherBillFlowInfoDO> standardFundBillFlowInfoDOList = currentPage.stream()
                    .map(rawData -> buildStandardOtherBillFlowInfoDO(
                            param,
                            rawData,
                            shopInfoDO)
                    )
                    .collect(Collectors.toList());

            //批量插入
            Integer rowCount = standardOtherBillFlowInfoDao.batchInsert(standardFundBillFlowInfoDOList);
            batchInsertCount.addAndGet(rowCount);
            // 准备查询下一页
            pageNo++;
        }
        //更新监控表解析成功时间
        monitorSummaryDao.updateAnalyzeTimeByShopAndSource(param.getCompanyId(),param.getShopId(),param.getSource().getCode());


    }


    /**
     * 构建其他资金账单流水信息
     *
     * @param rawData    原始数据
     * @param shopInfoDO 店铺信息
     * @return 其他资金账单流水信息
     */
    private StandardOtherBillFlowInfoDO buildStandardOtherBillFlowInfoDO(CategoryAnalyzeParam param,T rawData,
                                                                         ShopInfoDO shopInfoDO) {
        StandardOtherBillFlowInfoDO standardOtherBillFlowInfoDO = new StandardOtherBillFlowInfoDO();
        standardOtherBillFlowInfoDO.setPlatformCode(shopInfoDO.getPlatformCode());
        standardOtherBillFlowInfoDO.setShopId(shopInfoDO.getId());
        standardOtherBillFlowInfoDO.setSource(StandardFundBillSourceEnum.BILL.getCode());
        standardOtherBillFlowInfoDO.setCreator(CREATOR);
        standardOtherBillFlowInfoDO.setModified(new Date());
        standardOtherBillFlowInfoDO.setCreated(new Date());
        standardOtherBillFlowInfoDO.setCompanyId(shopInfoDO.getCompanyId());
        standardOtherBillFlowInfoDO.setDataRange(param.getDataRange());
        //从不同的原始数据表中设置
        setStandardFundBillFlowInfoDO(standardOtherBillFlowInfoDO, rawData);
        return standardOtherBillFlowInfoDO;
    }


}
