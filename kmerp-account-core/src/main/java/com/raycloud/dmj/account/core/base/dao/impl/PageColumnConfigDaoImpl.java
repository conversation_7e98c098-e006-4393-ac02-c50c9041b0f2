package com.raycloud.dmj.account.core.base.dao.impl;

import com.raycloud.dmj.account.core.base.dao.PageColumnConfigDao;
import com.raycloud.dmj.account.core.base.domain.PageColumnConfigDO;
import com.raycloud.dmj.account.core.enums.field.PageColumnConfigFieldEnum;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import com.raycloud.dmj.table.api.plus.common.ColumnValues;
import com.raycloud.dmj.table.api.plus.common.base.SQL;
import com.raycloud.dmj.table.api.plus.common.enums.LinkMode;
import com.raycloud.dmj.table.api.plus.component.condition.utils.Conditions;
import com.raycloud.dmj.table.api.plus.component.order.utils.Orders;
import com.raycloud.dmj.table.api.plus.delete.Deletes;
import com.raycloud.dmj.table.api.plus.insert.Inserts;
import com.raycloud.dmj.table.api.plus.insert.batch.InsertBatchSQL;
import com.raycloud.dmj.table.api.plus.insert.core.InsertMode;
import com.raycloud.dmj.table.api.plus.query.Queries;
import com.raycloud.dmj.table.api.plus.update.Updates;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 页面列配置信息表数据访问实现类
 *
 * <AUTHOR>
 */
@Repository
public class PageColumnConfigDaoImpl extends BaseDao implements PageColumnConfigDao {

    private static final String TABLE_NAME = "page_column_config";

    @Override
    public Integer batchInsert(List<PageColumnConfigDO> configList) {
        AsserUtils.notEmpty(configList, "配置列表不能为空");

        List<Object> objectList = configList.stream()
                .map(item -> (Object) item)
                .collect(Collectors.toList());

        InsertBatchSQL sql = Inserts.create(InsertMode.DEFAULT)
                .into(TABLE_NAME)
                .columns(PageColumnConfigFieldEnum.getInsertFields())
                .valueForEntities(objectList)
                .batch()
                .toSql();

        jdbcTemplate.batchUpdate(sql.getSqlPattern(), sql.getArguments());
        return configList.size();
    }

    @Override
    public Integer batchUpdate(List<PageColumnConfigDO> configList) {
        AsserUtils.notEmpty(configList, "配置列表不能为空");

        int updateCount = 0;
        for (PageColumnConfigDO config : configList) {
            AsserUtils.notNull(config.getId(), "配置ID不能为空");
            AsserUtils.notNull(config.getCompanyId(), "公司ID不能为空");

            SQL sql = Updates.create()
                    .table(TABLE_NAME)
                    .where(
                            Conditions.and(PageColumnConfigFieldEnum.ID.getFieldCode(), LinkMode.EQUAL, config.getId()),
                            Conditions.and(PageColumnConfigFieldEnum.COMPANY_ID.getFieldCode(), LinkMode.EQUAL, config.getCompanyId())
                    ).update(
                            ColumnValues.create(PageColumnConfigFieldEnum.COL_CODE.getFieldCode(), config.getColCode()),
                            ColumnValues.create(PageColumnConfigFieldEnum.COL_TITLE.getFieldCode(), config.getColTitle()),
                            ColumnValues.create(PageColumnConfigFieldEnum.WIDTH.getFieldCode(), config.getWidth()),
                            ColumnValues.create(PageColumnConfigFieldEnum.SORT.getFieldCode(), config.getSort()),
                            ColumnValues.create(PageColumnConfigFieldEnum.VISIBLE.getFieldCode(), config.getVisible()),
                            ColumnValues.create(PageColumnConfigFieldEnum.MODIFIED.getFieldCode(), config.getModified())
                    ).toSql();

            updateCount += jdbcTemplate.update(sql.getSqlCode(), sql.getArgs().toArray());
        }
        return updateCount;
    }

    @Override
    public Integer batchDelete(List<Long> ids, Long companyId) {
        AsserUtils.notEmpty(ids, "ID列表不能为空");
        AsserUtils.notNull(companyId, "公司ID不能为空");

        SQL sql = Deletes.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(PageColumnConfigFieldEnum.ID.getFieldCode(), LinkMode.IN, ids),
                        Conditions.and(PageColumnConfigFieldEnum.COMPANY_ID.getFieldCode(), LinkMode.EQUAL, companyId)
                )
                .toSql();

        return jdbcTemplate.update(sql.getSqlCode(), sql.getArgs().toArray());
    }

    @Override
    public Integer deleteByPageId(Long pageId, Long companyId) {
        AsserUtils.notNull(pageId, "页面ID不能为空");
        AsserUtils.notNull(companyId, "公司ID不能为空");

        SQL sql = Deletes.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(PageColumnConfigFieldEnum.PAGE_ID.getFieldCode(), LinkMode.EQUAL, pageId),
                        Conditions.and(PageColumnConfigFieldEnum.COMPANY_ID.getFieldCode(), LinkMode.EQUAL, companyId)
                )
                .toSql();

        return jdbcTemplate.update(sql.getSqlCode(), sql.getArgs().toArray());
    }

    @Override
    public List<PageColumnConfigDO> queryByPageId(Long pageId, Long companyId) {
        AsserUtils.notNull(pageId, "页面ID不能为空");
        AsserUtils.notNull(companyId, "公司ID不能为空");

        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(PageColumnConfigFieldEnum.PAGE_ID.getFieldCode(), LinkMode.EQUAL, pageId),
                        Conditions.and(PageColumnConfigFieldEnum.COMPANY_ID.getFieldCode(), LinkMode.EQUAL, companyId)
                )
                .orderBy(Orders.asc(PageColumnConfigFieldEnum.SORT.getFieldCode()))
                .select("*")
                .toSql();

        return jdbcTemplate.query(sql.getSqlCode(),new BeanPropertyRowMapper<>(PageColumnConfigDO.class), sql.getArgs().toArray());
    }

    @Override
    public PageColumnConfigDO queryById(Long id, Long companyId) {
        AsserUtils.notNull(id, "配置ID不能为空");
        AsserUtils.notNull(companyId, "公司ID不能为空");

        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(PageColumnConfigFieldEnum.ID.getFieldCode(), LinkMode.EQUAL, id),
                        Conditions.and(PageColumnConfigFieldEnum.COMPANY_ID.getFieldCode(), LinkMode.EQUAL, companyId)
                ).select()
                .toSql();

        List<PageColumnConfigDO> results = jdbcTemplate.query(
                sql.getSqlCode(),
                new BeanPropertyRowMapper<>(PageColumnConfigDO.class),
                sql.getArgs().toArray()
        );
        return CollectionUtils.isEmpty(results) ? null : results.get(0);
    }

    @Override
    public boolean existsByColCode(Long pageId, String colCode, Long companyId, Long excludeId) {
        AsserUtils.notNull(pageId, "页面ID不能为空");
        AsserUtils.notEmpty(colCode, "列编码不能为空");
        AsserUtils.notNull(companyId, "公司ID不能为空");

        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(PageColumnConfigFieldEnum.PAGE_ID.getFieldCode(), LinkMode.EQUAL, pageId),
                        Conditions.and(PageColumnConfigFieldEnum.COL_CODE.getFieldCode(), LinkMode.EQUAL, colCode),
                        Conditions.and(PageColumnConfigFieldEnum.COMPANY_ID.getFieldCode(), LinkMode.EQUAL, companyId),
                        excludeId != null ?
                                Conditions.and(PageColumnConfigFieldEnum.ID.getFieldCode(), LinkMode.NOT_EQUAL, excludeId) :
                                null
                )
                .select("count(*)")
                .toSql();

        Long count = jdbcTemplate.queryForObject(sql.getSqlCode(), Long.class, sql.getArgs().toArray());
        return count > 0;
    }
}
