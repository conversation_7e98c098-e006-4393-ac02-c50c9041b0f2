package com.raycloud.dmj.account.core.rawdata.utils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

public class UnzipProcessor {

    /**
     * 解压ZIP文件到指定目录并返回解压后的文件列表
     * 
     * @param zipFilePath ZIP文件的本地路径
     * @param destDirectory 解压目标目录
     * @return 解压后的文件名称列表
     * @throws IOException 如果解压过程中发生I/O错误
     */
    public static List<String> unzip(String zipFilePath, String destDirectory,String encoding){
        List<String> fileNames = new ArrayList<>();
        File destDir = new File(destDirectory);
        
        // 如果目标目录不存在，则创建
        if (!destDir.exists()) {
            destDir.mkdirs();
        }
        
        try (ZipFile zipFile = new ZipFile(zipFilePath, Charset.forName(encoding))) {
            Enumeration<? extends ZipEntry> entries = zipFile.entries();
            
            while (entries.hasMoreElements()) {
                ZipEntry entry = entries.nextElement();
                String entryName = entry.getName();


                File entryFile = new File(destDir, entryName);
                // 跳过Mac系统生成的文件和目录
                if (isMacSystemFile(entry.getName())) {
                    continue;
                }
                // 如果是目录，则创建目录
                if (entry.isDirectory()) {
                    entryFile.mkdirs();
                    continue;
                }
                
                // 如果是文件，则创建父目录并写入文件内容
                File parent = entryFile.getParentFile();
                if (parent != null && !parent.exists()) {
                    parent.mkdirs();
                }
                fileNames.add(destDirectory + File.separator + entryName);
                
                try (InputStream inputStream = zipFile.getInputStream(entry);
                     FileOutputStream outputStream = new FileOutputStream(entryFile)) {
                    
                    byte[] buffer = new byte[4096];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead);
                    }
                }catch (IOException e){
                    throw  new IOException("文件解析失败:" + e.getMessage(), e);
                }
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return fileNames;
    }
    private static boolean isMacSystemFile(String entryName) {
        // 检查是否是__MACOSX目录或者.DS_Store文件
        return entryName.startsWith("__MACOSX/") || entryName.endsWith("/.DS_Store") || entryName.equals(".DS_Store");
    }

    public static void deleteDirectory(File directory) {
        // 检查目录是否存在
        if (directory.exists()) {
            // 获取目录下的所有文件和子目录
            File[] contents = directory.listFiles();
            if (contents != null) { // 处理目录为空的情况
                for (File item : contents) {
                    if (item.isDirectory()) {
                        // 递归删除子目录
                        deleteDirectory(item);
                    } else {
                        // 删除文件
                        if (!item.delete()) {
                            System.err.println("无法删除文件: " + item.getAbsolutePath());
                        }
                    }
                }
            }
            // 删除空目录
            if (!directory.delete()) {
                System.err.println("无法删除目录: " + directory.getAbsolutePath());
            }
        }
    }
}