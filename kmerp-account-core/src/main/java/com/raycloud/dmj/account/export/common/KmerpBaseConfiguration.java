package com.raycloud.dmj.account.export.common;

import com.raycloud.dmj.account.infra.common.KmerpProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
@EnableConfigurationProperties(
        value = {
                KmerpProperties.class,
        }
)
public class KmerpBaseConfiguration {

}
