package com.raycloud.dmj.account.core.base.dao;

import com.raycloud.dmj.account.core.rawdata.domains.FileOriginalDataMonitorDO;

import java.util.List;
import java.util.Set;


/**
 * 文件原始数据监控表
 * <AUTHOR>
 */
public interface FileOriginalDataMonitorDao {


    /**
     * 根据ID查询批次监控表
     *
     * @param id ID
     * @return 批次监控表
     */
    FileOriginalDataMonitorDO getById(Long id);
    /**
     * 根据批次号查询批次监控表
     *
     * @param batchCode 批次号
     * @return 批次监控表
     */
    FileOriginalDataMonitorDO getByShopAndSourceAndBatch(Long companyId, Long shopId, Integer dataSource,String tableName, String batchCode);

    /**
     * 根据批次号查询批次监控表
     * @param companyId 公司ID
     * @param source 数据来源
     * @param shopId 店铺ID
     * @param dataRange 数据范围
     * @return 批次监控表
     */
    List<FileOriginalDataMonitorDO> getBySourceAndDataRange(Long companyId,Long shopId,Integer source, Integer dataRange);

    /**
     * 根据ID安全更新
     * @param fileOriginalDataMonitorDO 文件原始数据监控表
     */
    void updateById(FileOriginalDataMonitorDO fileOriginalDataMonitorDO);

    /**
     * 新增监控数据
     * @param fileOriginalDataMonitorDO 文件原始数据监控表
     */
    void insert(FileOriginalDataMonitorDO fileOriginalDataMonitorDO);

    /**
     * 根据批次号批量修改状态[只修改状态为导入中的]
     * @param companyId 公司ID
     * @param shopId 店铺ID
     * @param batchCodes 批次号
     * @param monitorStatus 监控状态
     */
    void batchUpdateStatusByBatchCode(Long companyId,Long shopId,Set<String> batchCodes, Integer monitorStatus);


    /**
     * 根据文件记录ID批量修改状态[只修改状态为导入中的]
     * @param lastRecordIds 文件记录ID
     * @param monitorStatus 监控状态
     */
    void batchUpdateStatusByLastRecordIds(Set<Long> lastRecordIds, Integer monitorStatus);

    /**
     * 根据文件记录ID查询数据
     * @param lastRecordIds 文件记录ID
     * @param companyId 监控状态
     */
    List<FileOriginalDataMonitorDO> listByLastRecordIds(Long companyId, Set<Long> lastRecordIds);


    /**
     * 根据批次号和店铺ID查询数据
     * @param companyId 公司 ID
     * @param shopId 店铺 ID
     * @param dataSource 数据源
     * @param batchCode 批次编号
     * @return FileOriginalDataMonitorDO
     */
    List<FileOriginalDataMonitorDO> listByShopAndSourceAndBatch(Long companyId,Long shopId,Integer dataSource, Set<String> batchCode);


}
