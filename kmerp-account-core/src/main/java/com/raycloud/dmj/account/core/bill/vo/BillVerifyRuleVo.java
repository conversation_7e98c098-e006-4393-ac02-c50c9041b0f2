package com.raycloud.dmj.account.core.bill.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class BillVerifyRuleVo {

    /**
     * 主键ID，自增
     */
    private Long id;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 核验规则名称
     */
    private String ruleName;

    /**
     * 生效时间
     */
    private String effectiveTime;

    /**
     * 失效时间
     */
    private String invalidTime;

    /**
     * 时间类型
     */
    private Integer dateType;

    /**
     * 核验规则内容
     */
    private List<VerifyRuleVo> ruleContent;

    /**
     * 异常是否提示(1是,0否)
     */
    private Integer isExceptionTip;

    /**
     * 是否启用(1开启,0关闭)
     */
    private Integer status;

    /**
     * 判断方式(1大于,2等于,3小于)
     */
    private Integer judgmentType;

    /**
     * 核验金额(元)
     */
    private BigDecimal amount;
}
