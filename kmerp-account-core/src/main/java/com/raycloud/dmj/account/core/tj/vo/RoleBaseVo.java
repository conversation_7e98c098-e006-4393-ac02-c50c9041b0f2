package com.raycloud.dmj.account.core.tj.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class RoleBaseVo implements Serializable {

    /**
     * 系统唯一编码（每个系统都会分配一个唯一不变的编码）
     */
    private String sysCode;

    /**
     * 外部租户编码
     */
    private Long companyId;

    /**
     * 角色ID
     */
    private Long roleId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 外部租户的角色编码
     */
    private String roleOuterCode;

}
