package com.raycloud.dmj.account.core.tj.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class EmployeePermissionVo implements Serializable {

    /**
     * 系统唯一编码
     */
    private String sysCode;

    /**
     * 外部租户编码
     */
    private Long tenantId;

    /**
     * 员工ID
     */
    private Long employeeId;

    /**
     * 报表的唯一编码
     */
    private String reportCode;

    /**
     * 报表的名称
     */
    private String reportName;

}
