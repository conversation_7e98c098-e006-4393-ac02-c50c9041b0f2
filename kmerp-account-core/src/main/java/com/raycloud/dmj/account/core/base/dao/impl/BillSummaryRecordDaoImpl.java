package com.raycloud.dmj.account.core.base.dao.impl;

import com.raycloud.dmj.account.core.base.dao.BillSummaryRecordDao;
import com.raycloud.dmj.account.core.base.domain.BillSummaryRecordDO;
import com.raycloud.dmj.account.core.bill.params.BillSummaryRecordParam;
import com.raycloud.dmj.account.core.bill.request.BillSummaryRecordRequest;
import com.raycloud.dmj.account.core.enums.field.BillSummaryRecordFieldEnum;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import com.raycloud.dmj.table.api.plus.common.base.SQL;
import com.raycloud.dmj.table.api.plus.common.enums.LinkMode;
import com.raycloud.dmj.table.api.plus.component.column.utils.Columns;
import com.raycloud.dmj.table.api.plus.component.condition.ConditionComponent;
import com.raycloud.dmj.table.api.plus.component.condition.utils.Conditions;
import com.raycloud.dmj.table.api.plus.insert.Inserts;
import com.raycloud.dmj.table.api.plus.insert.batch.InsertBatchSQL;
import com.raycloud.dmj.table.api.plus.insert.core.InsertMode;
import com.raycloud.dmj.table.api.plus.query.Queries;
import com.raycloud.dmj.table.api.plus.utils.CollectionUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 资金流水合计记录表DAO实现
 *
 * <AUTHOR>
 */
@Repository
public class BillSummaryRecordDaoImpl extends BaseDao implements BillSummaryRecordDao {

    private static final String TABLE_NAME = "bill_summary_record";

    @Override
    public Long insert(BillSummaryRecordDO billSummaryRecordDO) {
        AsserUtils.notNull(billSummaryRecordDO, "资金流水合计记录不能为空！");
        SQL sql = Inserts.insert()
                .into(TABLE_NAME)
                .columns(BillSummaryRecordFieldEnum.getInsertFields())
                .valueForEntity(billSummaryRecordDO)
                .columnNameCamelToUnderline()
                .toSql();

        // 创建KeyHolder用于获取生成的主键
        KeyHolder keyHolder = new GeneratedKeyHolder();
        // 使用带KeyHolder的update方法
        jdbcTemplate.update(
                connection -> {
                    PreparedStatement ps = connection.prepareStatement(
                            sql.getSqlCode(),
                            Statement.RETURN_GENERATED_KEYS
                    );
                    // 设置参数
                    Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
                    for (int i = 0; i < args.length; i++) {
                        ps.setObject(i + 1, args[i]);
                    }
                    return ps;
                },
                keyHolder
        );
        // 获取生成的主键值
        Number key = keyHolder.getKey();
        return key != null ? key.longValue() : null;
    }

    @Override
    public BillSummaryRecordDO queryById(Long id, Long companyId) {
        AsserUtils.notNull(id, "ID不能为空！");
        AsserUtils.notNull(companyId, "公司ID不能为空！");

        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(BillSummaryRecordFieldEnum.ID.getFieldCode()), LinkMode.EQUAL, id),
                        Conditions.and(Columns.toColumn(BillSummaryRecordFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId),
                        Conditions.and(Columns.toColumn(BillSummaryRecordFieldEnum.ENABLE_STATUS.getFieldCode()), LinkMode.EQUAL, 1)
                )
                .select()
                .limit(1)
                .toSql();

        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<BillSummaryRecordDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(BillSummaryRecordDO.class), args);
        return !query.isEmpty() ? query.get(0) : null;
    }

    @Override
    public List<BillSummaryRecordDO> queryByParam(BillSummaryRecordParam param, Long companyId) {
        AsserUtils.notNull(companyId, "公司ID不能为空！");
        // 组装查询条件
        List<ConditionComponent<?>> conditions = getConditionComponents(param, companyId);
        // 构建查询
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(conditions)
                .select()
                .toSql();

        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<BillSummaryRecordDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(BillSummaryRecordDO.class), args);
        return !query.isEmpty() ? query : null;
    }

    @Override
    public List<BillSummaryRecordDO> queryByShopAndCycle(Long shopId, Date billingCycle, Long companyId) {
        AsserUtils.notNull(shopId, "店铺ID不能为空！");
        AsserUtils.notNull(billingCycle, "帐期不能为空！");
        AsserUtils.notNull(companyId, "公司ID不能为空！");

        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(BillSummaryRecordFieldEnum.SHOP_ID.getFieldCode()), LinkMode.EQUAL, shopId),
                        Conditions.and(Columns.toColumn(BillSummaryRecordFieldEnum.BILLING_CYCLE.getFieldCode()), LinkMode.EQUAL, billingCycle),
                        Conditions.and(Columns.toColumn(BillSummaryRecordFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId),
                        Conditions.and(Columns.toColumn(BillSummaryRecordFieldEnum.ENABLE_STATUS.getFieldCode()), LinkMode.EQUAL, 1)
                )
                .select()
                .toSql();

        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<BillSummaryRecordDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(BillSummaryRecordDO.class), args);
        return !query.isEmpty() ? query : null;
    }

    @Override
    public Integer batchInsert(List<BillSummaryRecordDO> recordDOList) {
        AsserUtils.notEmpty(recordDOList, "流水信息列表不能为空");
        List<Object> objectList = recordDOList.stream()
                .map(item -> (Object) item)
                .collect(Collectors.toList());
        InsertBatchSQL sql = Inserts.create(InsertMode.DEFAULT)
                .into(TABLE_NAME)
                .columns(BillSummaryRecordFieldEnum.getInsertFields())
                .valueForEntities(objectList)
                .batch()
                .toSql();
        jdbcTemplate.batchUpdate(sql.getSqlPattern(), sql.getArguments());
        return recordDOList.size();
    }

    /**
     * 组装查询条件
     */
    private List<ConditionComponent<?>> getConditionComponents(BillSummaryRecordParam param, Long companyId) {
        List<ConditionComponent<?>> conditions = new ArrayList<>();

        // 公司ID条件（必须）
        conditions.add(Conditions.and(Columns.toColumn(BillSummaryRecordFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId));
        conditions.add(Conditions.and(Columns.toColumn(BillSummaryRecordFieldEnum.ENABLE_STATUS.getFieldCode()), LinkMode.EQUAL, 1));


        if (param != null) {
            // 资金账户ID条件
            if (CollectionUtils.isNotEmpty(param.getAccountIdList())) {
                conditions.add(Conditions.and(Columns.toColumn(BillSummaryRecordFieldEnum.ACCOUNT_ID.getFieldCode()), LinkMode.IN, param.getAccountIdList()));
            }

            // 流水类别ID条件
            if (CollectionUtils.isNotEmpty(param.getCategoryIdList())) {
                conditions.add(Conditions.and(Columns.toColumn(BillSummaryRecordFieldEnum.CATEGORY_ID.getFieldCode()), LinkMode.IN, param.getCategoryIdList()));
            }

            // 流水子类别ID条件
            if (CollectionUtils.isNotEmpty(param.getSubCategoryIdList())) {
                conditions.add(Conditions.and(Columns.toColumn(BillSummaryRecordFieldEnum.SUB_CATEGORY_ID.getFieldCode()), LinkMode.IN, param.getSubCategoryIdList()));
            }

            // 帐期条件
            if (param.getStartTime() != null && param.getEndTime() != null) {
                conditions.add(Conditions.and(Columns.toColumn(BillSummaryRecordFieldEnum.BILLING_CYCLE.getFieldCode()), LinkMode.GREATER_THAN_EQUAL, param.getStartTime()));
                conditions.add(Conditions.and(Columns.toColumn(BillSummaryRecordFieldEnum.BILLING_CYCLE.getFieldCode()), LinkMode.LESS_THAN, param.getEndTime()));
            }

            // 店铺ID条件
            if (param.getShopId() != null) {
                conditions.add(Conditions.and(Columns.toColumn(BillSummaryRecordFieldEnum.SHOP_ID.getFieldCode()), LinkMode.EQUAL, param.getShopId()));
            }
        }

        return conditions;
    }
}
