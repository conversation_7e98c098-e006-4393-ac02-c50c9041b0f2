package com.raycloud.dmj.account.core.enums.field;

import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.Set;
import java.util.stream.Collectors;

@Getter
public enum TmallHuiYingBaoRawBillFieldEnum {
    ID("id", "自增主键"),
    SORT_TITLE("sort_title", "店铺简称"),
    MONTH("month", "月份（格式如：202503 ）"),
    ALIPAY_TRANSACTION_NO("alipay_transaction_no", "支付宝交易流水号"),
    MERCHANT_ORDER_NO("merchant_order_no", "商家订单号"),
    PRODUCT_NAME("product_name", "商品名称"),
    ORDER_AMOUNT("order_amount", "订单金额（元）"),
    DEDUCTION_AMOUNT("deduction_amount", "抵扣金额（元）"),
    BUSINESS_OCCURRENCE_TIME("business_occurrence_time", "业务发生时间（格式如：******** ）"),
    RECEIVING_ACCOUNT("receiving_account", "收款账户"),
    PAYMENT_ACCOUNT("payment_account", "付款账户"),
    DOWNLOAD_TIME("download_time", "下载时间"),
    DOWNLOAD_ACCOUNT("download_account", "下载账户"),
    BIZ_KEY("biz_key", "唯一键"),
    BATCH_NO("batch_no", "批次号"),
    BATCH_TIME("batch_time", "批次时间 格式如********"),
    COMPANY_ID("company_id", "租户ID"),
    SHOP_ID("shop_id", "店铺ID"),
    CREATED("created", "创建时间");

    /**
     * 字段编码
     */
    private final String fieldCode;

    /**
     * 字段描述
     */
    private final String fieldDesc;

    TmallHuiYingBaoRawBillFieldEnum(String fieldCode, String fieldDesc) {
        this.fieldCode = fieldCode;
        this.fieldDesc = fieldDesc;
    }

    /**
     * 获取插入字段
     * @return 插入字段
     */
    public static Set<String> getInsertFields() {
        Set<TmallHuiYingBaoRawBillFieldEnum> filterSet = Collections.singleton(TmallHuiYingBaoRawBillFieldEnum.ID);
        return Arrays.stream(TmallHuiYingBaoRawBillFieldEnum.values())
               .filter(x ->!filterSet.contains(x))
               .map(x -> x.fieldCode)
               .collect(Collectors.toSet());
    }
}