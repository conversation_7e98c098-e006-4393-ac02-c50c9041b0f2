package com.raycloud.dmj.account.core.base.dao.impl;

import com.alibaba.fastjson2.JSON;
import com.mysql.jdbc.Statement;
import com.raycloud.dmj.account.core.base.dao.SubCategoryDao;
import com.raycloud.dmj.account.core.cleancategory.domain.QuerySubCategoryParam;
import com.raycloud.dmj.account.core.cleancategory.domain.SubCategoryDTO;
import com.raycloud.dmj.account.core.cleancategory.domain.object.SubCategoryDO;
import com.raycloud.dmj.account.core.common.Page;
import com.raycloud.dmj.account.core.enums.field.CategoryFieldEnum;
import com.raycloud.dmj.account.core.enums.field.SubCategoryFieldEnum;
import com.raycloud.dmj.account.infra.common.BusinessException;
import com.raycloud.dmj.account.infra.common.ErrorCodeEnum;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import com.raycloud.dmj.table.api.plus.common.ColumnValues;
import com.raycloud.dmj.table.api.plus.common.base.SQL;
import com.raycloud.dmj.table.api.plus.common.enums.LinkMode;
import com.raycloud.dmj.table.api.plus.component.column.utils.Columns;
import com.raycloud.dmj.table.api.plus.component.condition.ConditionComponent;
import com.raycloud.dmj.table.api.plus.component.condition.utils.Conditions;
import com.raycloud.dmj.table.api.plus.delete.Deletes;
import com.raycloud.dmj.table.api.plus.insert.Inserts;
import com.raycloud.dmj.table.api.plus.query.Queries;
import com.raycloud.dmj.table.api.plus.update.Updates;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.SingleColumnRowMapper;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.util.*;

/**
 * 子类别
 *
 * <AUTHOR>
 */
@Repository
@Slf4j
public class SubCategoryDaoImpl extends BaseDao implements SubCategoryDao {


    private final String TABLE_NAME = "sub_category";


    private final String CATEGORY_TABLE_NAME = "category";


    @Override
    public Long insert(SubCategoryDO subCategoryDO) {
        SQL sql = Inserts.insert()
                .into(TABLE_NAME)
                .columns(SubCategoryFieldEnum.getInsertFields())
                .valueForEntity(subCategoryDO)
                .columnNameCamelToUnderline()
                .toSql();

        // 创建KeyHolder用于获取生成的主键
        KeyHolder keyHolder = new GeneratedKeyHolder();
        // 使用带KeyHolder的update方法
        jdbcTemplate.update(
                connection -> {
                    PreparedStatement ps = connection.prepareStatement(
                            sql.getSqlCode(),
                            Statement.RETURN_GENERATED_KEYS
                    );
                    // 设置参数
                    Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
                    for (int i = 0; i < args.length; i++) {
                        ps.setObject(i + 1, args[i]);
                    }
                    return ps;
                },
                keyHolder
        );
        // 获取生成的主键值
        return keyHolder.getKey().longValue();
    }

    @Override
    public void updateById(SubCategoryDO subCategoryDO) {
        AsserUtils.notNull(subCategoryDO, "参数不能为空！");
        AsserUtils.notNull(subCategoryDO.getId(), "ID不能为空！");
        AsserUtils.notNull(subCategoryDO.getCompanyId(), "公司ID不能为空！");

        // 2. 生成SQL
        SQL sql = Updates.create()
                .table(TABLE_NAME)
                .where(
                        Conditions.and(SubCategoryFieldEnum.ID.getFieldCode(), LinkMode.EQUAL, subCategoryDO.getId()),
                        Conditions.and(SubCategoryFieldEnum.COMPANY_ID.getFieldCode(), LinkMode.EQUAL, subCategoryDO.getCompanyId())
                )
                .update(
                        ColumnValues.create(SubCategoryFieldEnum.MODIFIED.getFieldCode(), new Date()),
                        ColumnValues.create(SubCategoryFieldEnum.CATEGORY_GROUP_CODE.getFieldCode(), subCategoryDO.getCategoryGroupCode()),
                        ColumnValues.create(SubCategoryFieldEnum.CATEGORY_ID.getFieldCode(), subCategoryDO.getCategoryId()),
                        ColumnValues.create(SubCategoryFieldEnum.NAME.getFieldCode(), subCategoryDO.getName()),
                        ColumnValues.create(SubCategoryFieldEnum.OFFSET.getFieldCode(), subCategoryDO.getOffset()),
                        ColumnValues.create(SubCategoryFieldEnum.INCOME_EXPENSE_OBJECT_ID.getFieldCode(), subCategoryDO.getCategoryId())
                ).toSql();
        Object[] updateArgs = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        // 3. 执行更新操作
        int row = jdbcTemplate.update(sql.getSqlCode(), updateArgs);
        if (row <= 0) {
            log.error("|SubCategoryDaoImpl.updateById error|更新数据库失败！sql={},updateArgs={}", sql.getSqlCode(), JSON.toJSONString(sql.getArgs()));
            throw new BusinessException(ErrorCodeEnum.DB_ERROR);
        }
    }

    @Override
    public void deleteByIds(Long companyId, List<Long> ids) {
        SQL sql = Deletes.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(SubCategoryFieldEnum.COMPANY_ID.getFieldCode(), LinkMode.EQUAL, companyId),
                        Conditions.and(SubCategoryFieldEnum.ID.getFieldCode(), LinkMode.IN, ids)
                ).toSql();
        Object[] updateArgs = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        jdbcTemplate.update(sql.getSqlCode(), updateArgs);

    }



    private final String SUB_CATEGORY_ALIAS="a";
    private  final String CATEGORY_ALIAS="b";

    @Override
    public List<SubCategoryDTO> pageQueryByParam(Long companyId, QuerySubCategoryParam param, Page page) {

        // 组装查询条件
        List<ConditionComponent<?>> conditions = getConditionComponents(companyId, param);
        // 构建关联查询
        SQL sql = Queries.create()
                .from(TABLE_NAME, SUB_CATEGORY_ALIAS)
                .join(CATEGORY_TABLE_NAME, CATEGORY_ALIAS)
                .on(
                        Conditions.and(
                                Columns.create(SubCategoryFieldEnum.CATEGORY_ID.getFieldCode()).referenceTableAlias(SUB_CATEGORY_ALIAS),
                                LinkMode.EQUAL,
                                Columns.create(CategoryFieldEnum.ID.getFieldCode()).referenceTableAlias(CATEGORY_ALIAS)
                        )
                )
                .where(
                        conditions
                )
                .select(
                        Columns.toColumn(SubCategoryFieldEnum.ID.getFieldCode()).referenceTableAlias(SUB_CATEGORY_ALIAS).alias(SubCategoryFieldEnum.ID.getFieldCode()),
                        Columns.toColumn(SubCategoryFieldEnum.CREATED.getFieldCode()).referenceTableAlias(SUB_CATEGORY_ALIAS).alias(SubCategoryFieldEnum.CREATED.getFieldCode()),
                        Columns.toColumn(SubCategoryFieldEnum.MODIFIED.getFieldCode()).referenceTableAlias(SUB_CATEGORY_ALIAS).alias(SubCategoryFieldEnum.MODIFIED.getFieldCode()),
                        Columns.toColumn(SubCategoryFieldEnum.PLATFORM_CODE.getFieldCode()).referenceTableAlias(SUB_CATEGORY_ALIAS).alias(SubCategoryFieldEnum.PLATFORM_CODE.getFieldCode()),
                        Columns.toColumn(SubCategoryFieldEnum.CATEGORY_GROUP_CODE.getFieldCode()).referenceTableAlias(SUB_CATEGORY_ALIAS).alias(SubCategoryFieldEnum.CATEGORY_GROUP_CODE.getFieldCode()),
                        Columns.toColumn(SubCategoryFieldEnum.CATEGORY_ID.getFieldCode()).referenceTableAlias(SUB_CATEGORY_ALIAS).alias(SubCategoryFieldEnum.CATEGORY_ID.getFieldCode()),
                        Columns.toColumn(SubCategoryFieldEnum.SOURCE.getFieldCode()).referenceTableAlias(SUB_CATEGORY_ALIAS).alias(SubCategoryFieldEnum.SOURCE.getFieldCode()),
                        Columns.toColumn(SubCategoryFieldEnum.NAME.getFieldCode()).referenceTableAlias(SUB_CATEGORY_ALIAS).alias("sub_category_name"),
                        Columns.toColumn(SubCategoryFieldEnum.OFFSET.getFieldCode()).referenceTableAlias(SUB_CATEGORY_ALIAS).alias(SubCategoryFieldEnum.OFFSET.getFieldCode()),
                        Columns.toColumn(SubCategoryFieldEnum.INCOME_EXPENSE_OBJECT_ID.getFieldCode()).referenceTableAlias(SUB_CATEGORY_ALIAS).alias(SubCategoryFieldEnum.INCOME_EXPENSE_OBJECT_ID.getFieldCode()),
                        Columns.toColumn(SubCategoryFieldEnum.COMPANY_ID.getFieldCode()).referenceTableAlias(SUB_CATEGORY_ALIAS).alias(SubCategoryFieldEnum.COMPANY_ID.getFieldCode()),
                        Columns.toColumn(CategoryFieldEnum.NAME.getFieldCode()).referenceTableAlias(CATEGORY_ALIAS).alias("category_name"),
                        Columns.toColumn(CategoryFieldEnum.FUND_ACCOUNT_ID.getFieldCode()).referenceTableAlias(CATEGORY_ALIAS).alias(CategoryFieldEnum.FUND_ACCOUNT_ID.getFieldCode())

                )
                .page(page.getPageNo(), page.getPageSize())
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<SubCategoryDTO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(SubCategoryDTO.class), args);
        return !query.isEmpty() ? query : null;

    }

    @Override
    public List<Long> queryIdsByParam(Long companyId, QuerySubCategoryParam param) {
        // 组装查询条件
        List<ConditionComponent<?>> conditions = getConditionComponents(companyId, param);
        // 构建关联查询
        SQL sql = Queries.create()
                .from(TABLE_NAME, SUB_CATEGORY_ALIAS)
                .join(CATEGORY_TABLE_NAME, CATEGORY_ALIAS)
                .on(
                        Conditions.and(
                                Columns.create(SubCategoryFieldEnum.CATEGORY_ID.getFieldCode()).referenceTableAlias(SUB_CATEGORY_ALIAS),
                                LinkMode.EQUAL,
                                Columns.create(CategoryFieldEnum.ID.getFieldCode()).referenceTableAlias(CATEGORY_ALIAS)
                        )
                )
                .where(
                        conditions
                )
                .select(
                        Columns.toColumn(SubCategoryFieldEnum.ID.getFieldCode()).referenceTableAlias(SUB_CATEGORY_ALIAS).alias(SubCategoryFieldEnum.ID.getFieldCode())
                )
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<Long> query = jdbcTemplate.query(sql.getSqlCode(), new SingleColumnRowMapper<>(Long.class), args);
        return !query.isEmpty() ? query : null;
    }

    @Override
    public Long countByParam(Long companyId, QuerySubCategoryParam param) {
        // 组装查询条件
        List<ConditionComponent<?>> conditions = getConditionComponents(companyId, param);
        SQL sql = Queries.create()
                .from(TABLE_NAME, SUB_CATEGORY_ALIAS)
                .join(CATEGORY_TABLE_NAME, CATEGORY_ALIAS)
                .on(
                        Conditions.and(
                                Columns.create(SubCategoryFieldEnum.CATEGORY_ID.getFieldCode()).referenceTableAlias(SUB_CATEGORY_ALIAS),
                                LinkMode.EQUAL,
                                Columns.create(CategoryFieldEnum.ID.getFieldCode()).referenceTableAlias(CATEGORY_ALIAS)
                        )
                )
                .where(
                        conditions
                )
                .select(
                        Columns.create("count(*)")
                )
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        return jdbcTemplate.queryForObject(sql.getSqlCode(), Long.class, args);

    }

    @Override
    public SubCategoryDO queryByCategoryIdAndName(Long companyId, Long categoryId, String subCategoryName) {
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(SubCategoryFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId),
                        Conditions.and(Columns.toColumn(SubCategoryFieldEnum.CATEGORY_ID.getFieldCode()), LinkMode.EQUAL, categoryId),
                        Conditions.and(Columns.toColumn(SubCategoryFieldEnum.NAME.getFieldCode()), LinkMode.EQUAL, subCategoryName)
                )
                .select(
                )
                .limit(1)
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<SubCategoryDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(SubCategoryDO.class), args);
        return !query.isEmpty() ? query.get(0) : null;
    }

    @Override
    public List<SubCategoryDO> queryByParam(Long companyId, QuerySubCategoryParam param) {
        if (StringUtils.isEmpty(param.getPlatformCode()) && CollectionUtils.isEmpty(param.getPlatformCodeList())) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR,"平台code不能为空");
        }
        // 组装查询条件
        List<ConditionComponent<?>> conditions = getConditionComponents(companyId, param);
        // 构建关联查询
        SQL sql = Queries.create()
                .from(TABLE_NAME, SUB_CATEGORY_ALIAS)  // 添加表别名
                .join(CATEGORY_TABLE_NAME, CATEGORY_ALIAS)  // 添加关联表
                .on(
                        Conditions.and(
                                Columns.toColumn(SubCategoryFieldEnum.CATEGORY_ID.getFieldCode()).referenceTableAlias(SUB_CATEGORY_ALIAS),
                                LinkMode.EQUAL,
                                Columns.toColumn(CategoryFieldEnum.ID.getFieldCode()).referenceTableAlias(CATEGORY_ALIAS)
                        )
                )
                .where(
                        conditions
                )
                .select(
                        Columns.toColumn(SubCategoryFieldEnum.ID.getFieldCode()).referenceTableAlias(SUB_CATEGORY_ALIAS),
                        Columns.toColumn(SubCategoryFieldEnum.NAME.getFieldCode()).referenceTableAlias(SUB_CATEGORY_ALIAS),
                        Columns.toColumn(SubCategoryFieldEnum.COMPANY_ID.getFieldCode()).referenceTableAlias(SUB_CATEGORY_ALIAS),
                        Columns.toColumn(SubCategoryFieldEnum.PLATFORM_CODE.getFieldCode()).referenceTableAlias(SUB_CATEGORY_ALIAS),
                        Columns.toColumn(SubCategoryFieldEnum.CATEGORY_GROUP_CODE.getFieldCode()).referenceTableAlias(SUB_CATEGORY_ALIAS),
                        Columns.toColumn(SubCategoryFieldEnum.CATEGORY_ID.getFieldCode()).referenceTableAlias(SUB_CATEGORY_ALIAS),
                        Columns.toColumn(SubCategoryFieldEnum.OFFSET.getFieldCode()).referenceTableAlias(SUB_CATEGORY_ALIAS)
                )
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<SubCategoryDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(SubCategoryDO.class), args);
        return !query.isEmpty() ? query : null;

    }

    @Override
    public List<SubCategoryDO> queryByIds(Long companyId, List<Long> ids) {
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(SubCategoryFieldEnum.ID.getFieldCode()), LinkMode.IN, ids)
                )
                .select(
                ).toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<SubCategoryDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(SubCategoryDO.class), args);
        return !query.isEmpty() ? query : null;
    }

    @Override
    public List<SubCategoryDO> queryByCompanyId(Long companyId) {
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(SubCategoryFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId)
                )
                .select(
                ).toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<SubCategoryDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(SubCategoryDO.class), args);
        return !query.isEmpty() ? query : null;
    }

    @Override
    public SubCategoryDO getByCategoryIdAndName(Long companyId, Long categoryId, String subCategoryName) {
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(SubCategoryFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId),
                        Conditions.and(Columns.toColumn(SubCategoryFieldEnum.CATEGORY_ID.getFieldCode()), LinkMode.EQUAL, categoryId),
                        Conditions.and(Columns.toColumn(SubCategoryFieldEnum.NAME.getFieldCode()), LinkMode.EQUAL, subCategoryName)

                )
                .select(
                ).toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<SubCategoryDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(SubCategoryDO.class), args);
        return !query.isEmpty() ? query.get(0) : null;
    }

    @Override
    public List<SubCategoryDO> listByCategoryIds(Long companyId, Set<Long> categoryIds) {
        AsserUtils.notNull(companyId, "公司ID不能为空！");
        AsserUtils.notEmpty(categoryIds, "类别ID不能为空！");

        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(SubCategoryFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId),
                        Conditions.and(Columns.toColumn(SubCategoryFieldEnum.CATEGORY_ID.getFieldCode()), LinkMode.IN, categoryIds)

                )
                .select(
                ).toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<SubCategoryDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(SubCategoryDO.class), args);
        return !query.isEmpty() ? query : null;
    }

    /**
     * 组装动态查询条件
     *
     * @param companyId 公司ID
     * @param param     查询参数
     * @return 查询条件
     */
    private List<ConditionComponent<?>> getConditionComponents(Long companyId, QuerySubCategoryParam param) {
        List<ConditionComponent<?>> conditions = new ArrayList<>();
        //  组装查询条件
        conditions.add(Conditions.and(Columns.toColumn(SubCategoryFieldEnum.COMPANY_ID.getFieldCode()).referenceTableAlias(SUB_CATEGORY_ALIAS), LinkMode.EQUAL, companyId));
        if (CollectionUtils.isNotEmpty(param.getPlatformCodeList())) {
            conditions.add(Conditions.and(Columns.toColumn(SubCategoryFieldEnum.PLATFORM_CODE.getFieldCode()).referenceTableAlias(SUB_CATEGORY_ALIAS), LinkMode.IN, param.getPlatformCodeList()));
        }
        if (StringUtils.isNotEmpty(param.getPlatformCode())) {
            conditions.add(Conditions.and(Columns.toColumn(SubCategoryFieldEnum.PLATFORM_CODE.getFieldCode()).referenceTableAlias(SUB_CATEGORY_ALIAS), LinkMode.EQUAL, param.getPlatformCode()));
        }
        //分类code
        if (CollectionUtils.isNotEmpty(param.getCategoryGroupCodes())) {
            conditions.add(Conditions.and(Columns.toColumn(SubCategoryFieldEnum.CATEGORY_GROUP_CODE.getFieldCode()).referenceTableAlias(SUB_CATEGORY_ALIAS), LinkMode.IN, param.getCategoryGroupCodes()));
        }
        if (CollectionUtils.isNotEmpty(param.getCategoryIds())) {
            conditions.add(Conditions.and(Columns.toColumn(SubCategoryFieldEnum.CATEGORY_ID.getFieldCode()).referenceTableAlias(SUB_CATEGORY_ALIAS), LinkMode.IN, param.getCategoryIds()));
        }
        //子类别ID
        if (CollectionUtils.isNotEmpty(param.getSubCategoryIds())) {
            conditions.add(Conditions.and(Columns.toColumn(SubCategoryFieldEnum.ID.getFieldCode()).referenceTableAlias(SUB_CATEGORY_ALIAS), LinkMode.IN, param.getSubCategoryIds()));
        }
        //资金账户ID
        if (CollectionUtils.isNotEmpty(param.getFundAccountIds())) {
            conditions.add(Conditions.and(Columns.toColumn(CategoryFieldEnum.FUND_ACCOUNT_ID.getFieldCode()).referenceTableAlias(CATEGORY_ALIAS), LinkMode.IN, param.getFundAccountIds()));
        }
        //是否抵消
        if (Objects.nonNull(param.getOffset())) {
            conditions.add(Conditions.and(Columns.toColumn(SubCategoryFieldEnum.OFFSET.getFieldCode()).referenceTableAlias(SUB_CATEGORY_ALIAS), LinkMode.EQUAL, param.getOffset()));
        }
        return conditions;
    }


}
