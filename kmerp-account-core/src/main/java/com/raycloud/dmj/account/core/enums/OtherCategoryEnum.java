package com.raycloud.dmj.account.core.enums;

import lombok.Getter;

/**
 * 其他类目组枚举
 * <AUTHOR>
 */
@Getter
public enum OtherCategoryEnum {

    //集分宝
    JFB("JFB", "集分宝"),
    //淘宝联盟
    TBLM("TBLM", "淘宝联盟"),
    //淘金币
    TJJ("TJJ", "淘金币"),
    //消费积分
    XFJF("XFJF", "消费积分"),
    //惠营宝
    HYB("HYB", "惠营宝"),
    //直播红包
    ZBHB("ZBHB", "直播红包"),
    ;

    private final String code;

    private final String desc;

    OtherCategoryEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(String code) {
        for (OtherCategoryEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return "未知类别";
    }
}
