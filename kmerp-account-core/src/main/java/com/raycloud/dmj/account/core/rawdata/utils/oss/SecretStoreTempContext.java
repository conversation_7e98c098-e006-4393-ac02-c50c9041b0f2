package com.raycloud.dmj.account.core.rawdata.utils.oss;


import com.raycloud.secret.converter.SecretConverterFactory;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class SecretStoreTempContext {
    public final static String accessKeyIdAndKeySecret = "secretStore";
    public final static String accessKeyId = "accessKeyId";
    public final static String accessKeySecret = "accessKeySecret";
    static Map<String,String> secretStoreMap = new ConcurrentHashMap<>();
    static {
        //所有的坐标都往这里写即可
        Map<String,String> subscribeKeyMap = new ConcurrentHashMap<String,String>();
        subscribeKeyMap.put(accessKeyIdAndKeySecret,"oss.wxerpcrm");
        secretStoreMap.putAll(SecretConverterFactory.normalConverter(subscribeKeyMap));
        String accessKeyIdAndKeySecretStr = subscribeKeyMap.get(accessKeyIdAndKeySecret);
        String[] split = accessKeyIdAndKeySecretStr.split(",");
        secretStoreMap.put(accessKeyId,split[0]);
        secretStoreMap.put(accessKeySecret,split[1]);

    }

    public static String getSecretData(String key) {
        return secretStoreMap.get(key);
    }
}

