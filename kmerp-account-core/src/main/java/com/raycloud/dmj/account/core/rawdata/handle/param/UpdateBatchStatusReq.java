package com.raycloud.dmj.account.core.rawdata.handle.param;

import com.raycloud.dmj.account.core.enums.MonitorStatusEnum;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 *
 */
@Data
public class UpdateBatchStatusReq {

    /**
     * 公司ID
     */
    private Long companyId;


    /**
     * 店铺ID
     */
    private Long shopId;


    /**
     * 批次号集合
     */
    private Set<String> batchCodeSet;

    /**
     * 批次号状态
     */
    private MonitorStatusEnum updateStatus;



}
