package com.raycloud.dmj.account.core.base.dao.impl;

import com.raycloud.dmj.account.core.base.dao.RuleVerifyResultRecordDao;
import com.raycloud.dmj.account.core.base.domain.RuleVerifyResultRecordDO;
import com.raycloud.dmj.account.core.enums.field.RuleVerifyResultRecordFieldEnum;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import com.raycloud.dmj.table.api.plus.common.base.SQL;
import com.raycloud.dmj.table.api.plus.common.enums.LinkMode;
import com.raycloud.dmj.table.api.plus.component.column.utils.Columns;
import com.raycloud.dmj.table.api.plus.component.condition.utils.Conditions;
import com.raycloud.dmj.table.api.plus.insert.Inserts;
import com.raycloud.dmj.table.api.plus.insert.batch.InsertBatchSQL;
import com.raycloud.dmj.table.api.plus.insert.core.InsertMode;
import com.raycloud.dmj.table.api.plus.query.Queries;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 规则校验结果记录表DAO实现类
 * <AUTHOR>
 */
@Slf4j
@Repository
public class RuleVerifyResultRecordDaoImpl extends BaseDao implements RuleVerifyResultRecordDao {

    private static final String TABLE_NAME = "rule_verify_result_record";

    @Override
    public Long insert(RuleVerifyResultRecordDO record) {
        AsserUtils.notNull(record, "校验结果记录不能为空！");
        SQL sql = Inserts.insert()
                .into(TABLE_NAME)
                .columns(RuleVerifyResultRecordFieldEnum.getInsertFields())
                .valueForEntity(record)
                .columnNameCamelToUnderline()
                .toSql();

        KeyHolder keyHolder = new GeneratedKeyHolder();
        jdbcTemplate.update(
                connection -> {
                    PreparedStatement ps = connection.prepareStatement(sql.getSqlCode(), Statement.RETURN_GENERATED_KEYS);
                    Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
                    for (int i = 0; i < args.length; i++) {
                        ps.setObject(i + 1, args[i]);
                    }
                    return ps;
                },
                keyHolder
        );

        // 返回生成的主键ID
        Number key = keyHolder.getKey();
        return key != null ? key.longValue() : null;
    }

    @Override
    public RuleVerifyResultRecordDO queryById(Long id, Long companyId) {
        AsserUtils.notNull(id, "ID不能为空！");
        AsserUtils.notNull(companyId, "公司ID不能为空！");

        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(RuleVerifyResultRecordFieldEnum.ID.getFieldCode()), LinkMode.EQUAL, id),
                        Conditions.and(Columns.toColumn(RuleVerifyResultRecordFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId),
                        Conditions.and(Columns.toColumn(RuleVerifyResultRecordFieldEnum.ENABLE_STATUS.getFieldCode()), LinkMode.EQUAL, 1)
                )
                .select()
                .limit(1)
                .toSql();

        List<RuleVerifyResultRecordDO> list = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(RuleVerifyResultRecordDO.class) ,sql.getArgs().toArray());
        return ObjectUtils.isEmpty(list) ? null : list.get(0);
    }

    @Override
    public List<RuleVerifyResultRecordDO> queryByRuleId(Long ruleId, Long companyId) {
        AsserUtils.notNull(ruleId, "规则ID不能为空！");
        AsserUtils.notNull(companyId, "公司ID不能为空！");

        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(RuleVerifyResultRecordFieldEnum.RULE_ID.getFieldCode()), LinkMode.EQUAL, ruleId),
                        Conditions.and(Columns.toColumn(RuleVerifyResultRecordFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId),
                        Conditions.and(Columns.toColumn(RuleVerifyResultRecordFieldEnum.ENABLE_STATUS.getFieldCode()), LinkMode.EQUAL, 1)
                )
                .select()
                .toSql();

        return jdbcTemplate.query(sql.getSqlCode(), sql.getArgs().toArray(), new BeanPropertyRowMapper<>(RuleVerifyResultRecordDO.class));
    }

    @Override
    public int batchInsert(List<RuleVerifyResultRecordDO> recordList) {
        if (ObjectUtils.isEmpty(recordList)) {
            return 0;
        }

        List<Object> objectList = recordList.stream()
                .map(item -> (Object) item)
                .collect(Collectors.toList());

        InsertBatchSQL sql = Inserts.create(InsertMode.DEFAULT)
                .into(TABLE_NAME)
                .columns(RuleVerifyResultRecordFieldEnum.getInsertFields())
                .valueForEntities(objectList)
                .batch()
                .toSql();

        jdbcTemplate.batchUpdate(sql.getSqlPattern(), sql.getArguments());
        return recordList.size();
    }

    @Override
    public List<RuleVerifyResultRecordDO> queryRuleList(List<Long> ruleIdList, Date date, Long companyId) {
        AsserUtils.notNull(companyId, "公司ID不能为空！");
        AsserUtils.notNull(date, "账期时间不能为空！");
        AsserUtils.notNull(ruleIdList, "规则ID集合不能为空！");

        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(RuleVerifyResultRecordFieldEnum.VERIFY_TIME.getFieldCode()), LinkMode.EQUAL, date),
                        Conditions.and(Columns.toColumn(RuleVerifyResultRecordFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId),
                        Conditions.and(Columns.toColumn(RuleVerifyResultRecordFieldEnum.RULE_ID.getFieldCode()), LinkMode.IN, ruleIdList),
                        Conditions.and(Columns.toColumn(RuleVerifyResultRecordFieldEnum.ENABLE_STATUS.getFieldCode()), LinkMode.EQUAL, 1)
                )
                .select()
                .toSql();

        return jdbcTemplate.query(sql.getSqlCode(),new BeanPropertyRowMapper<>(RuleVerifyResultRecordDO.class), sql.getArgs().toArray());
    }

    @Override
    public List<RuleVerifyResultRecordDO> queryByParam(List<Long> ruleIdList, Date startTime, Date endTime, Long companyId) {
        AsserUtils.notNull(companyId, "公司ID不能为空！");
        AsserUtils.notNull(ruleIdList, "规则ID列表不能为空！");
        AsserUtils.notNull(startTime, "开始时间不能为空！");
        AsserUtils.notNull(endTime, "结束时间不能为空！");

        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(RuleVerifyResultRecordFieldEnum.RULE_ID.getFieldCode()), LinkMode.IN, ruleIdList),
                        Conditions.and(Columns.toColumn(RuleVerifyResultRecordFieldEnum.VERIFY_TIME.getFieldCode()), LinkMode.GREATER_THAN_EQUAL, startTime),
                        Conditions.and(Columns.toColumn(RuleVerifyResultRecordFieldEnum.VERIFY_TIME.getFieldCode()), LinkMode.LESS_THAN_EQUAL, endTime),
                        Conditions.and(Columns.toColumn(RuleVerifyResultRecordFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId),
                        Conditions.and(Columns.toColumn(RuleVerifyResultRecordFieldEnum.ENABLE_STATUS.getFieldCode()), LinkMode.EQUAL, 1)
                )
                .select()
                .toSql();

        return jdbcTemplate.query(sql.getSqlCode(),new BeanPropertyRowMapper<>(RuleVerifyResultRecordDO.class), sql.getArgs().toArray());
    }

}
