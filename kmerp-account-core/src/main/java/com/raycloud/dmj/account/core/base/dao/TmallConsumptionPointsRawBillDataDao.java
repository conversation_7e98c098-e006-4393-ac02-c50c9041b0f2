package com.raycloud.dmj.account.core.base.dao;

import com.raycloud.dmj.account.core.cleancategory.domain.object.TmallConsumptionPointsRawBillDataDO;
import com.raycloud.dmj.account.core.common.Page;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface TmallConsumptionPointsRawBillDataDao {

    /**
     * 根据批次号集分宝账单
     *
     * @param dataRange 批次号
     * @return 微信支付宝账单
     */
    List<TmallConsumptionPointsRawBillDataDO> listPageByDataRange(Long companyId, Long shopId, Integer dataRange, Page page);





}
