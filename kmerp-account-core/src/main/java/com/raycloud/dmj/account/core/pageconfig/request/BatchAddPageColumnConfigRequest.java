package com.raycloud.dmj.account.core.pageconfig.request;

import lombok.Data;

import java.util.List;

/**
 * 批量新增页面列配置请求
 * <AUTHOR>
 */
@Data
public class BatchAddPageColumnConfigRequest {

    /**
     * 页面ID
     */
    private Long pageId;

    /**
     * 配置列表
     */
    private List<PageColumnConfigItem> configList;

    @Data
    public static class PageColumnConfigItem {
        
        /**
         * 列编码
         */
        private String colCode;

        /**
         * 列名称
         */
        private String colTitle;

        /**
         * 列宽，默认100
         */
        private Integer width = 100;

        /**
         * 排序
         */
        private Integer sort;

        /**
         * 是否可见(0 不可见 1可见)，默认0
         */
        private Integer visible = 0;
    }
}
