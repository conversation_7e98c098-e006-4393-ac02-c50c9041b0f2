package com.raycloud.dmj.account.core.cleancategory.strategy.other;

import com.raycloud.dmj.account.core.base.dao.TmallTaobaoAllianceRawBillDataDao;
import com.raycloud.dmj.account.core.base.domain.StandardOtherBillFlowInfoDO;
import com.raycloud.dmj.account.core.cleancategory.domain.object.TmallTaobaoAllianceRawBillDataDO;
import com.raycloud.dmj.account.core.cleancategory.strategy.param.CategoryAnalyzeParam;
import com.raycloud.dmj.account.core.common.Page;
import com.raycloud.dmj.account.core.enums.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;


/**
 * 集分宝类目解析处理
 * <AUTHOR>
 */
@Component
public class TaobaoAllianceAnalyzeHandle extends StandardOtherCategoryAnalyzeHandle<TmallTaobaoAllianceRawBillDataDO> {


    @Resource
    private TmallTaobaoAllianceRawBillDataDao tmallTaobaoAllianceRawBillDataDao;

    @Override
    protected List<TmallTaobaoAllianceRawBillDataDO> listPageRawData(CategoryAnalyzeParam param, Page page) {
        return tmallTaobaoAllianceRawBillDataDao.listPageByDataRange(
                param.getCompanyId(),
                param.getShopId(),
                param.getDataRange(),
                page
        );
    }

    @Override
    protected void setStandardFundBillFlowInfoDO(StandardOtherBillFlowInfoDO standardOtherBillFlowInfoDO, TmallTaobaoAllianceRawBillDataDO rawDataDO) {
        standardOtherBillFlowInfoDO.setCategoryGroupCode(OtherCategoryGroupEnum.BT.getCode());
        standardOtherBillFlowInfoDO.setCategoryCode(OtherCategoryEnum.TBLM.getCode());
        standardOtherBillFlowInfoDO.setSubCategoryCode(OtherSubCategoryEnum.TBLM.getCode());
        standardOtherBillFlowInfoDO.setBillingCycle(rawDataDO.getBillingCycle());
        standardOtherBillFlowInfoDO.setOccurredAt(rawDataDO.getBusinessTime());
        BigDecimal amount = rawDataDO.getPointsCouponAmount();
        standardOtherBillFlowInfoDO.setAmount(amount);
        if (amount.compareTo(BigDecimal.ZERO)>0){
            standardOtherBillFlowInfoDO.setIncomeExpenseDirection(IncomeExpenseDirectionEnum.INCOME.getCode());
        }else {
            standardOtherBillFlowInfoDO.setIncomeExpenseDirection(IncomeExpenseDirectionEnum.EXPENSE.getCode());
        }
        standardOtherBillFlowInfoDO.setRemark(rawDataDO.getRemark());
        standardOtherBillFlowInfoDO.setOrderNo(rawDataDO.getOrderSn());
        standardOtherBillFlowInfoDO.setBatchNo(rawDataDO.getBatchNo());
        standardOtherBillFlowInfoDO.setBizKey(rawDataDO.getBizKey());



    }

    @Override
    protected void deleteByShopAndDataRangeAndCategoryGroup(CategoryAnalyzeParam param) {
        standardOtherBillFlowInfoDao.deleteByShopAndDataRangeAndCategory(param.getCompanyId(),
                param.getShopId(),param.getDataRange() , OtherCategoryEnum.TBLM.getCode());
    }

    @Override
    public boolean accord(RawDataSourceEnum source) {
        return RawDataSourceEnum.TAOBAO_UNION.equals(source);
    }
}
