package com.raycloud.dmj.account.core.rocketmq.dto;

import com.raycloud.dmj.account.core.enums.MonitorStatusEnum;
import lombok.Data;

import java.io.Serializable;


/**
 * <AUTHOR>
 */
@Data
public class RawDataStatusChangeMsg implements Serializable {

    private static final long serialVersionUID = -1L;

    /**
     * 原始数据批次监控ID
     */
    private Long rawDataBatchMonitorId;

    /**
     * @see MonitorStatusEnum
     * 监控状态
     */
    private Integer status;

}
