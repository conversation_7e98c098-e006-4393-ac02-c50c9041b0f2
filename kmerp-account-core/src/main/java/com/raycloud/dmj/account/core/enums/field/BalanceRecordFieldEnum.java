package com.raycloud.dmj.account.core.enums.field;

import lombok.Getter;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 余额记录表字段枚举
 * <AUTHOR>
 */
@Getter
public enum BalanceRecordFieldEnum {

    ID("id", "主键ID"),
    CREATED("created", "创建时间"),
    MODIFIED("modified", "修改时间"),
    FUND_ACCOUNT_ID("fund_account_id", "资金账户ID"),
    COMPANY_ID("company_id", "公司ID"),
    START_BALANCE("start_balance", "期初余额"),
    END_BALANCE("end_balance", "期末余额"),
    CHANGE_AMOUNT("change_amount", "变化金额"),
    DATE("date", "日期"),
    SHOP_ID("shop_id", "店铺ID");

    /**
     * 字段编码
     */
    private final String fieldCode;

    /**
     * 字段描述
     */
    private final String fieldDesc;

    BalanceRecordFieldEnum(String fieldCode, String fieldDesc) {
        this.fieldCode = fieldCode;
        this.fieldDesc = fieldDesc;
    }

    /**
     * 获取插入字段（排除自增主键）
     */
    public static Set<String> getInsertFields() {
        List<BalanceRecordFieldEnum> filterFields = Arrays.asList(ID);
        return Arrays.stream(values())
                .filter(f -> !filterFields.contains(f))
                .map(f -> f.fieldCode)
                .collect(Collectors.toSet());
    }
}