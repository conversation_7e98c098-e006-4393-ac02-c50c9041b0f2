package com.raycloud.dmj.account.core.enums.field;

import lombok.Getter;

/**
 * 微信原始数据字段枚举
 * <AUTHOR>
 */
@Getter
public enum TmallWechatRawBillFieldEnum {

    ID("id", "主键ID，自增"),
    ENTRY_TIME("entry_time", "入账时间"),
    PAYMENT_FLOW_NO("payment_flow_no", "支付流水号"),
    TAOBAO_ORDER_NO("taobao_order_no", "淘宝订单编号"),
    ENTRY_TYPE("entry_type", "入账类型"),
    INCOME_AMOUNT("income_amount", "收入金额"),
    EXPENSE_AMOUNT("expense_amount", "支出金额"),
    BIZ_DESCRIPTION("biz_description", "业务描述"),
    REMARK("remark", "备注"),
    DOWNLOAD_TIME("download_time", "下载时间"),
    DOWNLOAD_ACCOUNT("download_account", "下载账户"),
    BIZ_KEY("biz_key", "唯一键"),
    BATCH_NO("batch_no", "批次号"),
    BATCH_TIME("batch_time", "批次时间"),
    COMPANY_ID("company_id", "公司ID"),
    SHOP_ID("shop_id", "店铺ID"),
    CREATED("created", "创建时间");

    /**
     * 字段编码
     */
    private final String fieldCode;

    /**
     * 字段描述
     */
    private final String fieldDesc;

    TmallWechatRawBillFieldEnum(String fieldCode, String fieldDesc) {
        this.fieldCode = fieldCode;
        this.fieldDesc = fieldDesc;
    }
}    