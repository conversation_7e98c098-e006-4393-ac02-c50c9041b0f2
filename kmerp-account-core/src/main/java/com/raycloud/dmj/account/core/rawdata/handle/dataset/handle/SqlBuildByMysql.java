package com.raycloud.dmj.account.core.rawdata.handle.dataset.handle;

import com.raycloud.dmj.account.core.rawdata.utils.RawDataDateUtil;
import com.raycloud.dmj.account.core.enums.DateTypeEnum;
import com.raycloud.dmj.account.core.rawdata.handle.dataset.SqlBuildHandler;
import com.raycloud.dmj.account.core.rawdata.handle.param.OperateBatchDbInfo;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import com.raycloud.dmj.table.api.plus.common.base.SQL;
import com.raycloud.dmj.table.api.plus.common.enums.LinkMode;
import com.raycloud.dmj.table.api.plus.component.condition.utils.Conditions;
import com.raycloud.dmj.table.api.plus.delete.Deletes;
import com.raycloud.dmj.table.api.plus.insert.Inserts;
import com.raycloud.dmj.table.api.plus.insert.batch.InsertBatchSQL;
import com.raycloud.dmj.table.api.plus.insert.core.InsertMode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.util.Pair;

import java.util.List;
import java.util.stream.Collectors;


/**
 * mysql实现
 * <AUTHOR>
 */
public class SqlBuildByMysql implements SqlBuildHandler {
    @Override
    public Pair<String, List<Object[]>> insertBatchSql(OperateBatchDbInfo operateBatchDbInfo) {

        List<List<Object>> values = prepareBatchValues(operateBatchDbInfo);
        InsertBatchSQL sql = Inserts.create(InsertMode.DEFAULT)
                .into(operateBatchDbInfo.getTableName())
                .columns(operateBatchDbInfo.getTableFields())
                .values(values)
                .batch()
                .toSql();
        return Pair.of(sql.getSqlPattern(), sql.getArguments());
    }

    @Override
    public Pair<String, Object[]> deleteDataByBatchCode(OperateBatchDbInfo operateBatchDbInfo) {
        AsserUtils.notNull(operateBatchDbInfo.getCompanyId(), "companyId不能为空");
        AsserUtils.notEmpty(operateBatchDbInfo.getBatchNo(), "批次号不能为空");

        SQL sql = Deletes.create()
                .from(operateBatchDbInfo.getTableName())
                .where(
                        Conditions.and("company_id", LinkMode.EQUAL, operateBatchDbInfo.getCompanyId()),
                        Conditions.and(operateBatchDbInfo.getBatchField(), LinkMode.EQUAL, operateBatchDbInfo.getBatchNo())
                ).toSql();
        Object[] updateArgs = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        return Pair.of(sql.getSqlCode(), updateArgs);
    }


    /**
     * 批量插入数据
     *
     * @param operateBatchDbInfo 批次信息
     * @return 处理后添加到数据库的批次数据
     */
    private List<List<Object>> prepareBatchValues(OperateBatchDbInfo operateBatchDbInfo) {
        return operateBatchDbInfo.getBatcDataList().stream()
                .map(map -> operateBatchDbInfo.getTableFields().stream()
                        .map((tableField) -> {
                                    //处理时间字段，转换为指定格式 可能是日/月/年
                                    if (StringUtils.equals(tableField, operateBatchDbInfo.getDataTimeField())) {
                                        return RawDataDateUtil.formatDate(operateBatchDbInfo.getBatchDataTime(), DateTypeEnum.of(operateBatchDbInfo.getDateType()));
                                    }
                                    return map.get(tableField);
                                }

                        )
                        .collect(Collectors.toList()))
                .collect(Collectors.toList());
    }
}
