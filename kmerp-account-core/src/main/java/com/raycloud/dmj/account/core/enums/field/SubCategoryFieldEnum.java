package com.raycloud.dmj.account.core.enums.field;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 子类别表字段枚举
 */
@Getter
public enum SubCategoryFieldEnum {

    ID("id", "主键ID"),
    CREATED("created", "创建时间"),
    MODIFIED("modified", "修改时间"),
    PLATFORM_CODE("platform_code", "平台code，标识所属业务平台"),
    CATEGORY_GROUP_CODE("category_group_code", "分类的编码，关联分类分组体系"),
    CATEGORY_ID("category_id", "类别ID，关联category主表"),
    SOURCE("source", "来源：1-系统 2-手动"),
    NAME("name", "子类别名称，描述子分类"),
    OFFSET("offset", "是否抵消：0-不抵消 1-抵消"),
    INCOME_EXPENSE_OBJECT_ID("income_expense_object_id", "收支对象ID，关联收支主体"),
    COMPANY_ID("company_id", "公司ID");

    /**
     * 字段编码
     */
    private final String fieldCode;

    /**
     * 字段描述
     */
    private final String fieldDesc;

    SubCategoryFieldEnum(String fieldCode, String fieldDesc) {
        this.fieldCode = fieldCode;
        this.fieldDesc = fieldDesc;
    }

    /**
     * 获取插入字段
     * @return 插入字段
     */
    public static Set<String> getInsertFields() {
        List<SubCategoryFieldEnum> filterField = Arrays.asList(
                SubCategoryFieldEnum.ID
        );
        return Arrays.stream(SubCategoryFieldEnum.values()).filter(x -> !filterField.contains(x)).map(
                x -> x.fieldCode
        ).collect(Collectors.toSet());

    }

}