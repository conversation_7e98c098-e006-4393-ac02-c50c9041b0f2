package com.raycloud.dmj.account.core.mock;

//@Slf4j
//@Service
//@RequiredArgsConstructor
public class DbQueryMock {

//    private final DatasourceConfiguration datasourceConfiguration;

//    public Object query(Staff staff) {
//        Integer dbNo = staff.getDbNo();
//        Integer reportDbNo = staff.getReportDbNo();
//        Integer reportPgDbNo = staff.getReportPgDbNo();
//
//        String connectorId1 = "NACOS:KMAS:MYSQL:" + dbNo;
//        String connectorId2 = "NACOS:KMAS:MYSQL:RP" + reportDbNo;
//        String connectorId3 = "NACOS:KMAS:ADBPG:RP" + reportPgDbNo;
//
//        JdbcTemplate jdbcTemplate1 = datasourceConfiguration.getJdbcTemplate(connectorId1);
//        JdbcTemplate jdbcTemplate2 = datasourceConfiguration.getJdbcTemplate(connectorId2);
//        JdbcTemplate jdbcTemplate3 = datasourceConfiguration.getJdbcTemplate(connectorId3);
//
//        SQL ping = Queries.create()
//                .select(
//                        $.column("kmas-1").alias("ping")
//                )
//                .toSql();
//
//        SQL ping2 = Queries.create()
//                .select(
//                        $.column("kmas-2").alias("ping")
//                )
//                .toSql();
//
//        SQL ping3 = Queries.create()
//                .select(
//                        $.column("kmas-3").alias("ping")
//                )
//                .toSql();
//
//        List<Object> result = new ArrayList<>();
//        List<Map<String, Object>> maps1 = jdbcTemplate1.queryForList(ping.getSqlCode());
//        List<Map<String, Object>> maps2 = jdbcTemplate2.queryForList(ping2.getSqlCode());
//        List<Map<String, Object>> maps3 = jdbcTemplate3.queryForList(ping3.getSqlCode());
//        result.add(maps1);
//        result.add(maps2);
//        result.add(maps3);
//
//        return result;
//    }

}
