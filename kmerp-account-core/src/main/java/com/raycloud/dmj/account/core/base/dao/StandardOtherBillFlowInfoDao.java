package com.raycloud.dmj.account.core.base.dao;

import com.raycloud.dmj.account.core.base.domain.StandardOtherBillFlowInfoDO;
import com.raycloud.dmj.account.core.bill.parameter.StandardOtherBillFlowParameter;
import com.raycloud.dmj.account.core.bill.request.StandardOtherBillFlowRequest;
import com.raycloud.dmj.account.core.bill.vo.BillTotalInfo;
import com.raycloud.dmj.account.core.bill.vo.StandardOtherBillFlowInfoVO;
import com.raycloud.dmj.table.api.plus.common.base.SQL;

import java.util.List;

/**
 * 平台其他收支流水Dao
 */
public interface StandardOtherBillFlowInfoDao {

    /**
     * 新增平台其他收支流水信息
     * @param flowInfo
     * @return
     */
    Long addStandardOtherBillFlowInfo(StandardOtherBillFlowInfoDO flowInfo);

    /**
     * 统计符合条件的数据条数
     * @param request
     * @param companyId
     * @return
     */
    Long getCount(StandardOtherBillFlowRequest request, Long companyId);

    /**
     * 根据条件查询平台其他收支流水信息列表
     * @param request
     * @param companyId
     * @return
     */
    List<StandardOtherBillFlowInfoVO> getStandardOtherBillFlowInfoList(StandardOtherBillFlowRequest request, Long companyId);

    /**
     * 查询符合条件的分页汇总信息
     * @param request
     * @param companyId
     * @return
     */
    BillTotalInfo getTotalInfo(StandardOtherBillFlowRequest request, Long companyId);

    /**
     * 根据ID查询平台其他收支流水信息
     * @param id
     * @return
     */
    StandardOtherBillFlowInfoDO getStandardOtherBillFlowInfoById(Long id);

    /**
     * 批量新增平台其他收支流水信息
     * @param flowInfoList
     */
    Integer batchAddStandardOtherBillFlowInfo(List<StandardOtherBillFlowInfoDO> flowInfoList);


    /**
     * 批量新增平台其他收支流水信息
     * @param flowInfoList 流水信息列表
     * @return 插入数量
     */
    Integer batchInsert(List<StandardOtherBillFlowInfoDO> flowInfoList);


    /**
     * 批量删除标准资金账单流水信息
     * @param companyId 公司ID
     * @param shopId 店铺ID
     * @param dataRange 账期
     * @param categoryGroupCode 分类组编码
     */
    void deleteByShopAndDataRangeAndCategory(Long companyId, Long shopId, Integer dataRange, String categoryGroupCode);


    /**
     * 获取导出数据SQL
     * @param standardFundBillFlowParameter 查询条件
     * @param companyId 公司ID
     * @return 导出数据SQL
     */
    SQL getExportSQL(StandardOtherBillFlowParameter standardFundBillFlowParameter, Long companyId);
}
