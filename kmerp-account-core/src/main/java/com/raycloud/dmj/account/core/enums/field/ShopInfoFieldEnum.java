package com.raycloud.dmj.account.core.enums.field;

import lombok.Getter;

/**
 * 店铺信息表字段枚举
 * 
 * <AUTHOR>
 */
@Getter
public enum ShopInfoFieldEnum {

    ID("id", "店铺ID，主键自增"),
    PLATFORM_CODE("platform_code", "平台编码"),
    SHOP_CODE("shop_code", "店铺编码"),
    TITLE("title", "店铺名称"),
    SHORT_TITLE("short_title", "店铺简称"),
    AFFILIATED_COMPANY_ID("affiliated_company_id", "所属公司ID"),
    AMOUNT("amount", "期初应收余额"),
    ACCOUNT_STATUS("account_status", "账户状态 0:关闭对帐 1:开启对帐"),
    ENABLE_STATUS("enable_status", "启用状态 0:弃用 1:正常"),
    START_DATE("start_date", "期初时间"),
    RECONCILIATION_TYPE("reconciliation_type", "对帐维度类型 1：自然月 2:财务月"),
    RECONCILIATION_DATE("reconciliation_date", "财务月截止时间(每个月多少号) 示例：1 代表每个月1号，最大28"),
    COMPANY_ID("company_id", "公司ID"),
    CREATED("created", "创建时间"),
    MODIFIED("modified", "修改时间");

    /**
     * 字段编码
     */
    private final String fieldCode;

    /**
     * 字段描述
     */
    private final String fieldDesc;

    ShopInfoFieldEnum(String fieldCode, String fieldDesc){
        this.fieldCode = fieldCode;
        this.fieldDesc = fieldDesc;
    }
}
