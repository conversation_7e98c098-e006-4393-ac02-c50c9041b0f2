package com.raycloud.dmj.account.core.base.dao;

import com.raycloud.dmj.account.core.cleancategory.domain.object.BillAgainAnalyzeTaskDO;

import java.util.List;

/**
 * 账单重洗任务
 * <AUTHOR>
 */
public interface BillAgainAnalyzeTaskDao {


    /**
     * 新增账单重洗任务
     * @param billAgainAnalyzeTaskDO 类别解析规则
     * @return 新增类别解析规则ID
     */
    Long insert(BillAgainAnalyzeTaskDO billAgainAnalyzeTaskDO);

    /**
     * 获取账单重洗任务
     * @param companyId 公司ID
     * @return 账单重洗任务
     */
   List<BillAgainAnalyzeTaskDO> list(Long companyId);


    /**
     * 根据ID获取账单重洗任务
     * @param companyId 公司ID
     * @param id  ID
     * @return 账单重洗任务
     */
   BillAgainAnalyzeTaskDO getById(Long companyId,Long id);


   /**
     * 更新账单重洗任务状态
     * @param againAnalyzeTaskDO 账单重洗任务
     */
    void updateStatusById(BillAgainAnalyzeTaskDO againAnalyzeTaskDO);
}
