package com.raycloud.dmj.account.core.monitor.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.raycloud.dmj.account.core.base.dao.MonitorRuleDao;
import com.raycloud.dmj.account.core.base.dao.MonitorSummaryDao;
import com.raycloud.dmj.account.core.base.dao.ShopInfoDao;
import com.raycloud.dmj.account.core.base.domain.MonitorRuleDO;
import com.raycloud.dmj.account.core.base.domain.MonitorSummaryDO;
import com.raycloud.dmj.account.core.base.domain.ShopInfoDO;
import com.raycloud.dmj.account.core.common.Page;
import com.raycloud.dmj.account.core.common.PageInfo;
import com.raycloud.dmj.account.core.enums.DateTypeEnum;
import com.raycloud.dmj.account.core.enums.PlatformEnum;
import com.raycloud.dmj.account.core.enums.RawDataSourceEnum;
import com.raycloud.dmj.account.core.monitor.service.IMonitorService;
import com.raycloud.dmj.account.core.monitor.request.QueryShopMonitorReq;
import com.raycloud.dmj.account.core.monitor.vo.MonitorSummaryVO;
import com.raycloud.dmj.account.core.monitor.vo.MonitorSummeryDetail;
import com.raycloud.dmj.account.infra.common.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MonitorServiceImpl implements IMonitorService {

    @Resource
    private MonitorRuleDao monitorRuleDao;

    @Resource
    private MonitorSummaryDao monitorSummaryDao;
    @Resource
    private ShopInfoDao shopInfoDao;

    @Override
    public List<MonitorSummaryVO> queryShopDataMonitor(QueryShopMonitorReq req) {
        // 1. 参数校验前置
        validateSummary(req);
        List<MonitorSummaryDO> monitorSummaryList =
                monitorSummaryDao.pageDataSummaryByPlatformCode(
                        req.getCompanyId(),
                        req.getPlatformCode(),
                        req.getShopId(),
                        new Page(req.getPageNo(), req.getPageSize()));
        if (monitorSummaryList.isEmpty()) {
            return Lists.newArrayList();
        }
        List<Long> shopIdList = monitorSummaryList.stream().map(MonitorSummaryDO::getShopId).distinct().collect(Collectors.toList());
        List<ShopInfoDO> shopInfoList = shopInfoDao.listShopInfoByIds(req.getCompanyId(), shopIdList);
        Map<Long, String> shopMap = shopInfoList.stream().collect(Collectors.toMap(ShopInfoDO::getId, ShopInfoDO::getTitle));
        // 查询对应的商家监控规则
        List<MonitorRuleDO> monitorRuleList = monitorRuleDao.listMonitorRuleByShopIds(shopIdList);
        if (monitorRuleList == null || monitorRuleList.isEmpty()) {
            return Lists.newArrayList();
        }
        Map<Long, List<MonitorSummaryDO>> summaryByShopMap = monitorSummaryList.stream().collect(Collectors.groupingBy(MonitorSummaryDO::getShopId));
        Map<Long, List<MonitorRuleDO>> monitorRuleByShopMap = monitorRuleList.stream().collect(Collectors.groupingBy(MonitorRuleDO::getShopId));
        List<MonitorSummaryVO> monitorSummaryVOList = Lists.newArrayList();
        for (Long shopId : summaryByShopMap.keySet()) {
            List<MonitorSummaryDO> summaryList = summaryByShopMap.get(shopId);
            Map<Integer, MonitorSummaryDO> summaryByDataSourceMap = summaryList.stream().collect(Collectors.toMap(MonitorSummaryDO::getDataSource, obj -> obj));
            MonitorSummaryVO vo = new MonitorSummaryVO();
            vo.setShopName(shopMap.get(shopId));

            List<MonitorRuleDO> shopMonitorRuleList = monitorRuleByShopMap.get(shopId);
            Map<Integer, List<MonitorRuleDO>> shopMonitorRuleByDataSourceMap = shopMonitorRuleList.stream().collect(Collectors.groupingBy(MonitorRuleDO::getDataSource));

            List<MonitorSummeryDetail> detailsList = Lists.newArrayList();
            // 查询对应的每个数据来源的数据
            List<RawDataSourceEnum> enumList = RawDataSourceEnum.getEnumList();
            for (RawDataSourceEnum rawDataSourceEnum : enumList) {
                List<MonitorRuleDO> monitorRuleDOS = shopMonitorRuleByDataSourceMap.get(rawDataSourceEnum.getCode());
                MonitorSummeryDetail monitorSummeryDetail = new MonitorSummeryDetail();
                monitorSummeryDetail.setPlatform(rawDataSourceEnum.getCode());

                MonitorSummaryDO summary = summaryByDataSourceMap.get(rawDataSourceEnum.getCode());
                if (!Objects.isNull(summary)) {
                    monitorSummeryDetail.setUpdateTime(summary.getImportSuccessTime());
                    monitorSummeryDetail.setComputationTime(summary.getAnalyzeSuccessTime());
                    monitorSummeryDetail.setUpdateSuccess(checkMonitorDataStatus(monitorRuleDOS, summary.getImportSuccessTime()));
                    monitorSummeryDetail.setComputationSuccess(checkMonitorDataStatus(monitorRuleDOS, summary.getAnalyzeSuccessTime()));
                }
                detailsList.add(monitorSummeryDetail);
            }
            vo.setMonitorSummeryDetailList(detailsList);
            monitorSummaryVOList.add(vo);
        }
        return monitorSummaryVOList;
    }

    public PageInfo<Void> queryShopDataMonitorPageInfo(QueryShopMonitorReq req) {
        validateSummary(req);
       return monitorSummaryDao.pageInfoSummaryByPlatformCode(
               req.getCompanyId(),
               req.getPlatformCode(),
               req.getShopId(),
               new Page(req.getPageNo(), req.getPageSize()));
    }


    public void validateSummary(QueryShopMonitorReq req) {
        if (Objects.isNull(req.getCompanyId())) {
            throw new BizException("companyId不能为空");
        }
        req.setPlatformCode(StringUtils.defaultIfBlank(req.getPlatformCode(), PlatformEnum.TM.getCode()));
    }

    public boolean checkMonitorDataStatus(List<MonitorRuleDO> ruleList, LocalDateTime compareTime) {
        if (ruleList == null || ruleList.isEmpty() || compareTime == null) {
            return false;
        }

        // 正确的时间 应该  当前时间 < 推送时间后下一次应该推送的时间
        // 拿到上一次推送的时间
        int publishYear = compareTime.getYear();
        int publishMonth = compareTime.getMonthValue();
        int publishDay = compareTime.getDayOfMonth();

        LocalDateTime futurePushTime = null;
        for (MonitorRuleDO monitorRuleDO : ruleList) {
            LocalDateTime futureTime = LocalDateTime.now().plusYears(2);
            if (DateTypeEnum.YEAR.equals(DateTypeEnum.of(monitorRuleDO.getDateType()))) {
                JSONObject parse = JSONObject.parseObject(monitorRuleDO.getMonitorRule());
                futureTime = LocalDateTime.of(publishYear, parse.getInteger("check_month"), 1, 0, 0, 0).plusYears(1);
            }
            if (DateTypeEnum.MONTH.equals(DateTypeEnum.of(monitorRuleDO.getDateType()))) {
                JSONObject parse = JSONObject.parseObject(monitorRuleDO.getMonitorRule());
                futureTime = LocalDateTime.of(publishYear, publishMonth, parse.getInteger("check_day"), 0, 0, 0, 0).plusMonths(1);
            }

            if (DateTypeEnum.DAY.equals(DateTypeEnum.of(monitorRuleDO.getDateType()))) {
                JSONObject parse = JSONObject.parseObject(monitorRuleDO.getMonitorRule());
                futureTime = LocalDateTime.of(publishYear, publishMonth, publishDay, parse.getInteger("check_hour"), 0, 0, 0).plusDays(1);
            }
            // 最近一次预推送时间
            if (futurePushTime == null || futurePushTime.isAfter(futureTime)) {
                futurePushTime = futureTime;
            }
        }
        //判断数据是否正常推送 成功分析时间超过预计推送时间 则为正常
        return  LocalDateTime.now().isBefore(futurePushTime);
    }


}
