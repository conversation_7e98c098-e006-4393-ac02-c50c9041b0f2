package com.raycloud.dmj.account.core.cleancategory.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QueryCategoryParam {

    /**
     * 平台code，标识所属业务平台
     */
    private String platformCode;

    /**
     * 平台code集合，标识所属业务平台
     */
    private List<String> platformCodeList;

    /**
     * 分类编码
     */
    private List<String> categoryGroupCodes;


    /**
     * 资金账户ID
     */
    private List<Long> fundAccountIds;



}
