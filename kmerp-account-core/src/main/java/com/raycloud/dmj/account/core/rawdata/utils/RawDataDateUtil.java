package com.raycloud.dmj.account.core.rawdata.utils;

import com.raycloud.dmj.account.core.enums.DateTypeEnum;
import com.raycloud.dmj.account.infra.common.BusinessException;
import com.raycloud.dmj.account.infra.common.ErrorCodeEnum;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import com.raycloud.dmj.account.infra.utils.DateUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 时间工具类
 *
 * <AUTHOR>
 */
public class RawDataDateUtil {


    /**
     * 时间范围字符串和时间类型转换成时间范围
     * @param dateRange 时间范围字符串
     * @param dateType 时间类型
     * @return 时间范围
     */
    public static Pair<Date, Date> parseDateRange(String dateRange, DateTypeEnum dateType) {
        AsserUtils.notEmpty(dateRange, "时间范围不能为空");
        AsserUtils.notNull(dateType, "时间类型不能为空");
        Date date = parseDate(dateRange, dateType);
        return getStartAndEndDate(dateType, date);
    }

    /**
     * 时间范围字符串和时间类型转换成时间范围
     * @param dateRange 时间范围字符串
     * @param dateType 时间类型
     * @return 时间范围
     */
    public static Pair<Date, Date> parseDateRange(String dateRange,String format, DateTypeEnum dateType) {
        AsserUtils.notEmpty(dateRange, "时间范围不能为空");
        AsserUtils.notNull(dateType, "时间类型不能为空");
        Date date = parseDate(dateRange,format, dateType);
        return getStartAndEndDate(dateType, date);
    }


    /**
     * 根据时间类型获取开始时间和结束时间范围
     * @param dateType 时间类型枚举
     * @param date  时间
     * @return 时间范围
     */
    private static Pair<Date, Date> getStartAndEndDate(DateTypeEnum dateType, Date date) {
        switch (dateType) {
            case DAY:
                Date dayStartTime = DateUtils.getDayStartTime(date);
                Date dayEndTime = DateUtils.getDayEndTime(date);
                return Pair.of(dayStartTime, dayEndTime);
            case MONTH:
                Date monthStartTime = DateUtils.getMonthStartTime(date);
                Date monthEndTime = DateUtils.getMonthEndTime(date);
                return Pair.of(monthStartTime, monthEndTime);
            case YEAR:
                Date yearStartTime = DateUtils.getYearStartTime(date);
                Date yearEndTime = DateUtils.getYearEndTime(date);
                return Pair.of(yearStartTime, yearEndTime);
            default:
                throw new BusinessException(ErrorCodeEnum.PARAM_ERROR.getCode(), "不支持的日期类型: " + dateType);
        }
    }


    /**
     * 根据DateType枚举将字符串日期解析为Date类型
     */
    public static Date parseDate(String dateStr, DateTypeEnum dateType) {
        if (dateType == null || dateStr == null){
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR.getCode(), "dateStr或dateType为空！");
        }
        String patternByDateType = getPatternByDateType(dateType);
        return DateUtils.parse(dateStr, patternByDateType);
    }
    /**
     * 根据DateType枚举将字符串日期解析为Date类型
     */
    public static Date parseDate(String dateStr,String format, DateTypeEnum dateType) {
        if (dateType == null || dateStr == null || format == null){
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR.getCode(), "dateStr或dateType为空！");
        }
        return DateUtils.parse(dateStr, format);
    }


    /**
     * 根据DateType枚举将Date格式化为指定格式
     *
     * @param date     待格式化的日期
     * @param dateType 日期类型枚举
     * @return 格式化后的日期字符串
     */
    public static Integer formatDate(Date date, DateTypeEnum dateType) {
        if (date == null || dateType == null) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR.getCode(), "date或dateType为空！");
        }
        String pattern = getPatternByDateType(dateType);
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return Integer.parseInt(sdf.format(date));
    }

    /**
     * 获取上一个账期
     * @param dataRange 数据范围
     * @param dateType 日期类型枚举
     * @return 上一个账期
     */
    public static Integer getPreviousDataRange(String dataRange ,String format,DateTypeEnum dateType) {
        AsserUtils.notNull(dataRange, "dataRange不能为空");
        AsserUtils.notNull(dateType, "dateType不能为空");
        AsserUtils.notEmpty(format, "format不能为空");
        Date date = parseDate(dataRange,format, dateType);
        return getIntegerDataRange(dateType, date);

    }

    private static Integer getIntegerDataRange(DateTypeEnum dateType, Date date) {
        Date previousDataRangeDate;
        switch (dateType) {
            case DAY:
                previousDataRangeDate = DateUtils.getOffsetDay(date,-1);
                break;
            case MONTH:
                previousDataRangeDate = DateUtils.getOffsetMonth(date,-1);
                break;
            case YEAR:
                previousDataRangeDate = DateUtils.getOffsetYear(date,-1);
                break;
            default:
                throw new BusinessException(ErrorCodeEnum.PARAM_ERROR.getCode(), "不支持的日期类型: " + dateType);
        }
        return formatDate(previousDataRangeDate, dateType);
    }


    /**
     * 获取上一个账期
     * @param dataRange 数据范围
     * @param dateType 日期类型枚举
     * @return 上一个账期
     */
    public static Integer getPreviousDataRange(Integer dataRange ,DateTypeEnum dateType) {
        AsserUtils.notNull(dataRange, "dataRange不能为空");
        AsserUtils.notNull(dateType, "dateType不能为空");
        Date date = parseDate(String.valueOf(dataRange), dateType);
        return getIntegerDataRange(dateType, date);

    }




    /**
     * 根据DateType获取对应的日期格式模式
     *
     * @param dateType 日期类型枚举
     * @return 日期格式模式字符串
     */
    private static String getPatternByDateType(DateTypeEnum dateType) {
        switch (dateType) {
            case DAY:
                return "yyyyMMdd";
            case MONTH:
                return "yyyyMM";
            case YEAR:
                return "yyyy";
            default:
                throw new BusinessException(ErrorCodeEnum.PARAM_ERROR.getCode(), "不支持的日期类型: " + dateType);
        }
    }


}
