package com.raycloud.dmj.account.core.base.dao.impl;

import com.mysql.jdbc.Statement;
import com.raycloud.dmj.account.core.common.constant.Constant;
import com.raycloud.dmj.account.core.platform.base.domain.ShopAuthRecordDO;
import com.raycloud.dmj.account.infra.datasource.KmerpDatasourceConfiguration;
import com.raycloud.dmj.table.api.plus.common.base.SQL;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.sql.PreparedStatement;


/**
 * <AUTHOR>
 */
public class BaseDao {

    @Resource
    private KmerpDatasourceConfiguration kmerpDatasourceConfiguration;

    protected JdbcTemplate jdbcTemplate;

    @PostConstruct
    public void init() {
        jdbcTemplate = kmerpDatasourceConfiguration.getJdbcTemplate(Constant.ORIGINAL_DATA_BASE_CONNECTION_KEY);
    }


    /**
     * 执行插入并返回主键
     * @param sql  sql
     * @return 主键
     */
    protected long insertReturnPrimaryKey(SQL sql) {
        // 创建KeyHolder用于获取生成的主键
        KeyHolder keyHolder = new GeneratedKeyHolder();
        // 使用带KeyHolder的update方法
        jdbcTemplate.update(
                connection -> {
                    PreparedStatement ps = connection.prepareStatement(
                            sql.getSqlCode(),
                            Statement.RETURN_GENERATED_KEYS
                    );
                    // 设置参数
                    Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
                    for (int i = 0; i < args.length; i++) {
                        ps.setObject(i + 1, args[i]);
                    }
                    return ps;
                },
                keyHolder
        );
        // 获取生成的主键值
        return keyHolder.getKey().longValue();
    }
}
