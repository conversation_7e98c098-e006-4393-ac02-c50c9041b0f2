package com.raycloud.dmj.account.core.platform.base.domain;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class SharedDataInfoDO {

    /**
     * 自增主键
     */
    private Long id;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 公司ID
     */
    private Long companyId;

    /**
     * 授权平台id
     */
    private String platformCode;

    /**
     * 文件类型，默认值为 BILL
     */
    private String type;

    /**
     * 文件路径
     */
    private String url;

    /**
     * 账单的所属时间
     */
    private LocalDate date;



    private String extraData;

    /**
     * 创建时间
     */
    private LocalDateTime created;

    /**
     * 修改时间
     */
    private LocalDateTime modified;
}



