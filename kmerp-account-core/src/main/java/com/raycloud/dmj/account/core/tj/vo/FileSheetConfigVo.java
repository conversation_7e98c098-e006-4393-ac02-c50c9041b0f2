package com.raycloud.dmj.account.core.tj.vo;

import lombok.Data;

import java.util.List;

@Data
public class FileSheetConfigVo {

    private Long id;

    /**
     * 【文件解析配置ID】
     */
    private  Long configId;
    /**
     * 【数据表名称】用户自定的唯一名称，配置间不能重复
     */
    private String tableName;

    /**
     * 【数据表类型】取值范围   1：明细表，2：去重表
     */
    private String tableType;

    /**
     * 【批次号】批次号生成规则
     */
    private List<FileSheetTableConfigVo> batchNo;

    /**
     * 【唯一键】唯一键生成规则
     */
    private List<FileSheetTableConfigVo> uniqueKey;

    /**
     * 【表字段配置】
     */
    private List<FileSheetTableFieldVo> tableFields;

    /**
     * 【表入库源信息配置】
     */
    private String tableSourceConfig;
}
