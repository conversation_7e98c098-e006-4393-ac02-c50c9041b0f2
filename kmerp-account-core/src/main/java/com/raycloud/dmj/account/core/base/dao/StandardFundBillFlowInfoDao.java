package com.raycloud.dmj.account.core.base.dao;

import com.raycloud.dmj.account.core.base.domain.BillSummaryRecordDO;
import com.raycloud.dmj.account.core.base.domain.StandardFundBillFlowInfoDO;
import com.raycloud.dmj.account.core.bill.parameter.StandardFundBillFlowParameter;
import com.raycloud.dmj.account.core.bill.vo.BillTotalInfo;
import com.raycloud.dmj.account.core.bill.vo.StandardFundBillFlowInfoVO;
import com.raycloud.dmj.table.api.plus.common.base.SQL;
import org.apache.commons.lang3.tuple.Pair;

import java.util.Date;
import java.util.List;

/**
 * 标准资金账单流水Dao
 */
public interface StandardFundBillFlowInfoDao {

    /**
     * 新增标准资金账单流水信息
     * @param flowInfo
     * @return
     */
    Long addStandardFundBillFlowInfo(StandardFundBillFlowInfoDO flowInfo);

    /**
     * 统计符合条件的数据条数
     * @param parameter
     * @param companyId
     * @return
     */
    Long getCount(StandardFundBillFlowParameter parameter, Long companyId);

    /**
     * 根据条件查询标准资金账单流水信息列表
     * @param parameter
     * @param companyId
     * @return
     */
    List<StandardFundBillFlowInfoVO> getStandardFundBillFlowInfoList(StandardFundBillFlowParameter parameter, Long companyId);

    /**
     * 查询符合条件的总金额
     * @param parameter
     * @param companyId
     * @return
     */
    BillTotalInfo getTotalInfo(StandardFundBillFlowParameter parameter, Long companyId);

    /**
     * 根据ID查询标准资金账单流水信息
     * @param id
     * @return
     */
    StandardFundBillFlowInfoDO getStandardFundBillFlowInfoById(Long id);

    /**
     * 批量新增标准资金账单流水信息
     * @param flowInfoList
     */
    Integer batchAddStandardFundBillFlowInfo(List<StandardFundBillFlowInfoDO> flowInfoList);

    /**
     * 更新流水拆分状态
     * @param belongId
     * @param isSplit
     * @return
     */
    Integer updateIsSplitById(Long belongId, int isSplit);

    /**
     * 批量删除标准资金账单流水信息
     * @param companyId 公司ID
     * @param accountId 账户ID
     * @param dataRange 账期
     */
    void deleteByAccountIdAndDataRange(Long companyId, Long accountId, Integer dataRange);


    /**
     * 批量插入标准资金账单流水信息
     * @param flowInfoList 流水信息列表
     * @return 插入数量
     */
    Integer batchInsert(List<StandardFundBillFlowInfoDO> flowInfoList);


    /**
     * 根据公司ID、资金账户ID、账期确认标准资金账单流水信息
     * @param companyId 公司ID
     * @param accountId 资金账户ID
     * @param dataRange 账期
     */
     Integer confirmByAccountIdAndDataRange(Long companyId, Long accountId, Integer dataRange);

    /**
     * 获取时间段内标准资金流水合计信息
     * @param accountId
     * @param dateDatePair
     * @param companyId
     * @return
     */
    List<BillSummaryRecordDO> getBillSummary(Long accountId, Pair<Date, Date> dateDatePair, Long companyId);

    /**
     * 获取导出数据SQL
     */
    SQL getExportSQL(StandardFundBillFlowParameter parameter, Long companyId);

}
