package com.raycloud.dmj.account.core.enums.field;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Getter
public enum TmallWechatBalanceRawBillFieldEnum {
    ID("id", "主键ID，自增"),
    RECORD_DATE("record_date", "记录日期（格式：YYYYMMDD）"),
    TRANSACTION_COUNT("transaction_count", "明细总笔数"),
    INCOME_AMOUNT("income_amount", "收入金额（元）"),
    INCOME_COUNT("income_count", "收入笔数"),
    EXPENDITURE_AMOUNT("expenditure_amount", "支出金额（元）"),
    EXPENDITURE_COUNT("expenditure_count", "支出笔数"),
    END_BALANCE("end_balance", "期末余额（元）"),
    BIZ_KEY("biz_key", "业务唯一键"),
    BATCH_NO("batch_no", "批次号"),
    BATCH_TIME("batch_time", "批次时间"),
    COMPANY_ID("company_id", "公司ID"),
    SHOP_ID("shop_id", "店铺ID"),
    CREATED("created", "创建时间"),
    ;

    /**
     * 字段编码
     */
    private final String fieldCode;

    /**
     * 字段描述
     */
    private final String fieldDesc;

    TmallWechatBalanceRawBillFieldEnum(String fieldCode, String fieldDesc) {
        this.fieldCode = fieldCode;
        this.fieldDesc = fieldDesc;
    }
    /**
     * 获取插入字段
     * @return 插入字段
     */
    public static Set<String> getInsertFields() {
        List<TmallWechatBalanceRawBillFieldEnum> filterField = Arrays.asList(
                TmallWechatBalanceRawBillFieldEnum.ID
        );
        return Arrays.stream(TmallWechatBalanceRawBillFieldEnum.values()).filter(x ->!filterField.contains(x)).map(
                x -> x.fieldCode
        ).collect(Collectors.toSet());

    }
}