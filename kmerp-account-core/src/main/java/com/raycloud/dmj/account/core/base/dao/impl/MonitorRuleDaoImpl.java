package com.raycloud.dmj.account.core.base.dao.impl;

import com.mysql.jdbc.Statement;
import com.raycloud.dmj.account.core.base.domain.MonitorRuleDO;
import com.raycloud.dmj.account.core.base.dao.MonitorRuleDao;
import com.raycloud.dmj.account.core.enums.field.MonitorRuleFieldEnum;
import com.raycloud.dmj.table.api.plus.common.base.SQL;
import com.raycloud.dmj.table.api.plus.common.enums.LinkMode;
import com.raycloud.dmj.table.api.plus.component.column.utils.Columns;
import com.raycloud.dmj.table.api.plus.component.condition.utils.Conditions;
import com.raycloud.dmj.table.api.plus.insert.Inserts;
import com.raycloud.dmj.table.api.plus.query.Queries;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.util.List;

/**
 *  数据来源监控规则
 * <AUTHOR>
 */
@Repository
@Slf4j
public class MonitorRuleDaoImpl extends BaseDao implements MonitorRuleDao {

    private static final String TABLE_NAME = "monitor_rule";

    @Override
    public Long insert(MonitorRuleDO monitorRuleDO) {
        SQL sql = Inserts.insert()
                .into(TABLE_NAME)
                .columns(MonitorRuleFieldEnum.getInsertFields())
                .valueForEntity(monitorRuleDO)
                .columnNameCamelToUnderline()
                .toSql();

        // 创建KeyHolder用于获取生成的主键
        KeyHolder keyHolder = new GeneratedKeyHolder();

        // 使用带KeyHolder的update方法
        jdbcTemplate.update(
                connection -> {
                    PreparedStatement ps = connection.prepareStatement(
                            sql.getSqlCode(),
                            Statement.RETURN_GENERATED_KEYS
                    );
                    // 设置参数
                    Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
                    for (int i = 0; i < args.length; i++) {
                        ps.setObject(i + 1, args[i]);
                    }
                    return ps;
                },
                keyHolder
        );

        // 获取生成的主键值
        return keyHolder.getKey().longValue();
    }


    @Override
    public List<MonitorRuleDO> listMonitorRuleByShopId(Long shopId) {
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(MonitorRuleFieldEnum.SHOP_ID.getFieldCode()),
                                LinkMode.EQUAL, shopId))

                .select()
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<MonitorRuleDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(MonitorRuleDO.class), args);
        return !query.isEmpty() ? query : null;
    }

    @Override
    public List<MonitorRuleDO> listMonitorRuleByShopIds(List<Long> shopIds) {
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(MonitorRuleFieldEnum.SHOP_ID.getFieldCode()),
                                LinkMode.IN, shopIds))

                .select()
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<MonitorRuleDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(MonitorRuleDO.class), args);
        return !query.isEmpty() ? query : null;
    }


}
