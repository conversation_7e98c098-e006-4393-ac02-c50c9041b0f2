package com.raycloud.dmj.account.core.cleancategory.domain;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 子类别表DO
 * <AUTHOR>
 */

@Data
public class SubCategoryDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 修改时间
     */
    private Date modified;

    /**
     * 平台code，标识所属业务平台
     */
    private String platformCode;

    /**
     * 分类编码
     */
    private String categoryGroupCode;

    /**
     * 类别ID，关联category主表
     */
    private Long categoryId;

    /**
     * CategoryName
     */
    private String categoryName;

    /**
     * 来源：1-系统 2-手动
     */
    private Integer source;

    /**
     * 子类别名称，描述子分类
     */
    private String subCategoryName;


    /**
     * 是否抵消：0-不抵消 1-抵消
     */
    private Boolean offset;

    /**
     * 收支对象ID，关联收支主体
     */
    private Long incomeExpenseObjectId;

    /**
     * 公司ID
     */
    private Long companyId;

    /**
     * 资金账户ID
     */
    private Long fundAccountId;

}