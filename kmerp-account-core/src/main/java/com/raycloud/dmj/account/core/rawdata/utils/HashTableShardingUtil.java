package com.raycloud.dmj.account.core.rawdata.utils;

import com.raycloud.dmj.account.core.rawdata.domains.TableSourceConfig;
import com.raycloud.dmj.account.core.rawdata.domains.TableSplitConfig;
import com.raycloud.dmj.account.infra.common.BusinessException;
import com.raycloud.dmj.account.infra.common.ErrorCodeEnum;
import com.raycloud.dmj.account.infra.utils.AsserUtils;

import java.util.Objects;
import java.util.Optional;

/**
 * 哈希分表工具类 分开
 * 用于根据分表字段计算具体的分表表名
 * <AUTHOR>
 */
public class HashTableShardingUtil {

    /**
     * 根据哈希算法计算分表表名
     * 租户ID
     * @param baseTableName  基础表名（如：user_data）
     * @param  companyId    公司ID
     * @param tableSourceConfig 表配置
     * @return 分表表名（如：user_data_1）
     */
    public static String getShardTableName(String baseTableName,Long companyId,
            TableSourceConfig tableSourceConfig) {
        Integer tableSplitFlag = Optional.ofNullable(tableSourceConfig).map(TableSourceConfig::getTableSplitFlag).orElse(null);
        if (Objects.isNull(tableSplitFlag) || tableSplitFlag == 0){
            return baseTableName;
        }
        //这里先取模获取分片索引
        Integer shardCount = Optional.of(tableSourceConfig).map(TableSourceConfig::getTableSplitConfig).map(TableSplitConfig::getTableSplitNum).orElse(null);
        if (shardCount == null || shardCount <= 0){
            throw new BusinessException(ErrorCodeEnum.CONFIG_ERROR.getCode(), "分表数量必须大于0");
        }
        int shardIndex = (int) (companyId % shardCount);
        return baseTableName + "_" + shardIndex;
    }

    /**
     * 计算分表号
     * @param companyId 公司ID
     * @return 分表号
     */
    public static int getShardTableNo(Long companyId){
        AsserUtils.notNull(companyId, "公司ID不能为空！");
        //这里先写死 后面可以改写为
        return (int) (companyId % 10);
    }

}