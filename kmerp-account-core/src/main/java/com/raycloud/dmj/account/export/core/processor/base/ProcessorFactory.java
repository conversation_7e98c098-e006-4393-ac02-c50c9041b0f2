package com.raycloud.dmj.account.export.core.processor.base;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

@Configuration
public class ProcessorFactory implements ApplicationContextAware {

    private ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    public <R> ProcessorChainBuilder<R> builder(String name) {
        return ProcessorChainBuilder.create(name, applicationContext);
    }

    public ProcessorChainBuilder<Map<String,Object>> create(String name) {
        return ProcessorChainBuilder.create(name, applicationContext);
    }
}
