package com.raycloud.dmj.account.core.pageconfig.service;

import com.raycloud.dmj.account.core.pageconfig.dto.PageColumnConfigDto;
import com.raycloud.dmj.account.core.pageconfig.request.BatchAddPageColumnConfigRequest;
import com.raycloud.dmj.account.core.pageconfig.request.BatchDeletePageColumnConfigRequest;
import com.raycloud.dmj.account.core.pageconfig.request.BatchUpdatePageColumnConfigRequest;
import com.raycloud.dmj.account.core.pageconfig.vo.PageColumnConfigVO;
import com.raycloud.dmj.account.infra.session.AccountUser;

import java.util.List;

/**
 * 页面列配置服务接口
 * <AUTHOR>
 */
public interface IPageColumnConfigService {

    /**
     * 批量新增页面列配置
     * @param accountUser 用户信息
     * @param request 新增请求
     * @return 新增数量
     */
    Integer batchAddPageColumnConfig(AccountUser accountUser, BatchAddPageColumnConfigRequest request);

    /**
     * 批量更新页面列配置
     * @param accountUser 用户信息
     * @param request 更新请求
     * @return 更新数量
     */
    Integer batchUpdatePageColumnConfig(AccountUser accountUser, BatchUpdatePageColumnConfigRequest request);

    /**
     * 批量删除页面列配置
     * @param accountUser 用户信息
     * @param request 删除请求
     * @return 删除数量
     */
    Integer batchDeletePageColumnConfig(AccountUser accountUser, BatchDeletePageColumnConfigRequest request);

    /**
     * 根据页面ID删除配置
     * @param accountUser 用户信息
     * @param pageId 页面ID
     * @return 删除数量
     */
    Integer deleteByPageId(AccountUser accountUser, Long pageId);

    /**
     * 根据页面ID查询配置列表
     * @param accountUser 用户信息
     * @param pageId 页面ID
     * @return 配置列表
     */
    List<PageColumnConfigVO> queryByPageId(AccountUser accountUser, Long pageId);

    /**
     * 根据页面ID查询配置列表
     * @param companyId 用户租户公司ID
     * @param pageId 页面ID
     * @return 配置列表
     */
    List<PageColumnConfigDto> queryDtoByPageId(Long companyId, Long pageId);

    /**
     * 根据ID查询单个配置
     * @param accountUser 用户信息
     * @param id 配置ID
     * @return 配置信息
     */
    PageColumnConfigVO queryById(AccountUser accountUser, Long id);

    /**
     * 获取有效的列编码
     * @param companyId 用户租户公司ID
     * @param pageId 页面ID
     * @return 有效的列编码列表
     */
    List<String> getEffectiveColumnNames(Long companyId,Long pageId);
}
