package com.raycloud.dmj.account.core.bill.vo;

import com.raycloud.dmj.account.core.common.BaseInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 平台其他收支流水信息表Vo类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StandardOtherBillFlowInfoVO extends BaseInfo {

    private Long id;

    /**
     * 平台类型
     */
    private String platformCode;

    /**
     * 平台名称
     */
    private String platformName;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 店铺简称
     */
    private String shopShortTitle;


    /**
     * 数据清洗规则ID
     */
    private Long ruleId;

    /**
     * 分类code
     */
    private String categoryGroupCode;

    /**
     * 分类code名称
     */
    private String classifyCodeName;

    /**
     * 流水类别Code
     */
    private String categoryCode;

    /**
     * 流水类别名称
     */
    private String categoryName;

    /**
     * 流水子类别Code
     */
    private String subCategoryCode;

    /**
     * 流水子类别名称
     */
    private String subCategoryName;

    /**
     * 平台账期，格式：年月，例如202501
     */
    private Integer billingCycle;

    /**
     * 发生时间
     */
    private Date occurredAt;

    /**
     * 收支方向 1 收入 2 支出
     */
    private Integer incomeExpenseDirection;

    /**
     * 金额(元)（正数收入，负数支出）
     * 数据库字段类型：decimal(20,6)
     */
    private BigDecimal amount;

    /**
     * 关联订单号
     */
    private String orderNo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 收支对象ID
     */
    private Long counterpartyId;


    /**
     * 收支对象名称
     */
    private String counterpartyName;

    /**
     * 关联业务单据号，默认值-1
     */
    private String docNo;

    /**
     * 来源方式(1 账单,2 系统)，默认值1
     */
    private Integer source;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 唯一键（有唯一约束）
     */
    private String bizKey;

    /**
     * 创建人
     */
    private String creator;

}
