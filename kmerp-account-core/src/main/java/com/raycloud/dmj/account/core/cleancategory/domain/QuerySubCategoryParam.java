package com.raycloud.dmj.account.core.cleancategory.domain;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class QuerySubCategoryParam {

    /**
     * 平台code，标识所属业务平台
     */
    private String platformCode;

    /**
     * 平台code集合，标识所属业务平台
     */
    private List<String> platformCodeList;

    /**
     * 分类编码
     */
    private List<String> categoryGroupCodes;

    /**
     * 类别ID，关联category主表
     */
    private List<Long> categoryIds;


    /**
     * 子类目ID
     */
    private List<Long> subCategoryIds;


    /**
     * 资金账户ID
     */
    private List<Long>  fundAccountIds;


    /**
     * 是否抵消：0-不抵消 1-抵消
     */
    private Boolean offset;

}
