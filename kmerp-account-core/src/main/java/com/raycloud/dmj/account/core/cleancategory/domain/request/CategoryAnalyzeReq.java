package com.raycloud.dmj.account.core.cleancategory.domain.request;


import com.raycloud.dmj.account.core.enums.DateTypeEnum;
import com.raycloud.dmj.account.core.enums.RawDataSourceEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * 清理分类请求
 * <AUTHOR>
 */
@Data
public class CategoryAnalyzeReq implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 公司id
     */
    private Long companyId;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 账期
     */
    private Integer dataRange;

    /**
     * 账期类型
     */
    private DateTypeEnum dateType;

    /**
     * 原始数据来源
     */
    private RawDataSourceEnum source;



}
