package com.raycloud.dmj.account.core.enums.field;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Date:  2025/6/16
 * <AUTHOR>
 */
@Getter
public enum FileAnalyzeRecordFieldEnum {
    ID("id", "ID"),
    DATA_TYPE("data_type", "数据类型"),
    DATE_TYPE("date_type", "时间类型"),
    IMPORT_DATA("import_data", "导入数据"),
    ASSIGN_START_DATA_RANGE("assign_start_data_range", "指定开始时间"),
    ASSIGN_END_DATA_RANGE("assign_end_data_range", "指定结束时间"),
    FILE_START_DATA_RANGE("file_start_data_range", "文件开始时间"),
    FILE_END_DATA_RANGE("file_end_data_range", "文件结束时间"),
    ANALYZE_STATUS("analyze_status", "分析状态"),
    ERROR_MSG("error_msg", "错误信息"),
    MATCHING_CONFIG_IDS("matching_config_ids", "配置ID集合"),
    CREATED("created", "创建时间"),
    GROUP_CODE("group_code", "组code"),
    MODIFIED("modified", "修改时间"),
    CREATE_BY("create_by", "创建人"),
    COMPANY_ID("company_id", "租户ID"),
    SHOP_ID("shop_id", "店铺ID"),
    FILE_COUNT_INFO("file_count_info", "文件数量信息"),
    CHANNEL_SOURCE("channel_source", "渠道来源"),
    ;
    /**
     * 字段编码
     */
    private final String fieldCode;

    /**
     * 字段描述
     */
    private final String fieldDesc;

    FileAnalyzeRecordFieldEnum(String fieldCode, String fieldDesc){
        this.fieldCode = fieldCode;
        this.fieldDesc = fieldDesc;
    }


    /**
     * 获取插入字段
     * @return 插入字段
     */
    public static Set<String> getInsertFields() {
        List<FileAnalyzeRecordFieldEnum> filterField = Arrays.asList(
                FileAnalyzeRecordFieldEnum.ID
        );
        return Arrays.stream(FileAnalyzeRecordFieldEnum.values()).filter(x -> !filterField.contains(x)).map(
                x -> x.fieldCode
        ).collect(Collectors.toSet());

    }


}
