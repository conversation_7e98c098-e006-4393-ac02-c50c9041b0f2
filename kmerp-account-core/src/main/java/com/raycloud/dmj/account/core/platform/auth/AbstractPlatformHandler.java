package com.raycloud.dmj.account.core.platform.auth;

import com.alibaba.fastjson2.JSONObject;
import com.raycloud.dmj.account.common.AuthInfoResponse;
import com.raycloud.dmj.account.common.CreateAuthResponse;
import com.raycloud.dmj.account.common.SharedDataResponse;
import com.raycloud.dmj.account.core.enums.FileTypeEnum;
import com.raycloud.dmj.account.core.platform.base.domain.SharedDataInfoDO;
import com.raycloud.dmj.account.core.platform.base.domain.ShopAuthInfoDO;
import com.raycloud.dmj.account.core.platform.base.domain.ShopAuthRecordDO;
import com.raycloud.dmj.account.core.platform.common.constant.AlipayConstant;
import com.raycloud.dmj.account.core.platform.service.ISharedDataService;
import com.raycloud.dmj.account.core.platform.service.IShopAuthInfoService;
import com.raycloud.dmj.account.core.platform.service.IShopAuthRecordService;
import com.raycloud.dmj.account.core.rawdata.utils.oss.OSSClientHelper;
import com.raycloud.dmj.account.core.rawdata.utils.oss.OssUtils;
import com.raycloud.dmj.account.enums.DataType;
import com.raycloud.dmj.account.enums.PlatformType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public abstract class AbstractPlatformHandler {

    @Resource
    private IShopAuthRecordService shopAuthRecordService;

    @Resource
    private ISharedDataService sharedDataService;

    @Resource
    private IShopAuthInfoService shopAuthInfoService;

    @Resource
    private RedissonClient redissonClient;

    @Value("${file-path.tmp}")
    private String tmpPath;

    public abstract PlatformType getPlatformType();

    public CreateAuthResponse authHandle(PlatformType platform, Long companyId, Long shopId, String callbackUrl, Map<String, Object> extraData) {
        Long recordId = createShopAuthRecord(platform, companyId, shopId, callbackUrl);
        return CreateAuthResponse.builder()
                .authUrl(getAuthUrl(platform, companyId, shopId, callbackUrl, recordId, extraData))
                .build();
    }

    /**
     *  获取授权链接
     * @return
     */
    public abstract String getAuthUrl(PlatformType platform, Long companyId, Long shopId, String callbackUrl, Long recordId,Map<String, Object> extraData);
    /**
     * 获取授权的信息
     * @param type
     * @param companyId
     * @param shopId
     * @return
     */
    public AuthInfoResponse getAuthInfo(PlatformType type, Long companyId, Long shopId) {
        throw new RuntimeException("该平台暂不支持获取授权信息");
    }

    /**
     *  获取数据
     * @param companyId
     * @param shopId
     * @param billDate
     * @return
     */
    public abstract SharedDataResponse getDataUrl(Long companyId, Long shopId, LocalDate billDate, DataType dataType,String extraData);

    public SharedDataResponse billHandle(PlatformType platform, DataType dataType, Long companyId, Long shopId, LocalDate date) {
        SharedDataInfoDO sharedDataInfo = sharedDataService.getSharedDataInfo(shopId, companyId, dataType.getValue(), date);
        if (sharedDataInfo == null || StringUtils.isBlank(sharedDataInfo.getUrl())) {
            RLock lock = redissonClient.getLock(getSharedDataLock(dataType, companyId, shopId, date));
            try {
                if (lock.tryLock(3, 60, TimeUnit.SECONDS)) {
                    sharedDataInfo = sharedDataService.getSharedDataInfo(shopId, companyId, dataType.getValue(), date);
                    String extraData = sharedDataInfo == null ? null : sharedDataInfo.getExtraData();
                    if (sharedDataInfo == null) {
                        SharedDataResponse response = getDataUrl(companyId, shopId, date, dataType,extraData);
                        if (!response.isSuccess()) {
                            return response;
                        }
                        String objectName = upload2Oss(response.getUrl(), dataType, companyId, shopId, date);
                        sharedDataInfo = insertSharedDataInfo(platform, dataType, companyId, shopId, date, objectName);
                    }
                }
            } catch (InterruptedException e) {
                log.error("get shared data url error", e);
            } finally {
                if (lock.isHeldByCurrentThread()) {
                    lock.unlock();
                }
            }
        }
        return sharedDataInfo == null ?
                SharedDataResponse.error() :
                SharedDataResponse.success(OSSClientHelper.expireUrl(sharedDataInfo.getUrl()));
    }


    public void insertOrUpdatePlatformAuth(Long shopId, Long companyId, String authStatus, String token, LocalDateTime expirationTime, JSONObject extraData) {
        ShopAuthInfoDO save = new ShopAuthInfoDO();
        save.setShopId(shopId);
        save.setCompanyId(companyId);
        save.setPlatformCode(getPlatformType().getValue());
        save.setAuthStatus(authStatus);
        save.setToken(token);
        save.setExtraData(extraData.toJSONString());
        save.setExpirationTime(expirationTime);
        save.setCreated(LocalDateTime.now());
        save.setModified(LocalDateTime.now());
        shopAuthInfoService.insertOrUpdateShopAuthInfo(save);
    }

    /**
     *  文件上传至Oss
     * @param url
     * @param dataType
     * @param companyId
     * @param shopId
     * @param date
     * @return
     */
     public String upload2Oss(String url, DataType dataType, Long companyId, Long shopId, LocalDate date){
         String objectName = OssUtils.getSharedDataObjectName(
                 dataType.getValue() + "_" + companyId + "_" + shopId + "_" + date.toString()
                         + "." + FileTypeEnum.CSV.getFileType()
         );
         OssUtils.uploadByFile(new File(url), objectName, true);
         return objectName;
     }


    private SharedDataInfoDO insertSharedDataInfo(PlatformType platform, DataType dataType, Long companyId, Long shopId, LocalDate date, String objectName) {
        SharedDataInfoDO sharedDataInfo = new SharedDataInfoDO();
        sharedDataInfo.setCompanyId(companyId);
        sharedDataInfo.setShopId(shopId);
        sharedDataInfo.setPlatformCode(platform.getValue());
        sharedDataInfo.setType(dataType.getValue());
        sharedDataInfo.setDate(date);
        sharedDataInfo.setUrl(objectName);
        sharedDataInfo.setCreated(LocalDateTime.now());
        sharedDataInfo.setModified(LocalDateTime.now());
        sharedDataService.insertSharedDataInfo(sharedDataInfo);
        return sharedDataInfo;
    }

    private Long createShopAuthRecord(PlatformType platform, Long companyId, Long shopId, String callbackUrl) {
        ShopAuthRecordDO shopAuthRecordDO = new ShopAuthRecordDO();
        shopAuthRecordDO.setCompanyId(companyId);
        shopAuthRecordDO.setShopId(shopId);
        shopAuthRecordDO.setPlatformCode(platform.getValue());
        shopAuthRecordDO.setCallBackUrl(callbackUrl);
        return shopAuthRecordService.insertShopAuthRecord(shopAuthRecordDO);
    }


    public String getSharedDataLock(DataType type, Long companyId, Long shopId, LocalDate billDate){
        return AlipayConstant.QUERY_SHARED_DATA_LOCK + type.getValue()
                + AlipayConstant.SPLIT_SYMBOL + companyId
                + AlipayConstant.SPLIT_SYMBOL + shopId
                + AlipayConstant.SPLIT_SYMBOL + billDate;
    }

}