package com.raycloud.dmj.account.export.core.processor.base;

import com.raycloud.dmj.domain.account.Staff;

import java.util.Map;

/**
 * 处理链
 * @param <T>
 */
public interface IProcessChain<T> {

    /**
     * 组装处理链
     * @param staff
     * @param parameter
     * @param factory
     * @return
     */
    ProcessorChain<Map<String, Object>> assembleProcessors(Staff staff, T parameter, ProcessorFactory factory);

}
