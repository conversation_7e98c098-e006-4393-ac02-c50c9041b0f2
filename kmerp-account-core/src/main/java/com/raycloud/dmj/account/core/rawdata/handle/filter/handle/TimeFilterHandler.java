package com.raycloud.dmj.account.core.rawdata.handle.filter.handle;

import com.raycloud.dmj.account.core.rawdata.handle.filter.FilterHandler;
import com.raycloud.dmj.account.core.rawdata.handle.filter.param.TimeFilterParam;
import com.raycloud.dmj.account.infra.utils.DateUtils;

/**
 * <AUTHOR>
 */
public class TimeFilterHandler implements FilterHandler<TimeFilterParam> {


    @Override
    public boolean handle(TimeFilterParam param) {
        return !DateUtils.isInRange(param.getTargetTime(), param.getStartTime(), param.getEndTime());
    }
}
