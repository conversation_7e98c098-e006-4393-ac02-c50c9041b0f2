package com.raycloud.dmj.account.core.enums.field;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 平台其他收支流水信息表字段枚举
 */
@Getter
public enum StandardOtherBillFlowInfoFieldEnum {
    ID("id", "主键自增ID"),
    PLATFORM_CODE("platform_code", "平台类型"),
    SHOP_ID("shop_id", "店铺ID"),
    CATEGORY_GROUP_CODE("category_group_code", "分类code"),
    CATEGORY_CODE("category_code", "流水类别code"),
    SUB_CATEGORY_CODE("sub_category_code", "流水子类别code"),
    BILLING_CYCLE("billing_cycle", "平台账期"),
    OCCURRED_AT("occurred_at", "发生时间"),
    INCOME_EXPENSE_DIRECTION("income_expense_direction", "收支方向"),
    AMOUNT("amount", "金额"),
    ORDER_NO("order_no", "关联订单号"),
    REMARK("remark", "备注"),
    COUNTERPARTY_ID("counterparty_id", "收支对象ID"),
    DOC_NO("doc_no", "关联业务单据号"),
    SOURCE("source", "来源方式"),
    BATCH_NO("batch_no", "批次号"),
    BIZ_KEY("biz_key", "唯一键"),
    CREATOR("creator", "创建人"),
    CREATED("created", "创建时间"),
    MODIFIED("modified", "修改时间"),
    DATA_RANGE("data_range", "账期"),
    COMPANY_ID("company_id", "公司ID");

    private final String fieldCode;
    private final String fieldDesc;

    StandardOtherBillFlowInfoFieldEnum(String fieldCode, String fieldDesc) {
        this.fieldCode = fieldCode;
        this.fieldDesc = fieldDesc;
    }


    /**
     * 获取插入字段
     * @return 插入字段
     */
    public static Set<String> getInsertFields() {
        List<StandardOtherBillFlowInfoFieldEnum> filterField = Arrays.asList(
                StandardOtherBillFlowInfoFieldEnum.ID
        );
        return Arrays.stream(StandardOtherBillFlowInfoFieldEnum.values()).filter(x -> !filterField.contains(x)).map(
                x -> x.fieldCode
        ).collect(Collectors.toSet());

    }

    /**
     * 获取所有查询字段
     * @return 查询字段数组
     */
    public static String[] getSelectFields() {
        return new String[]{
                ID.getFieldCode(),
                PLATFORM_CODE.getFieldCode(),
                SHOP_ID.getFieldCode(),
                CATEGORY_CODE.getFieldCode(),
                CATEGORY_CODE.getFieldCode(),
                SUB_CATEGORY_CODE.getFieldCode(),
                BILLING_CYCLE.getFieldCode(),
                OCCURRED_AT.getFieldCode(),
                INCOME_EXPENSE_DIRECTION.getFieldCode(),
                AMOUNT.getFieldCode(),
                ORDER_NO.getFieldCode(),
                REMARK.getFieldCode(),
                COUNTERPARTY_ID.getFieldCode(),
                DOC_NO.getFieldCode(),
                SOURCE.getFieldCode(),
                BATCH_NO.getFieldCode(),
                BIZ_KEY.getFieldCode(),
                CREATOR.getFieldCode(),
                CREATED.getFieldCode(),
                MODIFIED.getFieldCode(),
                COMPANY_ID.getFieldCode()
        };
    }
}
