package com.raycloud.dmj.account.core.enums;


import lombok.Getter;

/**
 * 过滤类型
 *
 * <AUTHOR>
 */

@Getter
public enum FilterTypeEnum {

    //时间过滤
    TIME("TIME", "时间过滤"),
    ;

    /**
     * 编码
     */
    private final String code;

    /**
     * 描述
     */
    private final String desc;


    FilterTypeEnum(String code, String desc){
        this.code = code;
        this.desc = desc;
    }


}
