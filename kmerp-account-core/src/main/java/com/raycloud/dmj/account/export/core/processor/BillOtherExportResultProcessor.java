package com.raycloud.dmj.account.export.core.processor;

import com.raycloud.dmj.account.export.core.parameter.ConsignExpressSQLParameter;
import com.raycloud.dmj.account.export.core.processor.base.AbstractResultProcessor;
import com.raycloud.dmj.account.export.core.processor.base.ProcessorChain;
import com.raycloud.dmj.account.export.core.processor.base.ProcessorFactory;
import com.raycloud.dmj.domain.account.Staff;

import java.util.Map;

public class BillOtherExportResultProcessor extends AbstractResultProcessor<ConsignExpressSQLParameter> {

    @Override
    public ProcessorChain<Map<String, Object>> assembleProcessors(Staff staff, ConsignExpressSQLParameter parameter, ProcessorFactory factory) {
        return factory.create(getProcessorName())
                .chuckSize(1000)
                .parameter(parameter)
                .effectiveColumns(parameter.getEffectiveColumnNames())
                .build();
    }
}
