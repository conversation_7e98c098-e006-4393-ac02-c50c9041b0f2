package com.raycloud.dmj.account.core.common;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class BaseInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 创建时间，默认当前时间
     */
    private Date created;

    /**
     * 更新时间，默认当前时间，更新时自动刷新
     */
    private Date modified;

    /**
     * 公司ID
     */
    private Long companyId;
}
