package com.raycloud.dmj.account.export.core;

import com.raycloud.dmj.account.export.utils.ExportUtils;
import com.raycloud.dmj.account.infra.dubbo.KmerpDubboConfiguration;
import com.raycloud.dmj.account.infra.serialize.HessianSerializer;
import com.raycloud.dmj.account.infra.serialize.JacksonSerializer;
import com.raycloud.dmj.account.infra.utils.IdUtils;
import com.raycloud.dmj.data.chessboard.model.PushResult;
import com.raycloud.dmj.data.chessboard.model.TaskMessage;
import com.raycloud.dmj.domain.account.Staff;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@RequiredArgsConstructor
@Service
public class ExportApplicationService {

    private final KmerpDubboConfiguration kmerpDubboConfiguration;

    /**
     * 创建导出任务
     *
     * @param staff
     * @param taskCode
     * @param name
     * @param parameter
     * @return
     */
    public PushResult createExportTask(Staff staff, String taskCode, String name, Object parameter) {
        Map<String, Object> ext = new HashMap<>();
        return createExportTask(staff, taskCode, name, parameter, ext);
    }

    /**
     * 创建导出任务
     *
     * @param staff
     * @param taskCode
     * @param name
     * @param parameter
     * @return
     */
    public PushResult createExportTask(Staff staff, String taskCode, String name, Object parameter, Long pageId, Object bean) {
        Map<String, Object> ext = new HashMap<>();
        ext.put(ExportUtils.EXT_PARAM_PAGE_ID, pageId);
        ext.put(ExportUtils.EXT_PARAM_REPORT_BEAN_CLASS, bean.getClass().getName());
        return createExportTask(staff, taskCode, name, parameter, ext);
    }

    public PushResult createExportTask(Staff staff, String taskCode, String name, Object parameter, Map<String, Object> extendParameter) {
        TaskMessage message = ExportUtils.builder()
                .staff(staff)
                .taskCode(taskCode)
                .parameter(parameter)
                .autoExcelFileParameter(name)
                .extendParameter(extendParameter)
                .build();

        return kmerpDubboConfiguration.getExportApi()
                .pushTask(
                        JacksonSerializer.writeToString(message),
                        HessianSerializer.serialize(message)
                );
    }

    public PushResult mockExportTask(Staff staff, Integer sleeptime) {
        Map<String, Object> extendParameter = new HashMap<>();
        if (sleeptime != null) {
            extendParameter.put("sleepTime", sleeptime);
        }
        return createExportTask(staff, "dev.export", "模拟导出测试-" + IdUtils.genId(), null, extendParameter);
    }
}
