package com.raycloud.dmj.account.core.base.dao;

import com.raycloud.dmj.account.core.rawdata.domains.FileAnalyzeTableConfigDO;

import java.util.List;

/**
 * Date:  2025/6/16
 *
 * <AUTHOR>
 */
public interface FileAnalyzeTableConfigDao {
    /**
     * 查询文件解析配置表
     */
    List<FileAnalyzeTableConfigDO> queryFileAnalyzeTableConfig(List<Long> configIds);

    /**
     * 查询指定configId下的FileAnalyzeTableConfig记录数量
     * @param configId 配置ID
     * @return 记录数量
     */
    int countByConfigId(Long configId);

    /**
     * 根据配置ID查询表配置列表
     * @param configId 配置ID
     * @return 表配置列表
     */
    List<FileAnalyzeTableConfigDO> queryByConfigId(Long configId);

    /**
     * 根据ID查询单个表配置
     * @param id 表配置ID
     * @return 表配置对象
     */
    FileAnalyzeTableConfigDO queryById(Long id);

    /**
     * 新增表配置
     * @param tableConfig 表配置对象
     * @return 新增记录的主键ID
     */
    Long addFileAnalyzeTableConfig(FileAnalyzeTableConfigDO tableConfig);

    /**
     * 更新表配置
     * @param tableConfig 表配置对象
     * @return 更新影响的行数
     */
    int updateFileAnalyzeTableConfig(FileAnalyzeTableConfigDO tableConfig);
}
