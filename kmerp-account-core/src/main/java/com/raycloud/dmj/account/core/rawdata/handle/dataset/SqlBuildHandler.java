package com.raycloud.dmj.account.core.rawdata.handle.dataset;

import com.raycloud.dmj.account.core.rawdata.handle.param.OperateBatchDbInfo;
import org.springframework.data.util.Pair;

import java.util.List;

/**
 * SQL构建处理器
 * <AUTHOR>
 */
public interface SqlBuildHandler {

    /**
     * 构建添加原始数据的SQL
     * @param operateBatchDbInfo 当前数据集配置
     * @return 返回可执行的SQL
     * */
    Pair<String, List<Object[]>> insertBatchSql(OperateBatchDbInfo operateBatchDbInfo);

    /**
     * 删除指定批次的原始数据
     * @param operateBatchDbInfo 当前数据集配置
     * @return 返回可执行的SQL
     */
    Pair<String, Object[]> deleteDataByBatchCode(OperateBatchDbInfo operateBatchDbInfo);
}