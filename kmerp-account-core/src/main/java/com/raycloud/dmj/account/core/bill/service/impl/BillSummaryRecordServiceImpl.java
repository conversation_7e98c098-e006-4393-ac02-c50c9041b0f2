package com.raycloud.dmj.account.core.bill.service.impl;

import com.raycloud.dmj.account.core.base.dao.*;
import com.raycloud.dmj.account.core.base.domain.BillSummaryRecordDO;
import com.raycloud.dmj.account.core.base.domain.ShopInfoDO;
import com.raycloud.dmj.account.core.bill.params.BillSummaryRecordParam;
import com.raycloud.dmj.account.core.bill.request.BillSummaryRecordRequest;
import com.raycloud.dmj.account.core.bill.service.IBillSummaryRecordService;
import com.raycloud.dmj.account.core.bill.vo.BillInfoVo;
import com.raycloud.dmj.account.core.bill.vo.BillSummaryRecordVO;
import com.raycloud.dmj.account.core.bill.vo.BillSummaryVo;
import com.raycloud.dmj.account.core.cleancategory.domain.object.*;
import com.raycloud.dmj.account.core.cleancategory.service.CategoryManageService;
import com.raycloud.dmj.account.core.enums.DateTypeEnum;
import com.raycloud.dmj.account.core.rawdata.utils.RawDataDateUtil;
import com.raycloud.dmj.account.core.shop.req.ShopInfoRequest;
import com.raycloud.dmj.account.infra.common.BusinessException;
import com.raycloud.dmj.account.infra.common.ErrorCodeEnum;
import com.raycloud.dmj.account.infra.session.AccountUser;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import com.raycloud.dmj.account.infra.utils.DateUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 资金流水合计记录服务实现
 * <AUTHOR>
 */
@Service
public class BillSummaryRecordServiceImpl implements IBillSummaryRecordService {

    protected final Logger logger = Logger.getLogger(this.getClass());

    @Resource
    private BillSummaryRecordDao billSummaryRecordDao;
    @Resource
    private ShopInfoDao shopInfoDao;
    @Resource
    private CategoryManageService categoryManageService;
    @Resource
    public FundAccountDao accountDao;
    @Resource
    private StandardFundBillFlowInfoDao standardFundBillFlowInfoDao;
    @Resource
    private BalanceRecordDao balanceRecordDao;

    @Override
    public Long addBillSummaryRecord(BillSummaryRecordDO billSummaryRecordDO, AccountUser accountUser) {
        // 参数校验
        AsserUtils.notNull(accountUser, "用户不能为空");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空");
        AsserUtils.notNull(billSummaryRecordDO, "资金流水合计记录不能为空");
        AsserUtils.notNull(billSummaryRecordDO.getAccountId(), "资金账户ID不能为空");
        AsserUtils.notNull(billSummaryRecordDO.getCategoryId(), "流水类别ID不能为空");
        AsserUtils.notNull(billSummaryRecordDO.getSubCategoryId(), "流水子类别ID不能为空");
        AsserUtils.notNull(billSummaryRecordDO.getShopId(), "店铺ID不能为空");
        AsserUtils.notNull(billSummaryRecordDO.getBillingCycle(), "帐期不能为空");
        AsserUtils.notNull(billSummaryRecordDO.getAmount(), "金额不能为空");
        
        // 设置公司ID
        billSummaryRecordDO.setCompanyId(accountUser.getCompanyId());
        
        // 设置创建时间和修改时间
        Date now = new Date();
        billSummaryRecordDO.setCreated(now);
        billSummaryRecordDO.setModified(now);
        
        // 设置启用状态，默认为启用
        if (billSummaryRecordDO.getEnableStatus() == null) {
            billSummaryRecordDO.setEnableStatus(1);
        }
        
        // 执行新增操作
        return billSummaryRecordDao.insert(billSummaryRecordDO);
    }

    @Override
    public List<BillSummaryRecordVO> getBillSummaryRecordList(BillSummaryRecordRequest request, AccountUser accountUser) {
        // 参数校验
        validateRequestParams(request, accountUser);

        BillSummaryRecordParam param = covertParam(request);

        // 设置默认值
        setDefaultRequestParams(param);

        // 获取资金账户信息
        List<FundAccountDO> fundAccountDOList = getFundAccountList(request, accountUser);
        if (CollectionUtils.isEmpty(fundAccountDOList)) {
            return new ArrayList<>();
        }

        // 获取资金账户ID集合并设置到请求中
        List<Long> accountIdList = fundAccountDOList.stream()
                .map(FundAccountDO::getId)
                .collect(Collectors.toList());
        request.setAccountIdList(accountIdList);

        // 并行查询账单汇总和余额数据
        List<BillSummaryRecordDO> billSummaryRecordDOList = billSummaryRecordDao.queryByParam(param, accountUser.getCompanyId());
        List<BalanceRecordDO> balanceRecordDOList = balanceRecordDao.listByFundAccountIds(
                accountIdList, accountUser.getCompanyId(), param.getStartTime(), param.getEndTime());

        // 数据校验
        if (CollectionUtils.isEmpty(billSummaryRecordDOList)) {
            return new ArrayList<>();
        }
        if (CollectionUtils.isEmpty(balanceRecordDOList)) {
            throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR, "未查询到期初期末余额信息");
        }

        // 按账户类型分组处理数据
        return processDataByAccountType(fundAccountDOList, billSummaryRecordDOList, balanceRecordDOList, param, accountUser);
    }

    /**
     * 转化请求参数
     * @param request 请求参数
     * @return 转化后的参数
     */
    private BillSummaryRecordParam covertParam(BillSummaryRecordRequest request) {
        BillSummaryRecordParam param = new BillSummaryRecordParam();
        param.setAccountIdList(request.getAccountIdList());
        param.setCategoryIdList(request.getCategoryIdList());
        param.setSubCategoryIdList(request.getSubCategoryIdList());
        param.setShopId(request.getShopId());
        if (request.getStartTime() != null && request.getEndTime() != null) {
            DateTypeEnum dateTypeEnum = DateTypeEnum.of(request.getBillingCycleType());
            if (dateTypeEnum == null) {
                throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR,"时间类型异常");
            }
            param.setStartTime(DateUtils.parse(request.getStartTime(),dateTypeEnum.getPatter()));
            param.setEndTime(DateUtils.getStartOfNextDay(DateUtils.parse(request.getEndTime(),dateTypeEnum.getPatter()),dateTypeEnum.getCode()));
        }
        param.setBillingCycleType(request.getBillingCycleType());
        param.setPlatformCode(request.getPlatformCode());
        param.setShopId(request.getShopId());
        return param;
    }

    /**
     * 参数校验
     */
    private void validateRequestParams(BillSummaryRecordRequest request, AccountUser accountUser) {
        AsserUtils.notNull(accountUser, "用户不能为空");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空");
        AsserUtils.notNull(request, "查询参数不能为空");
        AsserUtils.hasText(request.getPlatformCode(), "请选择平台");
    }

    /**
     * 设置默认请求参数
     */
    private void setDefaultRequestParams(BillSummaryRecordParam param) {
        if (param.getBillingCycleType() == null) {
            param.setBillingCycleType(DateTypeEnum.DAY.getCode());
            param.setStartTime(DateUtils.getDayStartTime(DateUtils.getOffsetDay(new Date(), -30)));
            param.setEndTime(DateUtils.getStartOfNextDay(new Date(),DateTypeEnum.DAY.getCode()));
        }
    }

    /**
     * 获取资金账户列表
     */
    private List<FundAccountDO> getFundAccountList(BillSummaryRecordRequest request, AccountUser accountUser) {
        if (CollectionUtils.isNotEmpty(request.getAccountIdList())) {
            return accountDao.queryByIds(accountUser.getCompanyId(), new HashSet<>(request.getAccountIdList()));
        }

        // 根据平台查询店铺信息
        ShopInfoRequest shopInfoRequest = ShopInfoRequest.builder()
                .idList(Collections.singletonList(request.getShopId()))
                .platformCode(request.getPlatformCode())
                .build();
        List<ShopInfoDO> shopInfoDOList = shopInfoDao.getShopList(shopInfoRequest, accountUser.getCompanyId());
        AsserUtils.notEmpty(shopInfoDOList, "该平台下不存在店铺信息");

        List<Long> shopIdList = shopInfoDOList.stream()
                .map(ShopInfoDO::getId)
                .collect(Collectors.toList());
        return accountDao.queryByShopIdList(shopIdList, accountUser.getCompanyId());
    }

    /**
     * 按账户类型分组处理数据
     */
    private List<BillSummaryRecordVO> processDataByAccountType(List<FundAccountDO> fundAccountDOList,
                                                               List<BillSummaryRecordDO> billSummaryRecordDOList,
                                                               List<BalanceRecordDO> balanceRecordDOList,
                                                               BillSummaryRecordParam param,
                                                               AccountUser accountUser) {
        // 按账户类型分组
        Map<Integer, List<FundAccountDO>> accountTypeMap = fundAccountDOList.stream()
                .collect(Collectors.groupingBy(FundAccountDO::getType));

        // 预先按账户ID分组数据，提高查询效率
        Map<Long, List<BillSummaryRecordDO>> billSummaryMap = billSummaryRecordDOList.stream()
                .collect(Collectors.groupingBy(BillSummaryRecordDO::getAccountId));
        Map<Long, List<BalanceRecordDO>> balanceMap = balanceRecordDOList.stream()
                .collect(Collectors.groupingBy(BalanceRecordDO::getFundAccountId));

        List<BillSummaryRecordVO> voList = new ArrayList<>();

        // 按账户类型排序处理
        accountTypeMap.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .forEach(entry -> {
                    BillSummaryRecordVO vo = buildBillSummaryRecordVO(
                            entry.getValue(), billSummaryMap, balanceMap, param, accountUser);
                    if (vo != null) {
                        voList.add(vo);
                    }
                });

        return voList;
    }

    /**
     * 构建账单汇总记录VO
     */
    private BillSummaryRecordVO buildBillSummaryRecordVO(List<FundAccountDO> accountDOList,
                                                         Map<Long, List<BillSummaryRecordDO>> billSummaryMap,
                                                         Map<Long, List<BalanceRecordDO>> balanceMap,
                                                         BillSummaryRecordParam param,
                                                         AccountUser accountUser) {
        // 获取账户ID列表
        List<Long> accountIdList = accountDOList.stream()
                .map(FundAccountDO::getId)
                .collect(Collectors.toList());

        // 获取该类型账户的所有账单汇总记录
        List<BillSummaryRecordDO> recordDOList = accountIdList.stream()
                .flatMap(accountId -> billSummaryMap.getOrDefault(accountId, Collections.emptyList()).stream())
                .collect(Collectors.toList());

        // 如果没有账单记录，跳过
        if (CollectionUtils.isEmpty(recordDOList)) {
            return null;
        }

        // 获取该类型账户的所有余额记录
        List<BalanceRecordDO> balanceList = accountIdList.stream()
                .flatMap(accountId -> balanceMap.getOrDefault(accountId, Collections.emptyList()).stream())
                .collect(Collectors.toList());

        // 构建账户名称映射
        Map<Long, String> accountNameMap = accountDOList.stream()
                .collect(Collectors.toMap(FundAccountDO::getId, FundAccountDO::getAccountName));

        // 构建VO对象
        BillSummaryRecordVO vo = new BillSummaryRecordVO();

        // 设置账单汇总信息
        List<BillSummaryVo> summaryVoList = getBillSummaryVoListByTime(
                accountNameMap, recordDOList, accountUser, param.getBillingCycleType(),
                param.getStartTime(), param.getEndTime());
        vo.setBillInfoVoList(summaryVoList);

        // 设置期初期末余额信息
        List<BillInfoVo> beginAmountList = getBeginAmountList(
                balanceList, param.getBillingCycleType(), param.getStartTime(), param.getEndTime());
        List<BillInfoVo> endAmountList = getEndAmountList(
                balanceList, param.getBillingCycleType(), param.getStartTime(), param.getEndTime());

        vo.setBeginAmountList(beginAmountList);
        vo.setEndAmountList(endAmountList);

        return vo;
    }

    /**
     * 获取期初余额列表，按不同时间类型处理
     *
     * @param balanceList      余额记录列表
     * @param billingCycleType 账期类型 1-日 2-月 3-年
     * @param startTime
     * @param endTime
     * @return 期初余额信息列表
     */
    private List<BillInfoVo> getBeginAmountList(List<BalanceRecordDO> balanceList, Integer billingCycleType, Date startTime, Date endTime) {
        //生成时间范围内的所有时间点
        List<String> timeKeys = generateTimeKeys(startTime, endTime, billingCycleType);

        if (CollectionUtils.isEmpty(balanceList)) {
            List<BillInfoVo>  billInfoVoList = new ArrayList<>();
            timeKeys.forEach(timeKey -> {
                billInfoVoList.add(BillInfoVo.builder()
                        .billingCycleStr(timeKey)
                        .amount(BigDecimal.ZERO.stripTrailingZeros())
                        .build());
            });
            return billInfoVoList;
        }

        DateTypeEnum dateType = DateTypeEnum.of(billingCycleType);
        if (dateType == null) {
            throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR,"时间类型异常");
        }

        // 按时间格式化并分组，取期初余额
        Map<String, BigDecimal> timeAmountMap = balanceList.stream()
                .filter(balance -> balance.getDate() != null && balance.getStartBalance() != null)
                .collect(Collectors.groupingBy(
                        balance -> formatBillingCycleDate(balance.getDate(), dateType),
                        Collectors.mapping(
                                BalanceRecordDO::getStartBalance,
                                Collectors.reducing(BigDecimal.ZERO, BigDecimal::add)
                        )
                ));

        //对不存在的日期赋予默认值
        timeKeys.forEach(timeKey -> {
            if (!timeAmountMap.containsKey(timeKey)) {
                timeAmountMap.put(timeKey, BigDecimal.ZERO);
            }
        });

        // 转换为BillInfoVo列表并排序
        return timeAmountMap.entrySet().stream()
                .map(entry -> BillInfoVo.builder()
                        .billingCycleStr(entry.getKey())
                        .amount(entry.getValue().stripTrailingZeros())
                        .build())
                .sorted(Comparator.comparing(BillInfoVo::getBillingCycleStr))
                .collect(Collectors.toList());
    }

    /**
     * 生成时间范围内的所有时间点
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param billingCycleType 账期类型
     * @return 时间点列表
     */
    private static List<String> generateTimeKeys(Date startTime, Date endTime, Integer billingCycleType) {
        List<String> timeKeys = new ArrayList<>();
        if (startTime == null || endTime == null || billingCycleType == null) {
            return timeKeys;
        }

        DateTypeEnum dateType = DateTypeEnum.of(billingCycleType);
        if (dateType == null) {
            return timeKeys;
        }

        SimpleDateFormat formatter;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startTime);

        if (DateTypeEnum.DAY.getCode().equals(billingCycleType)) {
            formatter = new SimpleDateFormat(DateTypeEnum.DAY.getPatter());
            while (!calendar.getTime().after(endTime) && !calendar.getTime().equals(endTime)) {
                timeKeys.add(formatter.format(calendar.getTime()));
                calendar.add(Calendar.DAY_OF_MONTH, 1);
            }
        } else if (DateTypeEnum.MONTH.getCode().equals(billingCycleType)) {
            formatter = new SimpleDateFormat(DateTypeEnum.MONTH.getPatter());
            while (!calendar.getTime().after(endTime) && !calendar.getTime().equals(endTime)) {
                timeKeys.add(formatter.format(calendar.getTime()));
                calendar.add(Calendar.MONTH, 1);
            }
        } else if (DateTypeEnum.YEAR.getCode().equals(billingCycleType)) {
            formatter = new SimpleDateFormat(DateTypeEnum.YEAR.getPatter());
            while (!calendar.getTime().after(endTime) && !calendar.getTime().equals(endTime)) {
                timeKeys.add(formatter.format(calendar.getTime()));
                calendar.add(Calendar.YEAR, 1);
            }
        }

        return timeKeys;
    }

    /**
     * 获取期末余额列表，按不同时间类型处理
     *
     * @param balanceList      余额记录列表
     * @param billingCycleType 账期类型 1-日 2-月 3-年
     * @param startTime
     * @param endTime
     * @return 期末余额信息列表
     */
    private List<BillInfoVo> getEndAmountList(List<BalanceRecordDO> balanceList, Integer billingCycleType, Date startTime, Date endTime) {
        //生成时间范围内的所有时间点
        List<String> timeKeys = generateTimeKeys(startTime, endTime, billingCycleType);

        if (CollectionUtils.isEmpty(balanceList)) {
            List<BillInfoVo>  billInfoVoList = new ArrayList<>();
            timeKeys.forEach(timeKey -> {
                billInfoVoList.add(BillInfoVo.builder()
                        .billingCycleStr(timeKey)
                        .amount(BigDecimal.ZERO.stripTrailingZeros())
                        .build());
            });
            return billInfoVoList;
        }

        DateTypeEnum dateType = DateTypeEnum.of(billingCycleType);
        if (dateType == null) {
            throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR,"时间类型异常");
        }

        // 按时间格式化并分组，取期末余额
        Map<String, BigDecimal> timeAmountMap = balanceList.stream()
                .filter(balance -> balance.getDate() != null && balance.getEndBalance() != null)
                .collect(Collectors.groupingBy(
                        balance -> formatBillingCycleDate(balance.getDate(), dateType),
                        Collectors.mapping(
                                BalanceRecordDO::getEndBalance,
                                Collectors.reducing(BigDecimal.ZERO, BigDecimal::add)
                        )
                ));

        //对不存在的日期赋予默认值
        timeKeys.forEach(timeKey -> {
            if (!timeAmountMap.containsKey(timeKey)) {
                timeAmountMap.put(timeKey, BigDecimal.ZERO);
            }
        });

        // 转换为BillInfoVo列表并排序
        return timeAmountMap.entrySet().stream()
                .map(entry -> BillInfoVo.builder()
                        .billingCycleStr(entry.getKey())
                        .amount(entry.getValue().stripTrailingZeros())
                        .build())
                .sorted(Comparator.comparing(BillInfoVo::getBillingCycleStr))
                .collect(Collectors.toList());
    }

    /**
     * 格式化帐期为YYYY-MM格式
     */
    private String formatBillingCycleDate(Date billingCycle, DateTypeEnum dateType) {
        if (billingCycle == null) {
            return "";
        }
        SimpleDateFormat sdf = new SimpleDateFormat(dateType.getPatter());
        return sdf.format(billingCycle);
    }

    @Override
    public void saveBillSummary(Long accountId, Integer billingCycle,Integer billingCycleType,Long companyId) {
        AsserUtils.notNull(accountId, "资金账户ID不能为空");
        AsserUtils.notNull(billingCycle,"帐期时间不能为空");
        AsserUtils.notNull(billingCycleType,"帐期类型不能为空");
        AsserUtils.notNull(companyId,"公司ID不能为空");

        Pair<Date, Date> dateDatePair = RawDataDateUtil.parseDateRange(String.valueOf(billingCycle), DateTypeEnum.of(billingCycleType));
        List<BillSummaryRecordDO> recordDOList = standardFundBillFlowInfoDao.getBillSummary(accountId,dateDatePair,companyId);
        logger.info(String.format("查询到资金流水合计记录数量: %s",recordDOList == null ? 0 : recordDOList.size()));
        if (CollectionUtils.isNotEmpty(recordDOList)) {
            recordDOList.forEach(item -> {
                item.setCompanyId(companyId);
                item.setBillingCycle(RawDataDateUtil.parseDate(String.valueOf(billingCycle), DateTypeEnum.of(billingCycleType)));
            });
            Integer batchInsert = billSummaryRecordDao.batchInsert(recordDOList);
            logger.info(String.format("保存资金流水合计记录成功，数量: %s",batchInsert));
        }
    }

    /**
     * 按时间维度查询，将资金流水合计记录按账户、类别、子类别分组，并按时间类型汇总金额
     * @param billSummaryRecordDOList 资金流水合计记录列表
     * @return 按时间汇总的账单摘要列表
     */
    private List<BillSummaryVo> getBillSummaryVoListByTime(Map<Long,String> accountNameMap ,List<BillSummaryRecordDO> billSummaryRecordDOList,AccountUser accountUser,Integer billingCycleType,Date startTime,Date endTime) {
        if (CollectionUtils.isEmpty(billSummaryRecordDOList)) {
            return new ArrayList<>();
        }
        //查询分类
        Map<String, CategoryGroupDO> categoryGroupMap = categoryManageService.getCategoryGroupMap(accountUser);
        //查询类别
        Map<Long, CategoryDO> categoryMap = categoryManageService.getCategoryMap(accountUser);
        //查询子类别
        Map<Long, SubCategoryDO> subCategoryMap = categoryManageService.getSubCategoryMap(accountUser);

        //类别ID、子类别ID分组
        Map<String, List<BillSummaryRecordDO>> groupedRecords = billSummaryRecordDOList.stream()
                .collect(Collectors.groupingBy(record -> record.getCategoryId() + "_" + record.getSubCategoryId()
                ));

        List<BillSummaryVo> result = new ArrayList<>();

        for (Map.Entry<String, List<BillSummaryRecordDO>> entry : groupedRecords.entrySet()) {
            List<BillSummaryRecordDO> records = entry.getValue();
            if (CollectionUtils.isEmpty(records)) {
                continue;
            }
            // 取第一条记录获取基本信息
            BillSummaryRecordDO firstRecord = records.get(0);
            BillSummaryVo billSummaryVo = new BillSummaryVo();
            billSummaryVo.setAccountName(accountNameMap.get(firstRecord.getAccountId()));
            // 设置类别名称
            CategoryDO category = categoryMap.get(firstRecord.getCategoryId());
            billSummaryVo.setCategoryName(category != null ? category.getName() : "未知类别");
            // 设置子类别名称
            SubCategoryDO subCategory = subCategoryMap.get(firstRecord.getSubCategoryId());
            billSummaryVo.setSubCategoryName(subCategory != null ? subCategory.getName() : "未知子类别");

            // 设置分类code名称
            if (subCategory != null) {
                CategoryGroupDO categoryGroup = categoryGroupMap.get(subCategory.getCategoryGroupCode());
                billSummaryVo.setCategoryCodeName(categoryGroup != null ? categoryGroup.getName() : "未知分类组");
            } else {
                billSummaryVo.setCategoryCodeName("未知分类组");
            }
            // 按时间汇总金额
            Map<String, BigDecimal> monthlyAmounts = records.stream()
                    .filter(record -> record.getBillingCycle() != null && record.getAmount() != null)
                    .collect(Collectors.groupingBy(
                            record -> formatBillingCycleDate(record.getBillingCycle(),DateTypeEnum.of(billingCycleType)),
                            Collectors.reducing(BigDecimal.ZERO, BillSummaryRecordDO::getAmount, BigDecimal::add)
                    ));

            List<String> timeKeys = generateTimeKeys(startTime, endTime, billingCycleType);

            //对不存在的日期赋予默认值
            timeKeys.forEach(timeKey -> {
                if (!monthlyAmounts.containsKey(timeKey)) {
                    monthlyAmounts.put(timeKey, BigDecimal.ZERO);
                }
            });

            // 转换为BillInfoVo列表
            List<BillInfoVo> billInfoVoList = monthlyAmounts.entrySet().stream()
                    .map(monthEntry -> BillInfoVo.builder()
                            .billingCycleStr(monthEntry.getKey())
                            .amount(monthEntry.getValue().stripTrailingZeros())
                            .build())
                    .sorted(Comparator.comparing(BillInfoVo::getBillingCycleStr))
                    .collect(Collectors.toList());
            billSummaryVo.setBillInfoVoList(billInfoVoList);

            //获取合计金额信息
            BigDecimal totalAmount = monthlyAmounts.values().stream()
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            billSummaryVo.setTotalAmount(totalAmount.stripTrailingZeros());

            result.add(billSummaryVo);
        }

        return result;
    }

}

