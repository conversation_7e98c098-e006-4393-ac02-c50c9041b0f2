package com.raycloud.dmj.account.core.cleancategory.domain.request;

import com.raycloud.dmj.account.core.cleancategory.domain.AnalyzeRuleInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * 添加解析规则请求
 * <AUTHOR>
 */
@Data
public class AddAnalyzeRuleReq implements Serializable {

    /**
     * 平台code
     */
    private String platformCode;

    /**
     * 规则内容
     */
    private List<List<AnalyzeRuleInfo>> analyzeRuleContent;


    /**
     * 子类别ID
     */
    private Long subCategoryId;
}
