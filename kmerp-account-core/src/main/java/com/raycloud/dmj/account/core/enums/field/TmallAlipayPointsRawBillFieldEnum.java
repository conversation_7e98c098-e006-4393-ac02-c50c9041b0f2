package com.raycloud.dmj.account.core.enums.field;

import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Getter
public enum TmallAlipayPointsRawBillFieldEnum {
    ID("id", "自增主键"),
    SORT_TITLE("sort_title", "店铺简称"),
    MONTH("month", "交易发生的月份，格式如202503"),
    ALIPAY_TRANSACTION_ID("alipay_transaction_id", "支付宝交易对应的流水号"),
    MERCHANT_ORDER_ID("merchant_order_id", "商家系统内的订单编号"),
    PRODUCT_NAME("product_name", "所交易商品的名称"),
    ORDER_AMOUNT("order_amount", "订单的金额，单位为元"),
    DEDUCTION_AMOUNT("deduction_amount", "集分宝等抵扣的金额，单位为元"),
    BUSINESS_DATE("business_date", "业务实际发生的日期，格式如********"),
    RECEIVING_ACCOUNT("receiving_account", "收款的账户名称"),
    PAYING_ACCOUNT("paying_account", "付款的账户名称"),
    DOWNLOAD_TIME("download_time", "数据下载的时间"),
    DOWNLOAD_ACCOUNT("download_account", "执行下载操作的账户"),
    BIZ_KEY("biz_key", "唯一键"),
    BATCH_NO("batch_no", "批次号"),
    BATCH_TIME("batch_time", "批次时间 格式如********"),
    COMPANY_ID("company_id", "租户ID"),
    SHOP_ID("shop_id", "店铺ID"),
    CREATED("created", "创建时间");

    /**
     * 字段编码
     */
    private final String fieldCode;

    /**
     * 字段描述
     */
    private final String fieldDesc;

    TmallAlipayPointsRawBillFieldEnum(String fieldCode, String fieldDesc) {
        this.fieldCode = fieldCode;
        this.fieldDesc = fieldDesc;
    }

    /**
     * 获取插入字段
     * @return 插入字段
     */
    public static Set<String> getInsertFields() {
        // 假设ID在插入时通常由数据库自增，所以排除ID字段
        Set<TmallAlipayPointsRawBillFieldEnum> filterSet = Collections.singleton(TmallAlipayPointsRawBillFieldEnum.ID);
        return Arrays.stream(TmallAlipayPointsRawBillFieldEnum.values())
               .filter(x ->!filterSet.contains(x))
               .map(x -> x.fieldCode)
               .collect(Collectors.toSet());
    }
}