package com.raycloud.dmj.account.core.base.dao;

import com.raycloud.dmj.account.core.cleancategory.domain.object.TmallGuaranteeRawBillDataDO;
import com.raycloud.dmj.account.core.common.Page;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface TmallGuaranteeRawBillDataDao{

    /**
     * 根据批次号分页查询微信支付宝账单
     *
     * @param dataRange 账期
     * @return 微信支付宝账单
     */
    List<TmallGuaranteeRawBillDataDO> listPageByDataRange(Long companyId, Long shopId, Integer dataRange, Page page);


    /**
     * 根据批次号查询该批次最早的一条数据
     * @param companyId 公司 ID
     * @param shopId 店铺 ID
     * @param dataRange 账期
     * @return 天猫支付宝账单
     */
    TmallGuaranteeRawBillDataDO getEarliestByDataRange(Long companyId, Long shopId, Integer dataRange);



    /**
     * 根据批次号查询该批次最晚的一条数据
     * @param companyId 公司 ID
     * @param shopId 店铺 ID
     * @param dataRange 账期
     * @return 最晚的一条数据
     */
    TmallGuaranteeRawBillDataDO getLatestByDataRange(Long companyId, Long shopId, Integer dataRange);


}
