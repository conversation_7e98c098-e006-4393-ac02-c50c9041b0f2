package com.raycloud.dmj.account.core.base.dao.impl;

import com.mysql.jdbc.Statement;
import com.raycloud.dmj.account.core.base.dao.FileAnalyzeRecordDao;
import com.raycloud.dmj.account.core.enums.field.FileAnalyzeRecordFieldEnum;
import com.raycloud.dmj.account.core.rawdata.domains.FileAnalyzeRecordDO;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import com.raycloud.dmj.table.api.plus.common.base.SQL;
import com.raycloud.dmj.table.api.plus.common.enums.LinkMode;
import com.raycloud.dmj.table.api.plus.component.$;
import com.raycloud.dmj.table.api.plus.component.column.utils.Columns;
import com.raycloud.dmj.table.api.plus.component.condition.utils.Conditions;
import com.raycloud.dmj.table.api.plus.insert.Inserts;
import com.raycloud.dmj.table.api.plus.query.Queries;
import com.raycloud.dmj.table.api.plus.update.Updates;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * Date:  2025/6/16
 *
 * <AUTHOR>
 */
@Repository
public class FileAnalyzeRecordDaoImpl extends BaseDao implements FileAnalyzeRecordDao {

    private final String tableName = "file_analyze_record";

    /**
     * 新增文件解析记录表
     */
    @Override
    public Long addFileAnalyzeRecord(FileAnalyzeRecordDO fileAnalyzeRecordDO) {
        AsserUtils.notNull(fileAnalyzeRecordDO, "参数不能为空！");
        SQL sql = Inserts.insert()
                .into(tableName)
                .columns(FileAnalyzeRecordFieldEnum.getInsertFields())
                .valueForEntity(fileAnalyzeRecordDO)
                .columnNameCamelToUnderline()
                .toSql();

        // 创建KeyHolder用于获取生成的主键
        KeyHolder keyHolder = new GeneratedKeyHolder();

        // 使用带KeyHolder的update方法
        jdbcTemplate.update(
                connection -> {
                    PreparedStatement ps = connection.prepareStatement(
                            sql.getSqlCode(),
                            Statement.RETURN_GENERATED_KEYS
                    );
                    // 设置参数
                    Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
                    for (int i = 0; i < args.length; i++) {
                        ps.setObject(i + 1, args[i]);
                    }
                    return ps;
                },
                keyHolder
        );

        // 获取生成的主键值
        return keyHolder.getKey().longValue();
    }


    @Override
    public void updateFileAnalyzeStatusById(Long recordId, Integer status, String errorMsg) {
        SQL sql = Updates.create()
                .table(tableName)
                .where(
                        Conditions.and(Columns.toColumn(FileAnalyzeRecordFieldEnum.ID.getFieldCode()), LinkMode.EQUAL, recordId)
                ).update(
                        $.updateKeyValue(FileAnalyzeRecordFieldEnum.MODIFIED.getFieldCode(),new Date()),
                        $.updateKeyValue(FileAnalyzeRecordFieldEnum.ANALYZE_STATUS.getFieldCode(),status),
                        $.updateKeyValue(FileAnalyzeRecordFieldEnum.ERROR_MSG.getFieldCode(),errorMsg)
                )
                .toSql();
        jdbcTemplate.update(sql.getSqlCode(), sql.getArgs().toArray());
    }

    @Override
    public void updateFileCountInfo(Long recordId, String fileCountInfo) {
        SQL sql = Updates.create()
                .table(tableName)
                .where(
                        Conditions.and(Columns.toColumn(FileAnalyzeRecordFieldEnum.ID.getFieldCode()), LinkMode.EQUAL, recordId)
                ).update(
                        $.updateKeyValue(FileAnalyzeRecordFieldEnum.MODIFIED.getFieldCode(),new Date()),
                        $.updateKeyValue(FileAnalyzeRecordFieldEnum.FILE_COUNT_INFO.getFieldCode(),fileCountInfo)
                )
                .toSql();
        jdbcTemplate.update(sql.getSqlCode(), sql.getArgs().toArray());
    }

    /**
     * 查询一直卡在导入中的批次
     * @param analyzeStatus com.raycloud.dmj.account.core.enums.FileAnalyzeStatusEnum#IMPORTING
     * @param time 截止时间
     * @return 批次监控表
     */
    @Override
    public List<FileAnalyzeRecordDO> queryByStatusAndTime(Integer analyzeStatus, Date time) {
        AsserUtils.notNull(time, "time不能为空");
        AsserUtils.notNull(analyzeStatus, "fileAnalyzeStatus不能为空");
        SQL sql = Queries.create()
                .from(tableName)
                .where(
                        Conditions.and(FileAnalyzeRecordFieldEnum.CREATED.getFieldCode(), LinkMode.LESS_THAN, time)
                        , Conditions.and(FileAnalyzeRecordFieldEnum.ANALYZE_STATUS.getFieldCode(), LinkMode.EQUAL, analyzeStatus)
                )
                .select().toSql();

        String sqlCode = sql.getSqlCode();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        // 查询
        return jdbcTemplate.query(sqlCode, new BeanPropertyRowMapper<>(FileAnalyzeRecordDO.class), args);
    }


    /**
     * 根据ID批量更新文件解析记录表状态和错误信息
     * @param recordIds 记录ID
     * @param status  状态
     * @param errorMsg 错误信息
     */
    @Override
    public void batchUpdateStatusById(Set<Long> recordIds, Integer status, String errorMsg) {
        AsserUtils.notNull(recordIds, "recordIds不能为空");
        AsserUtils.notNull(status, "status不能为空");
        AsserUtils.notEmpty(errorMsg, "errorMsg不能为空");
        SQL sql = Updates.create()
                .table(tableName)
                .where(
                        Conditions.and(Columns.toColumn(FileAnalyzeRecordFieldEnum.ID.getFieldCode()), LinkMode.IN, recordIds)
                ).update(
                        $.updateKeyValue(FileAnalyzeRecordFieldEnum.MODIFIED.getFieldCode(),new Date()),
                        $.updateKeyValue(FileAnalyzeRecordFieldEnum.ANALYZE_STATUS.getFieldCode(),status),
                        $.updateKeyValue(FileAnalyzeRecordFieldEnum.ERROR_MSG.getFieldCode(),errorMsg)
                )
                .toSql();

        String sqlCode = sql.getSqlCode();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        jdbcTemplate.update(sqlCode, args);
    }


    @Override
    public void updateMatchingConfigIdsById(Long recordId, String configIds) {
        SQL sql = Updates.create()
                .table(tableName)
                .where(
                        Conditions.and(Columns.toColumn(FileAnalyzeRecordFieldEnum.ID.getFieldCode()), LinkMode.EQUAL, recordId)
                ).update(
                        $.updateKeyValue(FileAnalyzeRecordFieldEnum.MODIFIED.getFieldCode(),new Date()),
                        $.updateKeyValue(FileAnalyzeRecordFieldEnum.MATCHING_CONFIG_IDS.getFieldCode(),configIds)
                )
                .toSql();
        jdbcTemplate.update(sql.getSqlCode(), sql.getArgs().toArray());
    }

    @Override
    public FileAnalyzeRecordDO getById(Long recordId) {
        SQL sql = Queries.create()
                .from(tableName)
                .where(
                        Conditions.and(Columns.toColumn(FileAnalyzeRecordFieldEnum.ID.getFieldCode()), LinkMode.EQUAL, recordId)
                )
                .select(
                )
                .toSql();
        return jdbcTemplate.queryForObject(
                sql.getSqlCode(),
                new BeanPropertyRowMapper<>(FileAnalyzeRecordDO.class),
                sql.getArgs().toArray()
        );
    }

    @Override
    public List<FileAnalyzeRecordDO> listByGroupCode(Long companyId,String groupCode) {
        AsserUtils.notNull(companyId, "companyId不能为空");
        AsserUtils.notEmpty(groupCode, "groupCode不能为空");

        SQL sql = Queries.create()
                .from(tableName)
                .where(
                        Conditions.and(Columns.toColumn(FileAnalyzeRecordFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId),
                        Conditions.and(Columns.toColumn(FileAnalyzeRecordFieldEnum.GROUP_CODE.getFieldCode()), LinkMode.EQUAL, groupCode)
                )
                .select(
                )
                .toSql();

        String sqlCode = sql.getSqlCode();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        // 查询
        return jdbcTemplate.query(sqlCode, new BeanPropertyRowMapper<>(FileAnalyzeRecordDO.class), args);
    }
}
