package com.raycloud.dmj.account.core.enums;

import lombok.Getter;

@Getter
public enum OperatorTypeEnum {

    CONSTANT("constant","常量"),
    VARIABLE("variable","变量"),
    OPERATOR("operator","运算符"),
    PARENTHESIS("parenthesis","括号");

    private final String type;
    private final String desc;

    OperatorTypeEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static OperatorTypeEnum getByType(String type) {
        for (OperatorTypeEnum value : values()) {
            if (value.type.equals(type)) {
                return value;
            }
        }
        return null;
    }


}
