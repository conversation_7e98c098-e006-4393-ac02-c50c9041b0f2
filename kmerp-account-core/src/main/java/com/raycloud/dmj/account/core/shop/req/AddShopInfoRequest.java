package com.raycloud.dmj.account.core.shop.req;

import com.raycloud.dmj.account.core.common.Page;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class AddShopInfoRequest {

    /**
     * 店铺ID，主键自增
     */
    private Long id;

    /**
     * 平台编码
     */
    private String platformCode;

    /**
     * 店铺编码
     */
    private String shopCode;

    /**
     * 店铺名称
     */
    private String title;

    /**
     * 店铺简称
     */
    private String shortTitle;

    /**
     * 所属公司ID
     */
    private Long affiliatedCompanyId;

    /**
     * 期初应收余额
     */
    private BigDecimal amount;

    /**
     * 账户状态 0:关闭对帐 1:开启对帐
     */
    private Integer accountStatus;

    /**
     * 期初时间
     */
    private Date startDate;

    /**
     * 对帐维度类型 1：自然月 2:财务月
     */
    private Integer reconciliationType;

    /**
     * 财务月截止时间(每个月多少号) 示例：1 代表每个月1号，最大28
     */
    private Integer reconciliationDate;

}
