package com.raycloud.dmj.account.export.core.processor.base;

import com.google.common.collect.Lists;
import com.raycloud.dmj.domain.account.Staff;
import lombok.Data;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;

@Data
public class ProcessorChain<T> {

    private ProcessorContext context;

    private List<AbstractDataProcessor<T>> processors = new ArrayList<>();

    public ProcessorChain(ProcessorContext context) {
        this.context = context;
    }

    public void start(Staff staff) {
        // 开始处理
        for (AbstractDataProcessor<T> processor : processors) {
            processor.start(staff, context);
        }
    }

    public void finished(Staff staff) {
        // 完成处理
        for (AbstractDataProcessor<T> processor : processors) {
            processor.finished(staff, context);
        }
    }

    public List<T> process(Staff staff, List<T> data) {

        Integer chuckSize = context.getChuckSize();

        List<T> result = new ArrayList<>();

        if (chuckSize > 0) {
            List<List<T>> partition = Lists.partition(data, chuckSize);
            if (ObjectUtils.isEmpty(partition)) {
                return result;
            }
            for (List<T> batch : partition) {
                List<T> processed = process(staff, context, batch);
                if (ObjectUtils.isEmpty(processed)) {
                    return result;
                }
                result.addAll(processed);
            }
        } else {
            List<T> processed = process(staff, context, data);
            if (ObjectUtils.isEmpty(processed)) {
                return result;
            }
            result.addAll(processed);
        }
        return result;
    }

    private List<T> process(Staff staff, ProcessorContext context, List<T> data) {
        if (ObjectUtils.isEmpty(data)) {
            return data;
        }
        List<T> temp = data;
        for (AbstractDataProcessor<T> processor : processors) {
            try {
                temp = processor.doProcess(staff, context, temp);
            } catch (Throwable throwable) {
                processor.exception(staff, context, throwable);
                throw throwable;
            }
        }
        return temp;
    }

}
