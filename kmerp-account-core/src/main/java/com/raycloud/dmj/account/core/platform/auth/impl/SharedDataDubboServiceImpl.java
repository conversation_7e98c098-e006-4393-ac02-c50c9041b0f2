package com.raycloud.dmj.account.core.platform.auth.impl;

import com.raycloud.dmj.account.ISharedDataDubbo;
import com.raycloud.dmj.account.common.DubboResponse;
import com.raycloud.dmj.account.common.SharedDataResponse;
import com.raycloud.dmj.account.common.ShopBillUrlRequest;
import com.raycloud.dmj.account.core.platform.auth.AbstractPlatformHandler;
import com.raycloud.dmj.account.core.platform.auth.AuthHandlerFactory;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

@DubboService
public class SharedDataDubboServiceImpl implements ISharedDataDubbo {

    @Resource
    private AuthHandlerFactory authHandlerFactory;

    @Override
    public DubboResponse<SharedDataResponse> getDataUrl(ShopBillUrlRequest request) {
        AbstractPlatformHandler authHandler = authHandlerFactory.getHandler(request.getPlatformType().getValue());
        return DubboResponse.successOf(authHandler.billHandle(request.getPlatformType(), request.getDataType(), request.getCompanyId(), request.getShopId(), request.getBillDate()));
    }

}
