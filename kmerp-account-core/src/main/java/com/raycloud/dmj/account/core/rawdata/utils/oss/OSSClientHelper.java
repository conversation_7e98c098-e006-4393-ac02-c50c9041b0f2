package com.raycloud.dmj.account.core.rawdata.utils.oss;

import com.aliyun.oss.OSSClient;
import org.apache.commons.lang.StringUtils;

import java.net.URL;
import java.util.Date;


/**
 * <AUTHOR>
 * @date 2023/6/24 下午2:32
 */
public class OSSClientHelper {




    public final static String HTTP = "http://";

    public final static String HTTPS = "https://";
    /**
     * endpoint-张北区域
     */
    public final static String ZB_ENDPOINT = "oss-cn-zhangjiakou.aliyuncs.com";

    /**
     * 内网地址
     */
    public final static String ZB_ENDPOINT_INTERNAL = "oss-cn-zhangjiakou-internal.aliyuncs.com";

    /**
     * BUCKET
     */
    private final static String BUCKET = "wx-erpcrm";

    private final static String TEST_BUCKET = "lingzhi-project";

    public final static String REGION_ID = "cn-zhangjiakou";

    private final static String STS_ROLE_ARN = "acs:ram::****************:role/ramosslingzhiai";

    private final static String TEST_STS_ROLE_ARN = "acs:ram::****************:role/ramosslingzhiaiproject";


    private static OSSClient client = null;

    private static OSSClient pubClient = null;


    public synchronized static OSSClient getOSSClient() {

        if (client != null) {
            return client;
        }
        String url = HTTP + ZB_ENDPOINT_INTERNAL;
            url = HTTP + ZB_ENDPOINT;
        OSSClient ossClient = new OSSClient(url,SecretStoreTempContext.getSecretData(SecretStoreTempContext.accessKeyId) , SecretStoreTempContext.getSecretData(SecretStoreTempContext.accessKeySecret));
        client = ossClient;
        return ossClient;
    }

    public synchronized static void closeOSSClient() {
        if (client != null) {
            client.shutdown();
            client = null;
        }
    }

    /**
     * 公网
     */
    public synchronized static OSSClient getPubOSSClient() {
        if (pubClient != null) {
            return pubClient;
        }
        String url = HTTP + ZB_ENDPOINT;
        OSSClient ossClient = new OSSClient(url, SecretStoreTempContext.accessKeyId, SecretStoreTempContext.accessKeySecret);
        pubClient = ossClient;
        return ossClient;
    }

    public static String getBucket() {
        return BUCKET;
    }

    public static String expireUrl(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return "";
        }
        try {
            OSSClient ossClient = OSSClientHelper.getOSSClient();
            // 设置签名URL过期时间，单位为毫秒。本示例以设置过期时间为1小时为例。
            Date expiration = new Date(new Date().getTime() + 3600 * 1000L*24);
            // 生成以GET方法访问的签名URL，访客可以直接通过浏览器访问相关内容。
            URL url = ossClient.generatePresignedUrl(OSSClientHelper.getBucket(), fileName, expiration);
            return url.toString();
        } catch (Exception e) {
        }
        return "";
    }
}
