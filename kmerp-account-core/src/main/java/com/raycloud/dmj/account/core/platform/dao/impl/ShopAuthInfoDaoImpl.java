package com.raycloud.dmj.account.core.platform.dao.impl;

import com.raycloud.dmj.account.core.base.dao.impl.BaseDao;
import com.raycloud.dmj.account.core.platform.base.domain.ShopAuthInfoDO;
import com.raycloud.dmj.account.core.platform.base.domain.enums.ShopAuthInfoFieldEnum;
import com.raycloud.dmj.account.core.platform.dao.ShopAuthInfoDao;
import com.raycloud.dmj.table.api.plus.common.base.SQL;
import com.raycloud.dmj.table.api.plus.common.enums.LinkMode;
import com.raycloud.dmj.table.api.plus.component.$;
import com.raycloud.dmj.table.api.plus.component.column.utils.Columns;
import com.raycloud.dmj.table.api.plus.component.condition.utils.Conditions;
import com.raycloud.dmj.table.api.plus.insert.Inserts;
import com.raycloud.dmj.table.api.plus.query.Queries;
import com.raycloud.dmj.table.api.plus.update.Updates;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Service;

import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

@Slf4j
@Service
public class ShopAuthInfoDaoImpl extends BaseDao implements ShopAuthInfoDao {

    private static final String TABLE_NAME = "shop_auth_info";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public ShopAuthInfoDO selectByShopIdCompanyIdPlatform(Long shopId, Long companyId, String platformCode) {
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(Conditions.and(Columns.toColumn(ShopAuthInfoFieldEnum.SHOP_ID.getFieldCode()), LinkMode.EQUAL, shopId),
                        Conditions.and(Columns.toColumn(ShopAuthInfoFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId),
                        Conditions.and(Columns.toColumn(ShopAuthInfoFieldEnum.PLATFORM_CODE.getFieldCode()), LinkMode.EQUAL, platformCode))
                .select()
                .toSql();
        List<ShopAuthInfoDO> records = jdbcTemplate.query(
                sql.getSqlCode(),
                new BeanPropertyRowMapper<>(ShopAuthInfoDO.class),
                sql.getArgs().toArray()
        );
        return records.isEmpty() ? null : records.get(0);
    }

    @Override
    public int insertOrUpdate(ShopAuthInfoDO record) {
        ShopAuthInfoDO existing = selectByShopIdCompanyIdPlatform(
                record.getShopId(), record.getCompanyId(), record.getPlatformCode()
        );
        if (existing == null) {
            // 新增
            record.setCreated(LocalDateTime.now());
            record.setModified(LocalDateTime.now());
            SQL sql = Inserts.insert()
                    .into(TABLE_NAME)
                    .columns(ShopAuthInfoFieldEnum.getInsertFields())
                    .valueForEntity(record)
                    .toSql();
            return jdbcTemplate.update(sql.getSqlCode(), sql.getArgs().toArray());
        } else {
            // 更新
            record.setId(existing.getId());
            record.setCreated(existing.getCreated());
            record.setModified(LocalDateTime.now());
            SQL sql = Updates.create()
                    .table(TABLE_NAME)
                    .where(
                            Conditions.and(
                                    Columns.toColumn(ShopAuthInfoFieldEnum.ID.getFieldCode()),
                                    LinkMode.EQUAL,
                                    record.getId()
                            )
                    )
                    .update(
                            $.updateKeyValue(ShopAuthInfoFieldEnum.AUTH_STATUS.getFieldCode(), record.getAuthStatus()),
                            $.updateKeyValue(ShopAuthInfoFieldEnum.TOKEN.getFieldCode(), record.getToken()),
                            $.updateKeyValue(ShopAuthInfoFieldEnum.EXTRA_DATA.getFieldCode(), record.getExtraData()),
                            $.updateKeyValue(ShopAuthInfoFieldEnum.EXPIRATION_TIME.getFieldCode(), record.getExpirationTime()),
                            $.updateKeyValue(ShopAuthInfoFieldEnum.MODIFIED.getFieldCode(), record.getModified())
                    )
                    .toSql();
            return jdbcTemplate.update(sql.getSqlCode(), sql.getArgs().toArray());
        }
    }

}