package com.raycloud.dmj.account.core.platform.service;

import com.raycloud.dmj.account.core.platform.base.domain.ShopAuthInfoDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface IShopAuthInfoService {

    void insertOrUpdateShopAuthInfo(ShopAuthInfoDO shopAuthInfoDO);

    ShopAuthInfoDO getShopAuthInfoByShopIdAndPlatformCode(Long shopId, Long companyId, String platformCode);
}
