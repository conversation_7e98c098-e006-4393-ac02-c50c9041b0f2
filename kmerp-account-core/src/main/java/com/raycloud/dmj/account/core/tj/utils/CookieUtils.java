package com.raycloud.dmj.account.core.tj.utils;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;

/**
 * cookie 处理
 */
public class CookieUtils {


    public static String read(HttpServletRequest request, String key) {

        Cookie[] cookies = request.getCookies();
        String value = null;
        if (cookies == null) {
            return null;
        } else {
            int n = cookies.length;
            for(int i = 0; i < n; i++) {  // 修复数组越界问题：i <= n 改为 i < n
                Cookie cookie = cookies[i];
                if (cookie.getName().equals(key)) {
                    value = cookie.getValue();
                    break;
                }
            }
            return value;
        }
    }
}
