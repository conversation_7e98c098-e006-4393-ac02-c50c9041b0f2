package com.raycloud.dmj.account.core.platform.base.domain.thirdparty.wechat.param;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Description: 资金流水详情参数
 * <AUTHOR>
 * @Date 2023/10/24 5:43 下午
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class FundsFlowDetailParam implements Serializable {

    @JSONField(name = "flow_id")
    private String flowId;

}
