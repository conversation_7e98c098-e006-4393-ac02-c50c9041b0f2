package com.raycloud.dmj.account.core.enums;

import lombok.Getter;

/**
 * 收支方向枚举
 *  1 收入 2 支出
 * <AUTHOR>
 */
@Getter
public enum IncomeExpenseDirectionEnum {

    INCOME(1, "收入"),
    EXPENSE(2, "支出");

    /**
     * 来源编码
     */
    private final Integer code;

    /**
     * 来源描述
     */
    private final String desc;

    IncomeExpenseDirectionEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据来源编码获取枚举实例，方便从数据库值转换为枚举
     * @param sourceCode 来源编码
     * @return 对应的枚举实例，若未匹配到则返回 null
     */
    public static IncomeExpenseDirectionEnum getBySourceCode(Integer sourceCode) {
        for (IncomeExpenseDirectionEnum enumObj : values()) {
            if (enumObj.getCode().equals(sourceCode)) {
                return enumObj;
            }
        }
        return null;
    }

    public static String getDescBySourceCode(Integer sourceCode) {
        for (IncomeExpenseDirectionEnum enumObj : values()) {
            if (enumObj.getCode().equals(sourceCode)) {
                return enumObj.getDesc();
            }
        }
        return "未知";
    }
}