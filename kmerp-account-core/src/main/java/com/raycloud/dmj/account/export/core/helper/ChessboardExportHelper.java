package com.raycloud.dmj.account.export.core.helper;

import com.raycloud.dmj.account.export.common.env.ErpEnvironments;
import com.raycloud.dmj.account.infra.common.AppContextHolder;
import com.raycloud.dmj.account.infra.common.KmerpProperties;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Getter
@RequiredArgsConstructor
@Component
public class ChessboardExportHelper {


    private final KmerpProperties kmerpProperties;

    public ErpEnvironments getEnv() {
       return ErpEnvironments.findByEnvName(kmerpProperties.getEnv());
    }

    public String getChessboardServiceName() {
        return kmerpProperties.getChessboardServiceName();
    }

    public static ErpEnvironments getRunningEnv(){
        ChessboardExportHelper bean = AppContextHolder.getBean(ChessboardExportHelper.class);
        return bean.getEnv();
    }

    public static String getRunningServiceName(){
        ChessboardExportHelper bean = AppContextHolder.getBean(ChessboardExportHelper.class);
        return bean.getChessboardServiceName();
    }

}
