package com.raycloud.dmj.account.core.bill.service;

import com.raycloud.dmj.account.core.base.domain.BillSummaryRecordDO;
import com.raycloud.dmj.account.core.bill.request.BillSummaryRecordRequest;
import com.raycloud.dmj.account.core.bill.vo.BillSummaryRecordVO;
import com.raycloud.dmj.account.infra.session.AccountUser;

import java.util.List;

/**
 * 资金流水合计记录服务接口
 * <AUTHOR>
 */
public interface IBillSummaryRecordService {

    /**
     * 新增资金流水合计记录
     * @param billSummaryRecordDO 资金流水合计记录对象
     * @param accountUser 当前用户
     * @return 新增记录的主键ID
     */
    Long addBillSummaryRecord(BillSummaryRecordDO billSummaryRecordDO, AccountUser accountUser);

    /**
     * 根据参数查询资金流水合计记录列表
     * @param request 查询参数
     * @param accountUser 当前用户
     * @return 资金流水合计记录VO列表
     */
    List<BillSummaryRecordVO> getBillSummaryRecordList(BillSummaryRecordRequest request, AccountUser accountUser);

    /**
     * 监听器计算标准资金流水合计
     * @param accountId
     * @param billingCycle
     */
    void saveBillSummary(Long accountId, Integer billingCycle,Integer billingCycleType,Long companyId);
}
