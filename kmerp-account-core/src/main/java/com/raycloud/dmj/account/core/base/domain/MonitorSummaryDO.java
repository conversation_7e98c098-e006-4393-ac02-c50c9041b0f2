package com.raycloud.dmj.account.core.base.domain;

import com.raycloud.dmj.account.core.enums.RawDataSourceEnum;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Date;

@Data
public class MonitorSummaryDO implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    private Long id;

    /**
     * 记录创建时间
     */
    private Date created;

    /**
     * 记录更新时间
     */
    private Date modified;

    /**
     * 平台code
     */
    private String platformCode;
    /**
     * 关联的公司ID
     */
    private Long companyId;

    /**
     * 关联的店铺ID
     */
    private Long shopId;

    /**
     *  数据源类型 支付宝 微信
     * @see RawDataSourceEnum
     */
    private Integer dataSource;

    /**
     * 导入成功的时间
     */
    private LocalDateTime importSuccessTime;

    /**
     * 解析成功的时间
     */
    private LocalDateTime analyzeSuccessTime;

}
