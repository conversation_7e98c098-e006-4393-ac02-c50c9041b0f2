package com.raycloud.dmj.account.core.platform.base.domain.enums;

import java.util.Arrays;
import java.util.List;

import java.util.Set;
import java.util.stream.Collectors;

import lombok.Getter;

@Getter
public enum SharedDataInfoFieldEnum {

    ID("id", "自增主键"),
    SHOP_ID("shop_id", "店铺ID"),
    COMPANY_ID("company_id", "公司ID"),
    PLATFORM_CODE("platform_code", "授权平台id"),
    TYPE("type", "文件类型"),
    URL("url", "文件路径"),
    DATE("date", "账单的所属时间"),
    EXTRA_DATA("extra_data", "扩展字段"),
    CREATED("created", "创建时间"),
    MODIFIED("modified", "修改时间");

    private final String fieldCode;
    private final String fieldDesc;

    SharedDataInfoFieldEnum(String fieldCode, String fieldDesc) {
        this.fieldCode = fieldCode;
        this.fieldDesc = fieldDesc;
    }

    public static Set<String> getInsertFields() {
        List<SharedDataInfoFieldEnum> filterFields = Arrays.asList(ID);
        return Arrays.stream(values())
                .filter(f -> !filterFields.contains(f))
                .map(f -> f.fieldCode)
                .collect(Collectors.toSet());
    }
}
