package com.raycloud.dmj.account.core.common;

public class ApiResult<T> {

    private T value;
    private boolean success = true;
    private String errorMessage;

    public ApiResult() {
    }

    public static <T> ApiResult<T> success() {
        ApiResult<T> result = new ApiResult<T>();
        result.success = true;
        return result;
    }

    public static <T> ApiResult<T> success(T value) {
        ApiResult<T> result = new ApiResult<T>();
        result.success = true;
        result.setValue(value);
        return result;
    }

    public static <T> ApiResult<T> error(String errorMessage) {
        ApiResult<T> result = new ApiResult<T>();
        result.success = false;
        result.setErrorMessage(errorMessage);
        return result;
    }

    public static <T> ApiResult<T> error() {
        ApiResult<T> result = new ApiResult<T>();
        result.success = false;
        return result;
    }

    public ApiResult<T> value(T value) {
        this.value = value;
        return this;
    }

    public T getValue() {
        return this.value;
    }

    public boolean isSuccess() {
        return this.success;
    }

    public String getErrorMessage() {
        return this.errorMessage;
    }

    public void setValue(T value) {
        this.value = value;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}
