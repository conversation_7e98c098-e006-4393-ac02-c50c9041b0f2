package com.raycloud.dmj.account.core.bill.service.impl;

import com.raycloud.dmj.account.core.base.dao.*;
import com.raycloud.dmj.account.core.base.domain.ShopInfoDO;
import com.raycloud.dmj.account.core.base.domain.StandardOtherBillFlowInfoDO;
import com.raycloud.dmj.account.core.bill.parameter.StandardOtherBillFlowParameter;
import com.raycloud.dmj.account.core.bill.request.AddStandardOtherBillFlowInfoRequest;
import com.raycloud.dmj.account.core.bill.request.StandardOtherBillFlowRequest;
import com.raycloud.dmj.account.core.bill.service.IStandardOtherBillFlowInfoService;
import com.raycloud.dmj.account.core.bill.vo.BillTotalInfo;
import com.raycloud.dmj.account.core.bill.vo.StandardOtherBillFlowInfoVO;
import com.raycloud.dmj.account.core.cleancategory.domain.object.CategoryDO;
import com.raycloud.dmj.account.core.cleancategory.domain.object.CategoryGroupDO;
import com.raycloud.dmj.account.core.cleancategory.domain.object.SubCategoryDO;
import com.raycloud.dmj.account.core.common.PageInfo;
import com.raycloud.dmj.account.core.common.exception.SessionException;
import com.raycloud.dmj.account.core.enums.OtherCategoryEnum;
import com.raycloud.dmj.account.core.enums.OtherCategoryGroupEnum;
import com.raycloud.dmj.account.core.enums.OtherSubCategoryEnum;
import com.raycloud.dmj.account.core.enums.PlatformEnum;
import com.raycloud.dmj.account.export.core.ExportApplicationService;
import com.raycloud.dmj.account.export.sevice.BillOtherExport;
import com.raycloud.dmj.account.infra.common.BusinessException;
import com.raycloud.dmj.account.infra.common.ErrorCodeEnum;
import com.raycloud.dmj.account.infra.session.AccountUser;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import com.raycloud.dmj.account.infra.utils.BeanUtils;
import com.raycloud.dmj.account.infra.utils.DateUtils;
import com.raycloud.dmj.data.chessboard.model.PushResult;
import com.raycloud.dmj.domain.account.Staff;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 平台其他收支流水信息服务实现类
 */
@Slf4j
@Service
public class StandardOtherBillFlowInfoServiceImpl implements IStandardOtherBillFlowInfoService {

    @Resource
    private StandardOtherBillFlowInfoDao standardOtherBillFlowInfoDao;
    @Resource
    private CategoryDao categoryDao;
    @Resource
    private SubCategoryDao subCategoryDao;
    @Resource
    private CategoryGroupDao categoryGroupDao;
    @Resource
    private ShopInfoDao shopInfoDao;
    @Resource
    private ExportApplicationService exportApplicationService;
    @Resource
    private BillOtherExport export;

    @Override
    public Long addStandardOtherBillFlowInfo(AccountUser accountUser, AddStandardOtherBillFlowInfoRequest request) {
        // 参数校验
        AsserUtils.notNull(request, "流水信息不能为空");
        AsserUtils.notEmpty(request.getPlatformCode(), "平台类型不能为空");
        AsserUtils.notEmpty(request.getCategoryGroupCode(), "分类组code不能为空");
        AsserUtils.notEmpty(request.getCategoryCode(), "分类code不能为空");
        AsserUtils.notEmpty(request.getSubCategoryCode(), "子分类code不能为空");
        AsserUtils.notNull(request.getShopId(), "店铺ID不能为空");
        AsserUtils.notNull(request.getOccurredAt(), "发生时间不能为空");
        AsserUtils.notNull(request.getAmount(), "金额不能为空");
        AsserUtils.notNull(request.getIncomeExpenseDirection(), "收支方向不能为空");
        // 获取当前用户信息
        if (accountUser == null || accountUser.getCompanyId() == null) {
            throw new SessionException("无法获取当前用户的公司ID");
        }

        StandardOtherBillFlowInfoDO flowInfo = new StandardOtherBillFlowInfoDO();
        BeanUtils.copyProperties(request, flowInfo);

        // 设置公司ID和创建人
        flowInfo.setCompanyId(accountUser.getCompanyId());
        flowInfo.setCreator(accountUser.getAccountName());
        
        Date now = new Date();
        flowInfo.setCreated(now);
        flowInfo.setModified(now);

        return standardOtherBillFlowInfoDao.addStandardOtherBillFlowInfo(flowInfo);
    }

    @Override
    public List<StandardOtherBillFlowInfoVO> getStandardOtherBillFlowInfoList(AccountUser accountUser, StandardOtherBillFlowRequest request) {
        // 获取当前用户信息
        if (accountUser == null || accountUser.getCompanyId() == null) {
            throw new SessionException("无法获取当前用户的公司ID");
        }

        //判断时间,默认取最近30天的
        if (request.getStartTime() != null && request.getEndTime() != null) {
            request.setStartTime(DateUtils.getDayStartTime(DateUtils.getOffsetDay(new Date(), -30)));
            request.setEndTime(DateUtils.getDayEndTime(new Date()));
        }

        // 查询数据
        List<StandardOtherBillFlowInfoVO> dataList = standardOtherBillFlowInfoDao.getStandardOtherBillFlowInfoList(request, accountUser.getCompanyId());

        if (CollectionUtils.isNotEmpty(dataList)) {
            //设置分类名称
            dataList.forEach(flowInfo -> {
                flowInfo.setClassifyCodeName(OtherCategoryGroupEnum.valueOf(flowInfo.getCategoryGroupCode()).getDesc());
                flowInfo.setCategoryName(OtherCategoryEnum.valueOf(flowInfo.getCategoryCode()).getDesc());
                flowInfo.setSubCategoryName(OtherSubCategoryEnum.valueOf(flowInfo.getSubCategoryCode()).getDesc());
                flowInfo.setAmount(flowInfo.getAmount().stripTrailingZeros());
                flowInfo.setPlatformName(Objects.requireNonNull(PlatformEnum.getPlatformEnumByCode(flowInfo.getPlatformCode())).getTitle());
            });
        }
        return dataList;
    }

    @Override
    public PageInfo<Object> getPageInfo(AccountUser accountUser, StandardOtherBillFlowRequest request) {
        // 获取当前用户信息
        if (accountUser == null || accountUser.getCompanyId() == null) {
            throw new SessionException("无法获取当前用户的公司ID");
        }

        //判断时间,默认取最近30天的
        if (request.getStartTime() != null && request.getEndTime() != null) {
            request.setStartTime(DateUtils.getDayStartTime(DateUtils.getOffsetDay(new Date(), -30)));
            request.setEndTime(DateUtils.getDayEndTime(new Date()));
        }

        //获取分页汇总信息
        BillTotalInfo billTotalInfo = standardOtherBillFlowInfoDao.getTotalInfo(request, accountUser.getCompanyId());
        billTotalInfo.setTotal(billTotalInfo.getTotal() == null ? 0L : billTotalInfo.getTotal());
        billTotalInfo.setTotalAmount(billTotalInfo.getTotalAmount() == null ? BigDecimal.ZERO : billTotalInfo.getTotalAmount().stripTrailingZeros());

        return PageInfo.<Object>builder()
                .pageNo(request.getPageNo())
                .pageSize(request.getPageSize())
                .data(billTotalInfo)
                .total(billTotalInfo.getTotal()).build();
    }

    @Override
    public StandardOtherBillFlowInfoVO getStandardOtherBillFlowInfoById(AccountUser accountUser, Long id) {
        // 参数校验
        Assert.notNull(id, "ID不能为空");
        // 获取当前用户信息
        if (accountUser == null || accountUser.getCompanyId() == null) {
            throw new SessionException("无法获取当前用户的公司ID");
        }
        StandardOtherBillFlowInfoDO flowInfo = standardOtherBillFlowInfoDao.getStandardOtherBillFlowInfoById(id);
        // 数据权限校验：确保查询的数据属于当前用户的公司
        if (flowInfo == null){
            throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR,"补贴流水信息不存在");
        }
        if (!accountUser.getCompanyId().equals(flowInfo.getCompanyId())) {
            throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR,"无权限访问该数据");
        }

        StandardOtherBillFlowInfoVO flowInfoVO = new StandardOtherBillFlowInfoVO();
        BeanUtils.copyProperties(flowInfo, flowInfoVO, "amount");
        flowInfoVO.setAmount(flowInfo.getAmount() != null ? flowInfo.getAmount().stripTrailingZeros() : BigDecimal.ZERO);

        //设置店铺简称
        ShopInfoDO shopInfoDO = shopInfoDao.getShopInfoById(flowInfo.getShopId());
        if (shopInfoDO != null) {
            flowInfoVO.setShopShortTitle(shopInfoDO.getShortTitle());
        }

        return flowInfoVO;
    }

    @Override
    public PushResult export(Staff staff, StandardOtherBillFlowRequest request) {
        AsserUtils.notNull(staff, "用户不能为空");
        AsserUtils.notNull(request, "查询参数不能为空");
        AsserUtils.notNull(request.getPageId(), "页面ID不能为空");

        //判断时间,默认取最近30天的
        if (request.getStartTime() == null && request.getEndTime() == null) {
            request.setStartTime(DateUtils.getDayStartTime(DateUtils.getOffsetDay(new Date(), -30)));
            request.setEndTime(DateUtils.getDayEndTime(new Date()));
        }

        StandardOtherBillFlowParameter parameter = BeanUtils.copy(request, StandardOtherBillFlowParameter.class);
        // 参数转换
        return exportApplicationService.createExportTask(
                staff,
                "bill-other-export",
                "其他收支流水导出",
                parameter,
                request.getPageId(),
                export
        );
    }
}
