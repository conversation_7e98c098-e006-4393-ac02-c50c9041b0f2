package com.raycloud.dmj.account.core.rawdata.domains;

import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 文件分析配置实体类，对应数据库表 file_analyze_config
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileAnalyzeConfigDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 配置ID，主键自增
     */
    private Long id;

    /**
     * 数据类型
     */
    private String dataType;

    /**
     * sheet的类型，1为下标，2为名称
     */
    private Integer sheetType;

    /**
     * 配置对应的sheet
     */
    private String sheet;

    /**
     * 数据合法性过滤、清洗规则
     */
    private String filter;

    /**
     * 表头从第几行开始
     */
    private Integer headerStartIndex;

    /**
     * 表头从第几行结束
     */
    private Integer headerEndIndex;

    /**
     * 表头-业务字段配置
     */
    private String headConfig;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 修改时间，自动更新
     */
    private Date modified;

    /**
     * 启用状态：0弃用，1正常
     */
    private Integer enableStatus;

    /**
     * 版本号
     */
    private Integer version;
}