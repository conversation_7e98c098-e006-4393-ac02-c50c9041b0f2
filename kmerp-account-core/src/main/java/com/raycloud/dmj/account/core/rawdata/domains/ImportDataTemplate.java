package com.raycloud.dmj.account.core.rawdata.domains;

import com.raycloud.dmj.account.core.enums.DateTypeEnum;
import com.raycloud.dmj.account.core.enums.RawDataSourceEnum;
import lombok.Data;

import java.util.Date;
import java.util.Map;

/**
 * 导入数据模版
 */
@Data
public class ImportDataTemplate {


    /**
     * 文件解析记录ID
     */
    private Long recordId;


    /**
     * 用户想要导入的开始日期
     */
    private  Date needStartTime;

    /**
     * 用户想要导入的结束日期
     */
    private Date needEndTime;
    /**
     * company_id
     */
    private Long companyId;
    /**
     * 当前用户id
     */
    private Long staffId;
    /**
     * 当前店铺id
     */
    private Long shopId;
    /**
     * OSS文件地址
     */
    private String ossUrl;
    /**
     * 文件后缀
     */
    private String fileType;
    /**
     * 文件的开始时间
     */
    private Date startDataRange;
    /**
     * 文件的结束时间
     */
    private Date endDataRange;
    /**
     * 当前数据类型ID
     * 匹配解析配置
     */
    private String dataType;
    /**
     * 当前数据类型的时间类型
     * @see DateTypeEnum
     */
    private Integer dateType;
    /**
     * 数据来源
     */
    private Integer channelSource;

    /**
     * 下载时间
     */
    private Date downloadTime;

    /**
     * 同一批文件一组
     */
    private String groupCode;

    /**
     * 下载账户
     */
    private String downloadAccount;

    /**
     * 数据来源
     * @see RawDataSourceEnum
     */
    private Integer dataSource;

    /**
     * 扩展参数
     */
    private Map<String, String> extendParam;

}
