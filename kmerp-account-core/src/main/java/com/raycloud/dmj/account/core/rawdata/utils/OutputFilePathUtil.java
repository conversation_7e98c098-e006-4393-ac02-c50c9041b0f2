package com.raycloud.dmj.account.core.rawdata.utils;

import com.alibaba.excel.util.StringUtils;
import com.raycloud.dmj.account.core.common.constant.SpecialCharacterType;
import com.raycloud.readexcel.constant.CommonConstant;

import java.io.File;
import java.util.UUID;

import static com.raycloud.dmj.account.core.common.constant.OssObjectNameType.ERROR_FILE_NAME;
import static com.raycloud.dmj.account.core.common.constant.SpecialCharacterType.SPECIAL_CHARACTER_UNDERLINE;

public class OutputFilePathUtil {


    public static String getOutputFilePath(String bizType,String fileType){
        return System.getProperty(CommonConstant.tempPath) + UUID.randomUUID().toString() + SPECIAL_CHARACTER_UNDERLINE + bizType + "." + fileType;
    }

    public static String getOutputFolderPath(String userDefinedFolder){
        return System.getProperty(CommonConstant.tempPath) +  userDefinedFolder + SPECIAL_CHARACTER_UNDERLINE + UUID.randomUUID().toString();
    }

    public static String getOutputFileName(String recordStr){
        return ERROR_FILE_NAME + SPECIAL_CHARACTER_UNDERLINE + recordStr + SPECIAL_CHARACTER_UNDERLINE + UUID.randomUUID().toString();
    }
}
