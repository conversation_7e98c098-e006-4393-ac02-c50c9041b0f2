package com.raycloud.dmj.account.core.base.dao;

import com.raycloud.dmj.account.core.base.domain.BillVerifyRuleDO;
import com.raycloud.dmj.account.core.bill.request.BillVerifyRuleRequest;

import java.util.List;

/**
 * 账单核验规则表DAO
 * <AUTHOR>
 */
public interface BillVerifyRuleDao {

    /**
     * 新增账单核验规则
     */
    Long insert(BillVerifyRuleDO billVerifyRuleDO);

    /**
     * 根据ID查询账单核验规则
     */
    BillVerifyRuleDO queryById(Long id, Long companyId);

    /**
     * 根据ID更新账单核验规则
     */
    int updateById(BillVerifyRuleDO billVerifyRuleDO);

    /**
     * 根据ID删除账单核验规则（逻辑删除）
     */
    int deleteById(Long id, Long companyId);

    /**
     * 根据参数查询账单核验规则列表
     */
    List<BillVerifyRuleDO> queryByParam(BillVerifyRuleRequest request, Long companyId);

    /**
     * 根据资金账户ID获取账单核验规则列表
     */
    List<BillVerifyRuleDO> queryListByAccountId(Long accountId, Long companyId);

    /**
     * 修改核验规则启用状态
     * @param idList  规则ID集合
     * @param companyId  公司ID
     */
    int enableRule(List<Long> idList, Long companyId);

    /**
     * 修改核验规则启用状态
     * @param idList  规则ID集合
     * @param companyId  公司ID
     */
    int shutDownRule(List<Long> idList, Long companyId);

    /**
     * 批量删除核验规则
     * @param idList 规则ID集合
     * @param companyId 公司ID
     * @return 删除影响的行数
     */
    int bulkDeletion(List<Long> idList, Long companyId);
}
