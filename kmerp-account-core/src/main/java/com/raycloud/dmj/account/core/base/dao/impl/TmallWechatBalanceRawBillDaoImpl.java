package com.raycloud.dmj.account.core.base.dao.impl;

import com.raycloud.dmj.account.core.base.dao.TmallWechatBalanceRawBillDao;
import com.raycloud.dmj.account.core.cleancategory.domain.object.TmallWechatBalanceRawBillDO;
import com.raycloud.dmj.account.core.enums.field.TmallAlipayRawBillFieldEnum;
import com.raycloud.dmj.account.core.enums.field.TmallWechatBalanceRawBillFieldEnum;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import com.raycloud.dmj.table.api.plus.common.base.SQL;
import com.raycloud.dmj.table.api.plus.common.enums.LinkMode;
import com.raycloud.dmj.table.api.plus.component.column.utils.Columns;
import com.raycloud.dmj.table.api.plus.component.condition.utils.Conditions;
import com.raycloud.dmj.table.api.plus.component.order.utils.Orders;
import com.raycloud.dmj.table.api.plus.query.Queries;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * <AUTHOR>
 */
@Repository
public class TmallWechatBalanceRawBillDaoImpl extends BaseDao implements TmallWechatBalanceRawBillDao {

    private final String TABLE_NAME = "tmall_wechat_balance_raw_bill_data";


    @Override
    public TmallWechatBalanceRawBillDO getByDataRange(Long companyId, Long shopId, Integer dataRange) {
        AsserUtils.notNull(companyId, "公司ID不能为空！");
        AsserUtils.notNull(shopId, "店铺ID不能为空！");
        AsserUtils.notNull(dataRange, "账期余额不能为空！");
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(TmallWechatBalanceRawBillFieldEnum.BATCH_TIME.getFieldCode()), LinkMode.EQUAL, dataRange),
                        Conditions.and(Columns.toColumn(TmallWechatBalanceRawBillFieldEnum.SHOP_ID.getFieldCode()), LinkMode.EQUAL, shopId),
                        Conditions.and(Columns.toColumn(TmallWechatBalanceRawBillFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId)
                )
                .select(
                )
                .limit(1)
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<TmallWechatBalanceRawBillDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(TmallWechatBalanceRawBillDO.class), args);
        return !query.isEmpty() ? query.get(0) : null;
    }
}
