package com.raycloud.dmj.account.core.enums.field;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 标准资金账单流水信息表字段枚举
 */
@Getter
public enum StandardFundBillFlowInfoFieldEnum {

    ID("id", "主键ID"),
    PLATFORM_CODE("platform_code", "平台CODE"),
    SHOP_ID("shop_id", "店铺ID"),
    ACCOUNT_ID("account_id", "资金账户ID"),
    OCCURRED_AT("occurred_at", "发生时间"),
    RULE_ID("rule_id", "数据清洗规则ID"),
    CATEGORY_CODE("category_code", "分类code"),
    CATEGORY_ID("category_id", "流水类别ID"),
    SUB_CATEGORY_ID("sub_category_id", "流水子类别ID"),
    AMOUNT("amount", "金额"),
    INCOME_EXPENSE_DIRECTION("income_expense_direction", "收支方向"),
    BILL_NO("bill_no", "关联账单流水号"),
    ORDER_NO("order_no", "关联订单号"),
    DOC_NO("doc_no", "关联业务单据号"),
    REMARK("remark", "备注"),
    BELONG_ID("belong_id", "关联拆单信息ID"),
    IS_OFFSET("is_offset", "是否可抵消"),
    IS_SPLIT("is_split", "是否已拆分"),
    CONFIRMED("confirmed", "确认状态"),
    SOURCE("source", "来源方式"),
    CREATOR("creator", "创建人"),
    CREATED("created", "创建时间"),
    BATCH_NO("batch_no", "批次号"),
    BIZ_KEY("biz_key", "唯一键"),
    MODIFIED("modified", "更新时间"),
    DATA_RANGE("data_range", "账期"),
    COMPANY_ID("company_id", "公司ID");

    private final String fieldCode;
    private final String fieldDesc;

    StandardFundBillFlowInfoFieldEnum(String fieldCode, String fieldDesc) {
        this.fieldCode = fieldCode;
        this.fieldDesc = fieldDesc;
    }


    /**
     * 获取插入字段
     * @return 插入字段
     */
    public static Set<String> getInsertFields() {
        List<StandardFundBillFlowInfoFieldEnum> filterField = Arrays.asList(
                StandardFundBillFlowInfoFieldEnum.ID
        );
        return Arrays.stream(StandardFundBillFlowInfoFieldEnum.values()).filter(x -> !filterField.contains(x)).map(
                x -> x.fieldCode
        ).collect(Collectors.toSet());

    }

    /**
     * 获取所有查询字段
     * @return 查询字段数组
     */
    public static String[] getSelectFields() {
        return new String[]{
                ID.getFieldCode(),
                PLATFORM_CODE.getFieldCode(),
                SHOP_ID.getFieldCode(),
                ACCOUNT_ID.getFieldCode(),
                OCCURRED_AT.getFieldCode(),
                RULE_ID.getFieldCode(),
                CATEGORY_CODE.getFieldCode(),
                CATEGORY_ID.getFieldCode(),
                SUB_CATEGORY_ID.getFieldCode(),
                AMOUNT.getFieldCode(),
                INCOME_EXPENSE_DIRECTION.getFieldCode(),
                BILL_NO.getFieldCode(),
                ORDER_NO.getFieldCode(),
                DOC_NO.getFieldCode(),
                REMARK.getFieldCode(),
                BELONG_ID.getFieldCode(),
                IS_OFFSET.getFieldCode(),
                CONFIRMED.getFieldCode(),
                SOURCE.getFieldCode(),
                CREATOR.getFieldCode(),
                CREATED.getFieldCode(),
                BATCH_NO.getFieldCode(),
                BIZ_KEY.getFieldCode(),
                MODIFIED.getFieldCode(),
                COMPANY_ID.getFieldCode()
        };
    }
}
