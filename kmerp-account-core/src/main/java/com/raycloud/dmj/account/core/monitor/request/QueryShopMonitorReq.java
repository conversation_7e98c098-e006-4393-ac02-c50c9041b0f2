package com.raycloud.dmj.account.core.monitor.request;

import com.raycloud.dmj.account.core.common.Page;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.domain.PageRequest;

@EqualsAndHashCode(callSuper = true)
@Data
public class QueryShopMonitorReq extends Page {

    /**
     *  平台代码
     */
    private String platformCode;

    private Long shopId;

    private Long companyId;

}
