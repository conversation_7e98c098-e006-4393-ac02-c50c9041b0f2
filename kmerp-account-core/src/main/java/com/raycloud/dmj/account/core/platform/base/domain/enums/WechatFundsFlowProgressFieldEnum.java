package com.raycloud.dmj.account.core.platform.base.domain.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
@Getter
public enum WechatFundsFlowProgressFieldEnum {

    ID("id", "自增主键"),
    SHOP_ID("shop_id", "店铺ID"),
    COMPANY_ID("company_id", "公司ID"),
    NEXT_KEY("next_key", "分页参数"),
    PAGE_NO("page_no", "分页数"),
    OVER_TIME("over_time", "最近一次统计完成时间"),
    OBJECT_NAME("object_name", "暂存文件"),
    CREATED("created", "创建时间"),
    MODIFIED("modified", "修改时间");

    private final String fieldCode;

    private final String fieldDesc;

    WechatFundsFlowProgressFieldEnum(String fieldCode, String fieldDesc) {
        this.fieldCode = fieldCode;
        this.fieldDesc = fieldDesc;
    }

    public static Set<String> getInsertFields() {
        List<WechatFundsFlowProgressFieldEnum> filterFields = Collections.singletonList(ID);
        return Arrays.stream(values())
                .filter(f -> !filterFields.contains(f))
                .map(f -> f.fieldCode)
                .collect(Collectors.toSet());
    }
}
