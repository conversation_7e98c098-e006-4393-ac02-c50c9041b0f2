package com.raycloud.dmj.account.core.cleancategory.domain.object;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 账户信息 DO 类，对应数据库表中账户相关信息
 * <AUTHOR>
 */
@Data
public class FundAccountDO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键 ID
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 修改时间
     */
    private Date modified;

    /**
     * 店铺 ID
     */
    private Long shopId;

    /**
     * 公司 ID
     */
    private Long companyId;

    /**
     * 资金账户名称（支付宝、微信、默认账户）
     */
    private String accountName;

    /**
     * 资金账户类型（1-系统支付宝、2-系统微信）
     * @see com.raycloud.dmj.account.core.enums.AccountTypeEnum
     * 可结合下方枚举类 AccountTypeEnum 使用，限定取值范围
     */
    private Integer type;

    /**
     * 来源 1-系统，2-手动
     */
    private Integer source;

    /**
     * 账户编码
     */
    private String accountCode;

    /**
     * 期初余额
     */
    private BigDecimal startBalance;

    /**
     * 期初时间
     */
    private Date startDate;

    /**
     * 是否确认初期，0-未确认，1-确认
     */
    private Integer confirmStartPeriod;

    /**
     * 是否授权，0-未授权，1-授权
     */
    private Integer authorize;

}