package com.raycloud.dmj.account.core.cleancategory.strategy.fund;

import com.raycloud.dmj.account.core.base.dao.FundAccountDao;
import com.raycloud.dmj.account.core.base.dao.TmallGuaranteeRawBillDataDao;
import com.raycloud.dmj.account.core.base.domain.StandardFundBillFlowInfoDO;
import com.raycloud.dmj.account.core.cleancategory.domain.object.FundAccountDO;
import com.raycloud.dmj.account.core.cleancategory.domain.object.TmallGuaranteeRawBillDataDO;
import com.raycloud.dmj.account.core.cleancategory.strategy.param.CategoryAnalyzeParam;
import com.raycloud.dmj.account.core.common.Page;
import com.raycloud.dmj.account.core.enums.AccountTypeEnum;
import com.raycloud.dmj.account.core.enums.IncomeExpenseDirectionEnum;
import com.raycloud.dmj.account.core.enums.RawDataSourceEnum;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * 保证金分类解析
 *
 * <AUTHOR>
 */
@Component
public class GuaranteeCategoryAnalyzeHandle extends StandardFundCategoryAnalyzeHandle<TmallGuaranteeRawBillDataDO> {


    @Resource
    private TmallGuaranteeRawBillDataDao tmallGuaranteeRawBillDataDao;

    @Resource
    private FundAccountDao fundAccountDao;


    @Override
    protected FundAccountDO getFundAccount(CategoryAnalyzeParam param) {
        //根据查询资金账号类型为支付宝资金账户
        return fundAccountDao.getByShopIdAndType(param.getCompanyId(), param.getShopId(),
                AccountTypeEnum.TM_GUARANTEE.getTypeCode());
    }

    @Override
    protected Pair<BigDecimal, BigDecimal> calculateBalance(CategoryAnalyzeParam param) {
        //查询期初余额
        //查询最早的一天数据
        TmallGuaranteeRawBillDataDO earliestTmallGuaranteeRawBillDataDO = tmallGuaranteeRawBillDataDao.getEarliestByDataRange(param.getCompanyId(),
                param.getShopId(),
                param.getDataRange());
        //最早数据的余额
        BigDecimal accountBalance = earliestTmallGuaranteeRawBillDataDO.getCashBalance();
        //最早数据收支金额
        BigDecimal receiptAmount = earliestTmallGuaranteeRawBillDataDO.getReceiptAmount();
        //期初余额=最早数据的余额-最早数据收支金额
        BigDecimal startBalance = accountBalance.subtract(receiptAmount);
        //查询期末余额
        TmallGuaranteeRawBillDataDO latestTmallGuaranteeRawBillDataDO = tmallGuaranteeRawBillDataDao.getLatestByDataRange(param.getCompanyId(), param.getShopId(), param.getDataRange());
        BigDecimal endBalance = latestTmallGuaranteeRawBillDataDO.getCashBalance();
        return Pair.of(startBalance, endBalance);
    }

    @Override
    protected List<TmallGuaranteeRawBillDataDO> listPageRawData(CategoryAnalyzeParam param, Page page) {
        return tmallGuaranteeRawBillDataDao.listPageByDataRange(
                param.getCompanyId(),
                param.getShopId(),
                param.getDataRange(),
                page
        );
    }



    @Override
    protected void setStandardFundBillFlowInfoDO(StandardFundBillFlowInfoDO standardFundBillFlowInfoDO, TmallGuaranteeRawBillDataDO tmallGuaranteeRawBillDataDO) {
        standardFundBillFlowInfoDO.setOccurredAt(tmallGuaranteeRawBillDataDO.getCompletionTime());
        Integer incomeExpenseDirection;
        if (tmallGuaranteeRawBillDataDO.getReceiptAmount().compareTo(BigDecimal.ZERO) > 0){
            incomeExpenseDirection = IncomeExpenseDirectionEnum.INCOME.getCode();
        }else {
            incomeExpenseDirection = IncomeExpenseDirectionEnum.EXPENSE.getCode();
        }
        standardFundBillFlowInfoDO.setAmount(tmallGuaranteeRawBillDataDO.getReceiptAmount());
        standardFundBillFlowInfoDO.setIncomeExpenseDirection(incomeExpenseDirection);
        standardFundBillFlowInfoDO.setBillNo(tmallGuaranteeRawBillDataDO.getBusinessNumber());
        standardFundBillFlowInfoDO.setOrderNo(tmallGuaranteeRawBillDataDO.getOrderNumber());
//                standardFundBillFlowInfoDO.setDocNo(x.getBizOrderNo());
        standardFundBillFlowInfoDO.setRemark(tmallGuaranteeRawBillDataDO.getRemark());
        standardFundBillFlowInfoDO.setDataRange(tmallGuaranteeRawBillDataDO.getBatchTime());
        standardFundBillFlowInfoDO.setBatchNo(tmallGuaranteeRawBillDataDO.getBatchNo());
        standardFundBillFlowInfoDO.setBizKey(tmallGuaranteeRawBillDataDO.getBizKey());
    }


    @Override
    public boolean accord(RawDataSourceEnum source) {
        return RawDataSourceEnum.GUARANTEE.equals(source);
    }
}
