package com.raycloud.dmj.account.core.rawdata.handle.dataset.handle;

import com.raycloud.dmj.account.core.rawdata.handle.dataset.SqlBuildHandler;
import com.raycloud.dmj.account.core.rawdata.handle.param.OperateBatchDbInfo;
import org.springframework.data.util.Pair;

import java.util.List;


/**
 * mysql实现
 * <AUTHOR>
 */
public class SqlBuildByPG implements SqlBuildHandler {
    @Override
    public Pair<String, List<Object[]>> insertBatchSql(OperateBatchDbInfo operateBatchDbInfo) {
        return null;
    }

    @Override
    public Pair<String, Object[]> deleteDataByBatchCode(OperateBatchDbInfo operateBatchDbInfo) {
        return null;
    }
}
