package com.raycloud.dmj.account.core.cleancategory.domain;

import com.raycloud.dmj.account.core.enums.AnalyzeRuleOperatorEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AnalyzeRuleInfo implements Serializable {

    private static final long serialVersionUID = 1212121L;

    /**
     * 字段编码
     */
    private String fieldCode;

    /**
     * 操作符
     * @see AnalyzeRuleOperatorEnum
     */
    private String operator;

    /**
     * 值
     */
    private List<String> valueList;

}
