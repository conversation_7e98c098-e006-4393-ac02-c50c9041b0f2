package com.raycloud.dmj.account.core.platform.base.domain.thirdparty.wechat;

import com.alibaba.fastjson.JSON;
import com.raycloud.bizlogger.Logger;
import com.raycloud.dmj.account.core.platform.base.domain.thirdparty.wechat.exception.WxSyncReachQuotaException;
import com.raycloud.dmj.account.core.platform.base.domain.thirdparty.wechat.param.FundsFlowDetailParam;
import com.raycloud.dmj.account.core.platform.base.domain.thirdparty.wechat.param.FundsFlowListParam;
import com.raycloud.dmj.account.core.platform.base.domain.thirdparty.wechat.response.FundsFlowDetailResult;
import com.raycloud.dmj.account.core.platform.base.domain.thirdparty.wechat.response.FundsFlowListResult;
import com.raycloud.dmj.account.infra.utils.HttpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.springframework.util.Assert;


/**
 * @Description:
 * <AUTHOR>
 * @Date 2023/10/24 2:13 下午
 */
@Slf4j
public class WxVideoBillApi {

    /**
     * 获取access_token
     */
    private static final String URL_TOKEN = "https://api.weixin.qq.com/cgi-bin/token";

    /**
     * 获取资金流水列表url
     */
    private static final String FUNDS_FLOW_LIST_URL = "https://api.weixin.qq.com/channels/ec/funds/getfundsflowlist?access_token=%s";

    /**
     * 获取资金流水详情url
     */
    private static final String FUNDS_FLOW_DETAIL_URL = "https://api.weixin.qq.com/channels/ec/funds/getfundsflowdetail?access_token=%s";



    public static FundsFlowListResult getFundsFlowList(FundsFlowListParam param, String accessToken) {
        Assert.notNull(accessToken, "accessToken不能为空");
        String url = String.format(FUNDS_FLOW_LIST_URL, accessToken);
        String response = HttpUtils.httpPostJson(url, JSON.toJSONString(param), null, null);
        FundsFlowListResult result = JSON.parseObject(response, FundsFlowListResult.class);
        if (result.isSuccess()) {
            return result;
        }
        log.error("查询资金流水列表失败,param:{}，response:{}", JSON.toJSONString(param), JSON.toJSONString(response));
        if (StringUtils.isNotBlank(result.getErrmsg()) && result.getErrmsg().contains("reach max api daily quota limit")) {
            throw new WxSyncReachQuotaException("查询资金流水列表失败:"+result.getErrmsg());
        } else {
            throw new IllegalArgumentException("查询资金流水列表失败:"+result.getErrmsg());
        }
    }


    public static FundsFlowDetailResult getFundsFlowDetail(FundsFlowDetailParam param, String accessToken) {
        Assert.notNull(accessToken, "accessToken不能为空");
        String url = String.format(FUNDS_FLOW_DETAIL_URL, accessToken);
        String response = HttpUtils.httpPostJson(url, JSON.toJSONString(param), null, null);
        FundsFlowDetailResult result = JSON.parseObject(response, FundsFlowDetailResult.class);
        if (result.isSuccess()) {
            return result;
        }
        log.error("查询资金流水详情失败，error:".concat(response));
        if (StringUtils.isNotBlank(result.getErrmsg()) && result.getErrmsg().contains("reach max api daily quota limit")) {
            throw new WxSyncReachQuotaException("查询资金流水详情失败:"+result.getErrmsg());
        } else {
            throw new IllegalArgumentException("查询资金流水详情失败:"+result.getErrmsg());
        }
    }


}
