package com.raycloud.dmj.account.core.common;

import com.raycloud.dmj.domain.Sort;
import lombok.Builder;
import lombok.Data;

/**
 * 分页信息类
 * 用于封装分页查询的结果信息
 *
 * @param <T> 数据类型
 * <AUTHOR>
 * @version 1.0
 */
@Data
@Builder
public class PageInfo<T> {

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 当前页码
     */
    private Integer pageNo;

    /**
     * 每页大小
     */
    private Integer pageSize;

    /**
     * 数据内容
     */
    private T data;

    /**
     * 排序信息
     */
    private Sort sort;

}
