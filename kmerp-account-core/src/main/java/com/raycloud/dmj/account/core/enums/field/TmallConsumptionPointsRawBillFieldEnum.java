package com.raycloud.dmj.account.core.enums.field;

import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.Set;
import java.util.stream.Collectors;

@Getter
public enum TmallConsumptionPointsRawBillFieldEnum {
    ID("id", "自增主键"),
    SORT_TITLE("sort_title", "店铺简称"),
    BILLING_CYCLE("billing_cycle", "账期，格式如202503"),
    FUND_DIRECTION("fund_direction", "资金方向"),
    MERCHANT_CATEGORY("merchant_category", "账单大类"),
    BUSINESS_CATEGORY("business_category", "业务大类"),
    BUSINESS_SUB_CATEGORY("business_sub_category", "业务小类"),
    BUSINESS_TIME("business_time", "时间"),
    ORDER_SN("order_sn", "订单号"),
    POINTS_COUPON_AMOUNT("points_coupon_amount", "积分类服务金额"),
    ALIPAY_ORDER_SN("alipay_order_sn", "支付宝订单号"),
    REMARK("remark", "备注"),
    SETTLEMENT_CHANNEL("settlement_channel", "结算渠道"),
    TRANSACTION_MAIN_ORDER_SN("transaction_main_order_sn", "交易主订单号"),
    TRANSACTION_SUB_ORDER_SN("transaction_sub_order_sn", "交易子订单号"),
    PRODUCT_ID("product_id", "商品ID"),
    PRODUCT_NAME("product_name", "商品名称"),
    QUANTITY("quantity", "数量"),
    TOTAL_DISCOUNT_AMOUNT("total_discount_amount", "优惠总金额"),
    PLATFORM_CONTRIBUTION_RATIO("platform_contribution_ratio", "平台出资比例"),
    DOWNLOAD_TIME("download_time", "下载时间"),
    DOWNLOAD_ACCOUNT("download_account", "下载账户"),
    BIZ_KEY("biz_key", "唯一键"),
    BATCH_NO("batch_no", "批次号"),
    BATCH_TIME("batch_time", "批次时间 格式如********"),
    COMPANY_ID("company_id", "租户ID"),
    SHOP_ID("shop_id", "店铺ID"),
    CREATED("created", "创建时间");

    /**
     * 字段编码
     */
    private final String fieldCode;
    /**
     * 字段描述
     */
    private final String fieldDesc;

    TmallConsumptionPointsRawBillFieldEnum(String fieldCode, String fieldDesc) {
        this.fieldCode = fieldCode;
        this.fieldDesc = fieldDesc;
    }

    /**
     * 获取插入字段
     * @return 插入字段
     */
    public static Set<String> getInsertFields() {
        Set<TmallConsumptionPointsRawBillFieldEnum> filterSet = Collections.singleton(TmallConsumptionPointsRawBillFieldEnum.ID);
        return Arrays.stream(TmallConsumptionPointsRawBillFieldEnum.values())
               .filter(x ->!filterSet.contains(x))
               .map(x -> x.fieldCode)
               .collect(Collectors.toSet());
    }
}