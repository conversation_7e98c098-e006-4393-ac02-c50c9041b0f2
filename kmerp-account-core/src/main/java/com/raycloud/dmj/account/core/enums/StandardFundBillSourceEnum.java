package com.raycloud.dmj.account.core.enums;

import lombok.Getter;

/**
 * 标准资金账单来源
 *
 * <AUTHOR>
 */
@Getter
public enum StandardFundBillSourceEnum {

    SYSTEM(0, "系统"),
    BILL(1, "账单"),
;
    private final Integer code;
    private final String desc;

    StandardFundBillSourceEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDesByCode(Integer code) {
        for (StandardFundBillSourceEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return "未知";
    }

}