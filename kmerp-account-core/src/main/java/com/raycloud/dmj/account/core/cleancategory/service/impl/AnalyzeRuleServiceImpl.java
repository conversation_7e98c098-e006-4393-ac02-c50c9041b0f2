package com.raycloud.dmj.account.core.cleancategory.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.google.common.collect.Sets;
import com.raycloud.dmj.account.core.base.dao.*;
import com.raycloud.dmj.account.core.base.domain.ShopInfoDO;
import com.raycloud.dmj.account.core.cleancategory.domain.AnalyzeRuleInfo;
import com.raycloud.dmj.account.core.cleancategory.domain.QuerySubCategoryParam;
import com.raycloud.dmj.account.core.cleancategory.domain.object.*;
import com.raycloud.dmj.account.core.cleancategory.domain.request.*;
import com.raycloud.dmj.account.core.cleancategory.domain.vo.BillAgainAnalyzeTaskVO;
import com.raycloud.dmj.account.core.cleancategory.domain.vo.CategoryAnalyzeRuleVO;
import com.raycloud.dmj.account.core.cleancategory.mapstruct.CategoryAnalyzeRuleMapStruct;
import com.raycloud.dmj.account.core.cleancategory.service.AnalyzeRuleService;
import com.raycloud.dmj.account.core.cleancategory.service.CategoryAnalyzeService;
import com.raycloud.dmj.account.core.cleancategory.utils.InitAnalyzeRuleUtils;
import com.raycloud.dmj.account.core.common.Page;
import com.raycloud.dmj.account.core.common.PageListBase;
import com.raycloud.dmj.account.core.common.TreeVO;
import com.raycloud.dmj.account.core.common.constant.Constant;
import com.raycloud.dmj.account.core.common.utils.ThreadPoolUtil;
import com.raycloud.dmj.account.core.enums.*;
import com.raycloud.dmj.account.core.enums.feature.BillAgainAnalyzeTaskFeatureEnum;
import com.raycloud.dmj.account.core.rawdata.utils.RawDataDateUtil;
import com.raycloud.dmj.account.infra.common.BusinessException;
import com.raycloud.dmj.account.infra.common.ErrorCodeEnum;
import com.raycloud.dmj.account.infra.datasource.KmerpDatasourceConfiguration;
import com.raycloud.dmj.account.infra.session.AccountUser;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import com.raycloud.dmj.account.infra.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Slf4j
@Service
public class AnalyzeRuleServiceImpl implements AnalyzeRuleService {


    @Resource
    private KmerpDatasourceConfiguration kmerpDatasourceConfiguration;

    CategoryAnalyzeRuleMapStruct convert = CategoryAnalyzeRuleMapStruct.INSTANCE;

    @Resource
    private SubCategoryDao subCategoryDao;

    @Resource
    private CategoryGroupDao categoryGroupDao;

    @Resource
    private FundAccountDao fundAccountDao;

    @Resource
    private BillAgainAnalyzeTaskDao billAgainAnalyzeTaskDao;

    @Resource
    private ShopInfoDao shopInfoDao;


    @Resource
    private CategoryDao categoryDao;
    @Resource
    private CategoryAnalyzeService categoryAnalyzeService;

    @Resource
    private CategoryAnalyzeRuleDao categoryAnalyzeRuleDao;


    @Override
    public Long addAnalyzeRule(AccountUser accountUser, AddAnalyzeRuleReq req) {
        AsserUtils.notNull(accountUser, "用户不能为空！");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空！");
        AsserUtils.notNull(req, "参数不能为空！");
        AsserUtils.notNull(req.getPlatformCode(), "平台编码不能为空");
        AsserUtils.notNull(req.getSubCategoryId(), "子类别ID不能为空");
        List<SubCategoryDO> subCategoryDOList = subCategoryDao.queryByIds(accountUser.getCompanyId(), Collections.singletonList(req.getSubCategoryId()));
        if (CollectionUtils.isEmpty(subCategoryDOList)) {
            throw new BusinessException(ErrorCodeEnum.SUB_CATEGORY_NOT_EXISTS);
        }
        CategoryAnalyzeRuleDO analyzeRuleDO = convert.toAnalyzeRuleDO(accountUser, req);
        return categoryAnalyzeRuleDao.insert(analyzeRuleDO);
    }

    @Override
    public void editAnalyzeRule(AccountUser accountUser, EditAnalyzeRuleReq req) {
        AsserUtils.notNull(accountUser, "用户不能为空！");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空！");
        AsserUtils.notNull(req, "参数不能为空！");
        AsserUtils.notNull(req.getPlatformCode(), "平台编码不能为空");
        AsserUtils.notNull(req.getSubCategoryId(), "子类别ID不能为空");
        AsserUtils.notNull(req.getId(), "规则ID不能为空！");

        //查询规则是否存在
        CategoryAnalyzeRuleDO categoryAnalyzeRuleDO = categoryAnalyzeRuleDao.queryById(accountUser.getCompanyId(), req.getId());
        if (Objects.isNull(categoryAnalyzeRuleDO)) {
            throw new BusinessException(ErrorCodeEnum.ANALYZE_RULE_NOT_EXISTS);
        }
        List<SubCategoryDO> subCategoryDOList = subCategoryDao.queryByIds(accountUser.getCompanyId(), Collections.singletonList(req.getSubCategoryId()));
        if (CollectionUtils.isEmpty(subCategoryDOList)) {
            throw new BusinessException(ErrorCodeEnum.SUB_CATEGORY_NOT_EXISTS);
        }
        CategoryAnalyzeRuleDO analyzeRuleDO = convert.toAnalyzeRuleDO(accountUser, req);
        kmerpDatasourceConfiguration.doTransaction(Constant.ORIGINAL_DATA_BASE_CONNECTION_KEY, transactionStatus -> {
            try {
                //编辑直接先逻辑删除子类别下所有规则，再添加。目的：保留历史的解析规则，对标准资金流水分类溯源
                categoryAnalyzeRuleDao.logicDeleteBySubCategoryId(accountUser.getCompanyId(), categoryAnalyzeRuleDO.getSubCategoryId());
                categoryAnalyzeRuleDao.insert(analyzeRuleDO);
            } catch (Exception e) {
                transactionStatus.setRollbackOnly();
                throw e;
            }
            return transactionStatus;
        });
    }

    @Override
    public void deleteAnalyzeRule(AccountUser accountUser, DeleteAnalyzeRuleReq req) {
        AsserUtils.notNull(accountUser, "用户不能为空！");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空！");
        AsserUtils.notNull(req, "参数不能为空！");
        AsserUtils.notEmpty(req.getIds(), "ID集合不能为空！");
        categoryAnalyzeRuleDao.logicDelete(accountUser.getCompanyId(), req.getIds());
    }


    @Override
    public PageListBase<CategoryAnalyzeRuleVO> pageQueryAnalyzeRule(AccountUser accountUser, QueryAnalyzeRuleReq req) {
        // 参数校验
        validateParams(accountUser, req);

        // 构建子类别查询参数
        QuerySubCategoryParam querySubParam = buildQuerySubCategoryParam(req);
        Page page = new Page(req.getPageNo(), req.getPageSize());

        // 查询子类别ID列表
        List<Long> subCategoryIds = subCategoryDao.queryIdsByParam(accountUser.getCompanyId(), querySubParam);
        if (CollectionUtils.isEmpty(subCategoryIds)) {
            return new PageListBase<>(Collections.emptyList(), page, 0L);
        }

        // 查询总数并提前判断
        Long total = categoryAnalyzeRuleDao.countBySubCategoryIds(accountUser.getCompanyId(), subCategoryIds);
        if (total <= 0) {
            return new PageListBase<>(Collections.emptyList(), page, 0L);
        }

        // 查询核心数据DO列表
        List<CategoryAnalyzeRuleDO> ruleDOList = categoryAnalyzeRuleDao.pageQueryBySubCategoryIds(
                accountUser.getCompanyId(), subCategoryIds, page);

        // 批量查询关联数据并转换为映射
        Map<Long, SubCategoryDO> subCategoryMap = getSubCategoryMap(accountUser.getCompanyId(), ruleDOList);
        Map<Long, CategoryDO> categoryMap = getCategoryMap(accountUser.getCompanyId(), subCategoryMap);
        Map<String, String> categoryGroupMap = getCategoryGroupMap(accountUser.getCompanyId(), subCategoryMap);
        Map<Long, String> fundAccountMap = getFundAccountMap(accountUser.getCompanyId(), categoryMap);

        // 转换为VO列表
        List<CategoryAnalyzeRuleVO> ruleVOList = ruleDOList.stream()
                .map(ruleDO -> convertToVO(ruleDO, subCategoryMap, categoryMap, categoryGroupMap, fundAccountMap))
                .collect(Collectors.toList());

        // 返回正确的分页结果
        return new PageListBase<>(ruleVOList, page, total);
    }


    @Override
    public Long againAnalyze(AccountUser accountUser, AgainAnalyzeReq req) {
        AsserUtils.notNull(accountUser, "用户不能为空");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空");
        AsserUtils.notNull(req, "参数不能为空");
        AsserUtils.notNull(req.getFundAccountId(), "资金账户ID不能为空");
        AsserUtils.notEmpty(req.getReprocessStartTime(), "重新洗数开始时间不能为空");
        AsserUtils.notEmpty(req.getReprocessEndTime(), "重新洗数结束时间不能为空");
        Date startTime = DateUtils.parse(req.getReprocessStartTime(), "yyyy-MM-dd");
        AsserUtils.notNull(startTime, "重新洗数开始时间格式错误");
        Date endTime = DateUtils.parse(req.getReprocessEndTime(), "yyyy-MM-dd");
        AsserUtils.notNull(endTime, "重新洗数结束时间格式错误");
        if (DateUtils.isAfter(startTime,endTime)) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR, "开始时间不能晚于结束时间！");
        }
        //只能重洗6个月内的数据
        if (DateUtils.isAfter(DateUtils.getOffsetMonth(new Date(), -6),startTime)) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR, "只能重洗6个月内的数据！");
        }
        FundAccountDO fundAccountDO = fundAccountDao.queryById(accountUser.getCompanyId(), req.getFundAccountId());
        if (Objects.isNull(fundAccountDO)) {
            throw new BusinessException(ErrorCodeEnum.FUND_ACCOUNT_NOT_EXISTS);
        }
        RawDataSourceEnum rawDataSourceEnum = RawDataSourceEnum.getDataSourceByAccountTypeCode(fundAccountDO.getType());
        if (Objects.isNull(rawDataSourceEnum)) {
            throw new BusinessException(ErrorCodeEnum.CONFIG_ERROR, "暂不支持该资金账户类型！");
        }
        Long analyzeTaskId = billAgainAnalyzeTaskDao.insert(convert.toBillAgainAnalyzeTaskDO(accountUser, req,startTime,endTime));
        ThreadPoolExecutor categoryAnalyzeThreadPool = ThreadPoolUtil.getCategoryAnalyzeThreadPool();
        categoryAnalyzeThreadPool.execute(
                () -> {
                    categoryAnalyze(accountUser.getCompanyId(), fundAccountDO.getShopId(), rawDataSourceEnum, analyzeTaskId, startTime, endTime);
                }
        );
        return analyzeTaskId;
    }

    /**
     * 分类重新清洗
     * @param companyId 公司ID
     * @param shopId 店铺ID
     * @param rawDataSourceEnum 数据源枚举
     * @param analyzeTaskId 分类重新清洗任务ID
     * @param startTime 分类重新清洗开始时间
     * @param endTime 分类重新清洗结束时间
     */
    private void categoryAnalyze(Long companyId, Long shopId, RawDataSourceEnum rawDataSourceEnum, Long analyzeTaskId, Date startTime, Date endTime) {

        //幂等处理
        BillAgainAnalyzeTaskDO againAnalyzeTaskDO = billAgainAnalyzeTaskDao.getById(companyId, analyzeTaskId);
        if (Objects.isNull(againAnalyzeTaskDO)) {
            throw new BusinessException(ErrorCodeEnum.ANALYZE_TASK_NOT_EXISTS);
        }
        if (AgainAnalyzeStatusEnum.ANALYZE_SUCCESS.getStatus().equals(againAnalyzeTaskDO.getStatus())) {
            return;
        }
        try {
            CategoryAnalyzeReq categoryAnalyzeReq = new CategoryAnalyzeReq();
            categoryAnalyzeReq.setCompanyId(companyId);
            categoryAnalyzeReq.setShopId(shopId);
            categoryAnalyzeReq.setSource(rawDataSourceEnum);
            List<Date> datesBetween = DateUtils.getDatesBetween(startTime, endTime);
            DateTypeEnum dateType = DateTypeEnum.DAY;
            categoryAnalyzeReq.setDateType(dateType);
            for (Date date : datesBetween) {
                Integer dataRange = RawDataDateUtil.formatDate(date, dateType);
                categoryAnalyzeReq.setDataRange(dataRange);
                categoryAnalyzeService.categoryAnalyze(categoryAnalyzeReq);
            }
            //修改状态和扩展字段
            againAnalyzeTaskDO.setStatus(AgainAnalyzeStatusEnum.ANALYZE_SUCCESS.getStatus());
            againAnalyzeTaskDO.setCompletionDate(new Date());
            againAnalyzeTaskDO.putFeature(BillAgainAnalyzeTaskFeatureEnum.ERROR_MESSAGE, null);
            billAgainAnalyzeTaskDao.updateStatusById(againAnalyzeTaskDO);
        } catch (Exception e) {
            againAnalyzeTaskDO.setCompletionDate(null);
            againAnalyzeTaskDO.setStatus(AgainAnalyzeStatusEnum.ANALYZE_FAIL.getStatus());
            againAnalyzeTaskDO.putFeature(BillAgainAnalyzeTaskFeatureEnum.ERROR_MESSAGE, e.getMessage());
            billAgainAnalyzeTaskDao.updateStatusById(againAnalyzeTaskDO);
            throw e;
        }

    }

    @Override
    public List<BillAgainAnalyzeTaskVO> listBillAgainAnalyzeTask(AccountUser accountUser, QueryBillAgainAnalyzeTaskReq req) {
        AsserUtils.notNull(accountUser, "用户不能为空！");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空！");
        List<BillAgainAnalyzeTaskDO> list = billAgainAnalyzeTaskDao.list(accountUser.getCompanyId());
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }
        Set<Long> fundAccountIds = list.stream().map(BillAgainAnalyzeTaskDO::getFundAccountId).collect(Collectors.toSet());
        List<FundAccountDO> fundAccountDOList = fundAccountDao.queryByIds(accountUser.getCompanyId(), fundAccountIds);
        HashMap<Long, String> fundAccountIdToNameMap = Optional.ofNullable(fundAccountDOList)
                .orElse(Collections.emptyList())
                .stream()
                .collect(Collectors.toMap(
                        FundAccountDO::getId, FundAccountDO::getAccountName,
                        (v1, v2) -> v1,
                        HashMap::new
                ));

        return list.stream().map(x -> convert.toBillAgainAnalyzeTaskVO(fundAccountIdToNameMap, x))
                .collect(Collectors.toList());

    }

    @Override
    public List<TreeVO> treeMatchFieldTree(AccountUser accountUser, TreeMatchFieldReq req) {
        AsserUtils.notNull(accountUser, "用户不能为空！");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空！");
        AsserUtils.notNull(req, "参数不能为空！");
        AsserUtils.notNull(req.getFundAccountId(), "资金账户ID不能为空！");
        FundAccountDO fundAccountDO = fundAccountDao.queryById(accountUser.getCompanyId(), req.getFundAccountId());
        if (Objects.isNull(fundAccountDO)) {
            throw new BusinessException(ErrorCodeEnum.FUND_ACCOUNT_NOT_EXISTS);
        }
        Integer type = fundAccountDO.getType();
        AccountTypeEnum byTypeCode = AccountTypeEnum.getByTypeCode(type);
        if (byTypeCode == null) {
            throw new BusinessException(ErrorCodeEnum.FUND_ACCOUNT_NOT_EXISTS, "未找到对应的资金账户类型！");
        }
        switch (byTypeCode) {
            case TM_ALIPAY:
                return AlipayMatchFieldEnum.listTreeVO();
            case TM_GUARANTEE:
                return GuaranteeMatchFieldEnum.listTreeVO();
            case TM_WECHAT:
                return WeChatMatchFieldEnum.listTreeVO();
            default:
                throw new BusinessException(ErrorCodeEnum.FUND_ACCOUNT_NOT_EXISTS, "暂不支持该资金账户类型！");
        }

    }

    @Override
    public List<TreeVO> treeAnalyzeRuleOperator(AccountUser accountUser) {
        AsserUtils.notNull(accountUser, "用户不能为空！");
        return AnalyzeRuleOperatorEnum.listTreeVO();
    }

    @Override
    public void initAnalyzeRule(Long companyId, Long fundAccountId) {
        List<FundAccountDO> fundAccountDOList = fundAccountDao.queryByIds(companyId, Sets.newHashSet(fundAccountId));
        if (CollectionUtils.isEmpty(fundAccountDOList)) {
            log.error("|AnalyzeRuleServiceImpl.initAnalyzeRule error| 找不到资金账户！");
            throw new BusinessException(ErrorCodeEnum.FUND_ACCOUNT_NOT_EXISTS);
        }
        for (FundAccountDO fundAccountDO : fundAccountDOList) {
            ShopInfoDO shopInfoById = shopInfoDao.getShopInfoById(fundAccountDO.getShopId());
            if (Objects.isNull(shopInfoById)) {
                log.error("|AnalyzeRuleServiceImpl.initAnalyzeRule error| 找不到店铺！");
                throw new BusinessException(ErrorCodeEnum.SHOP_NOT_EXISTS);
            }
            Integer type = fundAccountDO.getType();
            List<InitAnalyzeRuleUtils.InitRule> initRules = InitAnalyzeRuleUtils.listInitRuleByTypeCode(type);
            for (InitAnalyzeRuleUtils.InitRule initRule : initRules) {
                List<CategoryGroupDO> categoryGroupDOList = categoryGroupDao.queryByCodes(companyId, Collections.singleton(initRule.getCategoryGroupCode().getCode()));
                if (CollectionUtils.isEmpty(categoryGroupDOList)) {
                    CategoryGroupDO categoryGroupDO = new CategoryGroupDO();
                    categoryGroupDO.setCode(initRule.getCategoryGroupCode().getCode());
                    categoryGroupDO.setName(initRule.getCategoryGroupCode().getDesc());
                    categoryGroupDO.setSource(CategorySourceEnum.SYSTEM.getSourceCode());
                    categoryGroupDO.setCreated(new Date());
                    categoryGroupDO.setModified(new Date());
                    categoryGroupDO.setCompanyId(companyId);
                    //创建分类
                    categoryGroupDao.insert(categoryGroupDO);
                }

                //创建类别
                //同一个资金账户下只一个相同的类别名称
                CategoryDO categoryDO = categoryDao.getByPlatformAndFundIdAndCategoryAndName(
                        companyId,
                        fundAccountId,
                        shopInfoById.getPlatformCode(),
                        initRule.getCategoryName());
                Long categoryId;
                if (Objects.isNull(categoryDO)) {
                    CategoryDO categoryDO1 = new CategoryDO();
                    categoryDO1.setCompanyId(companyId);
                    categoryDO1.setPlatformCode(shopInfoById.getPlatformCode());
                    categoryDO1.setCategoryGroupCode(initRule.getCategoryGroupCode().getCode());
                    categoryDO1.setName(initRule.getCategoryName());
                    categoryDO1.setFundAccountId(fundAccountId);
                    categoryDO1.setSource(CategorySourceEnum.SYSTEM.getSourceCode());
                    categoryDO1.setCreated(new Date());
                    categoryDO1.setModified(new Date());
                    categoryId = categoryDao.insert(categoryDO1);
                } else {
                    categoryId = categoryDO.getId();
                }
                //创建子类目
                SubCategoryDO subCategory = subCategoryDao.getByCategoryIdAndName(companyId, categoryId, initRule.getSubCategoryName());
                Long subCategoryId;
                if (Objects.isNull(subCategory)) {
                    SubCategoryDO subCategoryDO = new SubCategoryDO();
                    subCategoryDO.setCompanyId(companyId);
                    subCategoryDO.setCategoryId(categoryId);
                    subCategoryDO.setName(initRule.getSubCategoryName());
                    subCategoryDO.setModified(new Date());
                    subCategoryDO.setCreated(new Date());
                    subCategoryDO.setPlatformCode(shopInfoById.getPlatformCode());
                    subCategoryDO.setOffset(YesOrNoEnum.YES.getBoolean());

                    subCategoryDO.setCategoryGroupCode(initRule.getCategoryGroupCode().getCode());
                    subCategoryDO.setSource(CategorySourceEnum.SYSTEM.getSourceCode());
                    //todo 系统子类目
                    subCategoryDO.setIncomeExpenseObjectId(1L);
                    subCategoryId = subCategoryDao.insert(subCategoryDO);
                } else {
                    subCategoryId = subCategory.getId();
                }

                //创建规则
                List<CategoryAnalyzeRuleDO> categoryAnalyzeRuleDOList = categoryAnalyzeRuleDao.listBySubCategoryIds(companyId, Collections.singletonList(subCategoryId));
                if (CollectionUtils.isEmpty(categoryAnalyzeRuleDOList)) {
                    CategoryAnalyzeRuleDO categoryAnalyzeRuleDO = new CategoryAnalyzeRuleDO();
                    categoryAnalyzeRuleDO.setSubCategoryId(subCategoryId);
                    categoryAnalyzeRuleDO.setCompanyId(companyId);
                    categoryAnalyzeRuleDO.setEnableStatus(true);
                    categoryAnalyzeRuleDO.setRuleCondition(JSON.toJSONString(initRule.getAnalyzeRuleContent()));
                    categoryAnalyzeRuleDO.setDeleted(false);
                    categoryAnalyzeRuleDO.setModified(new Date());
                    categoryAnalyzeRuleDO.setCreated(new Date());
                    categoryAnalyzeRuleDao.insert(categoryAnalyzeRuleDO);
                }

            }
        }


    }


    /**
     * 参数校验
     */
    private void validateParams(AccountUser accountUser, QueryAnalyzeRuleReq req) {
        AsserUtils.notNull(accountUser, "用户不能为空！");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空！");
        AsserUtils.notNull(req, "参数不能为空！");
        AsserUtils.notEmpty(req.getPlatformCode(), "平台编码不能为空！");
    }

    /**
     * 构建子类别查询参数
     */
    private QuerySubCategoryParam buildQuerySubCategoryParam(QueryAnalyzeRuleReq req) {
        QuerySubCategoryParam param = new QuerySubCategoryParam();
        param.setPlatformCode(req.getPlatformCode());
        param.setCategoryGroupCodes(req.getCategoryGroupCodes());
        param.setCategoryIds(req.getCategoryIds());
        param.setOffset(req.getOffset());
        param.setFundAccountIds(req.getFundAccountIds());
        return param;
    }

    /**
     * 获取子类别映射
     */
    private Map<Long, SubCategoryDO> getSubCategoryMap(Long companyId, List<CategoryAnalyzeRuleDO> ruleDOList) {
        // 提取去重的子类别ID
        List<Long> subCategoryIds = ruleDOList.stream()
                .map(CategoryAnalyzeRuleDO::getSubCategoryId)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(subCategoryIds)) {
            return Collections.emptyMap();
        }

        // 转换为ID->对象映射
        return subCategoryDao.queryByIds(companyId, subCategoryIds).stream()
                .collect(Collectors.toMap(
                        SubCategoryDO::getId,
                        Function.identity(),
                        (v1, v2) -> v1,
                        HashMap::new
                ));
    }

    /**
     * 获取类别映射
     */
    private Map<Long, CategoryDO> getCategoryMap(Long companyId, Map<Long, SubCategoryDO> subCategoryMap) {
        // 提取去重的类别ID
        Set<Long> categoryIds = subCategoryMap.values().stream()
                .map(SubCategoryDO::getCategoryId)
                .collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(categoryIds)) {
            return Collections.emptyMap();
        }

        // 转换为ID->对象映射
        return categoryDao.queryByIds(companyId, categoryIds).stream()
                .collect(Collectors.toMap(
                        CategoryDO::getId,
                        Function.identity(),
                        (v1, v2) -> v1,
                        HashMap::new
                ));
    }

    /**
     * 获取类别组映射
     */
    private Map<String, String> getCategoryGroupMap(Long companyId, Map<Long, SubCategoryDO> subCategoryMap) {
        // 提取去重的类别组编码
        Set<String> groupCodes = subCategoryMap.values().stream()
                .map(SubCategoryDO::getCategoryGroupCode)
                .collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(groupCodes)) {
            return Collections.emptyMap();
        }

        // 转换为编码->名称映射
        return categoryGroupDao.queryByCodes(companyId, groupCodes).stream()
                .collect(Collectors.toMap(
                        CategoryGroupDO::getCode,
                        CategoryGroupDO::getName,
                        (v1, v2) -> v1,
                        HashMap::new
                ));
    }

    /**
     * 获取资金账户映射
     */
    private Map<Long, String> getFundAccountMap(Long companyId, Map<Long, CategoryDO> categoryMap) {
        // 提取去重的资金账户ID
        Set<Long> fundAccountIds = categoryMap.values().stream()
                .map(CategoryDO::getFundAccountId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(fundAccountIds)) {
            return Collections.emptyMap();
        }

        // 转换为ID->名称映射
        return fundAccountDao.queryByIds(companyId, fundAccountIds).stream()
                .collect(Collectors.toMap(
                        FundAccountDO::getId,
                        FundAccountDO::getAccountName,
                        (v1, v2) -> v1,
                        HashMap::new
                ));
    }

    /**
     * 将DO转换为VO
     */
    private CategoryAnalyzeRuleVO convertToVO(CategoryAnalyzeRuleDO ruleDO,
                                              Map<Long, SubCategoryDO> subCategoryMap,
                                              Map<Long, CategoryDO> categoryMap,
                                              Map<String, String> categoryGroupMap,
                                              Map<Long, String> fundAccountMap) {
        CategoryAnalyzeRuleVO vo = new CategoryAnalyzeRuleVO();
        vo.setId(ruleDO.getId());
        vo.setCreated(ruleDO.getCreated());
        vo.setModified(ruleDO.getModified());

        // 处理子类别信息
        SubCategoryDO subCategory = subCategoryMap.get(ruleDO.getSubCategoryId());
        if (subCategory != null) {
            vo.setCategoryGroupCode(subCategory.getCategoryGroupCode());
            vo.setCategoryGroupName(categoryGroupMap.get(subCategory.getCategoryGroupCode()));
            vo.setCategoryId(subCategory.getCategoryId());
            vo.setSubCategoryId(subCategory.getId());
            vo.setSubCategoryName(subCategory.getName());
            // 处理类别信息
            CategoryDO category = categoryMap.get(subCategory.getCategoryId());
            if (category != null) {
                vo.setFundAccountId(category.getFundAccountId());
                vo.setFundAccountName(fundAccountMap.get(category.getFundAccountId()));
                vo.setCategoryName(category.getName());
            }
        }

        // 解析规则条件（建议添加异常处理）
        try {
            List<List<AnalyzeRuleInfo>> ruleContent = JSON.parseObject(
                    ruleDO.getRuleCondition(),
                    new TypeReference<List<List<AnalyzeRuleInfo>>>() {
                    }
            );
            vo.setAnalyzeRuleContent(ruleContent);
        } catch (Exception e) {
            // 可根据业务需求处理解析异常，如日志记录、设置默认值等
            log.error("解析规则条件失败，ruleId:{}", ruleDO.getId(), e);
            vo.setAnalyzeRuleContent(Collections.emptyList());
        }

        return vo;
    }


}
