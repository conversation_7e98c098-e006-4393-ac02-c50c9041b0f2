package com.raycloud.dmj.account.core.rawdata.domains;

import com.raycloud.dmj.account.core.enums.MonitorStatusEnum;
import com.raycloud.dmj.account.core.enums.RawDataSourceEnum;
import com.raycloud.dmj.account.core.enums.field.CategoryAnalyzeStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 文件原始数据监控实体类，对应数据库表 file_original_data_monitor
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileOriginalDataMonitorDO {

    /**
     * 监控记录ID
     */
    private Long id;

    /**
     * 租户ID
     */
    private Long companyId;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 批次号
     */
    private String batchCode;

    /**
     * 当前平台对应的原始数据表名
     */
    private String tableName;

    /**
     * 时间类型，默认为1，1为日，2为月，3为年
     * @see com.raycloud.dmj.account.core.enums.DateTypeEnum
     */
    private Integer dateType;

    /**
     * 当前数据范围
     * 年：2025
     * 月：202501
     * 日：********
     */
    private Integer dataRange;

    /**
     * 分类解析数据记录表 analyze_category_record
     * 当前数据来源的记录ID
     */
    private Long lastRecordId;

    /**
     * 当前数据来源的表配置ID
     */
    private Long lastConfigId;


    /**
     * 数据状态
     * @see MonitorStatusEnum
     */
    private Integer dataStatus;


    /**
     * 原始数据来源
     * @see RawDataSourceEnum
     */
    private Integer dataSource;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 更新时间
     */
    private Date updated;

    /**
     * 版本号
     */
    private Integer version;
}