package com.raycloud.dmj.account.core.pageconfig.service.impl;

import com.raycloud.dmj.account.core.base.dao.PageColumnConfigDao;
import com.raycloud.dmj.account.core.base.domain.PageColumnConfigDO;
import com.raycloud.dmj.account.core.pageconfig.dto.PageColumnConfigDto;
import com.raycloud.dmj.account.core.pageconfig.request.BatchAddPageColumnConfigRequest;
import com.raycloud.dmj.account.core.pageconfig.request.BatchDeletePageColumnConfigRequest;
import com.raycloud.dmj.account.core.pageconfig.request.BatchUpdatePageColumnConfigRequest;
import com.raycloud.dmj.account.core.pageconfig.service.IPageColumnConfigService;
import com.raycloud.dmj.account.core.pageconfig.vo.PageColumnConfigVO;
import com.raycloud.dmj.account.infra.session.AccountUser;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 页面列配置服务实现类
 * <AUTHOR>
 */
@Slf4j
@Service
public class PageColumnConfigServiceImpl implements IPageColumnConfigService {

    @Resource
    private PageColumnConfigDao pageColumnConfigDao;

    @Override
    @Transactional(propagation = Propagation.REQUIRED, isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    public Integer batchAddPageColumnConfig(AccountUser accountUser, BatchAddPageColumnConfigRequest request) {
        // 参数校验
        AsserUtils.notNull(accountUser, "用户信息不能为空");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空");
        AsserUtils.notNull(request, "请求参数不能为空");
        AsserUtils.notNull(request.getPageId(), "页面ID不能为空");
        AsserUtils.notEmpty(request.getConfigList(), "配置列表不能为空");

        // 验证列编码唯一性
        validateColCodeUniqueness(request.getPageId(), request.getConfigList(), accountUser.getCompanyId());

        // 构建DO对象列表
        Date now = new Date();
        List<PageColumnConfigDO> configDOList = request.getConfigList().stream()
                .map(item -> {
                    PageColumnConfigDO configDO = PageColumnConfigDO.builder()
                            .pageId(request.getPageId())
                            .colCode(item.getColCode())
                            .colTitle(item.getColTitle())
                            .width(item.getWidth() != null ? item.getWidth() : 100)
                            .sort(item.getSort())
                            .visible(item.getVisible() != null ? item.getVisible() : 0)
                            .build();

                    configDO.setCreated(now);
                    configDO.setModified(now);
                    configDO.setCompanyId(accountUser.getCompanyId());
                    return configDO;
                })
                .collect(Collectors.toList());

        return pageColumnConfigDao.batchInsert(configDOList);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    public Integer batchUpdatePageColumnConfig(AccountUser accountUser, BatchUpdatePageColumnConfigRequest request) {
        // 参数校验
        AsserUtils.notNull(accountUser, "用户信息不能为空");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空");
        AsserUtils.notNull(request, "请求参数不能为空");
        AsserUtils.notEmpty(request.getConfigList(), "配置列表不能为空");

        // 验证配置是否存在并且属于当前公司
        validateConfigsExist(request.getConfigList(), accountUser.getCompanyId());

        // 验证列编码唯一性（更新时排除自身）
        validateColCodeUniquenessForUpdate(request.getConfigList(), accountUser.getCompanyId());

        // 构建DO对象列表
        Date now = new Date();
        List<PageColumnConfigDO> configDOList = request.getConfigList().stream()
                .map(item -> {
                    PageColumnConfigDO configDO = PageColumnConfigDO.builder()
                            .id(item.getId())
                            .colCode(item.getColCode())
                            .colTitle(item.getColTitle())
                            .width(item.getWidth())
                            .sort(item.getSort())
                            .visible(item.getVisible()).build();
                    configDO.setModified(now);
                    configDO.setCompanyId(accountUser.getCompanyId());
                    return configDO;
                })
                .collect(Collectors.toList());

        return pageColumnConfigDao.batchUpdate(configDOList);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    public Integer batchDeletePageColumnConfig(AccountUser accountUser, BatchDeletePageColumnConfigRequest request) {
        // 参数校验
        AsserUtils.notNull(accountUser, "用户信息不能为空");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空");
        AsserUtils.notNull(request, "请求参数不能为空");
        AsserUtils.notEmpty(request.getIds(), "配置ID列表不能为空");

        return pageColumnConfigDao.batchDelete(request.getIds(), accountUser.getCompanyId());
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, isolation = Isolation.READ_COMMITTED, rollbackFor = Exception.class)
    public Integer deleteByPageId(AccountUser accountUser, Long pageId) {
        // 参数校验
        AsserUtils.notNull(accountUser, "用户信息不能为空");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空");
        AsserUtils.notNull(pageId, "页面ID不能为空");

        return pageColumnConfigDao.deleteByPageId(pageId, accountUser.getCompanyId());
    }

    @Override
    public List<PageColumnConfigVO> queryByPageId(AccountUser accountUser, Long pageId) {
        // 参数校验
        AsserUtils.notNull(accountUser, "用户信息不能为空");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空");
        AsserUtils.notNull(pageId, "页面ID不能为空");

        List<PageColumnConfigDO> configDOList = pageColumnConfigDao.queryByPageId(pageId, accountUser.getCompanyId());
        
        if (CollectionUtils.isEmpty(configDOList)) {
            return Collections.emptyList();
        }

        return configDOList.stream()
                .map(this::convertToVO)
                .collect(Collectors.toList());
    }

    @Override
    public List<PageColumnConfigDto> queryDtoByPageId(Long companyId, Long pageId) {
        // 参数校验
        AsserUtils.notNull(companyId, "公司ID不能为空");
        AsserUtils.notNull(pageId, "页面ID不能为空");

        List<PageColumnConfigDO> configDOList = pageColumnConfigDao.queryByPageId(pageId, companyId);

        if (CollectionUtils.isEmpty(configDOList)) {
            return Collections.emptyList();
        }

        return configDOList.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public PageColumnConfigVO queryById(AccountUser accountUser, Long id) {
        // 参数校验
        AsserUtils.notNull(accountUser, "用户信息不能为空");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空");
        AsserUtils.notNull(id, "配置ID不能为空");

        PageColumnConfigDO configDO = pageColumnConfigDao.queryById(id, accountUser.getCompanyId());
        return configDO != null ? convertToVO(configDO) : null;
    }

    @Override
    public List<String> getEffectiveColumnNames(Long companyId, Long pageId) {
        List<PageColumnConfigDO> configDOList = pageColumnConfigDao.queryByPageId(pageId,companyId);

        if (!CollectionUtils.isEmpty(configDOList)) {
            return configDOList.stream()
                    .filter(item -> item.getVisible() != null && item.getVisible() == 1)
                    .map(PageColumnConfigDO::getColCode)
                    .collect(Collectors.toList());
        }

        return Collections.emptyList();
    }

    /**
     * 验证列编码唯一性
     */
    private void validateColCodeUniqueness(Long pageId, List<BatchAddPageColumnConfigRequest.PageColumnConfigItem> configList, Long companyId) {
        for (BatchAddPageColumnConfigRequest.PageColumnConfigItem item : configList) {
            boolean exists = pageColumnConfigDao.existsByColCode(pageId, item.getColCode(), companyId, null);
            AsserUtils.isTrue(!exists, "列编码[" + item.getColCode() + "]已存在，请使用其他编码");
        }
    }

    /**
     * 验证配置是否存在
     */
    private void validateConfigsExist(List<BatchUpdatePageColumnConfigRequest.PageColumnConfigUpdateItem> configList, Long companyId) {
        for (BatchUpdatePageColumnConfigRequest.PageColumnConfigUpdateItem item : configList) {
            PageColumnConfigDO config = pageColumnConfigDao.queryById(item.getId(), companyId);
            AsserUtils.notNull(config, "配置ID[" + item.getId() + "]不存在或不属于当前公司");
        }
    }

    /**
     * 验证列编码唯一性（更新时排除自身）
     */
    private void validateColCodeUniquenessForUpdate(List<BatchUpdatePageColumnConfigRequest.PageColumnConfigUpdateItem> configList, Long companyId) {
        for (BatchUpdatePageColumnConfigRequest.PageColumnConfigUpdateItem item : configList) {
            // 先获取当前配置信息以获取pageId
            PageColumnConfigDO currentConfig = pageColumnConfigDao.queryById(item.getId(), companyId);
            if (currentConfig != null) {
                boolean exists = pageColumnConfigDao.existsByColCode(currentConfig.getPageId(), item.getColCode(), companyId, item.getId());
                AsserUtils.isTrue(exists, "列编码[" + item.getColCode() + "]已存在，请使用其他编码");
            }
        }
    }

    /**
     * 转换DO为VO
     */
    private PageColumnConfigVO convertToVO(PageColumnConfigDO configDO) {
        return PageColumnConfigVO.builder()
                .id(configDO.getId())
                .pageId(configDO.getPageId())
                .colCode(configDO.getColCode())
                .colTitle(configDO.getColTitle())
                .width(configDO.getWidth())
                .sort(configDO.getSort())
                .visible(configDO.getVisible())
                .created(configDO.getCreated())
                .modified(configDO.getModified())
                .companyId(configDO.getCompanyId())
                .build();
    }

    /**
     * 转换DO为DTO
     */
    private PageColumnConfigDto convertToDTO(PageColumnConfigDO configDO) {
        return PageColumnConfigDto.builder()
                .id(configDO.getId())
                .pageId(configDO.getPageId())
                .colCode(configDO.getColCode())
                .colTitle(configDO.getColTitle())
                .width(configDO.getWidth())
                .sort(configDO.getSort())
                .visible(configDO.getVisible())
                .created(configDO.getCreated())
                .modified(configDO.getModified())
                .companyId(configDO.getCompanyId())
                .build();
    }
}
