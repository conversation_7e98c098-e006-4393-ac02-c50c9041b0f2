package com.raycloud.dmj.account.core.bill.service;

import com.raycloud.dmj.account.core.base.domain.RuleVerifyResultRecordDO;
import com.raycloud.dmj.account.core.bill.request.RuleVerifyResultRecordRequest;
import com.raycloud.dmj.account.core.bill.vo.RuleVerifyResultDetailVO;
import com.raycloud.dmj.account.infra.session.AccountUser;

import java.util.List;

/**
 * 规则校验结果记录服务接口
 * <AUTHOR>
 */
public interface IRuleVerifyResultRecordService {

    /**
     * 新增规则校验结果记录
     * @param record 校验结果记录对象
     * @param accountUser 当前用户
     * @return 新增记录的主键ID
     */
    Long addRuleVerifyResultRecord(RuleVerifyResultRecordDO record, AccountUser accountUser);


    /**
     * 查询规则校验结果记录列表（按时间维度，每个规则单独统计）
     * @param request 查询参数
     * @param accountUser 当前用户
     * @return 每个规则的校验结果详细信息列表
     */
    List<RuleVerifyResultDetailVO> getRuleVerifyResultRecordList(RuleVerifyResultRecordRequest request, AccountUser accountUser);

    /**
     * 根据规则ID查询校验结果记录列表
     * @param ruleId 规则ID
     * @param accountUser 当前用户
     * @return 校验结果记录列表
     */
    List<RuleVerifyResultRecordDO> getRuleVerifyResultRecordByRuleId(Long ruleId, AccountUser accountUser);

    /**
     * 批量新增规则校验结果记录
     * @param recordList 校验结果记录列表
     * @param accountUser 当前用户
     * @return 新增的记录数
     */
    int batchAddRuleVerifyResultRecord(List<RuleVerifyResultRecordDO> recordList, AccountUser accountUser);

    void verifyRule(Long accountId, Integer billingCycle, Integer billingCycleType, Long companyId);
}
