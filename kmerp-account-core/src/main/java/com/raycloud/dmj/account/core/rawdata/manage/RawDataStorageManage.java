package com.raycloud.dmj.account.core.rawdata.manage;

import com.raycloud.dmj.account.core.base.dao.FileOriginalDataMonitorDao;
import com.raycloud.dmj.account.core.enums.MonitorStatusEnum;
import com.raycloud.dmj.account.core.enums.RawDataSourceEnum;
import com.raycloud.dmj.account.core.rawdata.domains.FileOriginalDataMonitorDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * Date:  2025/7/31
 */

@Component
@Slf4j
public class RawDataStorageManage {


    @Resource
    private FileOriginalDataMonitorDao fileOriginalDataMonitorDao;


    /**
     * 校验账期数据是否已全部导入
     *
     * @param companyId         公司 id
     * @param shopId            店铺 id
     * @param rawDataSourceEnum 数据源
     * @param dataRange         账期
     * @return true:已导入 false:未导入
     */
    public boolean checkDataIsImport(Long companyId, Long shopId, RawDataSourceEnum rawDataSourceEnum, Integer dataRange) {
        List<FileOriginalDataMonitorDO> fileOriginalDataMonitorDOList = fileOriginalDataMonitorDao.getBySourceAndDataRange(companyId, shopId,
                rawDataSourceEnum.getCode(), dataRange);
        if (CollectionUtils.isEmpty(fileOriginalDataMonitorDOList)) {
            return false;
        }
        return fileOriginalDataMonitorDOList.stream()
                .allMatch(fileOriginalDataMonitorDO -> MonitorStatusEnum.IMPORT_SUCCESS.getStatus().equals(fileOriginalDataMonitorDO.getDataStatus()));
    }


}
