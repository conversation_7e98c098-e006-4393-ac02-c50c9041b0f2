package com.raycloud.dmj.account.core.cleancategory.domain.object;

import com.raycloud.dmj.account.core.enums.InitCategoryGroupEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 分类表DO
 * <AUTHOR>
 */

@Data
public class CategoryDO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 修改时间
     */
    private Date modified;

    /**
     * 资金账户ID，关联资金账户体系
     */
    private Long fundAccountId;

    /**
     * 分类编码
     * @see InitCategoryGroupEnum
     */
    private String categoryGroupCode;

    /**
     * 来源（1-系统，2-手动）
     * @see com.raycloud.dmj.account.core.enums.CategorySourceEnum
     */
    private Integer source;

    /**
     * 类别名称
     */
    private String name;

    /**
     * 平台code
     * @see com.raycloud.dmj.account.core.enums.PlatformEnum
     */
    private String platformCode;

    /**
     * 公司ID
     */
    private Long companyId;

}