package com.raycloud.dmj.account.export.core.statistics.export;


import com.raycloud.dmj.account.export.utils.ExportUtils;
import com.raycloud.dmj.account.infra.common.BizException;
import com.raycloud.dmj.data.chessboard.listener.ExportContext;
import com.raycloud.dmj.data.chessboard.utils.ExportContextHelper;
import com.raycloud.dmj.data.export.core.ICallback;
import lombok.Getter;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.LongAdder;

/**
 * 虚拟进度条导出回调
 */
@Getter
public abstract class VirtualProgressCallback implements ICallback<List<Map<String, Object>>> {

    private final ExportContext exportContext = ExportContextHelper.getExportContext();

    private final LongAdder adder = new LongAdder();

    @Override
    public void accept(List<Map<String, Object>> datas) throws Exception {

        if (ObjectUtils.isEmpty(datas)) {
            return;
        }

        if (!ObjectUtils.isEmpty(exportContext)) {
            // 中断判断
            if (exportContext.isInterrupt()) {
                throw new BizException("强制中断导出");
            }

            // 虚拟进度
            adder.add(10L);
            exportContext.updateProcess(ExportUtils.virtualProcess(adder.longValue()));
        }

        doAccept(exportContext, datas);
    }

    public abstract void doAccept(ExportContext context, List<Map<String, Object>> datas) throws Exception;
}
