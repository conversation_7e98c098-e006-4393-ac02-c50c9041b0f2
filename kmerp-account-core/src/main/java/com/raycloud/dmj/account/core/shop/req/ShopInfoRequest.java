package com.raycloud.dmj.account.core.shop.req;

import com.raycloud.dmj.account.core.common.Page;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShopInfoRequest extends Page {

    /**
     * 主键ID集合
     */
    private List<Long> idList;
    /**
     * 店铺code
     */
    private String shopCode;

    /**
     * 店铺标题
     */
    private String title;

    /**
     * 公司名称
     */
    private String affiliatedCompanyName;

    private Integer accountStatus;

    /**
     * 平台编码
     */
    private String platformCode;

    /**
     * 平台编码集合
     */
    private List<String> platformCodeList;


}
