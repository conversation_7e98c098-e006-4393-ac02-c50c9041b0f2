package com.raycloud.dmj.account.core.rawdata.service.impl;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Sets;
import com.raycloud.dmj.account.core.base.dao.*;
import com.raycloud.dmj.account.core.base.domain.MonitorSummaryDO;
import com.raycloud.dmj.account.core.cleancategory.domain.object.FundAccountDO;
import com.raycloud.dmj.account.core.common.constant.TempFilePathBizType;
import com.raycloud.dmj.account.core.enums.*;
import com.raycloud.dmj.account.core.rawdata.callback.RawDataStorageBatchCallback;
import com.raycloud.dmj.account.core.rawdata.callback.param.BatchCallbackContext;
import com.raycloud.dmj.account.core.rawdata.callback.param.RawDataStorageContext;
import com.raycloud.dmj.account.core.rawdata.domains.*;
import com.raycloud.dmj.account.core.rawdata.handle.RawDataStorageHandle;
import com.raycloud.dmj.account.core.rawdata.handle.param.UpdateBatchStatusReq;
import com.raycloud.dmj.account.core.rawdata.handle.rawfilefilter.RawFileFilterSelect;
import com.raycloud.dmj.account.core.rawdata.handle.rawfilefilter.param.RawFileFilterParam;
import com.raycloud.dmj.account.core.rawdata.manage.RawDataStorageManage;
import com.raycloud.dmj.account.core.rawdata.req.BillUploadRequest;
import com.raycloud.dmj.account.core.rawdata.req.OriginalDataCallbackReq;
import com.raycloud.dmj.account.core.rawdata.req.OriginalDataParam;
import com.raycloud.dmj.account.core.rawdata.service.RawDataStorageService;
import com.raycloud.dmj.account.core.rawdata.utils.*;
import com.raycloud.dmj.account.core.rawdata.utils.oss.OSSClientHelper;
import com.raycloud.dmj.account.core.rawdata.utils.oss.OssUtils;
import com.raycloud.dmj.account.core.rocketmq.dto.RawDataStatusChangeMsg;
import com.raycloud.dmj.account.core.rocketmq.dto.RawDataStorageMsg;
import com.raycloud.dmj.account.core.rocketmq.producer.ProducerClient;
import com.raycloud.dmj.account.infra.common.BusinessException;
import com.raycloud.dmj.account.infra.common.ErrorCodeEnum;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import com.raycloud.dmj.account.infra.utils.DateUtils;
import com.raycloud.readexcel.analyzeexcel.AnalyzeExcelFactory;
import com.raycloud.readexcel.context.ProcessingContext;
import com.raycloud.readexcel.domain.DataMapping;
import com.raycloud.readexcel.domain.ExcelConfig;
import com.raycloud.readexcel.domain.TableField;
import com.raycloud.readexcel.exception.ExcelConfigException;
import com.raycloud.readexcel.util.EncodeUtils;
import com.raycloud.readexcel.util.ExcelConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.*;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.raycloud.dmj.account.core.common.constant.UserDefinedKey.*;
import static com.raycloud.dmj.account.core.enums.ImportErrorEnum.FILE_TYPE_ERROR;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class RawDataStorageServiceImpl implements RawDataStorageService {

    @Autowired
    private FileAnalyzeRecordDao fileAnalyzeRecordDao;
    @Resource
    private ProducerClient producerClient;
    @Autowired
    private FileAnalyzeErrorLogDao fileAnalyzeErrorLogDao;
    @Autowired
    private FileAnalyzeConfigDao fileAnalyzeConfigDao;
    @Autowired
    private FileAnalyzeTableConfigDao fileAnalyzeTableConfigDao;
    @Autowired
    private RawDataStorageHandle rawDataStorageHandle;

    @Resource
    private FundAccountDao fundAccountDao;

    @Resource
    private FileOriginalDataMonitorDao fileOriginalDataMonitorDao;

    @Resource
    private MonitorSummaryDao monitorSummaryDao;

    @Resource
    private RawDataStorageManage rawDataStorageManage;


    @Override
    public void rpaOriginalDataCallback(OriginalDataCallbackReq req) {
        //参数校验
        validateParam(req);
        //租户ID
        Long companyId = req.getCompanyId();
        Integer processType = req.getProcessType();
        //生成一个唯一的组号
        String groupCode = UUID.randomUUID().toString();
        List<Long> recordIds = new ArrayList<>();
        for (OriginalDataParam originalDataParam : req.getFileList()) {
            DateTypeEnum dateTypeEnum = DateTypeEnum.of(originalDataParam.getDateType());
            String dataRange = originalDataParam.getDataRange();
            Pair<Date, Date> dateDatePair = RawDataDateUtil.parseDateRange(dataRange, dateTypeEnum.getPatter(), dateTypeEnum);
            ImportDataTemplate importDataTemplate = new ImportDataTemplate();
            importDataTemplate.setDataType(originalDataParam.getBlockId());
            importDataTemplate.setFileType(originalDataParam.getFileType());
            importDataTemplate.setNeedStartTime(dateDatePair.getLeft());
            importDataTemplate.setNeedEndTime(dateDatePair.getRight());
            importDataTemplate.setStartDataRange(originalDataParam.getRealStartDataRange());
            importDataTemplate.setEndDataRange(originalDataParam.getRealEndDataRange());
            importDataTemplate.setChannelSource(DataSourceEnum.RPA.getCode());
            importDataTemplate.setDataSource(RawDataSourceEnum.getByRpaSourceId(Long.valueOf(processType)).getCode());
            importDataTemplate.setDateType(dateTypeEnum.getCode());
            importDataTemplate.setOssUrl(originalDataParam.getContent());
            importDataTemplate.setShopId(originalDataParam.getShopUniId());
            importDataTemplate.setCompanyId(companyId);
            importDataTemplate.setDownloadTime(originalDataParam.getCreated());
            importDataTemplate.setDownloadAccount("admin");
            importDataTemplate.setGroupCode(groupCode);
            importDataTemplate.setExtendParam(originalDataParam.getExtendParam());
            Long recordId = addFileAnalyzeRecord(importDataTemplate);
            //保存记录ID
            recordIds.add(recordId);
        }

        //先添加记录所有的记录，再解析文件。因为一组可以有多个文件，需要一组文件解析成功，才算成功
        for (Long recordId : recordIds) {
            RawDataStorageMsg rawDataStorageMsg = new RawDataStorageMsg();
            rawDataStorageMsg.setRecordId(recordId);
            try {
                boolean flag = producerClient.sendRawDataStorageMsg(rawDataStorageMsg);
                if (!flag) {
                    throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR.getCode(), "发送任务消息失败！");
                }
            } catch (Exception e) {
                log.error("|RawDataStorageServiceImpl.rpaOriginalDataCallback|发送任务消息异常！", e);
                fileAnalyzeRecordDao.updateFileAnalyzeStatusById(recordId, FileAnalyzeStatusEnum.SYSTEM_ERROR_IMPORT_FAIL.getStatus(), "发送任务消息失败!");
                throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR.getCode(), "发送任务消息异常！");
            }

        }


    }

    /**
     * 参数校验
     *
     * @param req 请求参数
     */
    private void validateParam(OriginalDataCallbackReq req) {
        AsserUtils.notNull(req, "参数不能为空！");
        AsserUtils.notNull(req.getCompanyId(), "公司ID不能为空！");
        AsserUtils.notEmpty(req.getFileList(), "原始数据不能为空！");
        AsserUtils.notNull(req.getProcessType(), "数据源不能为空！");
        RawDataSourceEnum rawDataSourceEnum = RawDataSourceEnum.getByRpaSourceId(Long.valueOf(req.getProcessType()));
        AsserUtils.notNull(rawDataSourceEnum, "暂不支持的数据源！");
        for (OriginalDataParam originalDataParam : req.getFileList()) {
            AsserUtils.notNull(originalDataParam.getDataRange(), "数据范围不能为空！");
            AsserUtils.notNull(originalDataParam.getDateType(), "时间类型不能为空！");
            DateTypeEnum dateTypeEnum = DateTypeEnum.of(originalDataParam.getDateType());
            AsserUtils.notNull(dateTypeEnum, "时间类型错误！");
            AsserUtils.notNull(originalDataParam.getShopUniId(), "shopUniId不能为空！");
            AsserUtils.notEmpty(originalDataParam.getBlockId(), "blockId不能为空！");
            AsserUtils.notEmpty(originalDataParam.getContent(), "content不能为空！");
            AsserUtils.notEmpty(originalDataParam.getFileName(), "fileName不能为空！");
            AsserUtils.notEmpty(originalDataParam.getFileType(), "fileType不能为空！");
            AsserUtils.notNull(originalDataParam.getRealStartDataRange(), "realStartDataRange不能为空！");
            AsserUtils.notNull(originalDataParam.getRealEndDataRange(), "realEndDataRange不能为空！");
            AccountTypeEnum accountTypeEnum = RawDataSourceEnum.getAccountTypeByCode(rawDataSourceEnum.getCode());
            AsserUtils.notNull(accountTypeEnum, "该数据源未绑定资金账户类型！");
        }
    }

    @Override
    public void acceptRawDataStorageRequest(BillUploadRequest request) {
        Integer billType = request.getBillType();
        String startTime = request.getStartTime();
        String dataType = request.getDataType();
        AsserUtils.notEmpty(dataType, "数据类型描述不能为空！");
        String fileType = request.getFileType();
        String endTime = request.getEndTime();
        Long companyId = request.getCompanyId();
        AsserUtils.notNull(companyId, "公司ID不能为空！");
        Long shopId = request.getShopId();
        AsserUtils.notNull(shopId, "店铺ID不能为空！");
        MultipartFile file = request.getFile();
        AsserUtils.notNull(billType, "请选择账单类型");
        AsserUtils.notNull(DateTypeEnum.of(billType), "账单类型不合法");
        AsserUtils.isTrue(!file.isEmpty(), "请选择要上传的文件");
        // 解析日期时间
        Date start = DateUtils.parse(startTime);
        Date end = DateUtils.parse(endTime);
        // 验证日期逻辑
        if (start != null && end != null) {
            AsserUtils.isTrue(!start.after(end), "开始时间不能晚于结束时间");
        }
        String objectName = OssUtils.getOriginalImportObjectName(file.getOriginalFilename());
        try {
            OssUtils.upload(objectName, file.getInputStream());
        } catch (IOException e) {
            throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR.getCode(), "上传文件到OSS失败！");
        }
        String ossUrl = OSSClientHelper.expireUrl(objectName);
        ImportDataTemplate importDataTemplate = new ImportDataTemplate();
        importDataTemplate.setDataType(dataType);
        importDataTemplate.setFileType(fileType);
        importDataTemplate.setNeedStartTime(start);
        importDataTemplate.setNeedEndTime(end);
        importDataTemplate.setStartDataRange(start);
        importDataTemplate.setChannelSource(DataSourceEnum.USER.getCode());
        importDataTemplate.setEndDataRange(end);
        importDataTemplate.setDateType(billType);
        importDataTemplate.setShopId(shopId);
        importDataTemplate.setOssUrl(ossUrl);
        importDataTemplate.setCompanyId(companyId);
        Long recordId = addFileAnalyzeRecord(importDataTemplate);
        //发送消息
        RawDataStorageMsg rawDataStorageMsg = new RawDataStorageMsg();
        rawDataStorageMsg.setRecordId(recordId);
        try {
            boolean flag = producerClient.sendRawDataStorageMsg(rawDataStorageMsg);
            if (!flag) {
                throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR.getCode(), "发送任务消息失败！");
            }
        } catch (Exception e) {
            log.error("发送任务消息异常！rawDataStorageMsg={}", rawDataStorageMsg, e);
            throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR.getCode(), "发送任务消息异常！");
        }


    }

    @Override
    public void rpaRawDataStorage(Long recordId) {
        FileAnalyzeRecordDO fileAnalyzeRecordDO = fileAnalyzeRecordDao.getById(recordId);
        String importData = fileAnalyzeRecordDO.getImportData();
        ImportDataTemplate importDataTemplate = JSON.parseObject(importData, ImportDataTemplate.class);
        //前置校验
        try {
            preValidate(importDataTemplate);
        }catch (Exception e){
            fileAnalyzeRecordDao.updateFileAnalyzeStatusById(recordId, FileAnalyzeStatusEnum.SYSTEM_ERROR_IMPORT_FAIL.getStatus(),e.getMessage());
            throw e;
        }
        //获取上下文数据
        BatchCallbackContext batchCallbackContext = new BatchCallbackContext();
        batchCallbackContext.setDateType(importDataTemplate.getDateType());
        batchCallbackContext.setNeedStartTime(importDataTemplate.getNeedStartTime());
        batchCallbackContext.setNeedEndTime(importDataTemplate.getNeedEndTime());
        batchCallbackContext.setCompanyId(importDataTemplate.getCompanyId());
        batchCallbackContext.setShopId(importDataTemplate.getShopId());
        batchCallbackContext.setDataSource(importDataTemplate.getDataSource());
        ImportStaffInfo importStaffInfo = createImportStaffInfo(importDataTemplate, recordId);
        //根据dataType匹配文件配置信息
        ProcessingContext<ImportStaffInfo> importStaffInfoProcessingContext = null;
        try {
            importStaffInfoProcessingContext = mappingData(recordId, importDataTemplate, batchCallbackContext, importStaffInfo);
        } catch (Exception e) {
            fileAnalyzeRecordDao.updateFileAnalyzeStatusById(recordId, FileAnalyzeStatusEnum.SYSTEM_ERROR_IMPORT_FAIL.getStatus(), "配置文件匹配错误:" + e.getMessage());
            throw new BusinessException(ErrorCodeEnum.CONFIG_ERROR.getCode(), "配置文件匹配错误:" + e.getMessage());
        }
        //解析文件
        rawDataStorageHandle(importStaffInfo, batchCallbackContext, importStaffInfoProcessingContext);
    }

    private void preValidate(ImportDataTemplate importDataTemplate) {
        AsserUtils.notNull(importDataTemplate, "导入参数不能为空！");
        FileTypeEnum fileType = FileTypeEnum.getFileType(importDataTemplate.getFileType());
        AsserUtils.notNull(fileType, ImportErrorEnum.FILE_TYPE_ERROR.getDesc());
        AsserUtils.notNull(importDataTemplate.getCompanyId(), "公司ID不能为空！");
        AsserUtils.notNull(importDataTemplate.getShopId(), "店铺ID不能为空！");
        AsserUtils.notNull(importDataTemplate.getDataSource(),"数据来源不能为空");
        RawDataSourceEnum dataSourceEnum = RawDataSourceEnum.getByCode(importDataTemplate.getDataSource());
        AsserUtils.notNull(dataSourceEnum,"暂不支持该数据源！");
        AccountTypeEnum accountTypeEnum = RawDataSourceEnum.getAccountTypeByCode(importDataTemplate.getDataSource());
        AsserUtils.notNull(accountTypeEnum, "该数据源未绑定资金账户类型！");
        AsserUtils.notNull(importDataTemplate.getDateType(),"数据的时间类型不能为空！");
        FundAccountDO fundAccountDO = fundAccountDao.getByShopIdAndType(importDataTemplate.getCompanyId(),
                importDataTemplate.getShopId(), accountTypeEnum.getTypeCode());
        if (Objects.isNull(fundAccountDO)) {
            log.error("|RawDataStorageServiceImpl.validateParam error|店铺没有配置对应的资金账户,shopId:{},accountType:{}", importDataTemplate.getShopId(), accountTypeEnum.getTypeCode());
            throw new BusinessException(ErrorCodeEnum.FUND_ACCOUNT_NOT_EXISTS, "店铺ID:【%S】没有配置【%S】类型资金账户！", importDataTemplate.getShopId(), accountTypeEnum.getTypeDesc());
        }
        Integer confirmStartPeriod = fundAccountDO.getConfirmStartPeriod();
        if (Objects.isNull(confirmStartPeriod) || YesOrNoEnum.NO.getValue().equals(confirmStartPeriod)) {
            log.error("|RawDataStorageServiceImpl.validateParam error|资金账户未确认期初,fundAccountId:{}", fundAccountDO.getId());
            throw new BusinessException(ErrorCodeEnum.FUND_ACCOUNT_NOT_EXISTS, "资金账户【%S】未确认期初，无法开启对账！", fundAccountDO.getAccountName());
        }
        Date startDate = fundAccountDO.getStartDate();
        DateTypeEnum dateTypeEnum = DateTypeEnum.of(importDataTemplate.getDateType());
        Integer startDataRange = RawDataDateUtil.formatDate(startDate, dateTypeEnum);
        Integer needDataRange = RawDataDateUtil.formatDate(importDataTemplate.getNeedStartTime(), dateTypeEnum);
        //如果不是期初的数据，则校验上一期数据是否到达 前一期的数据未解析完成，禁止解析
        if (!Objects.equals(startDataRange, needDataRange)) {
            Integer previousDataRange = RawDataDateUtil.getPreviousDataRange(needDataRange, dateTypeEnum);
            //前一期的数据未导入完成，禁止导入
            boolean isImport = rawDataStorageManage.checkDataIsImport(importDataTemplate.getCompanyId(), importDataTemplate.getShopId(), dataSourceEnum, previousDataRange);
            if (!isImport){
                log.error("|RawDataStorageServiceImpl.validateParam error|上个账期未导入完成,shopId:{},dataRange:{}", importDataTemplate.getShopId(), previousDataRange);
                throw new BusinessException(ErrorCodeEnum.FILE_NOT_IMPORT_FINISH, "上个账期【%S】未导入完成，禁止导入！",previousDataRange);
            }

        }
    }

    @Override
    public void handleStuckTasks() {
        rawDataStorageHandle.handleStuckTasks();

    }

    /**
     * 解析文件
     *
     * @param importStaffInfo
     * @param batchCallbackContext
     * @param importStaffInfoProcessingContext
     */
    private void rawDataStorageHandle(ImportStaffInfo importStaffInfo, BatchCallbackContext batchCallbackContext, ProcessingContext<ImportStaffInfo> importStaffInfoProcessingContext) {
        String fileType = importStaffInfo.getFileType();
        Long recordId = importStaffInfo.getRecordId();
        try {
            if (StringUtils.equalsIgnoreCase(fileType, FileTypeEnum.ZIP.getFileType())) {
                // 处理zip文件
                rawDataStorageHandleByZipFile(importStaffInfo, batchCallbackContext, importStaffInfoProcessingContext);
            } else {
                // 处理excel文件
                rawDataStorageHandleByExcelFile(importStaffInfo, batchCallbackContext, importStaffInfoProcessingContext);
            }
            //处理完文件之后，更新批次状态
            updateBatchStatus(batchCallbackContext);
            //文件处理完成之后，更新记录状态和解析数量
            updateRecordStatusAndCountInfo(importStaffInfo, batchCallbackContext);
        } catch (Exception e) {
            log.error("系统异常,文件处理错误", e);
            //发生异常 将处理过的批次号改为失败
            handlePostException(batchCallbackContext);
            fileAnalyzeRecordDao.updateFileAnalyzeStatusById(recordId, FileAnalyzeStatusEnum.SYSTEM_ERROR_IMPORT_FAIL.getStatus(), "系统异常,文件处理错误:" + e.getMessage());
            throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR.getCode(), "系统异常,文件处理错误:" + e.getMessage());
        }
    }

    /**
     * 处理完成之后
     * @param batchCallbackContext 批量处理上下文
     * @param recordId 文件原始数据记录ID
     */
    private void afterProcessor(BatchCallbackContext batchCallbackContext, Long recordId) {
        FileAnalyzeRecordDO fileAnalyzeRecordDO = fileAnalyzeRecordDao.getById(recordId);
        String groupCode = fileAnalyzeRecordDO.getGroupCode();
        List<FileAnalyzeRecordDO> fileAnalyzeRecordDOList = fileAnalyzeRecordDao.listByGroupCode(batchCallbackContext.getCompanyId(), groupCode);
        //判断同组文件是否都成功
        boolean isGroupFileSuccess = fileAnalyzeRecordDOList.stream()
                .map(FileAnalyzeRecordDO::getAnalyzeStatus)
                .allMatch(x -> FileAnalyzeStatusEnum.IMPORT_SUCCESS.getStatus().equals(x));

        Set<String> handleBatchNoSet = batchCallbackContext.getHandleBatchNoSet();
        Set<String> skipBatchNoList = batchCallbackContext.getSkipBatchNoList();
        Set<String> unionBatchNoSet = Sets.union(handleBatchNoSet, skipBatchNoList);

        if (isGroupFileSuccess && CollectionUtils.isNotEmpty(unionBatchNoSet)) {
            //发送分类解析的消息
            List<FileOriginalDataMonitorDO> fileOriginalDataMonitorDOList = fileOriginalDataMonitorDao.listByShopAndSourceAndBatch(batchCallbackContext.getCompanyId(), batchCallbackContext.getShopId(), batchCallbackContext.getDataSource(), unionBatchNoSet);
            if (CollectionUtils.isEmpty(fileOriginalDataMonitorDOList)){
                log.error("|RawDataStorageServiceImpl.afterProcessor error| 未查询到批次监控数据！batchCallbackContext={}",JSON.toJSONString(batchCallbackContext));
                throw new BusinessException(ErrorCodeEnum.PARAM_ERROR,"未查询到批次监控数据！");
            }
            Map<Integer, List<FileOriginalDataMonitorDO>> groupedByFileType =
                    fileOriginalDataMonitorDOList.stream()
                            .collect(Collectors.groupingBy(FileOriginalDataMonitorDO::getDataRange));
            //每组监控表中添加导入成功的时间并发送一次消息分类解析
            groupedByFileType.forEach((fileType, fileOriginalDataMonitorList) -> {
                FileOriginalDataMonitorDO fileOriginalDataMonitorDO = fileOriginalDataMonitorDOList.get(0);
                //在监控表中添加导入成功记录
                MonitorSummaryDO monitorSummaryDO = new MonitorSummaryDO();
                monitorSummaryDO.setShopId(fileOriginalDataMonitorDO.getShopId());
                monitorSummaryDO.setCompanyId(fileOriginalDataMonitorDO.getCompanyId());
                monitorSummaryDO.setImportSuccessTime(LocalDateTime.now());
                monitorSummaryDO.setDataSource(fileOriginalDataMonitorDO.getDataSource());
                monitorSummaryDO.setCreated(new Date());
                monitorSummaryDO.setModified(new Date());
                monitorSummaryDao.insertOnDuplicateKey(monitorSummaryDO);
                //发送消息
                RawDataStatusChangeMsg rawDataStatusChangeMsg = new RawDataStatusChangeMsg();
                rawDataStatusChangeMsg.setStatus(fileOriginalDataMonitorDO.getDataStatus());
                rawDataStatusChangeMsg.setRawDataBatchMonitorId(fileOriginalDataMonitorDO.getId());
                try {
                    boolean flag = producerClient.sendRawDataStatusChangeMsg(rawDataStatusChangeMsg);
                    if (!flag) {
                        throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR.getCode(), "发送任务消息失败！");
                    }
                } catch (Exception e) {
                    log.error("发送任务消息异常！", e);
                    throw new BusinessException(ErrorCodeEnum.ROCKETMQ_ERROR, "发送任务消息异常！");
                }

            });
        }


    }


    /**
     * 文件处理完成之后，更新记录状态和解析数量
     *
     * @param importStaffInfo
     * @param batchCallbackContext
     */
    private void updateRecordStatusAndCountInfo(ImportStaffInfo importStaffInfo, BatchCallbackContext batchCallbackContext) throws UnsupportedEncodingException {
        Long recordId = importStaffInfo.getRecordId();
        if (batchCallbackContext.getBizErrorFlag()) {
            fileAnalyzeRecordDao.updateFileAnalyzeStatusById(batchCallbackContext.getLastRecordId(), FileAnalyzeStatusEnum.BIZ_ERROR_IMPORT_FAIL.getStatus(), ImportErrorEnum.BIZ_ERROR.getDesc());
        } else {
            fileAnalyzeRecordDao.updateFileAnalyzeStatusById(recordId, FileAnalyzeStatusEnum.IMPORT_SUCCESS.getStatus(), "");
            //成功后置处理器
            afterProcessor(batchCallbackContext, recordId);
        }
        HashMap<String, FileAnalyzeCountInfo> fileAnalyzeCountInfoHashMap = batchCallbackContext.getFileAnalyzeCountInfoHashMap();
        fileAnalyzeCountInfoHashMap.forEach(
                (key, value) -> {
                    FileAnalyzeCountInfo value1 = value;
                    Map<Integer, FileSheetCountInfo> fileSheetCountInfoMap = value1.getFileSheetCountInfoMap();
                    fileSheetCountInfoMap.forEach((sheetIndex, fileSheetCountInfo) -> {
                                fileSheetCountInfo.getTotalCount().addAndGet(fileSheetCountInfo.getSuccessCount().get() + fileSheetCountInfo.getInnerFilterCount().get() + fileSheetCountInfo.getFailCount().get());
                            }
                    );
                });
        String jsonString = JSON.toJSONString(fileAnalyzeCountInfoHashMap);
        // 先对整个字符串进行 URL 解码
        String decodedJson = URLDecoder.decode(jsonString, StandardCharsets.UTF_8.toString());
        fileAnalyzeRecordDao.updateFileCountInfo(recordId, decodedJson);
    }


    /**
     * 处理完文件之后，更新批次状态
     *
     * @param batchCallbackContext 处理批次上下文
     */
    private void updateBatchStatus(BatchCallbackContext batchCallbackContext) {
        if (CollectionUtils.isEmpty(batchCallbackContext.getHandleBatchNoSet())) {
            return;
        }
        MonitorStatusEnum updateStatus;
        if (batchCallbackContext.getBizErrorFlag()) {
            updateStatus = MonitorStatusEnum.IMPORT_FAIL;
        } else {
            //如果失败行数等于0，则更新批次状态为成功
            updateStatus = MonitorStatusEnum.IMPORT_SUCCESS;
        }
        UpdateBatchStatusReq updateBatchStatusReq = new UpdateBatchStatusReq();
        updateBatchStatusReq.setCompanyId(batchCallbackContext.getCompanyId());
        updateBatchStatusReq.setShopId(batchCallbackContext.getShopId());
        updateBatchStatusReq.setBatchCodeSet(batchCallbackContext.getHandleBatchNoSet());
        updateBatchStatusReq.setUpdateStatus(updateStatus);
        rawDataStorageHandle.updateBatchStatus(updateBatchStatusReq);
    }

    /**
     * 处理单个文件的业务逻辑
     *
     * @param importStaffInfo
     * @param batchCallbackContext
     * @param importStaffInfoProcessingContext
     */
    private void rawDataStorageHandleByExcelFile(ImportStaffInfo importStaffInfo, BatchCallbackContext batchCallbackContext, ProcessingContext<ImportStaffInfo> importStaffInfoProcessingContext) {
        try {
            rawDataStorageByUrl(importStaffInfo, importStaffInfoProcessingContext, batchCallbackContext);
            if (batchCallbackContext.getBizErrorFlag()) {
                handleBizError(batchCallbackContext, importStaffInfo);
            }
        } finally {
            clearFileByError(batchCallbackContext);
        }

    }

    /**
     * 处理zip文件的业务逻辑
     *
     * @param importStaffInfo
     * @param batchCallbackContext
     * @param importStaffInfoProcessingContext
     */
    private void rawDataStorageHandleByZipFile(ImportStaffInfo importStaffInfo, BatchCallbackContext batchCallbackContext, ProcessingContext<ImportStaffInfo> importStaffInfoProcessingContext) {
        String ossUrl = importStaffInfo.getOssUrl();
        importStaffInfo.setZipLocalFilePath(OutputFilePathUtil.getOutputFilePath(TempFilePathBizType.EXPORT_ZIP_FILE_PATH, FileTypeEnum.ZIP.getFileType()));
        importStaffInfo.setUnzipFolderPath(OutputFilePathUtil.getOutputFolderPath(TempFilePathBizType.EXPORT_UNZIP_FILE_PATH));
        try {
            //下载zip文件到本地
            DownloadFileByUrlUtil.downloadFile(ossUrl, importStaffInfo.getZipLocalFilePath());
            //获取文件编码
            String charset = EncodeUtils.guessFileEncoding(Files.newInputStream(Paths.get(importStaffInfo.getZipLocalFilePath())));
            //解压zip文件
            List<String> unzip = UnzipProcessor.unzip(importStaffInfo.getZipLocalFilePath(), importStaffInfo.getUnzipFolderPath(),charset);
            for (String s : unzip) {
                //对ZIP进行文件过滤
                RawFileFilterParam param = new RawFileFilterParam();
                param.setFilePath(s);
                param.setDataSource(batchCallbackContext.getDataSource());
                if (RawFileFilterSelect.execute(param)){
                    //跳过
                    continue;
                }
                rawDataStorageByLocalPath(importStaffInfo, importStaffInfoProcessingContext, batchCallbackContext, s);
            }
            if (batchCallbackContext.getBizErrorFlag()) {
                handleBizError(batchCallbackContext, importStaffInfo);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            clearFileByError(batchCallbackContext);
            clearTempZipFile(importStaffInfo);
        }

    }

    private void handleBizError(BatchCallbackContext batchCallbackContext, ImportStaffInfo importStaffInfo) {
        String outputFileName = OutputFilePathUtil.getOutputFileName(String.valueOf(importStaffInfo.getRecordId()));
        String originalImportBizErrorObjectName = OssUtils.getOriginalImportBizErrorObjectName(outputFileName);
        OssUtils.chunkedUpload(batchCallbackContext.getFailOutputFilePath(), OssUtils.getOriginalImportBizErrorObjectName(outputFileName));
        FileAnalyzeErrorLogDO fileAnalyzeErrorLogDO = FileAnalyzeErrorLogDO.builder()
                .recordId(batchCallbackContext.getLastRecordId())
                .errorLogFile(originalImportBizErrorObjectName)
                .created(new Date())
                .modified(new Date())
                .createdBy(importStaffInfo.getStaffId() + "")
                .tenantId(importStaffInfo.getCompanyId())
                .shopUniId(importStaffInfo.getShopUniId())
                .build();
        fileAnalyzeErrorLogDao.add(fileAnalyzeErrorLogDO);
    }

    private ImportStaffInfo createImportStaffInfo(ImportDataTemplate importDataTemplate, Long recordId) {
        ImportStaffInfo importStaffInfo = new ImportStaffInfo();
        importStaffInfo.setRecordId(recordId);
        importStaffInfo.setOssUrl(importDataTemplate.getOssUrl());
        importStaffInfo.setFileType(importDataTemplate.getFileType());
        importStaffInfo.setShopUniId(importDataTemplate.getShopId());
        importStaffInfo.setStaffId(importDataTemplate.getStaffId());
        importStaffInfo.setCompanyId(importDataTemplate.getCompanyId());
        return importStaffInfo;
    }

    /**
     * 匹配文件配置信息，获取最终数据配置
     *
     * @param recordId
     * @param importDataTemplate
     * @param batchCallbackContext
     * @param importUserInfo
     * @return
     */
    private ProcessingContext<ImportStaffInfo> mappingData(Long recordId, ImportDataTemplate importDataTemplate, BatchCallbackContext batchCallbackContext, ImportStaffInfo importUserInfo) {

        batchCallbackContext.setLastRecordId(recordId);
        RawDataStorageContext rawDataStorageContext = new RawDataStorageContext();
        //2.根据data_type去匹配原始数据，拿到数据配置，更新file_analyze_record的matching_config
        //3.合并原始数据config，生成最终数据的config
        List<ExcelConfig> excelConfigList = getExcelConfigList(importDataTemplate, batchCallbackContext, rawDataStorageContext);
        if (CollectionUtils.isEmpty(excelConfigList)) {
            throw new ExcelConfigException(ImportErrorEnum.FILE_NOT_EXIST.getDesc());
        }
        //更新file_analyze_record的matching_config
        fileAnalyzeRecordDao.updateMatchingConfigIdsById(recordId, String.join(",", rawDataStorageContext.getFileAnalyzeConfigIds().stream().map(s -> String.valueOf(s)).collect(Collectors.toList())));
        //4.生成ProcessingContext
        ProcessingContext<ImportStaffInfo> objectProcessingContext = new ProcessingContext<>();
        configProcessingContext(objectProcessingContext, excelConfigList, importUserInfo, importDataTemplate);
        return objectProcessingContext;
    }

    /**
     * 导入原始数据
     *
     * @param importUserInfo
     */
    public void rawDataStorageByUrl(ImportStaffInfo importUserInfo, ProcessingContext<ImportStaffInfo> objectProcessingContext, BatchCallbackContext batchCallbackContext) {
        AnalyzeExcelFactory.<ImportStaffInfo>readUrlExcel(importUserInfo.getOssUrl())
                .dataAnalyzeFailStop(false)
                .dataAnalyzeAloneSheet(false)
                .analyzeData(objectProcessingContext, new RawDataStorageBatchCallback(rawDataStorageHandle, batchCallbackContext))
                .doRead();
    }

    /**
     * 导入原始数据
     *
     * @param importUserInfo
     */
    public void rawDataStorageByLocalPath(ImportStaffInfo importUserInfo, ProcessingContext<ImportStaffInfo> objectProcessingContext, BatchCallbackContext batchCallbackContext, String localFilePath) {
        AnalyzeExcelFactory.<ImportStaffInfo>readLocalFileExcel(new File(localFilePath))
                .dataAnalyzeFailStop(false)
                .dataAnalyzeAloneSheet(false)
                .analyzeData(objectProcessingContext, new RawDataStorageBatchCallback(rawDataStorageHandle, batchCallbackContext)).doRead();
    }

    /**
     * 清理错误文件
     *
     * @param batchCallbackContext
     */
    private void clearFileByError(BatchCallbackContext batchCallbackContext) {
        String failOutputFilePath = batchCallbackContext.getFailOutputFilePath();
        if (StringUtils.isNotEmpty(failOutputFilePath)) {
            File file = new File(failOutputFilePath);
            if (file.exists()) {
                file.delete();
            }
        }
    }

    /**
     * 清理临时的zip文件
     */
    private void clearTempZipFile(ImportStaffInfo importStaffInfo) {
        if (StringUtils.isNotEmpty(importStaffInfo.getZipLocalFilePath())) {
            File file = new File(importStaffInfo.getZipLocalFilePath());
            if (file.exists()) {
                file.delete();
            }
        }
        if (StringUtils.isNotEmpty(importStaffInfo.getUnzipFolderPath())) {
            UnzipProcessor.deleteDirectory(new File(importStaffInfo.getUnzipFolderPath()));
        }
    }


    /**
     * 配置ProcessingContext
     *
     * @param objectProcessingContext
     * @param excelConfigList
     * @param importDataTemplate
     */
    private void configProcessingContext(ProcessingContext<ImportStaffInfo> objectProcessingContext, List<ExcelConfig> excelConfigList, ImportStaffInfo importUserInfo, ImportDataTemplate importDataTemplate) {
        HashMap<String, Object> userDefinedMap = new HashMap<>();
        objectProcessingContext.setExcelConfig(excelConfigList);
        objectProcessingContext.setUserDefinedData(importUserInfo);
        userDefinedMap.put(USER_DEFINED_KEY_DATE,
                RawDataDateUtil.formatDate(importDataTemplate.getStartDataRange(), DateTypeEnum.of(importDataTemplate.getDateType())));
        userDefinedMap.put(COMPANY_ID, importDataTemplate.getCompanyId());
        userDefinedMap.put(SHOP_ID, importDataTemplate.getShopId());
        userDefinedMap.put(DOWNLOAD_TIME, importDataTemplate.getDownloadTime());
        userDefinedMap.put(DOWNLOAD_ACCOUNT, importDataTemplate.getDownloadTime());
        if (MapUtils.isNotEmpty(importDataTemplate.getExtendParam())){
            userDefinedMap.putAll(importDataTemplate.getExtendParam());
        }
        objectProcessingContext.setUserDefinedMap(userDefinedMap);
    }

    /**
     * 根据匹配后的数据处理成ExcelConfig格式
     *
     * @param importDataTemplate
     * @return
     */
    private List<ExcelConfig> getExcelConfigList(ImportDataTemplate importDataTemplate, BatchCallbackContext batchCallbackContext, RawDataStorageContext rawDataStorageContext) {
        //获取匹配后的数据
        List<FileAnalyzeConfigDO> fileAnalyzeConfigDOS = fileAnalyzeConfigDao.queryFileAnalyzeConfig(importDataTemplate.getDataType());
        if (CollectionUtils.isEmpty(fileAnalyzeConfigDOS)) {
            throw new ExcelConfigException(ImportErrorEnum.FILE_NOT_EXIST.getDesc());
        }
        //拿到最新版本号数据
        List<FileAnalyzeConfigDO> fileAnalyzeConfigDOList = getFileAnalyzeConfig(fileAnalyzeConfigDOS);
        List<Long> configIds = fileAnalyzeConfigDOList.stream().map(config -> config.getId()).collect(Collectors.toList());
        //填充查询数据到上下文
        rawDataStorageContext.setFileAnalyzeConfigDOList(fileAnalyzeConfigDOList);
        rawDataStorageContext.setFileAnalyzeConfigIds(configIds);
        Map<String, Long> sheetConfigIdMap = new HashMap<>();
        fileAnalyzeConfigDOList.forEach(config -> {
            sheetConfigIdMap.put(BatchCallbackContext.getSheetKey(config.getSheet(), config.getSheetType()), config.getId());
        });
        batchCallbackContext.setSheetConfigIdMap(sheetConfigIdMap);
        //获取表配置
        List<FileAnalyzeTableConfigDO> fileAnalyzeTableConfigDOS = fileAnalyzeTableConfigDao.queryFileAnalyzeTableConfig(configIds);
        if (CollectionUtils.isEmpty(fileAnalyzeTableConfigDOS)) {
            throw new ExcelConfigException(ImportErrorEnum.FILE_NOT_EXIST.getDesc());
        }

        Map<Long, List<FileAnalyzeTableConfigDO>> fileAnalyzeTableConfigMap = fileAnalyzeTableConfigDOS.stream().collect(Collectors.groupingBy(s -> s.getConfigId(), Collectors.toList()));
        List<ExcelConfig> excelConfigList = new ArrayList<>();
        //匹配后的数据处理成ExcelConfig格式
        for (FileAnalyzeConfigDO fileAnalyzeConfigDO : fileAnalyzeConfigDOList) {
            ExcelConfig excelConfig = new ExcelConfig();
            excelConfig.setSheet(fileAnalyzeConfigDO.getSheet());
            excelConfig.setSheetType(fileAnalyzeConfigDO.getSheetType());
            String filter = fileAnalyzeConfigDO.getFilter();
            if (StringUtils.isNoneBlank(filter)) {
                excelConfig.setFilter(ExcelConfigUtils.parseExcelMultipleFilterDataConfig(filter));
            }
            excelConfig.setHeaderStartIndex(fileAnalyzeConfigDO.getHeaderStartIndex());
            excelConfig.setHeaderEndIndex(fileAnalyzeConfigDO.getHeaderEndIndex());
            excelConfig.setHeadConfig(ExcelConfigUtils.parseExcelMultipleHeadConfig(fileAnalyzeConfigDO.getHeadConfig()));
            List<FileAnalyzeTableConfigDO> fileAnalyzeTableConfigDOList = fileAnalyzeTableConfigMap.get(fileAnalyzeConfigDO.getId());
            if (CollectionUtils.isEmpty(fileAnalyzeTableConfigDOList)) {
                throw new ExcelConfigException(ImportErrorEnum.FILE_NOT_EXIST.getDesc());
            }
            List<DataMapping> dataMappings = new ArrayList<>();
            for (FileAnalyzeTableConfigDO fileAnalyzeTableConfigDO : fileAnalyzeTableConfigDOList) {
                DataMapping dataMapping = new DataMapping();
                dataMapping.setDataType(fileAnalyzeTableConfigDO.getTableDataType());
                dataMapping.setTableName(fileAnalyzeTableConfigDO.getTableName());
                dataMapping.setBatchNo(ExcelConfigUtils.parseExcelSingleHeadTableFieldConfig(fileAnalyzeTableConfigDO.getBatchNo()));
                dataMapping.setUniqueKey(ExcelConfigUtils.parseExcelSingleHeadTableFieldConfig(fileAnalyzeTableConfigDO.getUniqueKey()));
                dataMapping.setTableFields(ExcelConfigUtils.parseExcelMultipleHeadTableFieldConfig(fileAnalyzeTableConfigDO.getTableFields()));
                dataMapping.setTableSourceConfig(fileAnalyzeTableConfigDO.getTableSourceConfig());
                dataMappings.add(dataMapping);
                //检查文件配置参数
                checkFileConfig(dataMapping, importDataTemplate);
            }
            excelConfig.setDataMapping(dataMappings);
            excelConfigList.add(excelConfig);
        }
        return excelConfigList;
    }

    /**
     * 检查文件没有日期的情况下参数问题
     *
     * @param dataMapping
     * @param importDataTemplate
     */
    private void checkFileConfig(DataMapping dataMapping, ImportDataTemplate importDataTemplate) {
        if (CollectionUtils.isEmpty(dataMapping.getTableFields())) {
            throw new ExcelConfigException(ImportErrorEnum.FILE_NOT_EXIST.getDesc());
        }
        String tableSourceConfig = dataMapping.getTableSourceConfig();
        TableSourceConfig tableSourceConfig1 = BizExcelConfigUtils.parseExcelMultipleHeadConfig(tableSourceConfig);
        String dateField = tableSourceConfig1.getDateField();
        List<TableField> tableFields = dataMapping.getTableFields();
        Map<String, TableField> tableFieldMap = tableFields.stream().collect(Collectors.toMap(TableField::getTableField, s -> s));
        TableField tableField = tableFieldMap.get(dateField);
        if (Objects.isNull(tableField)) {
            throw new ExcelConfigException(ImportErrorEnum.NOT_CONFIG_DATE_FIELD.getDesc());
        }
        String formula = tableField.getFormula();
        if (!formula.contains("${" + USER_DEFINED_KEY_DATE + "}")) {
            return;
        }
        Date startDataRange = importDataTemplate.getStartDataRange();
        Date endDataRange = importDataTemplate.getEndDataRange();
        if (!Objects.equals(startDataRange, endDataRange)) {
            throw new ExcelConfigException(ImportErrorEnum.NOT_FILE_DATE_PARAM_ERROR.getDesc());
        }
    }


    /**
     * 拿到最新版本号的数据
     *
     * @param fileAnalyzeConfigDOS
     * @return
     */
    private List<FileAnalyzeConfigDO> getFileAnalyzeConfig(List<FileAnalyzeConfigDO> fileAnalyzeConfigDOS) {
        // 按分组字段（如 configType + fileName）分组
        Map<String, List<FileAnalyzeConfigDO>> groupedConfigs = fileAnalyzeConfigDOS.stream()
                .collect(Collectors.groupingBy(config ->
                        config.getSheetType() + "_" + config.getSheet()
                ));

        // 对每个分组，找出 version 最大的配置
        List<FileAnalyzeConfigDO> fileAnalyzeConfigDOList = new ArrayList<>();
        for (List<FileAnalyzeConfigDO> group : groupedConfigs.values()) {
            // 按 version 降序排序，取第一个
            group.sort(Comparator.comparingInt(FileAnalyzeConfigDO::getVersion).reversed());
            fileAnalyzeConfigDOList.add(group.get(0));
        }
        return fileAnalyzeConfigDOList;
    }


    /**
     * 异常后置处理
     */
    private void handlePostException(BatchCallbackContext batchCallbackContext) {
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(batchCallbackContext.getHandleBatchNoSet())) {
            UpdateBatchStatusReq updateBatchStatusReq = new UpdateBatchStatusReq();
            updateBatchStatusReq.setCompanyId(batchCallbackContext.getCompanyId());
            updateBatchStatusReq.setShopId(batchCallbackContext.getShopId());
            updateBatchStatusReq.setBatchCodeSet(batchCallbackContext.getHandleBatchNoSet());
            updateBatchStatusReq.setUpdateStatus(MonitorStatusEnum.IMPORT_FAIL);
            rawDataStorageHandle.updateBatchStatus(updateBatchStatusReq);
        }
    }

    /**
     * 添加文件解析记录
     *
     * @param importDataTemplate 账单模板
     */
    private Long addFileAnalyzeRecord(ImportDataTemplate importDataTemplate) {       //导出的时间
        Date date = new Date();
        FileAnalyzeRecordDO fileAnalyzeRecordDO = FileAnalyzeRecordDO.builder().
                analyzeStatus(FileAnalyzeStatusEnum.IMPORTING.getStatus()).
                dataType(importDataTemplate.getDataType()).
                dateType(importDataTemplate.getDateType()).
                fileStartDataRange(importDataTemplate.getStartDataRange()).
                fileEndDataRange(importDataTemplate.getEndDataRange()).
                assignStartDataRange(importDataTemplate.getNeedStartTime()).
                assignEndDataRange(importDataTemplate.getNeedEndTime()).
                companyId(importDataTemplate.getCompanyId()).
                groupCode(importDataTemplate.getGroupCode()).
                importData(JSON.toJSONString(importDataTemplate)).
                shopId(importDataTemplate.getShopId()).
                createBy(String.valueOf(importDataTemplate.getStaffId())).
                created(date).
                modified(date).
                channelSource(importDataTemplate.getChannelSource()).
                build();
        return fileAnalyzeRecordDao.addFileAnalyzeRecord(fileAnalyzeRecordDO);
    }


}
