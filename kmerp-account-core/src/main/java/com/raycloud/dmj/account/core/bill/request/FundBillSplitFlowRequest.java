package com.raycloud.dmj.account.core.bill.request;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class FundBillSplitFlowRequest {

    /**
     * 分类code
     */
    private String categoryCode;

    /**
     * 流水类别ID
     */
    private Long categoryId;

    /**
     * 流水子类别ID
     */
    private Long subCategoryId;

    /**
     * 金额(分)（正数收入，负数支出）
     */
    private BigDecimal amount;

}
