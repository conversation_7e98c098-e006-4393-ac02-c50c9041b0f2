package com.raycloud.dmj.account.core.base.domain;

import com.raycloud.dmj.account.core.common.BaseInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 页面列配置信息表实体类
 * 对应表：page_column_config
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PageColumnConfigDO extends BaseInfo {

    /**
     * 主键ID，自增
     */
    private Long id;

    /**
     * 页面ID
     */
    private Long pageId;

    /**
     * 列编码
     */
    private String colCode;

    /**
     * 列名称
     */
    private String colTitle;

    /**
     * 列宽，默认100
     */
    private Integer width;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否可见(0 不可见 1可见)，默认0
     */
    private Integer visible;
}
