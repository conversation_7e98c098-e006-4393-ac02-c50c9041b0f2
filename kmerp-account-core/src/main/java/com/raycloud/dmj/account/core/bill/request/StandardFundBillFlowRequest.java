package com.raycloud.dmj.account.core.bill.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.raycloud.dmj.account.core.common.Page;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
public class StandardFundBillFlowRequest extends Page {

    /**
     * 发生开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 发生结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 平台编码集合
     */
    private List<String> platformTypeList;

    /**
     * 店铺ID集合
     */
    private List<Long> shopIdList;

    /**
     * 资金账户ID集合
     */
    private List<Long> accountIdList;

    /**
     * 子类别ID集合
     */
    private List<Long> subCategoryIdList;

    /**
     * 收支方向
     */
    private Integer incomeExpenseDirection;

    /**
     * 关联订单流水号集合
     */
    private List<String> billNoList;

    /**
     * 关联账户订单号集合
     */
    private List<String> orderNoList;

    /**
     * 关联业务单据号
     */
    private String docNo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否手动拆分（0未拆分 1已拆分）
     */
    private Integer manualSplit;

    /**
     * 是否可抵消 （0否 1是）
     */
    private Integer isOffset;

    /**
     * 确认状态（0未确认 1已确认）
     */
    private Integer confirmed;

    /**
     * 是否关联业务单据（0否 1是）
     */
    private Integer isRelatedDoc;

    /**
     * 来源方式来源方式(1 账单,0 系统)
     */
    private Integer source;

    /**
     * 页面ID
     */
    private Long pageId;

}
