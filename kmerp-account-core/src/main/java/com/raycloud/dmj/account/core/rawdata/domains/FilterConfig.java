package com.raycloud.dmj.account.core.rawdata.domains;

import com.raycloud.dmj.account.core.enums.FilterTypeEnum;
import lombok.Data;

/**
 * 筛选配置
 * <AUTHOR>
 */
@Data
public class FilterConfig {

    /**
     * 是否过滤
     */
    private Boolean filterFlag;

    /**
     * 过滤字段
     */
    private String filterField;

    /**
     * 过滤类型
     * @see FilterTypeEnum
     */
    private String filterTypeCode;

}
