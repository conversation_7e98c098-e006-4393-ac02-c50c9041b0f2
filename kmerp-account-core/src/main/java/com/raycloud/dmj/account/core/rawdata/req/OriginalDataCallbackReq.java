package com.raycloud.dmj.account.core.rawdata.req;

import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
public class OriginalDataCallbackReq  implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 租户ID
     */
    private Long companyId;

    /**
     * RPA数据来源ID
     * 55-微信，58-天猫保证金账单，59-天猫消费积分，60-天猫集分宝，61-天猫惠营宝，62-天猫直播红包，63-天猫淘宝联盟，64-天猫淘金币
     */
    private Integer processType;

    /**
     * 回调参数
     */
    private List<OriginalDataParam> fileList;
}
