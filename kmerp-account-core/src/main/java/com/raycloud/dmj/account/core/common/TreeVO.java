package com.raycloud.dmj.account.core.common;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 树形结构 用于前端下拉选择
 * <AUTHOR>
 */
@Data
public class TreeVO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 值
     */
    private Object value;

    /**
     * 标签
     */
    private String label;

    /**
     * 是否禁用
     */
    private Boolean disabled=Boolean.FALSE;

    /**
     * 子类
     */
    private List<TreeVO> children;
}
