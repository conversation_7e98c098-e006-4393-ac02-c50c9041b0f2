package com.raycloud.dmj.account.core.enums.field;

import lombok.Getter;

/**
 * Date:  2025/6/16
 * <AUTHOR>
 */
@Getter
public enum FileAnalyzeErrorLogEnum {


    ID("id", "主键ID"),
    RECORD_ID("record_id", "记录ID"),
    ERROR_LOG_FILE("error_log_file", "错误日志文件"),
    CREATED("created", "创建时间"),
    MODIFIED("modified", "修改时间"),
    CREATED_BY("created_by", "创建人"),
    COMPANY_ID("company_id", "租户id"),
    SHOP_ID("shop_id", "（店铺id）")
    ;

    /**
     * 字段编码
     */
    private final String fieldCode;

    /**
     * 字段描述
     */
    private final String fieldDesc;

    FileAnalyzeErrorLogEnum(String fieldCode, String fieldDesc){
        this.fieldCode = fieldCode;
        this.fieldDesc = fieldDesc;
    }


}
