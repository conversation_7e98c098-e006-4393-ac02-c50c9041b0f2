package com.raycloud.dmj.account.core.rawdata.handle.dataset;


import com.raycloud.dmj.account.core.enums.DataSourceTypeEnum;
import com.raycloud.dmj.account.core.rawdata.handle.dataset.handle.SqlBuildByMysql;
import com.raycloud.dmj.account.core.rawdata.handle.dataset.handle.SqlBuildByPG;

import java.util.HashMap;
import java.util.Map;

/**
 * SQL构建处理工厂
 * <AUTHOR>
 */
public class SqlBuildFactory {

    private static final Map<String, SqlBuildHandler> HANDLER_MAP = new HashMap<>();

    static {
        HANDLER_MAP.put(DataSourceTypeEnum.MYSQL.getCode(), new SqlBuildByMysql());
        HANDLER_MAP.put(DataSourceTypeEnum.POSTGRESQL.getCode(), new SqlBuildByPG());
    }

    public static SqlBuildHandler getHandler(String type){

        return HANDLER_MAP.get(type);
    }

    public static boolean hasSupport(String type){
        return HANDLER_MAP.containsKey(type);
    }

}
