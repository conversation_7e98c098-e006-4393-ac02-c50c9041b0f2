package com.raycloud.dmj.account.core.enums.field;

import lombok.Getter;

/**
 * Date:  2025/6/16
 * <AUTHOR>
 */
@Getter
public enum FileAnalyzeTableConfigFieldEnum {


    ID("id", "主键ID"),
    CONFIG_ID("config_id", "文件解析表配置id"),
    TABLE_NAME("table_name", "表名"),
    TABLE_DATA_TYPE("table_data_type", "1:明细表  2：去重表"),
    BATCH_NO("batch_no", "批次号"),
    UNIQUE_KEY("unique_key", "唯一键"),
    TABLE_FIELDS("table_fields", "最终的数据映射"),
    TABLE_SOURCE_CONFIG("table_source_config", "表的入库源信息配置"),
    CREATED("created", "创建时间"),
    MODIFIED("modified", "修改时间"),
    ENABLE_STATUS("enable_status", "0:弃用 1：正常");

    /**
     * 字段编码
     */
    private final String fieldCode;

    /**
     * 字段描述
     */
    private final String fieldDesc;

    FileAnalyzeTableConfigFieldEnum(String fieldCode, String fieldDesc){
        this.fieldCode = fieldCode;
        this.fieldDesc = fieldDesc;
    }


}
