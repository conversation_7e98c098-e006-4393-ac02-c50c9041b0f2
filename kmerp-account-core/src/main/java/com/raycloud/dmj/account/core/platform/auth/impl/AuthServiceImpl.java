package com.raycloud.dmj.account.core.platform.auth.impl;

import com.alibaba.fastjson2.JSONObject;
import com.raycloud.dmj.account.IPlatformAuthDubbo;
import com.raycloud.dmj.account.common.*;
import com.raycloud.dmj.account.core.platform.auth.AbstractPlatformHandler;
import com.raycloud.dmj.account.core.platform.auth.AuthHandlerFactory;
import org.apache.dubbo.config.annotation.DubboService;
import javax.annotation.Resource;

@DubboService
public class AuthServiceImpl implements IPlatformAuthDubbo {

    @Resource
    private AuthHandlerFactory authHandlerFactory;

    @Override
    public DubboResponse<CreateAuthResponse> createAuthUrl(CreateAuthUrlReq req) {
        AbstractPlatformHandler authHandler = authHandlerFactory.getHandler(req.getPlatformType().getValue());
        return DubboResponse.successOf(authHandler.authHandle(req.getPlatformType(), req.getCompanyId(), req.getShopId(), req.getCallbackUrl(), req.getExtraData()));
    }

    @Override
    public DubboResponse<AuthInfoResponse> getAuthInfo(AuthInfoReq req) {
        AbstractPlatformHandler authHandler = authHandlerFactory.getHandler(req.getPlatformType().getValue());
        return DubboResponse.successOf(authHandler.getAuthInfo(req.getPlatformType(), req.getCompanyId(), req.getShopId()));
    }

}
