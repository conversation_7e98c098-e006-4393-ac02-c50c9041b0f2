package com.raycloud.dmj.account.core.cleancategory.domain.object;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 微信原始数据表
 * <AUTHOR>
 */
@Data
public class TmallWechatRawBillDataDO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID，自增
     */
    private Long id;

    /**
     * 入账时间
     */
    private Date entryTime;

    /**
     * 支付流水号
     */
    private String paymentFlowNo;

    /**
     * 淘宝订单编号
     */
    private String taobaoOrderNo;

    /**
     * 入账类型
     */
    private String entryType;

    /**
     * 收入金额
     */
    private BigDecimal incomeAmount;

    /**
     * 支出金额
     */
    private BigDecimal expenseAmount;

    /**
     * 业务描述
     */
    private String bizDescription;

    /**
     * 备注
     */
    private String remark;

    /**
     * 下载时间
     */
    private Date downloadTime;

    /**
     * 下载账户
     */
    private String downloadAccount;

    /**
     * 唯一键
     */
    private String bizKey;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 批次时间
     */
    private Integer batchTime;

    /**
     * 公司ID
     */
    private Long companyId;

    /**
     * 店铺ID
     */
    private String shopId;

    /**
     * 创建时间
     */
    private Date created;
}    