package com.raycloud.dmj.account.core.enums;

import lombok.Getter;

/**
 * 原始数据导入状态枚举
 * <AUTHOR>
 */
@Getter
public enum MonitorStatusEnum {

    //数据状态，0-导入中， 30-导入失败  50-导入成功

    IMPORTING(0, "导入中"),
    IMPORT_FAIL(30, "导入失败"),
    IMPORT_SUCCESS(50, "导入成功");


    private final Integer status;

    private final String desc;

    MonitorStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
