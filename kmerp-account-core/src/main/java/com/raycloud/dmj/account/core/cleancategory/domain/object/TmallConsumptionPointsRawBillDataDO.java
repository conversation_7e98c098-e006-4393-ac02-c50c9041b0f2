package com.raycloud.dmj.account.core.cleancategory.domain.object;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class TmallConsumptionPointsRawBillDataDO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 自增主键
     */
    private Long id;
    /**
     * 店铺简称
     */
    private String sortTitle;
    /**
     * 账期，格式如202503
     */
    private Integer billingCycle;
    /**
     * 资金方向
     */
    private String fundDirection;
    /**
     * 账单大类
     */
    private String merchantCategory;
    /**
     * 业务大类
     */
    private String businessCategory;
    /**
     * 业务小类
     */
    private String businessSubCategory;
    /**
     * 时间
     */
    private Date businessTime;
    /**
     * 订单号
     */
    private String orderSn;
    /**
     * 积分类服务金额
     */
    private BigDecimal pointsCouponAmount;
    /**
     * 支付宝订单号
     */
    private String alipayOrderSn;
    /**
     * 备注
     */
    private String remark;
    /**
     * 结算渠道
     */
    private String settlementChannel;
    /**
     * 交易主订单号
     */
    private String transactionMainOrderSn;
    /**
     * 交易子订单号
     */
    private String transactionSubOrderSn;
    /**
     * 商品ID
     */
    private String productId;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 数量
     */
    private Integer quantity;
    /**
     * 优惠总金额
     */
    private BigDecimal totalDiscountAmount;
    /**
     * 平台出资比例
     */
    private String platformContributionRatio;
    /**
     * 下载时间
     */
    private Date downloadTime;
    /**
     * 下载账户
     */
    private String downloadAccount;
    /**
     * 唯一键
     */
    private String bizKey;
    /**
     * 批次号
     */
    private String batchNo;
    /**
     * 批次时间 格式如********
     */
    private Integer batchTime;
    /**
     * 租户ID
     */
    private Long companyId;
    /**
     * 店铺ID
     */
    private String shopId;
    /**
     * 创建时间
     */
    private Date created;
}