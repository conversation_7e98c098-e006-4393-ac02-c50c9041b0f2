package com.raycloud.dmj.account.core.rawdata.domains;

import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 文件分析错误日志实体类，对应数据库表 file_analyze_error_log
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileAnalyzeErrorLogDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 日志ID
     */
    private Long id;

    /**
     * 导入记录的id
     */
    private Long recordId;

    /**
     * OSS的Excel文件路径
     */
    private String errorLogFile;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 修改时间
     */
    private Date modified;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 租户ID
     */
    private Long tenantId;
    /**
     * 用户id
     */
    private Long shopUniId;
}