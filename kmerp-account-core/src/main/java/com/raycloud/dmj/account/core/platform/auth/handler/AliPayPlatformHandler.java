package com.raycloud.dmj.account.core.platform.auth.handler;

import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Maps;
import com.raycloud.common.Md5Encrypt;
import com.raycloud.dmj.account.common.AuthInfoResponse;
import com.raycloud.dmj.account.common.SharedDataResponse;
import com.raycloud.dmj.account.core.enums.FileTypeEnum;
import com.raycloud.dmj.account.core.platform.auth.AbstractPlatformHandler;
import com.raycloud.dmj.account.core.platform.base.domain.ShopAuthRecordDO;
import com.raycloud.dmj.account.core.platform.common.constant.AlipayConstant;
import com.raycloud.dmj.account.core.platform.service.IShopAuthRecordService;
import com.raycloud.dmj.account.core.rawdata.utils.oss.OssUtils;
import com.raycloud.dmj.account.enums.AuthStatus;
import com.raycloud.dmj.account.enums.DataType;
import com.raycloud.dmj.account.enums.PlatformType;
import com.raycloud.dmj.account.exeception.DubboBizException;
import com.raycloud.dmj.account.infra.utils.HttpUtils;
import com.raycloud.dmj.account.infra.utils.TimeUtils;
import com.raycloud.dmj.account.utils.ShopIdUtil;
import com.raycloud.middle.gateway.common.domain.GatewayAppInfo;
import com.raycloud.middle.gateway.dubbo.MiddleGatewayDubbo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import javax.crypto.spec.IvParameterSpec;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

import static com.raycloud.dmj.account.core.platform.common.constant.AlipayConstant.SPLIT_SYMBOL;

@Slf4j
@Service
public class AliPayPlatformHandler extends AbstractPlatformHandler {

    @Resource
    private IShopAuthRecordService shopAuthRecordService;

    @DubboReference(check = false, registry = "middleZk",version = "1.0.0")
    private static MiddleGatewayDubbo middleGatewayDubbo;

    @Value("${spring.profiles.active}")
    private String env;

    public String getCallBackUrl() {
        if (StringUtils.equals(env, "prod")) {
            return "https://www.raycloud.com/account/shop/auth/callback";
        }
        return AlipayConstant.CALLBACK_URL;
    }

    private static final Map<Long, GatewayAppInfo> APP_INFO_MAP = Maps.newConcurrentMap();

    private GatewayAppInfo getAppInfo(Long appId) {
        if (APP_INFO_MAP.isEmpty() || !APP_INFO_MAP.containsKey(appId)) {
            List<GatewayAppInfo> list = middleGatewayDubbo.getValidGatewayAppsWithSecret();
//            List<GatewayAppInfo> list  = new  ArrayList<>();
//            list.add(  JSONObject.parseObject(" {\n" +
//                    "            \"appId\": \"5\",\n" +
//                    "            \"appKey\": \"F6P9JX7I\",\n" +
//                    "            \"appSecret\": \"E8521D2B5D60CF68E31E79324B3ECE36EAD5777B13F0693E\",\n" +
//                    "            \"platform\": \"TAOBAO\",\n" +
//                    "            \"dept\": \"CJDZ\",\n" +
//                    "            \"appName\": \"淘宝店长企业版\",\n" +
//                    "            \"platAppKey\": null,\n" +
//                    "            \"platAppSecret\": null,\n" +
//                    "            \"platRandomNum\": null\n" +
//                    "        }",GatewayAppInfo.class));

            if (CollectionUtils.isEmpty(list)) {
                throw new DubboBizException("获取支付宝应用信息列表失败");
            }
            for (GatewayAppInfo info : list) {
                info.setAppSecret(decrypt(info.getAppSecret(), "ray-mid^"));
                info.setPlatAppKey(decrypt(info.getPlatAppKey(), "ray-mid^"));
                info.setPlatAppSecret(decrypt(info.getPlatAppSecret(), "ray-mid^"));
                info.setPlatRandomNum(decrypt(info.getPlatRandomNum(), "ray-mid^"));
                log.info("获取支付宝应用信息[AppId:{}][info:{}]", info.getAppId(), JSONObject.toJSONString(info));
                APP_INFO_MAP.put(info.getAppId(), info);
            }
        }
        return APP_INFO_MAP.get(appId);
    }


    @Override
    public PlatformType getPlatformType() {
        return PlatformType.ALIPAY;
    }

    @Override
    public String getAuthUrl(PlatformType type, Long companyId, Long shopId, String callbackUrl, Long recordId, Map<String, Object> extraData) {
        String shopUniId = companyId + SPLIT_SYMBOL + shopId;
        JSONObject content = new JSONObject();
        content.put("userId", ShopIdUtil.getShopId(shopUniId));
        content.put("appId", AlipayConstant.MIDDLE_ID);
        content.put("callbackUrl", getCallBackUrl());
        content.put("callbackObj", shopUniId + SPLIT_SYMBOL + recordId);
        try {
            return AlipayConstant.AUTH_URL + "?" +
                    "app_id=" + AlipayConstant.MIDDLE_APP_ID +
                    "&application_type=WEBAPP,MOBILEAPP" +
                    "&redirect_uri=https%3A%2F%2Fpublic-gateway.kuaidizs.cn%2Fpay%2Fcallback" +
                    "&conetent=" + URLEncoder.encode(content.toJSONString(), "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public String upload2Oss(String url, DataType dataType, Long companyId, Long shopId, LocalDate date) {
        // 下载文件 支付宝文件三十秒未下载则链接失效
        String objectName = OssUtils.getSharedDataObjectName(
                dataType.getValue() + "_" + companyId + "_" + shopId + "_" + date.toString()
                        + "." + FileTypeEnum.CSV.getFileType() + "." + FileTypeEnum.ZIP.getFileType()
        );
        OssUtils.uploadByUrl(url, objectName);
        return objectName;
    }

    public String callback(Boolean isAuth, String callbackObj, String sign, String message) {
        log.info("alipay auth callback params: [isAuth:{}][callbackObj:{}][sign:{}][message:{}]", isAuth, callbackObj, sign, message);
        String[] obj = callbackObj.split(SPLIT_SYMBOL);
        callbackObj = obj[0] + SPLIT_SYMBOL + obj[1];
        ShopAuthRecordDO shopAuthRecordDO = shopAuthRecordService.queryShopAuthRecordById(Long.parseLong(obj[2]));
        if (shopAuthRecordDO == null) {
            log.error("alipay auth callback error , recordId:{}", obj[2]);
            throw new RuntimeException("查询店铺授权记录失败");
        }
        String callbackUrl = shopAuthRecordDO.getCallBackUrl();
        AuthStatus status = AuthStatus.DISABLE;
        if (isAuth) {
            status = AuthStatus.AUTHORIZE;
            ShopAuthRecordDO updatedRecord = new ShopAuthRecordDO();
            updatedRecord.setId(shopAuthRecordDO.getId());
            updatedRecord.setAuthStatus(status.getValue());
            try {
                shopAuthRecordService.updateShopAuthRecord(updatedRecord);
                log.info("成功更新店铺授权记录，记录 ID: {}", shopAuthRecordDO.getId());
            } catch (Exception e) {
                log.error("更新店铺授权记录失败，记录 ID: {}", shopAuthRecordDO.getId(), e);
                throw new RuntimeException("更新店铺授权记录失败", e);
            }
        }
        insertOrUpdatePlatformAuth(
                shopAuthRecordDO.getShopId(),
                shopAuthRecordDO.getCompanyId(),
                status.getValue(),
                sign,
                isAuth ? LocalDateTime.now().plusYears(1).plusDays(-1) : null,
                new JSONObject());
        return buildRedirectUrl(callbackUrl, isAuth, callbackObj, sign, message);
    }

    public AuthInfoResponse getAuthInfo(PlatformType type, Long companyId, Long shopId) {
        String shopUniId = String.valueOf(ShopIdUtil.getShopId(companyId + SPLIT_SYMBOL + shopId));
        GatewayAppInfo appInfo = getAppInfo(AlipayConstant.MIDDLE_ID);
        if (appInfo == null) {
            throw new DubboBizException("获取支付宝应用-淘宝店长企业版失败");
        }
        JSONObject object = new JSONObject();
        object.put("userIds", shopUniId);
        Map<String, String> param = new HashMap<>();
        param.put("method", "pay.authList");
        param.put("appKey", appInfo.getAppKey());
        param.put("timestamp", Long.toString(new Timestamp(new Date().getTime()).getTime()));
        param.put("userId", shopUniId);
        param.put("version", "1.0");
        param.put("params", JSONObject.toJSONString(object));
        param.put("sign", signParam(param, appInfo.getAppSecret()));

        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/x-www-form-urlencoded");
        String response = HttpUtils.httpPostForm(AlipayConstant.MIDDLE_WEB_URL, param, headers, 20000);
        JSONObject resp = JSONObject.parseObject(response);
        Integer code = resp.getInteger("code");
        String errorMsg = resp.getString("errorMsg");
        JSONObject data = resp.getJSONObject("data");
        JSONObject value = data.getJSONObject(shopUniId);
        AuthInfoResponse dto = new AuthInfoResponse();
        dto.setCode(code);
        if (code != 100) {
            dto.setErrorMsg(errorMsg);
            return dto;
        }
        if (value != null) {
            dto.setCompanyId(companyId);
            dto.setShopId(shopId);
            dto.setAuth(value.getBoolean("isAuth"));
            dto.setEndTime(TimeUtils.convertToDate(value.getString("endTime")));
            dto.setSignDate(TimeUtils.convertToDate(value.getString("signDate")));
            return dto;
        }
        return null;
    }

    // 封装构建重定向 URL 的方法
    private String buildRedirectUrl(String baseUrl, Boolean isAuth, String callbackObj, String sign, String message) {
        StringBuilder redirectUrl = new StringBuilder(baseUrl);
        try {
            redirectUrl.append("?isAuth=").append(isAuth)
                    .append("&callbackObj=").append(URLEncoder.encode(callbackObj, "UTF-8"))
                    .append("&sign=").append(URLEncoder.encode(sign, "UTF-8"))
                    .append("&message=").append(URLEncoder.encode(message, "UTF-8"));
        } catch (UnsupportedEncodingException e) {
            log.error("构建重定向 URL 时编码失败", e);
            throw new RuntimeException("构建重定向 URL 时编码失败", e);
        }
        return redirectUrl.toString();
    }

    @Override
    public SharedDataResponse getDataUrl(Long companyId, Long shopId, LocalDate billDate, DataType dataType, String extraData){
        Long shopUniId = ShopIdUtil.getShopId(companyId + SPLIT_SYMBOL + shopId);
        GatewayAppInfo appInfo = getAppInfo(AlipayConstant.MIDDLE_ID);
        if (appInfo == null) {
            throw new DubboBizException("获取支付宝应用-淘宝店长企业版失败");
        }
        com.alibaba.fastjson.JSONObject object = new com.alibaba.fastjson.JSONObject();
        object.put("billType", "signcustomer");
//        object.put("billType", "trade");
        object.put("billDate", billDate.toString());
        Map<String, String> param = new HashMap<>();
        param.put("method", "pay.queryBill");
        param.put("appKey", appInfo.getAppKey());
        param.put("timestamp", Long.toString(new Timestamp(new Date().getTime()).getTime()));
        param.put("userId", String.valueOf(shopUniId));
        param.put("version", "1.0");
        param.put("params", com.alibaba.fastjson.JSONObject.toJSONString(object));
        param.put("sign", signParam(param, appInfo.getAppSecret()));

        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/x-www-form-urlencoded");
        String response = HttpUtils.httpPostForm(AlipayConstant.MIDDLE_WEB_URL, param, headers, 20000);
        log.info("alipay get bill, params :{} response:{}", JSONObject.toJSONString(param), response);
        com.alibaba.fastjson.JSONObject resp = com.alibaba.fastjson.JSONObject.parseObject(response);
        Integer code = resp.getInteger("code");
        String errorMsg = resp.getString("errorMsg");
        String data = resp.getString("data");
        if (code != 100) {
            // 账单不存在的情况直接忽略
            if (errorMsg.contains("账单不存在")) {
                return SharedDataResponse.error(SharedDataResponse.DataNotExistCode, "账单不存在");
            }
            if (errorMsg.contains("支付宝认证失效")) {
                return SharedDataResponse.error(SharedDataResponse.AuthDisableCode, "支付宝认证失效");
            }
            return SharedDataResponse.error();
        }
        return SharedDataResponse.success(data);
    }

    public static String signParam(Map<String, String> gatewayParam, String appSecret) {
        Map<String, String> params = new HashMap(6);
        params.put("method", gatewayParam.get("method"));
        params.put("timestamp", gatewayParam.get("timestamp"));
        params.put("version", gatewayParam.get("version"));
        params.put("appKey", gatewayParam.get("appKey"));
        params.put("params", gatewayParam.get("params"));
        params.put("userId", gatewayParam.get("userId"));
        return sign(params, appSecret);
    }

    private static String sign(Map<String, String> params, String appSecret) {
        StringBuilder sb = new StringBuilder(appSecret);
        String[] keys = (String[]) params.keySet().toArray(new String[0]);
        Arrays.sort(keys);
        int len$ = keys.length;

        for (int i$ = 0; i$ < len$; ++i$) {
            String key = keys[i$];
            String value = (String) params.get(key);
            if (StringUtils.isNotBlank(key) && StringUtils.isNotBlank(value)) {
                sb.append(key).append(value);
            }
        }

        sb.append(appSecret);
        return Md5Encrypt.md5(sb.toString()).toUpperCase();
    }

    public static final String decrypt(String password, String key) {
        if (password == null || key == null) {
            return null;
        }
        try {
            return new String(decrypt(String2byte(password.getBytes()), key.getBytes()));
        } catch (Exception var3) {
            var3.printStackTrace();
            return null;
        }
    }

    public static byte[] decrypt(byte[] src, byte[] key) throws Exception {
        DESKeySpec dks = new DESKeySpec(key);
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
        SecretKey securekey = keyFactory.generateSecret(dks);
        Cipher cipher = Cipher.getInstance("DES/CBC/PKCS5Padding");
        IvParameterSpec iv = new IvParameterSpec(key);
        cipher.init(2, securekey, iv);
        return cipher.doFinal(src);
    }

    public static byte[] String2byte(byte[] b) {
        if (b.length % 2 != 0) {
            throw new IllegalArgumentException("长度不是偶数");
        } else {
            byte[] b2 = new byte[b.length / 2];

            for (int n = 0; n < b.length; n += 2) {
                String item = new String(b, n, 2);
                b2[n / 2] = (byte) Integer.parseInt(item, 16);
            }
            return b2;
        }
    }

}
