package com.raycloud.dmj.account.core.cleancategory.domain.object;

import com.alibaba.fastjson2.JSONObject;
import com.raycloud.dmj.account.core.enums.feature.AnalyzeCategoryRecordFeatureEnum;
import com.raycloud.dmj.account.core.enums.feature.BillAgainAnalyzeTaskFeatureEnum;
import com.raycloud.dmj.account.core.enums.field.CategoryAnalyzeStatusEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Date;

@Data
public class AnalyzeCategoryRecordDO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 更新时间
     */
    private Date modified;

    /**
     * 时间类型，默认为1，1为日，2为月，3为年
     */
    private Integer dateType;

    /**
     * 当前数据范围，如果date_type为日，则是yyyyMMdd，如果是月，则是yyyyMM，年则是yyyy
     */
    private Integer dataRange;

    /**
     * 公司ID
     */
    private Long companyId;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 解析状态：0-解析中， 30-解析失败  50-解析成功
     * @see  CategoryAnalyzeStatusEnum
     */
    private Integer analyzeStatus;

    /**
     * 数据源
     */
    private Integer dataSource;

    /**
     * 扩展字段
     */
    private String feature;

    /**
     * 版本号
     */
    private Integer version;


    /**
     * 添加扩展字段
     *
     * @param featureEnum 扩展字段枚举
     * @param value       值
     */
    public void putFeature(AnalyzeCategoryRecordFeatureEnum featureEnum, Object value) {
        JSONObject jsonObject = StringUtils.isNotBlank(feature) ? JSONObject.parseObject(feature) : new JSONObject();
        jsonObject.put(featureEnum.getFieldCode(), value);
        feature = jsonObject.toJSONString();
    }


}