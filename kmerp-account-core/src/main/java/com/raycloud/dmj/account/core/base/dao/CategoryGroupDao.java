package com.raycloud.dmj.account.core.base.dao;

import com.raycloud.dmj.account.core.cleancategory.domain.object.CategoryGroupDO;

import java.util.List;
import java.util.Set;

/**
 * 分类Dao
 * <AUTHOR>
 */
public interface CategoryGroupDao {

    /**
     * 添加分类
     */
    Long insert(CategoryGroupDO categoryGroupDO);


    /**
     * 根据code集合查询分类
     * @param companyId 公司ID
     * @param codes 资金账户ID
     * @return 分类
     */
    List<CategoryGroupDO> queryByCodes(Long companyId, Set<String> codes);


    /**
     * 根据code修改分类
     * @param categoryGroupDO 分类
     */
    void updateByCode(CategoryGroupDO categoryGroupDO);


    /**
     * 根据code查询分类
     * @param companyId 公司ID
     * @return 分类
     */
    List<CategoryGroupDO> list(Long companyId);
}
