package com.raycloud.dmj.account.core.base.dao;

import com.raycloud.dmj.account.core.base.domain.RuleVerifyResultRecordDO;

import java.util.Date;
import java.util.List;

/**
 * 规则校验结果记录表DAO
 * <AUTHOR>
 */
public interface RuleVerifyResultRecordDao {

    /**
     * 新增规则校验结果记录
     * @param record 校验结果记录对象
     * @return 新增记录的主键ID
     */
    Long insert(RuleVerifyResultRecordDO record);

    /**
     * 根据ID查询规则校验结果记录
     * @param id 主键ID
     * @param companyId 公司ID
     * @return 校验结果记录
     */
    RuleVerifyResultRecordDO queryById(Long id, Long companyId);

    /**
     * 根据参数查询规则校验结果记录列表
     * @param ruleIdList 规则ID列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param companyId 公司ID
     * @return 校验结果记录VO列表
     */
    List<RuleVerifyResultRecordDO> queryByParam(List<Long> ruleIdList, Date startTime, Date endTime, Long companyId);

    /**
     * 根据规则ID查询校验结果记录列表
     * @param ruleId 规则ID
     * @param companyId 公司ID
     * @return 校验结果记录列表
     */
    List<RuleVerifyResultRecordDO> queryByRuleId(Long ruleId, Long companyId);

    /**
     * 批量新增规则校验结果记录
     * @param recordList 校验结果记录列表
     * @return 新增的记录数
     */
    int batchInsert(List<RuleVerifyResultRecordDO> recordList);

    /**
     * 根据资金账户ID和账期时间查询校验记录结果
     * @param ruleIdList 规则ID集合
     * @param date  帐期时间
     * @param companyId 公司ID
     * @return
     */
    List<RuleVerifyResultRecordDO> queryRuleList(List<Long> ruleIdList, Date date, Long companyId);
}
