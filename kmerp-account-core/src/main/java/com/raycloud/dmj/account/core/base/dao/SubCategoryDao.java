package com.raycloud.dmj.account.core.base.dao;

import com.raycloud.dmj.account.core.cleancategory.domain.QuerySubCategoryParam;
import com.raycloud.dmj.account.core.cleancategory.domain.SubCategoryDTO;
import com.raycloud.dmj.account.core.cleancategory.domain.object.SubCategoryDO;
import com.raycloud.dmj.account.core.common.Page;

import java.util.List;
import java.util.Set;

/**
 * 资金账户Dao
 * <AUTHOR>
 */
public interface SubCategoryDao {


    /**
     * 插入
     * @param subCategoryDO 子类别
     * @return 插入的id
     */
    Long insert(SubCategoryDO subCategoryDO);

    /**
     * 根据ID修改类目
     * @param subCategoryDO 子类别
     */
    void updateById(SubCategoryDO subCategoryDO);


    /**
     * 根据ID集合删除类目
     * @param companyId 公司ID
     * @param ids ID集合
     */
    void deleteByIds(Long companyId,List<Long> ids);



    /**
     * 根据查询条件分页查询
     * @param companyId 公司ID
     * @param param    查询条件
     * @param page 分页信息
     * @return 类别
     */
    List<SubCategoryDTO> pageQueryByParam(Long companyId, QuerySubCategoryParam param, Page page);



    /**
     * 根据查询条件查询子类目ID集合
     * @param companyId 公司ID
     * @param param    查询条件
     * @return 类别
     */
    List<Long> queryIdsByParam(Long companyId, QuerySubCategoryParam param);


    /**
     * 根据查询条件查询数量
     * @param companyId 公司ID
     * @param param 查询条件
     * @return  数量
     */
    Long countByParam(Long companyId, QuerySubCategoryParam param);


    /**
     * 根据公司ID和资金账户和名称查询类别
     * @param companyId 公司ID
     * @param categoryId 类别ID
     * @param subCategoryName 子类别名称
     * @return 类别
     */
    SubCategoryDO queryByCategoryIdAndName(Long companyId, Long categoryId,
                                           String  subCategoryName);


    /**
     * 根据公司ID和参数查询类别
     * @param companyId 公司ID
     * @param querySubCategoryParam 查询参数
     * @return 类别
     */
    List<SubCategoryDO> queryByParam(Long companyId, QuerySubCategoryParam querySubCategoryParam);

    /**
     * 根据公司ID和类别ID查询类别
     * @param companyId 公司ID
     * @param ids 子类别ID
     * @return 子类别
     */
    List<SubCategoryDO> queryByIds(Long companyId, List<Long> ids);


    /**
     * 根据公司ID查询子类别
     * @param companyId 公司ID
     * @return
     */
    List<SubCategoryDO> queryByCompanyId(Long companyId);

    /**
     * 根据公司ID、类别ID和子类别名称查询子类别
     * @param companyId 公司ID
     * @param categoryId 类别ID
     * @param subCategoryName 子类别名称
     * @return 子类别
     */
    SubCategoryDO getByCategoryIdAndName(Long companyId, Long categoryId, String subCategoryName);


    /**
     * 根据公司ID、类别ID和子类别名称查询子类别
     * @param companyId 公司ID
     * @param categoryIds 类别ID
     * @return 子类别
     */
    List<SubCategoryDO> listByCategoryIds(Long companyId, Set<Long> categoryIds);


}
