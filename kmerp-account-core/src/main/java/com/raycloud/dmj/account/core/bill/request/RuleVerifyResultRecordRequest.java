package com.raycloud.dmj.account.core.bill.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 规则校验结果记录查询请求对象
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RuleVerifyResultRecordRequest {

    /**
     * 平台code
     */
    private String  platformCode;

    /**
     * 店铺ID
     */
    private List<Long> shopIdList;

    /**
     * 帐期开始时间
     */
    private String startTime;

    /**
     * 帐期结束时间
     */
    private String endTime;

    /**
     * 帐期类型 1:日账单 2:月账单
     */
    private Integer billingCycleType;

}
