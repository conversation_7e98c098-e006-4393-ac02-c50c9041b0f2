package com.raycloud.dmj.account.core.enums;

import com.raycloud.dmj.account.core.common.TreeVO;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 操作符枚举
 *
 * <AUTHOR>
 */
@Getter
public enum AnalyzeRuleOperatorEnum {

    EQUAL("EQUAL", "等于"),
    CONTAINS("CONTAINS", "包含"),
    STARTS_WITH("STARTS_WITH", "开头"),
    ENDS_WITH("ENDS_WITH", "结尾"),
    REGEX("REGEX", "正则"),
    ;

    /**
     * 操作符编码
     */
    private final String operatorCode;

    /**
     * 操作符描述
     */
    private final String operatorDesc;

    AnalyzeRuleOperatorEnum(String operatorCode, String operatorDesc) {
        this.operatorCode = operatorCode;
        this.operatorDesc = operatorDesc;
    }

    /**
     * 根据操作符编码获取操作符枚举
     *
     * @param operatorCode 操作符编码
     * @return 操作符枚举
     */
    public static AnalyzeRuleOperatorEnum getByOperatorCode(String operatorCode) {
        for (AnalyzeRuleOperatorEnum value : values()) {
            if (value.operatorCode.equals(operatorCode)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 获取操作符枚举列表
     *
     * @return 操作符枚举列表
     */
    public static List<TreeVO> listTreeVO() {

        return Arrays.stream(AnalyzeRuleOperatorEnum.values())
                //正则功能暂不给用户使用
                .filter(x -> !REGEX.equals(x)).map(x -> {
                    TreeVO treeVO = new TreeVO();
                    treeVO.setValue(x.operatorCode);
                    treeVO.setLabel(x.operatorDesc);
                    return treeVO;
                }).collect(Collectors.toList());
    }
}
