package com.raycloud.dmj.account.core.enums.field;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 账户信息表字段枚举，用于描述表中各字段的编码及对应含义
 * <AUTHOR>
 */
@Getter
public enum FundAccountFieldEnum {

    ID("id", "主键ID"),
    CREATED("created", "创建时间"),
    MODIFIED("modified", "修改时间"),
    SHOP_ID("shop_id", "店铺ID"),
    COMPANY_ID("company_id", "公司ID"),
    ACCOUNT_NAME("account_name", "资金账户名称(支付宝、微信、默认账户)"),
    TYPE("type", "资金账户类型(1-系统支付宝、2-系统微信)"),
    SOURCE("source", "来源 1-系统，2-手动"),
    ACCOUNT_CODE("account_code", "账户编码"),
    START_BALANCE("start_balance", "期初余额"),
    START_DATE("start_date", "期初时间"),
    CONFIRM_START_PERIOD("confirm_start_period", "是否确认初期，0-未确认，1-确认"),
    AUTHORIZE("authorize", "是否授权，0-未授权，1-授权");

    /**
     * 字段编码，对应数据库表中的字段名
     */
    private final String fieldCode;
    /**
     * 字段描述，说明该字段代表的业务含义
     */
    private final String fieldDesc;

    FundAccountFieldEnum(String fieldCode, String fieldDesc) {
        this.fieldCode = fieldCode;
        this.fieldDesc = fieldDesc;
    }


    /**
     * 获取插入字段
     * @return 插入字段
     */
    public static Set<String> getInsertFields() {
        List<FundAccountFieldEnum> filterField = Arrays.asList(
                FundAccountFieldEnum.ID
        );
        return Arrays.stream(FundAccountFieldEnum.values()).filter(x -> !filterField.contains(x)).map(
                x -> x.fieldCode
        ).collect(Collectors.toSet());

    }
}