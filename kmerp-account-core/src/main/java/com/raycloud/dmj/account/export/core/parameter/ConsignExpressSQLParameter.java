package com.raycloud.dmj.account.export.core.parameter;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.raycloud.dmj.domain.account.Staff;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ConsignExpressSQLParameter implements Serializable {

    /**
     * 追踪ID
     */
    private Long clueId;

    /**
     * 登录用户
     */
    private Staff staff;

    /**
     * 页面ID
     */
    private Long pageId;


    /**
     * 生效列的列名称
     */
    private List<String> effectiveColumnNames;

    /**
     * 分类名称集合
     */
    private Map<String, String> categoryGroupCodeNames;

    /**
     * 类别名称集合
     */
    private Map<Long, String> categoryNames;

    /**
     * 子类别名称集合
     */
    private Map<Long, String> subCategoryNames;


}
