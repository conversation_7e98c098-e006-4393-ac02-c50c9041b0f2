package com.raycloud.dmj.account.core.cleancategory.strategy.fund;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.raycloud.dmj.account.core.base.dao.*;
import com.raycloud.dmj.account.core.base.domain.ShopInfoDO;
import com.raycloud.dmj.account.core.base.domain.StandardFundBillFlowInfoDO;
import com.raycloud.dmj.account.core.cleancategory.domain.AnalyzeRuleInfo;
import com.raycloud.dmj.account.core.cleancategory.domain.object.*;
import com.raycloud.dmj.account.core.cleancategory.strategy.CategoryAnalyzeHandle;
import com.raycloud.dmj.account.core.cleancategory.strategy.param.CategoryAnalyzeParam;
import com.raycloud.dmj.account.core.cleancategory.strategy.param.CategoryAnalyzeRuleParam;
import com.raycloud.dmj.account.core.common.Page;
import com.raycloud.dmj.account.core.enums.*;
import com.raycloud.dmj.account.core.enums.feature.AnalyzeCategoryRecordFeatureEnum;
import com.raycloud.dmj.account.core.enums.field.CategoryAnalyzeStatusEnum;
import com.raycloud.dmj.account.core.rawdata.manage.RawDataStorageManage;
import com.raycloud.dmj.account.core.rawdata.utils.RawDataDateUtil;
import com.raycloud.dmj.account.core.rocketmq.dto.BillSummaryMsg;
import com.raycloud.dmj.account.core.rocketmq.producer.ProducerClient;
import com.raycloud.dmj.account.infra.common.BusinessException;
import com.raycloud.dmj.account.infra.common.ErrorCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;
import java.util.stream.Collectors;

/**
 * 标准资金类目解析
 *
 * <AUTHOR>
 */

@Slf4j
public abstract class StandardFundCategoryAnalyzeHandle<T> implements CategoryAnalyzeHandle {

    /**
     * 未识别的默认ID
     */
    protected static final Long DEFAULT_UNKNOWN_ID = -1L;

    /**
     * 标准资金流水创建人
     */
    protected static final String CREATOR = "admin";


    @Resource
    private AnalyzeCategoryRecordDao analyzeCategoryRecordDao;

    @Resource
    private BalanceRecordDao balanceRecordDao;

    @Resource
    private ShopInfoDao shopInfoDao;

    @Resource
    private StandardFundBillFlowInfoDao standardFundBillFlowInfoDao;

    @Resource
    private CategoryAnalyzeRuleDao categoryAnalyzeRuleDao;

    @Resource
    private SubCategoryDao subCategoryDao;

    @Resource
    private CategoryDao categoryDao;

    @Resource
    private RawDataStorageManage rawDataStorageManage;

    @Resource
    private MonitorSummaryDao monitorSummaryDao;

    @Resource
    private ProducerClient producerClient;


    /**
     * 获取资金账户
     *
     * @param param 参数
     * @return 资金账户
     */
    protected abstract FundAccountDO getFundAccount(CategoryAnalyzeParam param);


    /**
     * 获取期初/期末余额
     *
     * @param param 参数
     * @return 结果  L-期初  R-期末余额
     */
    protected abstract Pair<BigDecimal, BigDecimal> calculateBalance(CategoryAnalyzeParam param);


    /**
     * 分页查询原始数据
     *
     * @param param 参数
     * @param page  分页参数
     * @return 结果
     */
    protected abstract List<T> listPageRawData(CategoryAnalyzeParam param, Page page);


    /**
     * 设置标准账单流水
     *
     * @param standardFundBillFlowInfoDO 标准账单流水
     * @param rawDataDO                  原始数据
     */
    protected abstract void setStandardFundBillFlowInfoDO(StandardFundBillFlowInfoDO standardFundBillFlowInfoDO, T rawDataDO);


    @Override
    public void handle(CategoryAnalyzeParam param) {
        //参数检验
        paramVerify(param);
        AnalyzeCategoryRecordDO analyzeCategoryRecordDO = analyzeCategoryRecordDao.getByShopAndSourceAndDataRange(param.getCompanyId(), param.getShopId(), param.getSource().getCode(), param.getDataRange());
        //幂等性检验
        if (idempotentVerify(analyzeCategoryRecordDO)) {
            return;
        }
        //新增或更新解析记录为解析中
        AnalyzeCategoryRecordDO categoryRecordDO = insertOrUpdateAnalyzeCategoryRecord(analyzeCategoryRecordDao, param, analyzeCategoryRecordDO);
        try {
            //解析数据
            analyzeData(param);
        } catch (Exception e) {
            log.error("|StandardFundCategoryAnalyzeHandle.handle error|原始数据解析失败！，param:{}", JSON.toJSONString(param), e);
            //更新监控表状态,更新解析状态 解析失败
            categoryRecordDO.setAnalyzeStatus(CategoryAnalyzeStatusEnum.ANALYZE_FAIL.getStatus());
            categoryRecordDO.putFeature(AnalyzeCategoryRecordFeatureEnum.ERROR_MESSAGE, e.getMessage());
            analyzeCategoryRecordDao.updateAnalyzeStatusById(categoryRecordDO);
            throw e;
        }
        //更新监控表状态,更新解析状态 解析完成
        categoryRecordDO.putFeature(AnalyzeCategoryRecordFeatureEnum.ERROR_MESSAGE, null);
        categoryRecordDO.setAnalyzeStatus(CategoryAnalyzeStatusEnum.ANALYZE_SUCCESS.getStatus());
        analyzeCategoryRecordDao.updateAnalyzeStatusById(categoryRecordDO);
    }


    /**
     * 解析数据
     *
     * @param param 解析参数
     */
    private void analyzeData(CategoryAnalyzeParam param) {
        //查询店铺信息
        ShopInfoDO shopInfoDO = shopInfoDao.getShopInfoById(param.getShopId());
        if (Objects.isNull(shopInfoDO)) {
            log.error("|StandardFundCategoryAnalyzeHandle.analyzeData error|店铺不存在,param:{}", JSON.toJSONString(param));
            throw new BusinessException(ErrorCodeEnum.SHOP_NOT_EXISTS);
        }
        //根据查询资金账号类型资金账户
        FundAccountDO fundAccountDO = getFundAccount(param);
        if (Objects.isNull(fundAccountDO)) {
            log.error("|StandardFundCategoryAnalyzeHandle.analyzeData error|店铺没有配置资金账户,shopId:{}", JSON.toJSONString(param));
            throw new BusinessException(ErrorCodeEnum.FUND_ACCOUNT_NOT_EXISTS, "店铺ID:【%S】没有配置资金账户！", param.getShopId());
        }
        //校验数据是否导入成功
        verifyDataIsImport(rawDataStorageManage, param);
        //校验上期数据是否成功解析
        verifyPreviousDataRange(analyzeCategoryRecordDao, param, fundAccountDO);
        //先删除该资金账户下该批次的标准流水
        standardFundBillFlowInfoDao.deleteByAccountIdAndDataRange(param.getCompanyId(), fundAccountDO.getId(), param.getDataRange());
        //根据资金账户查询类别ID集合
        List<CategoryDO> categoryDOList = categoryDao.listByFundId(param.getCompanyId(), fundAccountDO.getId());
        if (CollectionUtils.isEmpty(categoryDOList)) {
            log.error("|StandardFundCategoryAnalyzeHandle.analyzeData error|资金账户没有配置类别,param:{},fundId:{}", JSON.toJSONString(param), JSON.toJSONString(fundAccountDO.getId()));
            throw new BusinessException(ErrorCodeEnum.SUB_CATEGORY_NOT_EXISTS, "资金账户没有配置类别!");
        }
        Set<Long> categoryIds = categoryDOList.stream().map(CategoryDO::getId).collect(Collectors.toSet());
        List<SubCategoryDO> subCategoryDOList = subCategoryDao.listByCategoryIds(param.getCompanyId(), categoryIds);
        if (CollectionUtils.isEmpty(subCategoryDOList)) {
            log.error("|StandardFundCategoryAnalyzeHandle.analyzeData error|资金账户没有配置子类别,param:{},fundId:{}", JSON.toJSONString(param), JSON.toJSONString(fundAccountDO.getId()));
            throw new BusinessException(ErrorCodeEnum.SUB_CATEGORY_NOT_EXISTS, "资金账户没有配置子类别!");
        }
        Map<Long, SubCategoryDO> subCategoryIdToObjMap = subCategoryDOList.stream()
                .collect(Collectors.toMap(SubCategoryDO::getId, x -> x));
        List<Long> subCategoryIds = subCategoryDOList.stream().map(SubCategoryDO::getId).distinct().collect(Collectors.toList());
        //根据查询解析规则
        List<CategoryAnalyzeRuleDO> categoryAnalyzeRuleDOList = categoryAnalyzeRuleDao.listBySubCategoryIds(param.getCompanyId(), subCategoryIds);
        if (CollectionUtils.isEmpty(categoryAnalyzeRuleDOList)) {
            log.error("|StandardFundCategoryAnalyzeHandle.analyzeData error|子类别没有配置解析规则,param:{},subCategoryIds:{}", JSON.toJSONString(param), JSON.toJSONString(subCategoryIds));
            throw new BusinessException(ErrorCodeEnum.ANALYZE_RULE_NOT_EXISTS, "子类别没有配置解析规则!");
        }
        List<CategoryAnalyzeRuleParam> categoryAnalyzeRuleParamList = categoryAnalyzeRuleDOList.stream().map(categoryAnalyzeRuleDO -> {
            CategoryAnalyzeRuleParam categoryAnalyzeRuleParam = new CategoryAnalyzeRuleParam();
            categoryAnalyzeRuleParam.setRuleId(categoryAnalyzeRuleDO.getId());
            categoryAnalyzeRuleParam.setSubCategoryId(categoryAnalyzeRuleDO.getSubCategoryId());
            String ruleCondition = categoryAnalyzeRuleDO.getRuleCondition();
            List<List<AnalyzeRuleInfo>> analyzeRuleInfoLists = JSON.parseObject(ruleCondition, new TypeReference<List<List<AnalyzeRuleInfo>>>() {
            });
            categoryAnalyzeRuleParam.setRuleGroups(analyzeRuleInfoLists);
            return categoryAnalyzeRuleParam;
        }).collect(Collectors.toList());

        //计算期初/期末余额
        Pair<BigDecimal, BigDecimal> pair = calculateBalance(param);
        BigDecimal startBalance = pair.getKey();
        BigDecimal endBalance = pair.getValue();

        //本期变化的余额
        BigDecimal changeBalance = BigDecimal.ZERO;
        int pageNo = 1;
        AtomicInteger batchInsertCount = new AtomicInteger(0);
        //账单全部识别标志,默认识别成功
        boolean isAllAnalyzeSuccess = true;

        while (true) {
            Page page = new Page(pageNo, DEFAULT_ANALYZE_SIZE);
            List<T> currentPage = listPageRawData(param, page);
            if (CollectionUtils.isEmpty(currentPage)) {
                break;
            }
            //构建标准资金账单流水信息
            List<StandardFundBillFlowInfoDO> standardFundBillFlowInfoDOList = currentPage.stream()
                    .map(rawData -> buildStandardFundBillFlowInfoDO(
                            rawData,
                            shopInfoDO,
                            fundAccountDO,
                            categoryAnalyzeRuleParamList,
                            subCategoryIdToObjMap)
                    )
                    .collect(Collectors.toList());

            BigDecimal reduce = standardFundBillFlowInfoDOList.stream()
                    .map(StandardFundBillFlowInfoDO::getAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            changeBalance = changeBalance.add(reduce);
            if (isAllAnalyzeSuccess) {
                //如果存在未识别的分类 则修改allAnalyzeSuccess为false
                if (standardFundBillFlowInfoDOList.stream()
                        .map(StandardFundBillFlowInfoDO::getCategoryCode)
                        .anyMatch(DEFAULT_UNKNOWN_CATEGORY_CODE::equals)) {
                    isAllAnalyzeSuccess = false;
                }
            }
            //批量插入
            Integer rowCount = standardFundBillFlowInfoDao.batchInsert(standardFundBillFlowInfoDOList);
            batchInsertCount.addAndGet(rowCount);
            // 准备查询下一页
            pageNo++;
        }
        //账单确定
        boolean isChangeBalanceEqual = changeBalance.compareTo(endBalance.subtract(startBalance)) == 0;
        if (isAllAnalyzeSuccess && isChangeBalanceEqual) {
            //如果变化的金额等于期末余额减期初余额 且账单都已识别，则账单确定
            Integer confirmSum = standardFundBillFlowInfoDao.confirmByAccountIdAndDataRange(param.getCompanyId(), fundAccountDO.getId(), param.getDataRange());
            if (batchInsertCount.get() != confirmSum) {
                log.error("|StandardFundCategoryAnalyzeHandle.analyzeData error|标准资金流水添加数量和确认数量不一致，账单确定失败！param:{},changeBalance={},endBalance={},startBalance={}", JSON.toJSONString(param), changeBalance, endBalance, startBalance);
                throw new BusinessException(ErrorCodeEnum.CONFIRM_ERROR, "标准资金流水添加数量和确认数量不一致！");
            }
        } else if (!isChangeBalanceEqual) {
            log.error("|StandardFundCategoryAnalyzeHandle.analyzeData error|当期变化的金额不等于期末余额减期初余额，账单确定失败！param:{},changeBalance={},endBalance={},startBalance={}", JSON.toJSONString(param), changeBalance, endBalance, startBalance);
            throw new BusinessException(ErrorCodeEnum.CONFIRM_ERROR, "变化的金额[%S]不等于期末余额[%S]减期初余额[%S]", changeBalance, endBalance, startBalance);
        } else {
            //存在未识别的账单
            log.error("|StandardFundCategoryAnalyzeHandle.analyzeData error|存在未识别的账单，账单确定失败！param:{}", JSON.toJSONString(param));
            throw new BusinessException(ErrorCodeEnum.CONFIRM_ERROR, "存在未识别的账单，账单确定失败！");
        }
        //添加余额到余额流水表
        insertBalanceRecord(param, fundAccountDO.getId(), startBalance, endBalance, changeBalance);
        //更新监控表解析成功时间
        monitorSummaryDao.updateAnalyzeTimeByShopAndSource(param.getCompanyId(), param.getShopId(), param.getSource().getCode());
        //发送账单确定消息
        sendBillSummaryMsg(param, fundAccountDO);

    }

    /**
     * 发送账单确定消息
     *
     * @param param         参数
     * @param fundAccountDO 基金账户
     */
    private void sendBillSummaryMsg(CategoryAnalyzeParam param, FundAccountDO fundAccountDO) {
        BillSummaryMsg billSummaryMsg = new BillSummaryMsg();
        billSummaryMsg.setAccountId(fundAccountDO.getId());
        billSummaryMsg.setBillingCycle(param.getDataRange());
        billSummaryMsg.setBillingCycleType(param.getDateType().getCode());
        billSummaryMsg.setCompanyId(param.getCompanyId());
        try {
            boolean flag = producerClient.sendBillSummaryMsg(billSummaryMsg);
            if (!flag) {
                throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR.getCode(), "发送任务消息失败！");
            }
        } catch (Exception e) {
            log.error("发送任务消息异常！", e);
            throw new BusinessException(ErrorCodeEnum.ROCKETMQ_ERROR, "发送任务消息异常！");
        }
    }

    /**
     * 校验上期数据是否成功解析
     *
     * @param param         参数
     * @param fundAccountDO 基金账户
     */
    private void verifyPreviousDataRange(CategoryAnalyzeParam param, FundAccountDO fundAccountDO) {
        Date startDate = fundAccountDO.getStartDate();
        Integer startDataRange = RawDataDateUtil.formatDate(startDate, param.getDateType());
        //如果不是期初的数据，则校验上一期数据是否到达 前一期的数据未解析完成，禁止解析
        if (!Objects.equals(startDataRange, param.getDataRange())) {
            //获取上一个账期
            Integer dataRange = param.getDataRange();
            Integer dateType = param.getDateType().getCode();
            Integer previousDataRange = RawDataDateUtil.getPreviousDataRange(dataRange, DateTypeEnum.of(dateType));
            //前一期的数据未解析完成，禁止解析
            AnalyzeCategoryRecordDO previousAnalyzeCategoryRecordDO = analyzeCategoryRecordDao.getByShopAndSourceAndDataRange(param.getCompanyId(), param.getShopId(), param.getSource().getCode(), previousDataRange);
            if (Objects.isNull(previousAnalyzeCategoryRecordDO) ||
                    !CategoryAnalyzeStatusEnum.ANALYZE_SUCCESS.getStatus().equals(previousAnalyzeCategoryRecordDO.getAnalyzeStatus())) {
                log.error("【{}】的【{}】上个账期：【{}】未解析成功，无法继续解析！", param.getDataRange(), param.getSource().getDesc(), previousDataRange);
                throw new BusinessException(ErrorCodeEnum.PARAM_ERROR.getCode(), String.format("【%S】的【%S】上个账期：【%S】未解析成功，无法继续解析！", param.getDataRange(), param.getSource().getDesc(), previousDataRange));
            }
        }
    }


    /**
     * 构建标准资金账单流水信息
     *
     * @param rawData                      原始数据
     * @param shopInfoDO                   店铺信息
     * @param fundAccountDO                资金账户
     * @param categoryAnalyzeRuleParamList 解析规则
     * @param subCategoryIdToObjMap        子类别ID映射
     * @return 标准资金账单流水信息
     */
    private StandardFundBillFlowInfoDO buildStandardFundBillFlowInfoDO(T rawData,
                                                                       ShopInfoDO shopInfoDO, FundAccountDO fundAccountDO,
                                                                       List<CategoryAnalyzeRuleParam> categoryAnalyzeRuleParamList,
                                                                       Map<Long, SubCategoryDO> subCategoryIdToObjMap) {
        StandardFundBillFlowInfoDO standardFundBillFlowInfoDO = new StandardFundBillFlowInfoDO();
        standardFundBillFlowInfoDO.setPlatformCode(shopInfoDO.getPlatformCode());
        standardFundBillFlowInfoDO.setShopId(shopInfoDO.getId());
        standardFundBillFlowInfoDO.setAccountId(fundAccountDO.getId());
        // 直接使用对象转JSON，避免中间字符串转换
        JSONObject fieldValues = (JSONObject) JSON.toJSON(rawData);
        //匹配子类别
        CategoryAnalyzeRuleParam categoryAnalyzeRuleParam = matchRule(categoryAnalyzeRuleParamList, fieldValues);
        if (Objects.nonNull(categoryAnalyzeRuleParam)) {
            //已识别
            standardFundBillFlowInfoDO.setRuleId(categoryAnalyzeRuleParam.getRuleId());
            SubCategoryDO subCategoryDO = subCategoryIdToObjMap.get(categoryAnalyzeRuleParam.getSubCategoryId());
            standardFundBillFlowInfoDO.setCategoryCode(subCategoryDO.getCategoryGroupCode());
            standardFundBillFlowInfoDO.setCategoryId(subCategoryDO.getCategoryId());
            standardFundBillFlowInfoDO.setSubCategoryId(subCategoryDO.getId());
            standardFundBillFlowInfoDO.setIsOffset(subCategoryDO.getOffset() ? 1 : 0);
        } else {
            //未识别
            standardFundBillFlowInfoDO.setRuleId(DEFAULT_UNKNOWN_ID);
            standardFundBillFlowInfoDO.setCategoryCode(DEFAULT_UNKNOWN_CATEGORY_CODE);
            standardFundBillFlowInfoDO.setCategoryId(DEFAULT_UNKNOWN_ID);
            standardFundBillFlowInfoDO.setSubCategoryId(DEFAULT_UNKNOWN_ID);
            standardFundBillFlowInfoDO.setIsOffset(null);
        }
        standardFundBillFlowInfoDO.setSource(StandardFundBillSourceEnum.BILL.getCode());
        standardFundBillFlowInfoDO.setCreator(CREATOR);
        standardFundBillFlowInfoDO.setCreated(new Date());
        standardFundBillFlowInfoDO.setModified(new Date());
        standardFundBillFlowInfoDO.setCompanyId(shopInfoDO.getCompanyId());
        //从不同的原始数据表中设置
        setStandardFundBillFlowInfoDO(standardFundBillFlowInfoDO, rawData);
        return standardFundBillFlowInfoDO;
    }


    /**
     * 添加资金流水
     *
     * @param param         参数
     * @param fundAccountId 资金账户ID
     * @param startBalance  开始余额
     * @param endBalance    结束余额
     */
    protected void insertBalanceRecord(CategoryAnalyzeParam param, Long fundAccountId, BigDecimal startBalance, BigDecimal endBalance, BigDecimal changeAmount) {
        BalanceRecordDO balanceRecordDO = new BalanceRecordDO();
        balanceRecordDO.setCompanyId(param.getCompanyId());
        balanceRecordDO.setFundAccountId(fundAccountId);
        balanceRecordDO.setStartBalance(startBalance);
        balanceRecordDO.setEndBalance(endBalance);
        balanceRecordDO.setDate(RawDataDateUtil.parseDate(String.valueOf(param.getDataRange()), param.getDateType()));
        balanceRecordDO.setShopId(param.getShopId());
        balanceRecordDO.setChangeAmount(changeAmount);
        balanceRecordDO.setCreated(new Date());
        balanceRecordDO.setModified(new Date());
        balanceRecordDao.insertOnDuplicateKey(balanceRecordDO);
    }


    /**
     * 匹配规则
     *
     * @param categoryAnalyzeRuleParamList 规则列表
     * @param fieldValues                  字段值映射
     * @return 匹配到的规则
     */
    protected CategoryAnalyzeRuleParam matchRule(List<CategoryAnalyzeRuleParam> categoryAnalyzeRuleParamList, JSONObject fieldValues) {
        //如果匹配规则为空或者字段值为空 则返回null
        if (CollectionUtils.isEmpty(categoryAnalyzeRuleParamList) || MapUtils.isEmpty(fieldValues)) {
            return null;
        }
        for (CategoryAnalyzeRuleParam categoryAnalyzeRuleParam : categoryAnalyzeRuleParamList) {
            if (validate(categoryAnalyzeRuleParam.getRuleGroups(), fieldValues)) {
                return categoryAnalyzeRuleParam;
            }
        }
        return null;
    }

    /**
     * 校验字段值是否符合规则内容
     *
     * @param ruleGroups  规则组列表（外层List为OR关系，内层List为AND关系）
     * @param fieldValues 字段值映射（key=fieldCode，value=待校验文本）
     * @return 符合规则返回true，否则返回false
     */
    public boolean validate(List<List<AnalyzeRuleInfo>> ruleGroups, JSONObject fieldValues) {
        // 处理顶层条件列表（顶层条件之间为OR关系）
        if (CollectionUtils.isEmpty(ruleGroups)) {
            // 无规则时默认不匹配
            return false;
        }

        // 顶层OR逻辑：任一条件组匹配则整体匹配
        for (List<AnalyzeRuleInfo> ruleGroup : ruleGroups) {
            if (validateAndGroup(ruleGroup, fieldValues)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 验证AND关系的规则组（组内所有规则必须全部匹配）
     *
     * @param ruleGroup   AND关系的规则组
     * @param fieldValues 字段值映射
     * @return 组内所有规则匹配返回true，否则返回false
     */
    private boolean validateAndGroup(List<AnalyzeRuleInfo> ruleGroup, JSONObject fieldValues) {
        if (CollectionUtils.isEmpty(ruleGroup)) {
            return false;
        }

        // AND逻辑：所有规则必须全部匹配
        for (AnalyzeRuleInfo ruleInfo : ruleGroup) {
            if (!checkCondition(ruleInfo, fieldValues)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 校验单个规则条件
     *
     * @param ruleInfo    规则信息
     * @param fieldValues 字段值映射
     * @return 条件匹配结果
     */
    private boolean checkCondition(AnalyzeRuleInfo ruleInfo, JSONObject fieldValues) {
        String fieldCode = ruleInfo.getFieldCode();
        String operator = ruleInfo.getOperator();
        List<String> targetValueList = ruleInfo.getValueList();
        // 获取待校验的实际文本
        String actualValue = Optional.ofNullable(fieldValues.getString(fieldCode)).map(String::trim).orElse(null);
        // 实际文本或目标值为null时，所有条件均不匹配
        AnalyzeRuleOperatorEnum operatorEnum = AnalyzeRuleOperatorEnum.getByOperatorCode(operator);
        if (StringUtils.isBlank(actualValue) || CollectionUtils.isEmpty(targetValueList) || operatorEnum == null) {
            return false;
        }
        // 根据操作符执行不同的匹配逻辑
        switch (operatorEnum) {
            case CONTAINS:
                return targetValueList.stream()
                        .anyMatch(actualValue::contains);
            case EQUAL:
                return targetValueList.stream()
                        .anyMatch(actualValue::equals);
            case STARTS_WITH:
                return targetValueList.stream()
                        .anyMatch(actualValue::startsWith);
            case ENDS_WITH:
                return targetValueList.stream()
                        .anyMatch(actualValue::endsWith);

            case REGEX:
                try {
                    // 正则匹配：文本中存在符合正则的部分
                    return targetValueList.stream()
                            .anyMatch(targetValue -> Pattern.compile(targetValue).matcher(actualValue).find());
                } catch (PatternSyntaxException e) {
                    // 正则表达式无效时返回false（避免校验异常）
                    return false;
                }
            default:
                // 未知操作符默认不匹配
                return false;
        }
    }

}
