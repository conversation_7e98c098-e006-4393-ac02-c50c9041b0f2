package com.raycloud.dmj.account.core.cleancategory.domain.request;

import com.raycloud.dmj.account.core.common.Page;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.List;


/**
 * 添加资金账户
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QueryCategoryReq extends Page implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分类编码
     */
    private List<String> categoryGroupCodes;

    /**
     * 类别ID，关联category主表
     */
    private List<Long> categoryIds;


    /**
     * 资金账户ID
     */
    private List<Long> fundAccountIds;


    /**
     * 子类目ID
     */
    private List<Long> subCategoryIds;

    /**
     * 是否抵消：0-不抵消 1-抵消
     */
    private Boolean offset;

    /**
     * 平台code
     */
    private String platformCode;

}
