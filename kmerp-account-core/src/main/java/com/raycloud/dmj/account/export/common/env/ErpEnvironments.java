package com.raycloud.dmj.account.export.common.env;


import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * ERP环境枚举
 */
@Getter
public enum ErpEnvironments {
    NONE("none"),
    ALL("all"),
    GRAY("gray", "gray1"),
    GRAY2("gray2"),
    GRAY3("gray3"),
    GRAY4("gray4"),
    GRAY5("gray5"),
    GRAY6("gray6"),
    PREISSUE("preissue"),
    PREISSUE2("preissue2"),
    VIP("vipprod"),
    PROD("prod"),

    STAGE("stage"),
    STAGE2("stage2"),
    STAGE3("stage3"),

    ;

    private final String envName;

    private String envAliasName;

    ErpEnvironments(String envName) {
        this.envName = envName;
    }

    ErpEnvironments(String envName, String envAliasName) {
        this.envName = envName;
        this.envAliasName = envAliasName;
    }

    public static ErpEnvironments findByEnvName(String env) {
        if (StringUtils.isBlank(env)) {
            return NONE;
        }
        for (ErpEnvironments value : values()) {
            boolean equals = value.getEnvName().equalsIgnoreCase(env);
            if (equals) {
                return value;
            }
            if (env.equalsIgnoreCase(value.getEnvAliasName())) {
                return value;
            }
        }
        return NONE;
    }
}
