package com.raycloud.dmj.account.core.base.dao;

import com.raycloud.dmj.account.core.base.domain.PageColumnConfigDO;
import com.raycloud.dmj.account.core.common.Page;

import java.util.List;

/**
 * 页面列配置信息表数据访问接口
 * <AUTHOR>
 */
public interface PageColumnConfigDao {

    /**
     * 批量新增页面列配置
     * @param configList 配置列表
     * @return 新增数量
     */
    Integer batchInsert(List<PageColumnConfigDO> configList);

    /**
     * 批量更新页面列配置
     * @param configList 配置列表
     * @return 更新数量
     */
    Integer batchUpdate(List<PageColumnConfigDO> configList);

    /**
     * 批量删除页面列配置
     * @param ids ID列表
     * @param companyId 公司ID
     * @return 删除数量
     */
    Integer batchDelete(List<Long> ids, Long companyId);

    /**
     * 根据页面ID删除配置
     * @param pageId 页面ID
     * @param companyId 公司ID
     * @return 删除数量
     */
    Integer deleteByPageId(Long pageId, Long companyId);

    /**
     * 根据页面ID查询配置列表
     * @param pageId 页面ID
     * @param companyId 公司ID
     * @return 配置列表
     */
    List<PageColumnConfigDO> queryByPageId(Long pageId, Long companyId);

    /**
     * 根据ID查询单个配置
     * @param id 配置ID
     * @param companyId 公司ID
     * @return 配置信息
     */
    PageColumnConfigDO queryById(Long id, Long companyId);

    /**
     * 检查列编码是否存在
     * @param pageId 页面ID
     * @param colCode 列编码
     * @param companyId 公司ID
     * @param excludeId 排除的ID（用于更新时检查）
     * @return 是否存在
     */
    boolean existsByColCode(Long pageId, String colCode, Long companyId, Long excludeId);
}
