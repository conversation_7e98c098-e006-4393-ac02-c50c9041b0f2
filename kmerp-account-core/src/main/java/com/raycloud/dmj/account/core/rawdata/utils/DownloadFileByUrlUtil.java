package com.raycloud.dmj.account.core.rawdata.utils;

import com.raycloud.readexcel.domain.ExcelInfo;
import com.raycloud.readexcel.domain.ReadData;
import com.raycloud.readexcel.exception.ExcelFileException;

import java.io.BufferedOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;

import static com.raycloud.readexcel.excelreader.excelreceive.ExcelReceive.BUFFER_SIZE;

public class DownloadFileByUrlUtil {

    public static void downloadFile(String apiUrl, String outputFilePath) {
        try {
            URL url = new URL(apiUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            // 获取响应码
            int responseCode = connection.getResponseCode();
            // 获取文件大小用于进度监控
            if (responseCode == HttpURLConnection.HTTP_OK) {
                // 获取输入流
                try (InputStream inputStream = connection.getInputStream();
                     BufferedOutputStream outputStream = new BufferedOutputStream(
                             new FileOutputStream(outputFilePath))) {
                    byte[] buffer = new byte[BUFFER_SIZE];
                    int bytesRead;
                    // 循环读取输入流并写入文件
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead);
                    }
                } finally {
                    connection.disconnect();
                }
            } else {
                throw new IOException("HTTP请求失败，状态码: " + responseCode);
            }

        } catch (Exception e) {
            throw new ExcelFileException("文件解析失败:" + e.getMessage(), e);
        }
    }
}
