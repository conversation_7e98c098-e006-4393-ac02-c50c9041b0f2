package com.raycloud.dmj.account.core.base.dao.impl;

import com.raycloud.dmj.account.core.base.dao.TmallLiveRedEnvelopeSumRawBillDataDao;
import com.raycloud.dmj.account.core.cleancategory.domain.object.TmallLiveRedEnvelopeSumRawBillDataDO;
import com.raycloud.dmj.account.core.common.Page;
import com.raycloud.dmj.account.core.enums.field.TmallAlipayRawBillFieldEnum;
import com.raycloud.dmj.account.core.enums.field.TmallLiveRedEnvelopeSumRawBillFieldEnum;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import com.raycloud.dmj.table.api.plus.common.base.SQL;
import com.raycloud.dmj.table.api.plus.common.enums.LinkMode;
import com.raycloud.dmj.table.api.plus.component.column.utils.Columns;
import com.raycloud.dmj.table.api.plus.component.condition.utils.Conditions;
import com.raycloud.dmj.table.api.plus.component.order.utils.Orders;
import com.raycloud.dmj.table.api.plus.query.Queries;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class TmallLiveRedEnvelopeSumRawBillDataDaoImpl extends BaseDao implements TmallLiveRedEnvelopeSumRawBillDataDao {


    private final String TABLE_NAME = "tmall_live_red_envelope_sum_raw_bill_data";

    @Override
    public List<TmallLiveRedEnvelopeSumRawBillDataDO> listPageByBatchCode(Long companyId, Long shopId, Integer batchTime, Page page) {
        AsserUtils.notNull(companyId, "公司ID不能为空！");
        AsserUtils.notNull(shopId, "店铺ID不能为空！");
        AsserUtils.notNull(batchTime, "批次号不能为空！");
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(TmallLiveRedEnvelopeSumRawBillFieldEnum.BATCH_TIME.getFieldCode()), LinkMode.EQUAL, batchTime),
                        Conditions.and(Columns.toColumn(TmallLiveRedEnvelopeSumRawBillFieldEnum.SHOP_ID.getFieldCode()), LinkMode.EQUAL, shopId),
                        Conditions.and(Columns.toColumn(TmallLiveRedEnvelopeSumRawBillFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId)
                )
                .orderBy(
                        Orders.desc(TmallAlipayRawBillFieldEnum.CREATED.getFieldCode())
                )
                .select(
                )
                .page(page.getPageNo(), page.getPageSize())
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<TmallLiveRedEnvelopeSumRawBillDataDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(TmallLiveRedEnvelopeSumRawBillDataDO.class), args);
        return !query.isEmpty() ? query : null;

    }
}
