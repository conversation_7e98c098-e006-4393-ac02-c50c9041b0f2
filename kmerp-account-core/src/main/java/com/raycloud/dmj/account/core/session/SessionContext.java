package com.raycloud.dmj.account.core.session;


import com.raycloud.dmj.account.core.tj.vo.TenantEmployeeVo;

public class SessionContext {

    private static final ThreadLocal<TenantEmployeeVo> sessionMap = new ThreadLocal<>();

    public SessionContext() {
    }

    public static void put(TenantEmployeeVo user) {
        sessionMap.set(user);
    }

    public static TenantEmployeeVo get() {
        return sessionMap.get();
    }

    public static void remove() {
        sessionMap.remove();
    }

}
