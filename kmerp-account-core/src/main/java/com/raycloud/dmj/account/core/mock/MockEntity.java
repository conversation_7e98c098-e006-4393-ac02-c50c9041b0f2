package com.raycloud.dmj.account.core.mock;

import com.raycloud.dmj.account.infra.repository.base.TableMeta;
import com.raycloud.dmj.table.api.plus.common.annotation.TableColumn;

@TableMeta(
        tableName = "mock_entity_?",
        primaryKey = "id",
        softDeleteColumn = "enable_status",
        sharding = true
)
public class MockEntity {

    private Long id;

    /**
     * 有这个注解才会被写入SQL
     */
    @TableColumn
    private String name;

}
