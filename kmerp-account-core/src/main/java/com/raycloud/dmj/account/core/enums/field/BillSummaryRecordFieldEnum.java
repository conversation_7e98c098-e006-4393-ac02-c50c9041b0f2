package com.raycloud.dmj.account.core.enums.field;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 资金流水合计记录表字段枚举
 * <AUTHOR>
 */
@Getter
public enum BillSummaryRecordFieldEnum {

    //扩展字段 features
    ID("id", "主键ID"),
    ACCOUNT_ID("account_id", "资金账户ID"),
    CATEGORY_ID("category_id", "流水类别ID"),
    SUB_CATEGORY_ID("sub_category_id", "流水子类别ID"),
    BILLING_CYCLE("billing_cycle", "帐期"),
    AMOUNT("amount", "金额(元)（正数收入，负数支出）"),
    SHOP_ID("shop_id", "店铺ID"),
    ENABLE_STATUS("enable_status", "启用状态：0弃用，1正常"),
    CREATED("created", "创建时间"),
    MODIFIED("modified", "修改时间"),
    COMPANY_ID("company_id", "租户ID");

    /**
     * 字段编码
     */
    private final String fieldCode;

    /**
     * 字段描述
     */
    private final String fieldDesc;

    BillSummaryRecordFieldEnum(String fieldCode, String fieldDesc) {
        this.fieldCode = fieldCode;
        this.fieldDesc = fieldDesc;
    }

    /**
     * 获取插入字段
     * @return 插入字段
     */
    public static Set<String> getInsertFields() {
        List<BillSummaryRecordFieldEnum> filterField = Arrays.asList(
                BillSummaryRecordFieldEnum.ID
        );
        return Arrays.stream(BillSummaryRecordFieldEnum.values()).filter(x -> !filterField.contains(x)).map(
                x -> x.fieldCode
        ).collect(Collectors.toSet());
    }

    /**
     * 获取所有查询字段
     * @return 查询字段数组
     */
    public static String[] getSelectFields() {
        return Arrays.stream(BillSummaryRecordFieldEnum.values())
                .map(BillSummaryRecordFieldEnum::getFieldCode)
                .toArray(String[]::new);
    }
}
