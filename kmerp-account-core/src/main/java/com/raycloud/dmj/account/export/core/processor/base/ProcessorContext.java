package com.raycloud.dmj.account.export.core.processor.base;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.context.ApplicationContext;

import java.util.*;

@Data
@Accessors(chain = true)
public class ProcessorContext {

    /**
     * 查询列名称
     */
    private List<String> effectiveColumnNames = new ArrayList<>();

    /**
     * Spring 上下文
     */
    private ApplicationContext appContext;

    /**
     * 处理场景名称
     */
    private String name;

    /**
     * 数据分片大小
     */
    private Integer chuckSize = 0;

    /**
     * 上下文参数
     */
    private Object parameter;

    /**
     * 0:init
     * 1:start
     * 2:finished
     * 3:exception
     */
    private int status = 0;

    /**
     * 处理器开始时间
     * key : processorName
     * value : startTime
     */
    private Map<String, Long> processorStartTime = new LinkedHashMap<>();

    /**
     * 处理器检查结果
     * key : processorName
     * value : checked
     */
    private Map<String, Boolean> processorChecked = new LinkedHashMap<>();

    /**
     * 处理数据缓存
     */
    private ProcessorMemoryStorage processorCache = new ProcessorMemoryStorage();

    /**
     * 执行上下文
     */
    private Map<String, AppParameter> executionContext = new HashMap<>();

    /**
     * 获取上下文内容
     * @param clazz
     * @return
     * @param <T>
     */
    public <T> T getExecutionContext(Class<T> clazz) {
        return getExecutionContext(clazz.getName(),clazz);
    }

    /**
     * 获取上下文内容
     * @param key
     * @param clazz
     * @return
     * @param <T>
     */
    public <T> T getExecutionContext(String key, Class<T> clazz) {
        Object object = executionContext.get(key);
        if (object != null && object.getClass().equals(clazz)) {
            return (T) object;
        }
        return null;
    }



    public <B> B getBean(Class<B> clazz) {
        return appContext.getBean(clazz);
    }

    public <B> B getBean(String beanName, Class<B> clazz) {
        return appContext.getBean(beanName, clazz);
    }
}
