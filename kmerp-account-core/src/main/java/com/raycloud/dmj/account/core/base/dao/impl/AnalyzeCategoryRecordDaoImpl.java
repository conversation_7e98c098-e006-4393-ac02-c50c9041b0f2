package com.raycloud.dmj.account.core.base.dao.impl;

import com.raycloud.dmj.account.core.base.dao.AnalyzeCategoryRecordDao;
import com.raycloud.dmj.account.core.cleancategory.domain.object.AnalyzeCategoryRecordDO;
import com.raycloud.dmj.account.core.enums.field.AnalyzeCategoryRecordFieldEnum;
import com.raycloud.dmj.account.core.enums.field.CategoryAnalyzeStatusEnum;
import com.raycloud.dmj.account.infra.common.BusinessException;
import com.raycloud.dmj.account.infra.common.ErrorCodeEnum;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import com.raycloud.dmj.table.api.plus.common.ColumnValues;
import com.raycloud.dmj.table.api.plus.common.base.SQL;
import com.raycloud.dmj.table.api.plus.common.enums.LinkMode;
import com.raycloud.dmj.table.api.plus.component.column.utils.Columns;
import com.raycloud.dmj.table.api.plus.component.condition.utils.Conditions;
import com.raycloud.dmj.table.api.plus.insert.Inserts;
import com.raycloud.dmj.table.api.plus.query.Queries;
import com.raycloud.dmj.table.api.plus.update.Updates;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 */
@Repository
@Slf4j
public class AnalyzeCategoryRecordDaoImpl extends BaseDao implements AnalyzeCategoryRecordDao {

    private final String TABLE_NAME = "analyze_category_record";

    @Override
    public void updateAnalyzeStatusById(AnalyzeCategoryRecordDO categoryDO) {
        AsserUtils.notNull(categoryDO, "参数不能为空！");
        AsserUtils.notNull(categoryDO.getId(), "ID不能为空！");
        AsserUtils.notNull(categoryDO.getCompanyId(), "公司ID不能为空！");
        SQL sql = Updates.create()
                .table(TABLE_NAME)
                .where(
                        Conditions.and(AnalyzeCategoryRecordFieldEnum.ID.getFieldCode(), LinkMode.EQUAL, categoryDO.getId()),
                        Conditions.and(AnalyzeCategoryRecordFieldEnum.VERSION.getFieldCode(), LinkMode.EQUAL, categoryDO.getVersion())
                )
                .update(
                        ColumnValues.create(AnalyzeCategoryRecordFieldEnum.VERSION.getFieldCode(), Columns.create(AnalyzeCategoryRecordFieldEnum.VERSION.getFieldCode() + "+1")),
                        ColumnValues.create(AnalyzeCategoryRecordFieldEnum.MODIFIED.getFieldCode(), new Date()),
                        ColumnValues.create(AnalyzeCategoryRecordFieldEnum.ANALYZE_STATUS.getFieldCode(), categoryDO.getAnalyzeStatus()),
                        ColumnValues.create(AnalyzeCategoryRecordFieldEnum.FEATURE.getFieldCode(), categoryDO.getFeature())
                ).toSql();
        Object[] updateArgs = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        // 3. 执行更新操作
        int i= jdbcTemplate.update(sql.getSqlCode(), updateArgs);
        if (i<=0){
            throw new BusinessException(ErrorCodeEnum.DB_ERROR,"更新解析状态失败");
        }
    }

    @Override
    public long insert(AnalyzeCategoryRecordDO categoryDO) {
        SQL sql = Inserts.insert()
                .into(TABLE_NAME)
                .columns(AnalyzeCategoryRecordFieldEnum.getInsertFields())
                .valueForEntity(categoryDO)
                .onDuplicateKey()
                .update(
                        ColumnValues.create(AnalyzeCategoryRecordFieldEnum.ANALYZE_STATUS.getFieldCode(), CategoryAnalyzeStatusEnum.ANALYZING.getStatus()),
                        ColumnValues.create(AnalyzeCategoryRecordFieldEnum.MODIFIED.getFieldCode(),new Date())
                )
                .columnNameCamelToUnderline()
                .toSql();

        return insertReturnPrimaryKey(sql);

    }

    @Override
    public AnalyzeCategoryRecordDO getByShopAndSourceAndDataRange(Long companyId, Long shopId, Integer dataSource, Integer dataRange) {

        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(AnalyzeCategoryRecordFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId),
                        Conditions.and(Columns.toColumn(AnalyzeCategoryRecordFieldEnum.SHOP_ID.getFieldCode()), LinkMode.EQUAL, shopId),
                        Conditions.and(Columns.toColumn(AnalyzeCategoryRecordFieldEnum.DATA_RANGE.getFieldCode()), LinkMode.EQUAL, dataRange),
                        Conditions.and(Columns.toColumn(AnalyzeCategoryRecordFieldEnum.DATA_SOURCE.getFieldCode()), LinkMode.EQUAL, dataSource)
                )
                .select()
                .limit(1)
                .toSql();

        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<AnalyzeCategoryRecordDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(AnalyzeCategoryRecordDO.class), args);
        return !query.isEmpty() ? query.get(0) : null;
    }


}
