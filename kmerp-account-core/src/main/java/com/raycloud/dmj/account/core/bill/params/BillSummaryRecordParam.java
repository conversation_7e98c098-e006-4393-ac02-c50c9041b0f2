package com.raycloud.dmj.account.core.bill.params;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
public class BillSummaryRecordParam {

    /**
     * 平台code
     */
    private String  platformCode;

    /**
     * 资金账户ID
     */
    private List<Long> accountIdList;

    /**
     * 流水类别ID
     */
    private List<Long> categoryIdList;

    /**
     * 流水子类别ID
     */
    private List<Long> subCategoryIdList;

    /**
     * 帐期开始时间
     */
    private Date startTime;

    /**
     * 帐期结束时间
     */
    private Date endTime;

    /**
     * 帐期类型 1:日账单 2:月账单
     */
    private Integer billingCycleType;

    /**
     * 店铺ID
     */
    private Long shopId;
}
