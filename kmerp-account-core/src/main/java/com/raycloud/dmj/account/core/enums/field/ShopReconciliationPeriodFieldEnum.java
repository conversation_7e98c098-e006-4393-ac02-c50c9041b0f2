package com.raycloud.dmj.account.core.enums.field;

import lombok.Getter;

/**
 * 店铺对账周期信息表字段枚举
 * 
 * <AUTHOR>
 */
@Getter
public enum ShopReconciliationPeriodFieldEnum {

    ID("id", "主键ID"),
    PERIOD_ID("period_id", "对账周期ID"),
    START_TIME("start_time", "开始时间"),
    END_TIME("end_time", "结束时间"),
    SHOP_CODE("shop_code", "店铺编码"),
    STATUS("status", "对账状态 0:未对账 1:已对账"),
    ENABLE_STATUS("enable_status", "启用状态 0:弃用 1:正常"),
    COMPANY_ID("company_id", "公司ID"),
    CREATED("created", "创建时间"),
    MODIFIED("modified", "修改时间");

    /**
     * 字段编码
     */
    private final String fieldCode;

    /**
     * 字段描述
     */
    private final String fieldDesc;

    ShopReconciliationPeriodFieldEnum(String fieldCode, String fieldDesc) {
        this.fieldCode = fieldCode;
        this.fieldDesc = fieldDesc;
    }
}
