package com.raycloud.dmj.account.core.cleancategory.domain.object;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class TmallHuiYingBaoRawBillDataDO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    private Long id;

    /**
     * 店铺简称
     */
    private String sortTitle;

    /**
     * 月份（格式如：202503 ）
     */
    private Integer month;

    /**
     * 支付宝交易流水号
     */
    private String alipayTransactionNo;

    /**
     * 商家订单号
     */
    private String merchantOrderNo;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 订单金额（元）
     */
    private BigDecimal orderAmount;

    /**
     * 抵扣金额（元）
     */
    private BigDecimal deductionAmount;

    /**
     * 业务发生时间
     */
    private Date businessOccurrenceTime;

    /**
     * 收款账户
     */
    private String receivingAccount;

    /**
     * 付款账户
     */
    private String paymentAccount;

    /**
     * 下载时间
     */
    private Date downloadTime;

    /**
     * 下载账户
     */
    private String downloadAccount;

    /**
     * 唯一键
     */
    private String bizKey;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 批次时间 格式如********
     */
    private Integer batchTime;

    /**
     * 租户ID
     */
    private Long companyId;

    /**
     * 店铺ID
     */
    private String shopId;

    /**
     * 创建时间
     */
    private Date created;
}