package com.raycloud.dmj.account.core.base.dao.impl;

import com.raycloud.dmj.account.core.base.dao.TmallGuaranteeRawBillDataDao;
import com.raycloud.dmj.account.core.cleancategory.domain.object.TmallGuaranteeRawBillDataDO;
import com.raycloud.dmj.account.core.common.Page;
import com.raycloud.dmj.account.core.enums.field.TmallAlipayRawBillFieldEnum;
import com.raycloud.dmj.account.core.enums.field.TmallGuaranteeRawBillFieldEnum;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import com.raycloud.dmj.table.api.plus.common.base.SQL;
import com.raycloud.dmj.table.api.plus.common.enums.LinkMode;
import com.raycloud.dmj.table.api.plus.component.column.utils.Columns;
import com.raycloud.dmj.table.api.plus.component.condition.utils.Conditions;
import com.raycloud.dmj.table.api.plus.component.order.utils.Orders;
import com.raycloud.dmj.table.api.plus.query.Queries;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class TmallGuaranteeRawBillDataDaoImpl extends BaseDao implements TmallGuaranteeRawBillDataDao {

    private final String TABLE_NAME = "tmall_guarantee_raw_bill_data";


    @Override
    public List<TmallGuaranteeRawBillDataDO> listPageByDataRange(Long companyId, Long shopId, Integer dataRange, Page page) {
        AsserUtils.notNull(companyId, "公司ID不能为空！");
        AsserUtils.notNull(shopId, "店铺ID不能为空！");
        AsserUtils.notNull(dataRange, "账期不能为空！");
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(TmallGuaranteeRawBillFieldEnum.BATCH_TIME.getFieldCode()), LinkMode.EQUAL, dataRange),
                        Conditions.and(Columns.toColumn(TmallGuaranteeRawBillFieldEnum.SHOP_ID.getFieldCode()), LinkMode.EQUAL, shopId),
                        Conditions.and(Columns.toColumn(TmallGuaranteeRawBillFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId)
                )
                .select(
                )
                .page(page.getPageNo(), page.getPageSize())
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<TmallGuaranteeRawBillDataDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(TmallGuaranteeRawBillDataDO.class), args);
        return !query.isEmpty() ? query : null;
    }

    @Override
    public TmallGuaranteeRawBillDataDO getEarliestByDataRange(Long companyId, Long shopId, Integer dataRange) {
        AsserUtils.notNull(companyId, "公司ID不能为空！");
        AsserUtils.notNull(shopId, "店铺ID不能为空！");
        AsserUtils.notNull(dataRange, "账期不能为空！");
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(TmallGuaranteeRawBillFieldEnum.BATCH_TIME.getFieldCode()), LinkMode.EQUAL, dataRange),
                        Conditions.and(Columns.toColumn(TmallGuaranteeRawBillFieldEnum.SHOP_ID.getFieldCode()), LinkMode.EQUAL, shopId),
                        Conditions.and(Columns.toColumn(TmallGuaranteeRawBillFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId)
                ).orderBy(
                        Orders.asc(TmallGuaranteeRawBillFieldEnum.COMPLETION_TIME.getFieldCode())
                )
                .select(
                )
                .limit(1)
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<TmallGuaranteeRawBillDataDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(TmallGuaranteeRawBillDataDO.class), args);
        return !query.isEmpty() ? query.get(0) : null;
    }

    @Override
    public TmallGuaranteeRawBillDataDO getLatestByDataRange(Long companyId, Long shopId, Integer dataRange) {
        AsserUtils.notNull(companyId, "公司ID不能为空！");
        AsserUtils.notNull(shopId, "店铺ID不能为空！");
        AsserUtils.notNull(dataRange, "账期不能为空！");
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(TmallGuaranteeRawBillFieldEnum.BATCH_TIME.getFieldCode()), LinkMode.EQUAL, dataRange),
                        Conditions.and(Columns.toColumn(TmallGuaranteeRawBillFieldEnum.SHOP_ID.getFieldCode()), LinkMode.EQUAL, shopId),
                        Conditions.and(Columns.toColumn(TmallGuaranteeRawBillFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId)
                ).orderBy(
                        Orders.desc(TmallGuaranteeRawBillFieldEnum.COMPLETION_TIME.getFieldCode())
                )
                .select(
                )
                .limit(1)
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<TmallGuaranteeRawBillDataDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(TmallGuaranteeRawBillDataDO.class), args);
        return !query.isEmpty() ? query.get(0) : null;
    }
}
