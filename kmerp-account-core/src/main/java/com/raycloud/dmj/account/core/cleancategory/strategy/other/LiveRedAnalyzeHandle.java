package com.raycloud.dmj.account.core.cleancategory.strategy.other;

import com.raycloud.dmj.account.core.base.dao.TmallLiveRedEnvelopeRawBillDataDao;
import com.raycloud.dmj.account.core.base.domain.StandardOtherBillFlowInfoDO;
import com.raycloud.dmj.account.core.cleancategory.domain.TmallLiveRedEnvelopeRawBillDataDTO;
import com.raycloud.dmj.account.core.cleancategory.strategy.CategoryAnalyzeHandle;
import com.raycloud.dmj.account.core.cleancategory.strategy.param.CategoryAnalyzeParam;
import com.raycloud.dmj.account.core.common.Page;
import com.raycloud.dmj.account.core.enums.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;


/**
 * 直播红包解析处理
 * <AUTHOR>
 */
@Component
public class LiveRedAnalyzeHandle extends StandardOtherCategoryAnalyzeHandle<TmallLiveRedEnvelopeRawBillDataDTO> {


    /**
     * serialNo
     */
    @Resource
    private TmallLiveRedEnvelopeRawBillDataDao tmallLiveRedEnvelopeRawBillDataDao;

    @Override
    protected List<TmallLiveRedEnvelopeRawBillDataDTO> listPageRawData(CategoryAnalyzeParam param, Page page) {
        return tmallLiveRedEnvelopeRawBillDataDao.listPageByBatchTime(
                param.getCompanyId(),
                param.getShopId(),
                param.getDataRange(),
                page
        );
    }

    @Override
    protected void setStandardFundBillFlowInfoDO(StandardOtherBillFlowInfoDO standardOtherBillFlowInfoDO, TmallLiveRedEnvelopeRawBillDataDTO rawDataDO) {
        standardOtherBillFlowInfoDO.setCategoryGroupCode(OtherCategoryGroupEnum.BT.getCode());
        standardOtherBillFlowInfoDO.setCategoryCode(OtherCategoryEnum.ZBHB.getCode());
        OtherSubCategoryEnum otherSubCategoryEnum = OtherSubCategoryEnum.getLiveRedSubCategorByDesc(rawDataDO.getRedEnvelopeType());
        String subCategoryCode = Optional.ofNullable(otherSubCategoryEnum).map(OtherSubCategoryEnum::getCode).orElse(CategoryAnalyzeHandle.DEFAULT_UNKNOWN_CATEGORY_CODE);
        standardOtherBillFlowInfoDO.setSubCategoryCode(subCategoryCode);
        standardOtherBillFlowInfoDO.setBillingCycle(rawDataDO.getBillingMonth());
        standardOtherBillFlowInfoDO.setOccurredAt(rawDataDO.getOrderConfirmReceiveTime());
        BigDecimal amount = rawDataDO.getRedEnvelopeFlowAmount();
        standardOtherBillFlowInfoDO.setAmount(amount);
        if (amount.compareTo(BigDecimal.ZERO)>0){
            standardOtherBillFlowInfoDO.setIncomeExpenseDirection(IncomeExpenseDirectionEnum.INCOME.getCode());
        }else {
            standardOtherBillFlowInfoDO.setIncomeExpenseDirection(IncomeExpenseDirectionEnum.EXPENSE.getCode());
        }
        standardOtherBillFlowInfoDO.setOrderNo(rawDataDO.getTaobaoMainOrderNo());
        standardOtherBillFlowInfoDO.setRemark(rawDataDO.getRemark());
        standardOtherBillFlowInfoDO.setBatchNo(rawDataDO.getBatchNo());
        standardOtherBillFlowInfoDO.setBizKey(rawDataDO.getBizKey());
    }
    @Override
    protected void deleteByShopAndDataRangeAndCategoryGroup(CategoryAnalyzeParam param) {
        standardOtherBillFlowInfoDao.deleteByShopAndDataRangeAndCategory(param.getCompanyId(),
                param.getShopId(),param.getDataRange() , OtherCategoryEnum.ZBHB.getCode());
    }

    @Override
    public boolean accord(RawDataSourceEnum source) {
        return RawDataSourceEnum.LIVE_RED_PACKET.equals(source);
    }
}
