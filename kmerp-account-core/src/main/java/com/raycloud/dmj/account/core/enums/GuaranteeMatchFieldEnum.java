package com.raycloud.dmj.account.core.enums;

import com.google.common.base.CaseFormat;
import com.raycloud.dmj.account.core.common.TreeVO;
import com.raycloud.dmj.account.core.enums.field.TmallGuaranteeRawBillFieldEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 解析规则中满足条件字段
 * <AUTHOR>
 */

@Getter
public enum GuaranteeMatchFieldEnum {

    REMARK(TmallGuaranteeRawBillFieldEnum.REMARK, "备注"),
    REASON(TmallGuaranteeRawBillFieldEnum.REASON, "原因"),
    BUSINESS_DESCRIPTION(TmallGuaranteeRawBillFieldEnum.BUSINESS_DESCRIPTION, "业务描述"),
    ;


    private final String fieldCode;

    private final TmallGuaranteeRawBillFieldEnum matchField;

    private final String fieldDesc;

    GuaranteeMatchFieldEnum(TmallGuaranteeRawBillFieldEnum matchField, String fieldDesc) {
        //改为小驼峰命名，用于配置解析规则
        this.fieldCode=CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL,matchField.getFieldCode());
        this.matchField = matchField;
        this.fieldDesc = fieldDesc;
    }

    public static List<TreeVO> listTreeVO() {
        return Arrays.stream(GuaranteeMatchFieldEnum.values()).map(
                x -> {
                    TreeVO treeVO = new TreeVO();
                    treeVO.setLabel(x.getFieldDesc());
                    treeVO.setValue(x.getFieldCode());
                    return treeVO;
                }
        ).collect(Collectors.toList());
    }

}
