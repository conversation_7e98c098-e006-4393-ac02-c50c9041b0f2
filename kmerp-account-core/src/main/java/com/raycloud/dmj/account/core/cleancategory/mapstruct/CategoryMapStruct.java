package com.raycloud.dmj.account.core.cleancategory.mapstruct;

import com.raycloud.dmj.account.core.cleancategory.domain.object.CategoryDO;
import com.raycloud.dmj.account.core.cleancategory.domain.object.CategoryGroupDO;
import com.raycloud.dmj.account.core.cleancategory.domain.object.SubCategoryDO;
import com.raycloud.dmj.account.core.cleancategory.domain.request.AddCategoryReq;
import com.raycloud.dmj.account.core.cleancategory.domain.request.EditCategoryGroupReq;
import com.raycloud.dmj.account.core.cleancategory.domain.request.EditCategoryReq;
import com.raycloud.dmj.account.core.cleancategory.domain.vo.CategoryGroupVO;
import com.raycloud.dmj.account.core.common.TreeVO;
import com.raycloud.dmj.account.infra.session.AccountUser;
import com.raycloud.dmj.account.core.enums.CategorySourceEnum;
import com.raycloud.dmj.account.core.enums.YesOrNoEnum;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.Date;
import java.util.Optional;


/**
 * 账户映射
 *
 * <AUTHOR>
 */
@Mapper(imports = {YesOrNoEnum.class, Date.class, CategorySourceEnum.class})
public interface CategoryMapStruct {

    CategoryMapStruct INSTANCE = Mappers.getMapper(CategoryMapStruct.class);


    @Mapping(target = "created", expression = "java(new Date())")
    @Mapping(target = "modified", expression = "java(new Date())")
    @Mapping(target = "source", expression = "java(CategorySourceEnum.MANUAL.getSourceCode())")
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "companyId", source = "accountUser.companyId")
    @Mapping(target = "name", source = "req.categoryName")
    CategoryDO toCategoryDO(AccountUser accountUser, AddCategoryReq req);

    @Mapping(target = "created", expression = "java(new Date())")
    @Mapping(target = "modified", expression = "java(new Date())")
    @Mapping(target = "source", expression = "java(CategorySourceEnum.MANUAL.getSourceCode())")
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "companyId", source = "user.companyId")
    @Mapping(target = "name", source = "req.categoryName")
    CategoryDO toCategoryDO(AccountUser user, EditCategoryReq req);


    @Mapping(target = "code", source = "req.categoryGroupCode")
    @Mapping(target = "name", source = "req.categoryGroupName")
    @Mapping(target = "modified", expression = "java(new Date())")
    @Mapping(target = "companyId", source = "user.companyId")
    CategoryGroupDO toCategoryGroupDO(AccountUser user, EditCategoryGroupReq req);


    @Mapping(target = "created", expression = "java(new Date())")
    @Mapping(target = "modified", expression = "java(new Date())")
    @Mapping(target = "source", expression = "java(CategorySourceEnum.MANUAL.getSourceCode())")
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "name", source = "req.subCategoryName")
    @Mapping(target = "offset", source = "req.offset")
    @Mapping(target = "companyId", source = "accountUser.companyId")
    SubCategoryDO toSubCategoryDO(AccountUser accountUser, AddCategoryReq req);


    @Mapping(target = "created", expression = "java(new Date())")
    @Mapping(target = "modified", expression = "java(new Date())")
    @Mapping(target = "source", expression = "java(CategorySourceEnum.MANUAL.getSourceCode())")
    @Mapping(target = "id", source = "req.id")
    @Mapping(target = "name", source = "req.subCategoryName")
    @Mapping(target = "offset", source = "req.offset")
    @Mapping(target = "companyId", source = "user.companyId")
    SubCategoryDO toSubCategoryDO(AccountUser user, EditCategoryReq req);


    @Mapping(target = "value", source = "code")
    @Mapping(target = "label", source = "name")
    TreeVO toTreeVO(CategoryGroupDO categoryGroupDO);

    @Mapping(target = "value", source = "id")
    @Mapping(target = "label", source = "name")
    TreeVO toTreeVO(CategoryDO categoryGroupDO);

    @Mapping(target = "value", source = "id")
    @Mapping(target = "label", source = "name")
    TreeVO toTreeVO(SubCategoryDO categoryGroupDO);


    @Mapping(target = "categoryGroupCode", source = "code")
    @Mapping(target = "categoryGroupName", source = "name")
    @Mapping(target = "typeName", expression = "java(CategoryMapStruct.getSourceDesc(categoryGroupDO.getSource()))")
    CategoryGroupVO toCategoryGroupVO(CategoryGroupDO categoryGroupDO);


    static String getSourceDesc(Integer sourceCode) {

        return Optional.ofNullable(CategorySourceEnum.getBySourceCode(sourceCode))
                .map(CategorySourceEnum::getSourceDesc).orElse(null);


    }
}
