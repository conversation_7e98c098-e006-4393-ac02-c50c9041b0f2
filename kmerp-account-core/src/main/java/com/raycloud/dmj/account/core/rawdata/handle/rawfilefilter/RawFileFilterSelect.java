package com.raycloud.dmj.account.core.rawdata.handle.rawfilefilter;


import com.raycloud.dmj.account.core.enums.FilterTypeEnum;
import com.raycloud.dmj.account.core.enums.RawDataSourceEnum;
import com.raycloud.dmj.account.core.rawdata.handle.filter.FilterHandler;
import com.raycloud.dmj.account.core.rawdata.handle.filter.handle.TimeFilterHandler;
import com.raycloud.dmj.account.core.rawdata.handle.rawfilefilter.param.RawFileFilterParam;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 过滤处理工厂 选择
 * <AUTHOR>
 */
public class RawFileFilterSelect {

    private static final Map<Integer, RawFileFilterHandle> HANDLER_MAP = new HashMap<>();

    static {
        HANDLER_MAP.put(RawDataSourceEnum.ALIPAY.getCode(), new AlipayRawFileFilterHandle());
    }

    /**
     * 获取指定类型的处理器
     * @param param 参数
     * @return true-过滤/false-不过滤
     */
    public static boolean execute(RawFileFilterParam param) {
        if (Objects.isNull( param) || Objects.isNull(param.getDataSource()) || Objects.isNull(param.getFilePath())){
            return false;
        }
        RawFileFilterHandle rawFileFilterHandle = HANDLER_MAP.get(param.getDataSource());
        if (Objects.isNull(rawFileFilterHandle)){
            return false;
        }
        return rawFileFilterHandle.handle( param);
    }

}
