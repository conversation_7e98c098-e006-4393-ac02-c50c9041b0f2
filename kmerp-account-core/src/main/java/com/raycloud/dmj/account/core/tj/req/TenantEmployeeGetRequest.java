package com.raycloud.dmj.account.core.tj.req;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class TenantEmployeeGetRequest implements Serializable {

    /**
     * 系统唯一编码（每个系统都会分配一个唯一不变的编码）
     */
    private String sysCode;

    /**
     * 外部租户编码
     */
    private String tenantOuterCode;

    /**
     * 外部租户的员工编码
     */
    private String employeeOuterCode;
}
