package com.raycloud.dmj.account.core.base.dao;

import com.raycloud.dmj.account.core.cleancategory.domain.object.FundAccountDO;

import java.util.List;
import java.util.Set;

/**
 * 资金账户Dao
 * <AUTHOR>
 */
public interface FundAccountDao {


    /**
     * 插入
     * @param fundAccountDO 资金账户
     * @return 插入的id
     */
    Long insert(FundAccountDO fundAccountDO);

    /**
     *
     * 根据资金账户code查询
     * @param accountCode 资金账户code
     * @return 资金账户
     */
    FundAccountDO queryByAccountCode(Long companyId,String accountCode);

    /**
     * 根据ID修改
     * @param fundAccountDO 资金账户
     */
    void updateById(FundAccountDO fundAccountDO);

    /**
     * 根据公司ID查询
     * @param companyId 公司ID
     * @return 资金账户
     */
    List<FundAccountDO> queryByCompanyId(Long companyId);


    /**
     * 根据资金账户ID集合查询
     * @param companyId 公司ID
     * @param ids 资金账户ID集合
     * @return 资金账户
     */
    List<FundAccountDO> queryByIds(Long companyId, Set<Long> ids);

    /**
     * 根据资金账户ID集合查询
     * @param companyId 公司ID
     * @param id 资金账户ID集合
     * @return 资金账户
     */
    FundAccountDO queryById(Long companyId, Long id);

    /**
     * 根据资金账户类型集合查询
     * @param companyId 公司ID
     * @param shopId 店铺ID
     * @param type 资金账户类型
     * @return 资金账户
     */
    FundAccountDO getByShopIdAndType(Long companyId, Long shopId, Integer type);

    /**
     * 根据ID修改授权状态
     * @param authorize 授权状态
     * @param id 资金账户ID
     */
    void updateAuthorizeById(Integer authorize ,Long id);

    /**
     * 根据ID修改确认期初状态
     * @param confirmStartPeriod 确认期初状态
     * @param id 资金账户ID
     */
    void updateConfirmStartPeriodById(Integer confirmStartPeriod ,Long id);

    /**
     * 根据店铺ID查询资金账户
     */
    List<FundAccountDO> queryByShopIdList(List<Long> shopIdList,Long companyId);

    /**
     * 根据平台code查询资金账户
     */
    List<FundAccountDO> queryByPlatformCode(String platformCode,Long companyId);


    /**
     * 根据资金账户名称查询
     * @param companyId 公司ID
     * @param accountName 资金账户名称
     * @return 资金账户
     */
    FundAccountDO queryByAccountName(Long companyId, String accountName);
}
