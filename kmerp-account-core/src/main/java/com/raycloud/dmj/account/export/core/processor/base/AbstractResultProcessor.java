package com.raycloud.dmj.account.export.core.processor.base;

import com.google.common.collect.Lists;
import com.raycloud.dmj.account.infra.common.AppContextHolder;
import com.raycloud.dmj.account.infra.common.BizException;
import com.raycloud.dmj.domain.account.Staff;
import org.springframework.util.ObjectUtils;

import java.util.List;
import java.util.Map;

public abstract class AbstractResultProcessor<T> implements ResultProcessor<T, Map<String, Object>>, IProcessChain<T> {

    /**
     * 结果数据处理器名称
     *
     * @return
     */
    public String getProcessorName() {
        return this.getClass().getSimpleName();
    }

    /**
     * 处理器链
     */
    protected ProcessorChain<Map<String, Object>> chain;

    /**
     * 处理器启动事件
     *
     * @param staff
     * @param parameter
     */
    public void start(Staff staff, T parameter) {
        ProcessorFactory factory = AppContextHolder.getContext().getBean(ProcessorFactory.class);
        chain = assembleProcessors(staff, parameter, factory);
        chain.start(staff);
    }

    /**
     * 处理器完成事件
     *
     * @param staff
     * @param parameter
     */
    public void finished(Staff staff, T parameter) {
        chain.finished(staff);
    }

    /**
     * 处理多条数据（适用于多次查询）
     *
     * @param staff
     * @param parameter
     * @param list
     * @return
     */
    public List<Map<String, Object>> handle(Staff staff, T parameter, List<Map<String, Object>> list) {
        if (chain == null) {
            throw new BizException("查询结果数据处理链路未初始化");
        }
        return chain.process(staff, list);
    }

    /**
     * 处理单条数据（适用于多次查询）
     *
     * @param staff
     * @param parameter
     * @param row
     * @return
     */
    public Map<String, Object> handle(Staff staff, T parameter, Map<String, Object> row) {
        List<Map<String, Object>> maps = handle(staff, parameter, Lists.newArrayList(row));
        if (ObjectUtils.isEmpty(maps)) {
            return null;
        }
        return maps.get(0);
    }

    /**
     * 快速处理单条数据（适用于一次性查询）
     *
     * @param staff
     * @param parameter
     * @param list
     * @return
     */
    public List<Map<String, Object>> fastHandle(Staff staff, T parameter, List<Map<String, Object>> list) {
        start(staff, parameter);
        if (chain == null) {
            throw new BizException("查询结果数据处理链路未初始化");
        }
        List<Map<String, Object>> processed = null;
        try {
            processed = chain.process(staff, list);
        } finally {
            finished(staff, parameter);
        }
        return processed;
    }

    /**
     * 快速处理单行数据（适用于一次性查询）
     *
     * @param staff
     * @param parameter
     * @param row
     * @return
     */
    public Map<String, Object> fastHandle(Staff staff, T parameter, Map<String, Object> row) {
        List<Map<String, Object>> maps = fastHandle(staff, parameter, Lists.newArrayList(row));
        if (ObjectUtils.isEmpty(maps)) {
            return null;
        }
        return maps.get(0);
    }

}
