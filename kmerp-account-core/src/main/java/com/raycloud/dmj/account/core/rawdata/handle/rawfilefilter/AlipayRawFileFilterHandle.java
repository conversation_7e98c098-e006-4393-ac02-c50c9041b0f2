package com.raycloud.dmj.account.core.rawdata.handle.rawfilefilter;

import com.raycloud.dmj.account.core.rawdata.handle.rawfilefilter.param.RawFileFilterParam;
import com.raycloud.dmj.account.core.rawdata.req.OriginalDataCallbackReq;
import com.raycloud.dmj.account.core.rawdata.req.OriginalDataParam;
import org.apache.commons.compress.utils.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * Date:  2025/7/31
 */
public class AlipayRawFileFilterHandle implements RawFileFilterHandle{
    @Override
    public boolean handle(RawFileFilterParam param) {
        return !param.getFilePath().contains("账务明细");
    }
}
