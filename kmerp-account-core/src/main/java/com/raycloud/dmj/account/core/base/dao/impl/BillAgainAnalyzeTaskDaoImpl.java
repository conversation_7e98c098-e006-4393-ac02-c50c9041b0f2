package com.raycloud.dmj.account.core.base.dao.impl;

import com.raycloud.dmj.account.core.base.dao.BillAgainAnalyzeTaskDao;
import com.raycloud.dmj.account.core.cleancategory.domain.object.BillAgainAnalyzeTaskDO;
import com.raycloud.dmj.account.core.enums.YesOrNoEnum;
import com.raycloud.dmj.account.core.enums.field.BillAgainAnalyzeTaskFieldEnum;
import com.raycloud.dmj.account.core.enums.field.CategoryAnalyzeRuleFieldEnum;
import com.raycloud.dmj.account.core.enums.field.FileOriginalDataMonitorEnum;
import com.raycloud.dmj.account.infra.common.BusinessException;
import com.raycloud.dmj.account.infra.common.ErrorCodeEnum;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import com.raycloud.dmj.table.api.plus.common.ColumnValues;
import com.raycloud.dmj.table.api.plus.common.base.SQL;
import com.raycloud.dmj.table.api.plus.common.enums.LinkMode;
import com.raycloud.dmj.table.api.plus.component.column.utils.Columns;
import com.raycloud.dmj.table.api.plus.component.condition.utils.Conditions;
import com.raycloud.dmj.table.api.plus.component.order.utils.Orders;
import com.raycloud.dmj.table.api.plus.insert.Inserts;
import com.raycloud.dmj.table.api.plus.query.Queries;
import com.raycloud.dmj.table.api.plus.update.Updates;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * <AUTHOR>
 */
@Repository
public class BillAgainAnalyzeTaskDaoImpl extends BaseDao implements BillAgainAnalyzeTaskDao {

    private final String TABLE_NAME = "bill_again_analyze_task";

    @Override
    public Long insert(BillAgainAnalyzeTaskDO billAgainAnalyzeTaskDO) {
        AsserUtils.notNull(billAgainAnalyzeTaskDO, "参数不能为空！");
        SQL sql = Inserts.insert()
                .into(TABLE_NAME)
                .columns(BillAgainAnalyzeTaskFieldEnum.getInsertFields())
                .valueForEntity(billAgainAnalyzeTaskDO)
                .columnNameCamelToUnderline()
                .toSql();

        return insertReturnPrimaryKey(sql);
    }

    @Override
    public List<BillAgainAnalyzeTaskDO> list(Long companyId) {
        AsserUtils.notNull(companyId, "公司ID不能为空！");
        // 组装查询条件
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(BillAgainAnalyzeTaskFieldEnum.COMPANY_ID.getFieldCode(), LinkMode.EQUAL,companyId)
                )
                .orderBy(
                        Orders.desc(CategoryAnalyzeRuleFieldEnum.MODIFIED.getFieldCode())
                )
                .select(
                )
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<BillAgainAnalyzeTaskDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(BillAgainAnalyzeTaskDO.class), args);
        return !query.isEmpty() ? query : null;

    }

    @Override
    public BillAgainAnalyzeTaskDO getById(Long companyId, Long id) {
        AsserUtils.notNull(companyId, "公司ID不能为空！");
        AsserUtils.notNull(id, "任务ID不能为空！");
        // 组装查询条件
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(BillAgainAnalyzeTaskFieldEnum.COMPANY_ID.getFieldCode(), LinkMode.EQUAL,companyId),
                        Conditions.and(BillAgainAnalyzeTaskFieldEnum.ID.getFieldCode(), LinkMode.EQUAL,id)
                )
                .select(
                ).limit(1)
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<BillAgainAnalyzeTaskDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(BillAgainAnalyzeTaskDO.class), args);
        return !query.isEmpty() ? query.get(0) : null;
    }

    @Override
    public void updateStatusById(BillAgainAnalyzeTaskDO againAnalyzeTaskDO) {
        AsserUtils.notNull(againAnalyzeTaskDO.getCompanyId(), "公司ID不能为空！");
        AsserUtils.notNull(againAnalyzeTaskDO.getId(), "任务ID不能为空！");
        // 2. 生成SQL
        SQL sql = Updates.create()
                .table(TABLE_NAME)
                .where(
                        Conditions.and(BillAgainAnalyzeTaskFieldEnum.COMPANY_ID.getFieldCode(), LinkMode.EQUAL, againAnalyzeTaskDO.getCompanyId()),
                        Conditions.and(BillAgainAnalyzeTaskFieldEnum.ID.getFieldCode(), LinkMode.EQUAL, againAnalyzeTaskDO.getId()),
                        Conditions.and(BillAgainAnalyzeTaskFieldEnum.VERSION.getFieldCode(), LinkMode.EQUAL, againAnalyzeTaskDO.getVersion())
                )
                .update(
                        ColumnValues.create(BillAgainAnalyzeTaskFieldEnum.FEATURE.getFieldCode(), againAnalyzeTaskDO.getFeature()),
                        ColumnValues.create(BillAgainAnalyzeTaskFieldEnum.STATUS.getFieldCode(), againAnalyzeTaskDO.getStatus()),
                        ColumnValues.create(BillAgainAnalyzeTaskFieldEnum.COMPLETION_DATE.getFieldCode(), againAnalyzeTaskDO.getCompletionDate()),
                        ColumnValues.create(BillAgainAnalyzeTaskFieldEnum.VERSION.getFieldCode(), Columns.create(FileOriginalDataMonitorEnum.VERSION.getFieldCode() + "+1"))
                        ).toSql();

        Object[] updateArgs = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        int i= jdbcTemplate.update(sql.getSqlCode(), updateArgs);
        if (i<=0){
            throw new BusinessException(ErrorCodeEnum.DB_ERROR,"数据库更新解析任务状态失败");
        }

    }
}
