package com.raycloud.dmj.account.core.enums.field;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 分类解析规则表字段枚举
 * <AUTHOR>
 */
@Getter
public enum CategoryAnalyzeRuleFieldEnum {

    ID("id", "主键ID"),
    CREATED("created", "创建时间"),
    MODIFIED("modified", "修改时间"),
    RULE_CONDITION("rule_condition", "规则条件（JSON格式）"),
    ENABLE_STATUS("enable_status", "是否可用，1-可用，0-不可用"),
    SUB_CATEGORY_ID("sub_category_id", "子类别ID"),
    COMPANY_ID("company_id", "公司ID"),
    DELETED("deleted", "是否删除，1-已删除，0-未删除"),
    ;

    /**
     * 字段编码
     */
    private final String fieldCode;

    /**
     * 字段描述
     */
    private final String fieldDesc;

    CategoryAnalyzeRuleFieldEnum(String fieldCode, String fieldDesc) {
        this.fieldCode = fieldCode;
        this.fieldDesc = fieldDesc;
    }

    /**
     * 获取插入字段
     * @return 插入字段
     */
    public static Set<String> getInsertFields() {
        List<CategoryAnalyzeRuleFieldEnum> filterField = Arrays.asList(
                CategoryAnalyzeRuleFieldEnum.ID
        );
        return Arrays.stream(CategoryAnalyzeRuleFieldEnum.values()).filter(x -> !filterField.contains(x)).map(
                x -> x.fieldCode
        ).collect(Collectors.toSet());

    }
}