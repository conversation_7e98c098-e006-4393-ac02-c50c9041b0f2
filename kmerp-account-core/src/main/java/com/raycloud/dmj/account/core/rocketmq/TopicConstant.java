package com.raycloud.dmj.account.core.rocketmq;

/**
 * 话题常量
 * <AUTHOR>
 */
public interface TopicConstant {

    /**
     * 原始数据存储TOPIC
     */
    String RAW_DATA_STORAGE_TOPIC = "RAW_DATA_STORAGE_TOPIC";

    /**
     * 原始数据批次入库状态变更消息
     */
    String BATCH_RAW_DATA_STATUS_CHANGE_TOPIC = "BATCH_RAW_DATA_STATUS_CHANGE_TOPIC";


    /**
     * 标准资金流水合计
     */
    String FOUND_BILL_SUMMARY_TOPIC = "FOUND_BILL_SUMMARY_TOPIC";

    /**
     * 规则校验
     */
    String RULE_VERIFY_TOPIC = "RULE_VERIFY_TOPIC";


}
