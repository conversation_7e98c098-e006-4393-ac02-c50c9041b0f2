package com.raycloud.dmj.account.core.platform.base.domain.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Getter
public enum ShopAuthRecordFieldEnum {

    ID("id", "自增主键"),
    SHOP_ID("shop_id", "店铺ID"),
    COMPANY_ID("company_id", "公司ID"),
    PLATFORM_CODE("platform_code", "授权平台id"),
    CALL_BACK_URL("call_back_url", "回调url"),
    AUTH_STATUS("auth_status", "授权状态"),
    CREATED("created", "创建时间"),
    MODIFIED("modified", "修改时间");

    private final String fieldCode;
    private final String fieldDesc;

    ShopAuthRecordFieldEnum(String fieldCode, String fieldDesc) {
        this.fieldCode = fieldCode;
        this.fieldDesc = fieldDesc;
    }

    public static Set<String> getInsertFields() {
        List<ShopAuthRecordFieldEnum> filterFields = Arrays.asList(ID);
        return Arrays.stream(values())
                .filter(f -> !filterFields.contains(f))
                .map(f -> f.fieldCode)
                .collect(Collectors.toSet());
    }

}
