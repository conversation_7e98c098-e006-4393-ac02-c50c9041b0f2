package com.raycloud.dmj.account.core.enums.field;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Date:  2025/6/16
 * 文件原始数据监控表数据库字段枚举
 * <AUTHOR>
 */
@Getter
public enum FileOriginalDataMonitorEnum {

    ID("id", "主键"),
    COMPANY_ID("company_id", "公司ID"),
    SHOP_ID("shop_id", "店铺ID"),
    BATCH_CODE("batch_code", "批次号"),
    TABLE_NAME("table_name", "当前平台对应的原始数据表名"),
    DATE_TYPE("date_type", "时间类型，默认为1，1为日，2为月，3为年"),
    DATA_RANGE("data_range", "当前数据的日期"),
    LAST_RECORD_ID("last_record_id", "当前数据来源的文件解析记录ID"),
    LAST_CONFIG_ID("last_config_id", "当前数据来源的表配置ID"),
    DATA_STATUS("data_status", "数据状态：1无异常成功数据，2存在异常导入的数据"),
    DATA_SOURCE("data_source", "数据来源：1-支付宝，2-微信，3-保证金，4-消费积分，5-集分宝，6-惠营宝，7-直播红包，8-淘宝联盟，9-淘金币"),
    CREATED("created", "创建时间"),
    UPDATED("updated", "更新时间"),
    VERSION("version", "版本号"),
    ;

    /**
     * 字段编码
     */
    private final String fieldCode;

    /**
     * 字段描述
     */
    private final String fieldDesc;

    FileOriginalDataMonitorEnum(String fieldCode, String fieldDesc) {
        this.fieldCode = fieldCode;
        this.fieldDesc = fieldDesc;
    }


    /**
     * 获取插入字段
     * @return 插入字段
     */
    public static Set<String> getInsertFields() {
        List<FileOriginalDataMonitorEnum> filterField = Arrays.asList(
                FileOriginalDataMonitorEnum.ID,
                FileOriginalDataMonitorEnum.VERSION
        );
       return Arrays.stream(FileOriginalDataMonitorEnum.values()).filter(x -> !filterField.contains(x)).map(
                x -> x.fieldCode
        ).collect(Collectors.toSet());

    }

    /**
     * 获取查询字段
     * @return 插入字段
     */
    public static String[] getSelectFields() {

        return Arrays.stream(FileOriginalDataMonitorEnum.values())
                .map(FileOriginalDataMonitorEnum::getFieldCode)
                .toArray(String[]::new);

    }


}
