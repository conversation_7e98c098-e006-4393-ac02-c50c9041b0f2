package com.raycloud.dmj.account.core.bill.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 资金流水合计记录查询请求对象
 * <AUTHOR>
 */
@Data
public class BillSummaryRecordRequest {

    /**
     * 平台code
     */
    private String  platformCode;

    /**
     * 资金账户ID
     */
    private List<Long> accountIdList;

    /**
     * 流水类别ID
     */
    private List<Long> categoryIdList;

    /**
     * 流水子类别ID
     */
    private List<Long> subCategoryIdList;

    /**
     * 帐期开始时间
     */
    private String startTime;

    /**
     * 帐期结束时间
     */
    private String endTime;

    /**
     * 帐期类型 1:日账单 2:月账单
     */
    private Integer billingCycleType;

    /**
     * 店铺ID
     */
    private Long shopId;

}
