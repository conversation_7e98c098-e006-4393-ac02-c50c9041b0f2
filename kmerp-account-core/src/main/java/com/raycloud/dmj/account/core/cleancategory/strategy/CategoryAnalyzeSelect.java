package com.raycloud.dmj.account.core.cleancategory.strategy;

import com.raycloud.dmj.account.core.cleancategory.strategy.param.CategoryAnalyzeParam;
import com.raycloud.dmj.account.infra.common.BusinessException;
import com.raycloud.dmj.account.infra.common.ErrorCodeEnum;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;


/**
 * 类别别解析处理选择器
 *
 * <AUTHOR>
 */
@Component
public class CategoryAnalyzeSelect {

    @Resource
    private List<CategoryAnalyzeHandle> handles;


    /**
     * 选择类别别解析处理
     * @param param 类别别解析参数
     */
    public void execute(CategoryAnalyzeParam param) {
        AsserUtils.notNull(param, "参数不能为空");
        AsserUtils.notNull(param.getSource(), "原始数据来源不能为空");
        CategoryAnalyzeHandle categoryAnalyzeHandle = handles.stream()
                .filter(handle -> handle.accord(param.getSource()))
                .findFirst()
                .orElseThrow(() -> new BusinessException(
                        ErrorCodeEnum.STRATEGY_NOT_FOUND.getCode(),
                        "未找到对应的类别解析处理器！"
                ));
        categoryAnalyzeHandle.handle(param);

    }

}
