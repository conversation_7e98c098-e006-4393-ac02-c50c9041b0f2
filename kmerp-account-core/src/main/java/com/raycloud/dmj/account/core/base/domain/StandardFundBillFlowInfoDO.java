package com.raycloud.dmj.account.core.base.domain;

import com.raycloud.dmj.account.core.common.BaseInfo;
import com.raycloud.dmj.account.core.enums.IncomeExpenseDirectionEnum;
import com.raycloud.dmj.account.core.enums.StandardFundBillSourceEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 标准资金账单流水信息表实体类
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StandardFundBillFlowInfoDO extends BaseInfo {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 平台类型
     */
    private String platformCode;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 资金账户ID
     */
    private Long accountId;

    /**
     * 发生时间
     */
    private Date occurredAt;

    /**
     * 数据清洗规则ID
     */
    private Long ruleId;

    /**
     * 分类code
     */
    private String categoryCode;

    /**
     * 流水类别ID
     */
    private Long categoryId;

    /**
     * 流水子类别ID
     */
    private Long subCategoryId;

    /**
     * 金额(分)（正数收入，负数支出）
     */
    private BigDecimal amount;

    /**
     * 收支方向 1 收入 2 支出
     * @see IncomeExpenseDirectionEnum
     */
    private Integer incomeExpenseDirection;

    /**
     * 关联账单流水号
     */
    private String billNo;

    /**
     * 关联订单号
     */
    private String orderNo;

    /**
     * 关联业务单据号
     */
    private String docNo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 关联拆单信息ID
     */
    private Long belongId;

    /**
     * 是否可抵消（1可抵消，0不可抵消）
     */
    private Integer isOffset;

    /**
     * 是否已拆分（1是，0否）
     */
    private Integer isSplit;

    /**
     * 确认状态(1已确认,0待确认)
     */
    private Integer confirmed;

    /**
     * 来源方式(1 账单,0 系统)
     * @see StandardFundBillSourceEnum
     * 标准资金账单来源方式
     */
    private Integer source;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 账期
     */
    private Integer dataRange;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 唯一键
     */
    private String bizKey;
}
