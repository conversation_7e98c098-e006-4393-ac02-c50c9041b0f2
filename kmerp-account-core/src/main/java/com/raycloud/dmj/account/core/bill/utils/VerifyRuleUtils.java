package com.raycloud.dmj.account.core.bill.utils;

import com.raycloud.dmj.account.core.bill.dto.VerifyRuleDTO;
import com.raycloud.dmj.account.core.bill.request.VerifyRuleRequest;
import com.raycloud.dmj.account.core.enums.OperatorTypeEnum;
import com.raycloud.dmj.account.infra.common.BusinessException;
import com.raycloud.dmj.account.infra.common.ErrorCodeEnum;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * 校验规则工具类
 * 用于处理数学表达式的解析、验证和计算
 * 支持常量、变量、运算符（+、-、*、/）和括号的组合表达式
 *
 * <AUTHOR>
 */
public class VerifyRuleUtils {
    /**
     * 根据校验规则计算表达式结果
     * 将中缀表达式转换为后缀表达式，然后进行计算
     *
     * @param ruleVoList 校验规则列表，包含表达式的各个组成部分
     * @param variables 变量映射表，key为变量名，value为变量值
     * @return 表达式计算结果
     */
    public static BigDecimal evaluateExpression(List<VerifyRuleDTO> ruleVoList, Map<String, BigDecimal> variables) {
        // 将中缀表达式转换为后缀表达式
        List<Object> postfix = infixToPostfix(ruleVoList);
        // 计算后缀表达式的值
        return evaluatePostfix(postfix, variables);
    }

    /**
     * 将中缀表达式转换为后缀表达式（逆波兰表达式）
     * 使用调度场算法（Shunting Yard Algorithm）进行转换
     *
     * @param ruleDTOList 中缀表达式的规则列表
     * @return 后缀表达式列表，包含操作数和运算符
     */
    private static List<Object> infixToPostfix(List<VerifyRuleDTO> ruleDTOList) {
        // 存储后缀表达式结果
        List<Object> postfix = new ArrayList<>();
        // 运算符栈，用于临时存储运算符和括号
        Stack<String> operatorStack = new Stack<>();

        // 定义运算符优先级，数值越大优先级越高
        Map<String, Integer> precedence = new HashMap<>();
        precedence.put("+", 1);  // 加法优先级
        precedence.put("-", 1);  // 减法优先级
        precedence.put("*", 2);  // 乘法优先级
        precedence.put("/", 2);  // 除法优先级
        precedence.put("(", 0);  // 左括号优先级最低

        // 遍历中缀表达式的每个元素
        for (VerifyRuleDTO ruleDTO : ruleDTOList) {
            String type = ruleDTO.getType();
            String value = ruleDTO.getValue();

            // 验证规则类型是否有效
            OperatorTypeEnum operatorTypeEnum = OperatorTypeEnum.getByType(type);
            if (operatorTypeEnum == null) {
                throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR,"未知的规则类型: " + type);
            }

            // 根据不同类型处理元素
            switch (operatorTypeEnum) {
                case CONSTANT:
                    // 常量直接添加到后缀表达式
                    postfix.add(new BigDecimal(value));
                    break;
                case VARIABLE:
                    // 变量名直接添加到后缀表达式
                    postfix.add(value);
                    break;
                case OPERATOR:
                    // 处理运算符：将栈中优先级大于等于当前运算符的运算符弹出
                    while (!operatorStack.isEmpty() &&
                            precedence.getOrDefault(operatorStack.peek(), 0) >= precedence.get(value)) {
                        postfix.add(operatorStack.pop());
                    }
                    // 将当前运算符压入栈
                    operatorStack.push(value);
                    break;
                case PARENTHESIS:
                    if (value.equals("(")) {
                        // 左括号直接压入栈
                        operatorStack.push(value);
                    } else {
                        // 右括号：弹出栈中元素直到遇到左括号
                        while (!operatorStack.peek().equals("(")) {
                            postfix.add(operatorStack.pop());
                        }
                        // 弹出左括号（不添加到后缀表达式）
                        operatorStack.pop();
                    }
                    break;
            }
        }

        // 将栈中剩余的运算符全部弹出到后缀表达式
        while (!operatorStack.isEmpty()) {
            postfix.add(operatorStack.pop());
        }
        return postfix;
    }

    /**
     * 计算后缀表达式的值
     * 使用栈结构进行计算，遇到操作数入栈，遇到运算符弹出两个操作数进行计算
     *
     * @param postfix 后缀表达式列表
     * @param variables 变量映射表，用于获取变量的实际值
     * @return 表达式计算结果
     */
    private static BigDecimal evaluatePostfix(List<Object> postfix, Map<String, BigDecimal> variables) {
        System.out.println(postfix);
        // 操作数栈，用于存储计算过程中的数值
        Stack<BigDecimal> stack = new Stack<>();

        // 遍历后缀表达式的每个元素
        for (Object value : postfix) {
            if (value instanceof BigDecimal) {
                // 如果是常量，直接入栈
                stack.push((BigDecimal) value);
            } else if (value instanceof String) {
                String valueStr = (String) value;
                if (isOperator(valueStr)) {
                    // 如果是运算符，弹出两个操作数进行计算
                    BigDecimal b = stack.pop();  // 第二个操作数
                    BigDecimal a = stack.pop();  // 第一个操作数
                    BigDecimal result = applyOperator(valueStr, a, b);
                    stack.push(result);  // 计算结果入栈
                } else {
                    // 如果是变量，从变量映射表中获取值
                    System.out.println(valueStr);
                    if (!variables.containsKey(valueStr)) {
                        throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR,"未定义的变量: " + valueStr);
                    }
                    stack.push(variables.get(valueStr));
                }
            }
        }

        // 栈中最后剩余的元素就是表达式的计算结果
        return stack.pop();
    }

    /**
     * 判断字符串是否为运算符
     *
     * @param token 待检查的字符串
     * @return 如果是运算符返回true，否则返回false
     */
    private static boolean isOperator(String token) {
        return "+-*/".contains(token);
    }

    /**
     * 执行具体的运算操作
     * 支持加法、减法、乘法、除法四种基本运算
     *
     * @param operator 运算符（+、-、*、/）
     * @param a 第一个操作数
     * @param b 第二个操作数
     * @return 运算结果
     * @throws BusinessException 当除数为零或运算符不支持时抛出异常
     */
    private static BigDecimal applyOperator(String operator, BigDecimal a, BigDecimal b) {
        switch (operator) {
            case "+":
                // 加法运算
                return a.add(b);
            case "-":
                // 减法运算
                return a.subtract(b);
            case "*":
                // 乘法运算
                return a.multiply(b);
            case "/":
                // 除法运算，需要检查除数是否为零
                if (b.equals(BigDecimal.ZERO)) {
                    throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR,"除数不能为零");
                }
                return a.divide(b);
            default:
                throw new BusinessException(ErrorCodeEnum.SYSTEM_ERROR,"不支持的运算符: " + operator);
        }
    }

    /**
     * 校验表达式规则的合法性
     * 包括语法检查、括号匹配、运算符序列验证等
     *
     * @param ruleList 待验证的规则列表
     * @throws BusinessException 当规则不合法时抛出异常
     */
    public static void validateExpression(List<VerifyRuleRequest> ruleList) {
        // 检查规则列表是否为空
        if (CollectionUtils.isEmpty(ruleList)) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR,"规则表达式不能为空");
        }

        // 括号平衡计数器，用于检查括号是否匹配
        int parenthesisBalance = 0;
        // 记录上一个规则的类型，用于检查相邻规则的合法性
        String prevType = null;

        // 遍历每个规则进行验证
        for (VerifyRuleRequest request : ruleList) {
            String type = request.getType();
            String value = request.getValue(); // 修复：应该获取value而不是type

            // 验证规则类型是否有效
            OperatorTypeEnum operatorTypeEnum = OperatorTypeEnum.getByType(type);
            if (operatorTypeEnum == null) {
                throw new BusinessException(ErrorCodeEnum.PARAM_ERROR,"未知的规则类型: " + type);
            }

            // 根据不同类型验证value字段的格式
            switch (operatorTypeEnum) {
                case CONSTANT:
                    // 验证常量值是否为有效数字
                    try {
                        new BigDecimal(value);
                    } catch (NumberFormatException e) {
                        throw new BusinessException(ErrorCodeEnum.PARAM_ERROR,"常量值格式错误: " + value);
                    }
                    break;
                case OPERATOR:
                    // 验证运算符是否支持
                    if (!"+-*/".contains(value)) {
                        throw new BusinessException(ErrorCodeEnum.PARAM_ERROR,"不支持的运算符: " + value);
                    }
                    break;
                case PARENTHESIS:
                    // 验证括号类型是否支持
                    if (!Arrays.asList("(", ")").contains(value)) {
                        throw new BusinessException(ErrorCodeEnum.PARAM_ERROR,"不支持的括号类型: " + value);
                    }
                    // 更新括号平衡计数器
                    if (value.equals("(")) {
                        parenthesisBalance++;
                    } else {
                        parenthesisBalance--;
                    }
                    // 检查是否出现右括号多于左括号的情况
                    if (parenthesisBalance < 0) {
                        throw new BusinessException(ErrorCodeEnum.PARAM_ERROR,"括号不匹配: 右括号多于左括号");
                    }
                    break;
            }

            // 验证rule序列的合法性
            if (prevType != null) {
                // 不允许两个运算符相邻
                if (OperatorTypeEnum.OPERATOR.getType().equals(prevType) && OperatorTypeEnum.OPERATOR.getType().equals(type)) {
                    throw new BusinessException(ErrorCodeEnum.PARAM_ERROR,"不允许两个运算符相邻");
                }
                // 不允许两个常量/变量相邻
                if ((isOperand(prevType) && isOperand(type))) {
                    throw new BusinessException(ErrorCodeEnum.PARAM_ERROR,"不允许两个操作数相邻");
                }

                // 左括号后面不能是运算符
                if (OperatorTypeEnum.PARENTHESIS.getType().equals(prevType) && "(".equals(ruleList.get(ruleList.indexOf(request) - 1).getValue()) && OperatorTypeEnum.OPERATOR.getType().equals(type)) {
                    throw new BusinessException(ErrorCodeEnum.PARAM_ERROR,"左括号后面不能是运算符");
                }

                // 右括号前面不能是运算符
                if (OperatorTypeEnum.OPERATOR.getType().equals(prevType) && OperatorTypeEnum.PARENTHESIS.getType().equals(type) && ")".equals(value)) {
                    throw new BusinessException(ErrorCodeEnum.PARAM_ERROR,"右括号前面不能是运算符");
                }
            }

            prevType = type;
        }

        // 检查括号是否完全匹配
        if (parenthesisBalance != 0) {
            throw new IllegalArgumentException("括号不匹配: 左括号多于右括号");
        }

        // 检查表达式是否以操作数或右括号结尾
        VerifyRuleRequest lastRule = ruleList.get(ruleList.size() - 1);
        if (!isOperand(lastRule.getType()) && !(lastRule.getType().equals(OperatorTypeEnum.PARENTHESIS.getType()) && lastRule.getValue().equals(")"))) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR,"表达式不能以运算符或左括号结尾");
        }
    }

    //检查是不是常量或者变量
    private static boolean isOperand(String type) {
        return OperatorTypeEnum.CONSTANT.getType().equals(type) || OperatorTypeEnum.VARIABLE.getType().equals(type);
    }
}
