package com.raycloud.dmj.account.export.core.processor.base;

import lombok.Data;
import org.springframework.context.ApplicationContext;
import org.springframework.util.ObjectUtils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
public class ProcessorChainBuilder<T> {

    private ApplicationContext applicationContext;

    private String name;

    private Object parameter;

    private Integer chuckSize = 0;

    private List<String> effectiveColumnNames = new ArrayList<>();

    private final List<AbstractDataProcessor<T>> processors = new ArrayList<>();

    public static <T> ProcessorChainBuilder<T> create(String name, ApplicationContext applicationContext) {
        ProcessorChainBuilder<T> builder = new ProcessorChainBuilder<>();
        builder.setName(name);
        builder.setApplicationContext(applicationContext);
        return builder;
    }

    public ProcessorChainBuilder<T> chuckSize(Integer chuckSize) {
        this.chuckSize = chuckSize;
        return this;
    }

    public ProcessorChainBuilder<T> effectiveColumns(List<String> effectiveColumnNames) {
        if (ObjectUtils.isEmpty(effectiveColumnNames)) {
            return this;
        }
        this.effectiveColumnNames.addAll(effectiveColumnNames);
        return this;
    }

    public ProcessorChainBuilder<T> parameter(Object parameter) {
        this.parameter = parameter;
        return this;
    }


    public ProcessorChainBuilder<T> addProcessor(AbstractDataProcessor<T> processor) {
        this.processors.add(processor);
        return this;
    }

    public ProcessorChainBuilder<T> addProcessor(String processorBeanName, Class<? extends AbstractDataProcessor<T>> processorClass) {
        AbstractDataProcessor<T> bean = applicationContext.getBean(processorBeanName, processorClass);
        this.processors.add(bean);
        return this;
    }

    public ProcessorChainBuilder<T> addProcessor(Class<? extends AbstractDataProcessor<T>> processorClass) {
        AbstractDataProcessor<T> bean = applicationContext.getBean(processorClass);
        this.processors.add(bean);
        return this;
    }

    public ProcessorChain<T> build() {

        ProcessorContext context = new ProcessorContext();
        context.setName(name);
        context.setChuckSize(chuckSize);
        context.setParameter(parameter);
        context.setAppContext(applicationContext);
        context.getEffectiveColumnNames().addAll(effectiveColumnNames);

        initContext(context);

        ProcessorChain<T> chain = new ProcessorChain<>(context);
        chain.getProcessors().addAll(processors);

        return chain;
    }


    private void initContext(ProcessorContext context) {
        Object param = context.getParameter();
        if (ObjectUtils.isEmpty(param)) {
            return;
        }

        Field[] fields = param.getClass().getDeclaredFields();
        if (ObjectUtils.isEmpty(fields)) {
            return;
        }

        Map<String, AppParameter> executionContext = context.getExecutionContext();

        for (Field field : fields) {
            field.setAccessible(true);
            Object fieldVal = null;
            try {
                fieldVal = field.get(param);
            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            }
            if (fieldVal instanceof AppParameter) {
                executionContext.put(fieldVal.getClass().getName(), (AppParameter) fieldVal);
            }
        }
    }
}
