package com.raycloud.dmj.account.core.enums;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 其他类目组枚举
 *
 * <AUTHOR>
 */
@Getter
public enum OtherSubCategoryEnum {

    //集分宝
    JFB(OtherCategoryEnum.JFB, "JFB", "集分宝"),
    //淘宝联盟
    TBLM(OtherCategoryEnum.TBLM, "TBLM", "淘宝联盟"),
    //淘金币
    TJJ(OtherCategoryEnum.TJJ, "TJJ", "淘金币"),
    //消费积分
    XFJF(OtherCategoryEnum.XFJF, "XFJF", "消费积分"),
    //惠营宝
    HYB(OtherCategoryEnum.HYB, "HYB", "惠营宝"),
    //直播红包-红包雨
    ZBHB_HBY(OtherCategoryEnum.ZBHB, "ZBHB_HBY", "红包雨"),
    //直播红包-直播新人红包
    ZBHB_ZBXRHB(OtherCategoryEnum.ZBHB, "ZBHB_ZBXRHB", "直播新人红包"),
    //直播红包-口令红包
    ZBHB_KLHB(OtherCategoryEnum.ZBHB, "ZBHB_KLHB", "口令红包"),
    //直播红包-宠粉红包
    ZBHB_CFBD(OtherCategoryEnum.ZBHB, "ZBHB_ZBDF", "宠粉红包"),
    //直播红包-直播定金
    ZBHB_ZBDJ(OtherCategoryEnum.ZBHB, "ZBHB_ZBDJ", "直播定金红包"),
    ;

    private final OtherCategoryEnum category;

    private final String code;

    private final String desc;

    OtherSubCategoryEnum(OtherCategoryEnum category, String code, String desc) {
        this.category = category;
        this.code = code;
        this.desc = desc;
    }


    /**
     * 根据描述获取直播红包子类目
     *
     * @param desc 描述
     * @return 子类目枚举
     */
    public static OtherSubCategoryEnum getLiveRedSubCategorByDesc(String desc) {
        return Arrays.stream(OtherSubCategoryEnum.values()).filter(value ->
                value.getCategory().equals(OtherCategoryEnum.ZBHB)
        ).filter(value ->
                StringUtils.equals(value.desc, desc)
        ).findFirst().orElse(null);
    }

    public static String getDescByCode(String code) {
        for (OtherSubCategoryEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return "未知子分类";
    }

    /**
     * 获取子分类集合
     */
    public static List<Map<String, String>> getSubCategoryList() {
        List<Map<String,String>> subCategoryList = new ArrayList<>();
        for (OtherSubCategoryEnum subCategoryEnum : OtherSubCategoryEnum.values()) {
            HashMap<String, String> subCategoryMap = new HashMap<>();
            subCategoryMap.put("code", subCategoryEnum.code);
            subCategoryMap.put("desc", subCategoryEnum.desc);
            subCategoryList.add(subCategoryMap);
        }
        return  subCategoryList;
    }
}
