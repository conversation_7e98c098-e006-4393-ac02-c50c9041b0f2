package com.raycloud.dmj.account.core.common.response;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.http.HttpStatus;


/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class Response<T> extends BaseResponse<T> {


    public static <T> Response<T> success(T value) {
        Response<T> result = new Response<T>();
        result.setResult(HttpStatus.OK.value());
        result.setData(value);
        result.setErrorCode("SUCCESS");
        return result;
    }

    public static <T> Response<T> success(T value,String clueId) {
        Response<T> result = new Response<T>();
        result.setResult(HttpStatus.OK.value());
        result.setData(value);
        result.setClueId(clueId);
        return result;
    }

    public static <T> Response<T> success() {
        Response<T> result = new Response<T>();
        result.setResult(HttpStatus.OK.value());
        result.setData(null);
        result.setErrorCode("SUCCESS");
        return result;
    }


    public static <T> Response<T> error(String errorMessage) {
        Response<T> response = new Response<T>();
        response.setResult(HttpStatus.OK.value());
        response.setErrorMessage(errorMessage);
        return response;
    }

    public static <T> Response<T> error(String errorCode,String errorMessage) {
        Response<T> response = new Response<T>();
        response.setResult(HttpStatus.OK.value());
        response.setErrorCode(errorCode);
        response.setErrorMessage(errorMessage);
        return response;
    }

    public static <T> Response<T> error(Integer result,String errorCode,String errorMessage) {
        Response<T> response = new Response<T>();
        response.setResult(result);
        response.setErrorCode(errorCode);
        response.setErrorMessage(errorMessage);
        return response;
    }

    /**
     * 错误信息描述
     */
    private String errorMessage;

    /**
     * 错误码
     */
    private String errorCode;

}
