package com.raycloud.dmj.account.export.utils;

import com.alibaba.fastjson2.JSONObject;
import com.raycloud.dmj.account.export.core.helper.ChessboardExportHelper;
import com.raycloud.dmj.account.infra.common.BizException;
import com.raycloud.dmj.data.chessboard.model.FileParameter;
import com.raycloud.dmj.data.chessboard.model.TaskMessage;
import com.raycloud.dmj.data.export.core.ExportFileType;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.domain.enums.EnumDownloadCenterModule;
import com.raycloud.dmj.download.domain.FileDownloadParam;
import com.raycloud.dmj.table.api.plus.utils.IdUtils;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.log4j.MDC;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Consumer;

public class ExportUtils {

    public final static String EXT_PARAM_QUERY_TOPIC = "queryTopic";
    public final static String EXT_PARAM_REPORT_BEAN_CLASS = "reportBeanClass";
    public final static String EXT_PARAM_PAGE_ID = "pageId";
    public final static String EXT_PARAM_QUERY_FLAG = "queryFlag";

    public static <T> T extractParameter(Object parameter, Class<T> clazz) {
        if (parameter == null) {
            return null;
        }
        if (clazz.equals(parameter.getClass())) {
            return (T) parameter;
        } else if (parameter instanceof JSONObject) {
            return ((JSONObject) parameter).toJavaObject(clazz);
        } else if (parameter instanceof String) {
            try {
                return JSONObject.parseObject(parameter.toString(), clazz);
            } catch (Exception e) {
                return null;
            }
        }
        throw new RuntimeException("无法解析的参数：" + parameter.getClass());
    }

    public static Class<?> extractReportBeanClass(Map<String, Object> extendParameter) {
        if (extendParameter == null) {
            return null;
        }
        Object object = extendParameter.get(EXT_PARAM_REPORT_BEAN_CLASS);
        if (object instanceof Class) {
            return (Class<?>) object;
        } else if (object instanceof String) {
            try {
                return Class.forName((String) object);
            } catch (ClassNotFoundException e) {
                return null;
            }
        }
        return null;
    }

    public static Long extractPageId(Map<String, Object> extendParameter) {
        if (extendParameter == null) {
            return null;
        }
        Object object = extendParameter.get(EXT_PARAM_PAGE_ID);
        if (object instanceof Long) {
            return (Long) object;
        } else if (object instanceof String) {
            return NumberUtils.toLong((String) object);
        }
        return null;
    }

    public static void preprocess(TaskMessage taskMessage) {

        final Map<String, Object> extendParameter = taskMessage.getExtendParameter();
        preprocess(taskMessage, new Consumer<Map.Entry<String, Object>>() {
            @Override
            public void accept(Map.Entry<String, Object> entry) {
                String key = entry.getKey();
                if (key.equals(EXT_PARAM_REPORT_BEAN_CLASS)) {
                    Class<?> clazz = extractReportBeanClass(extendParameter);
                    entry.setValue(clazz);
                } else if (key.equals(EXT_PARAM_PAGE_ID)) {
                    Object value = entry.getValue();
                    if (value == null) {
                        return;
                    }
                    Long pageId = Long.valueOf(String.valueOf(value));
                    entry.setValue(pageId);
                }
            }
        });
    }

    public static void preprocess(TaskMessage taskMessage, Consumer<Map.Entry<String, Object>> consumer) {
        if (taskMessage == null) {
            return;
        }
        Map<String, Object> extendParameter = taskMessage.getExtendParameter();
        if (extendParameter == null) {
            return;
        }
        for (Map.Entry<String, Object> entry : extendParameter.entrySet()) {
            consumer.accept(entry);
        }
    }


    public static FileParameter buildFileParameter(Long companyId, FileDownloadParam fdp) {

        String fileName = standardizeFileName(
                fdp.getFileName(),
                ExportFileType.EXCEL,
                companyId,
                true
        );

        FileParameter fp = new FileParameter();
        fp.setFileName(fileName);
        fp.setTitle(fdp.getExcelTitle());
        fp.setFileType("excel");
        fp.setFileModule(fdp.getModule());
        fp.setNeedVerify(fdp.getNeedVerify() != null && fdp.getNeedVerify());
        return fp;
    }

    public static String standardizeFileName(String name, ExportFileType fileType, Object uid, boolean addRandomString) {

        if (StringUtils.isEmpty(name)) {
            throw new IllegalArgumentException("文件名不能为空");
        }
        if (uid == null) {
            uid = BigDecimal.valueOf(1).divide(BigDecimal.valueOf(System.currentTimeMillis()), 16, RoundingMode.HALF_UP);
        }
        int index = name.lastIndexOf('.');
        String suffix = fileType.getSuffix();
        if (index > 0) {
            if (addRandomString) {
                String hex = DigestUtils.md5Hex(uid.toString() + "_" + System.currentTimeMillis());
                String subHex = hex.substring(0, 16);
                return name.substring(0, index) + "_" + subHex + suffix;
            }
            return name.substring(0, index) + "_" + uid + suffix;
        } else {
            if (addRandomString) {
                String hex = DigestUtils.md5Hex(uid.toString() + "_" + System.currentTimeMillis());
                String subHex = hex.substring(0, 16);
                return name + "_" + subHex + suffix;
            }
            return name + "_" + uid + suffix;
        }
    }


    public static Map<String, Object> buildExtendParameter(Class<?> beanClass) {
        Map<String, Object> map = new HashMap<>();
        map.put(EXT_PARAM_REPORT_BEAN_CLASS, beanClass);
        return map;
    }


    public static String virtualProcess(Long adder) {
        if (adder == null) {
            return "1%";
        }
        if (adder <= 0L) {
            return "1%";
        }
        if (adder > 98L) {
            return "98%";
        }
        return String.valueOf(adder) + "%";
    }

    public static String realProcess(Long current, Long total) {
        if (total == null || current == null || total == 0L || current == 0L) {
            return "0%";
        }
        return String.valueOf(current * 100 / total) + "%";
    }

    public static TaskBuilder builder() {
        return new TaskBuilder();
    }


    public static class TaskBuilder {

        private TaskMessage tm = new TaskMessage();

        private String hex = null;

        public TaskBuilder() {
            tm.setEnv(ChessboardExportHelper.getRunningEnv().getEnvName());
            tm.setServiceName(ChessboardExportHelper.getRunningServiceName());
            String clueId = String.valueOf(MDC.get("clueId"));
            if (StringUtils.isBlank(clueId) || "null".equals(clueId)) {
                clueId = String.valueOf(IdUtils.gen());
            }
            tm.setClueId(clueId);
            String md5Hex = DigestUtils.md5Hex(tm.getClueId());
            int length = md5Hex.length();
            hex = md5Hex.substring(length - 8);
        }

        public TaskBuilder staff(Staff staff) {
            tm.setCompanyId(staff.getCompanyId());
            tm.setStaffId(staff.getId());
            tm.setStaffName(staff.getName());
            return this;
        }

        public TaskBuilder taskCode(String taskCode) {
            tm.setTaskCode(taskCode);
            return this;
        }

        public TaskBuilder taskName(String taskName) {
            tm.setTaskName(taskName);
            return this;
        }

        public TaskBuilder parameter(Object parameter) {
            tm.setParameter(parameter);
            return this;
        }

        public TaskBuilder autoExcelFileParameter(String title) {

            if (StringUtils.isBlank(title)) {
                throw new BizException("excel标题不能为空");
            }
            if (title.contains("/")) {
                title = title.replaceAll("/", "-");
            }

            String taskName = tm.getTaskName();
            String timeTag = DateTimeUtils.format(new Date(), "yyyyMMddHHmmss");

            if (StringUtils.isBlank(taskName)) {
                String temp = title + "-" + timeTag;
                taskName = standardizeFileName(temp, ExportFileType.EXCEL, tm.getStaffId(), true);
                tm.setTaskName(taskName);
            }

            String fileName = taskName;

            boolean needVerify = false;
            FileParameter fileParameter = new FileParameter();
            fileParameter.setTitle(title);
            fileParameter.setFileType("excel");
            fileParameter.setFileName(fileName);
            fileParameter.setFileModule(EnumDownloadCenterModule.DATA.getCode());
            fileParameter.setNeedVerify(needVerify);
            return fileParameter(fileParameter);
        }

        public TaskBuilder appendExtendParameter(String key, Object value) {
            Map<String, Object> extendParameter = tm.getExtendParameter();
            if (extendParameter == null) {
                extendParameter = new HashMap<>();
                tm.setExtendParameter(extendParameter);
            }
            extendParameter.put(key, value);
            return this;
        }

        public TaskBuilder fileParameter(FileParameter fileParameter) {
            tm.setFileParameter(fileParameter);
            return this;
        }


        public TaskBuilder extendParameter(Map<String, Object> extendParameter) {
            tm.setExtendParameter(extendParameter);
            return this;
        }


        public TaskMessage build() {
            return tm;
        }
    }
}
