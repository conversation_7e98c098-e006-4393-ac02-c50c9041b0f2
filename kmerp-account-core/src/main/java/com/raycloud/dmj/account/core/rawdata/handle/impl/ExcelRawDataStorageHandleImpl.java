package com.raycloud.dmj.account.core.rawdata.handle.impl;

import com.raycloud.dmj.account.core.common.constant.Constant;
import com.raycloud.dmj.account.core.enums.field.CategoryAnalyzeStatusEnum;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import com.raycloud.dmj.account.core.rawdata.utils.RawDataDateUtil;
import com.raycloud.dmj.account.core.rawdata.utils.HashTableShardingUtil;
import com.raycloud.dmj.account.core.base.dao.FileAnalyzeRecordDao;
import com.raycloud.dmj.account.core.base.dao.FileOriginalDataMonitorDao;
import com.raycloud.dmj.account.core.rawdata.domains.*;
import com.raycloud.dmj.account.core.enums.DateTypeEnum;
import com.raycloud.dmj.account.core.enums.FileAnalyzeStatusEnum;
import com.raycloud.dmj.account.core.enums.MonitorStatusEnum;
import com.raycloud.dmj.account.core.rawdata.handle.RawDataStorageHandle;
import com.raycloud.dmj.account.core.rawdata.handle.dataset.SqlBuildFactory;
import com.raycloud.dmj.account.core.rawdata.handle.dataset.SqlBuildHandler;
import com.raycloud.dmj.account.core.rawdata.handle.filter.FilterFactory;
import com.raycloud.dmj.account.core.rawdata.handle.filter.FilterHandler;
import com.raycloud.dmj.account.core.rawdata.handle.filter.param.TimeFilterParam;
import com.raycloud.dmj.account.core.rawdata.handle.param.MonitorDataInfo;
import com.raycloud.dmj.account.core.rawdata.handle.param.OperateBatchDbInfo;
import com.raycloud.dmj.account.core.rawdata.handle.param.RawDataStorageBatchInsertContext;
import com.raycloud.dmj.account.core.rawdata.handle.param.UpdateBatchStatusReq;
import com.raycloud.dmj.account.infra.common.BusinessException;
import com.raycloud.dmj.account.infra.common.ErrorCodeEnum;
import com.raycloud.dmj.account.infra.datasource.KmerpDatasourceConfiguration;
import com.raycloud.dmj.account.infra.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.data.util.Pair;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class ExcelRawDataStorageHandleImpl implements RawDataStorageHandle {


    @Resource
    private KmerpDatasourceConfiguration kmerpDatasourceConfiguration;


    @Resource
    private FileOriginalDataMonitorDao fileOriginalDataMonitorDao;


    @Resource
    private FileAnalyzeRecordDao fileAnalyzeRecordDao;


    @Override
    public void batchInsert(RawDataStorageBatchInsertContext context) {
        //参数校验
        validateContext(context);
        MonitorDataInfo monitorDataInfo = context.getMonitorDataInfo();
        Set<String> handleBatchNoList = context.getHandleBatchNoList();
        Set<String> skipBatchNoList = context.getSkipBatchNoList();
        //根据批次号进行分组
        Map<String, List<Map<String, Object>>> batchMaps = groupDataByBatchCode(context.getDataList(), context.getBasicTableName(), context.getBatchField());
        //分批次处理，系统异常时可以根据批次修改监控表状态
        batchMaps.forEach((batchNo, batchList) -> {
            OperateBatchDbInfo operateBatchDbInfo = buildOperateBatchDbInfo(context, batchNo, batchList);
            //是否跳过当前批次
            boolean skipBatch = isSkipBatch(context.getNeedStartTime(), context.getNeedEndTime(), handleBatchNoList, operateBatchDbInfo);
            if (skipBatch) {
                log.info("满足过滤条件，跳过当前批次,batchNo={},", batchNo);
                //记录过滤的条数
                skipBatchNoList.add(batchNo);
                FileSheetCountInfo fileSheetCountInfo = operateBatchDbInfo.getFileSheetCountInfo();
                fileSheetCountInfo.getFilterCount().addAndGet(batchList.size());
                return;
            }
            //前置处理
            preHandle(operateBatchDbInfo, monitorDataInfo, handleBatchNoList);
            //添加批次数据到数据库中
            insertData(operateBatchDbInfo);
        });
    }

    @Override
    public void updateBatchStatus(UpdateBatchStatusReq param) {
        AsserUtils.notNull(param, "参数不能为空！");
        AsserUtils.notNull(param.getCompanyId(), "公司ID不能为空！");
        AsserUtils.notNull(param.getShopId(), "店铺ID不能为空！");
        AsserUtils.notEmpty(param.getBatchCodeSet(), "批次号集合不能为空！");
        AsserUtils.notNull(param.getUpdateStatus(), "批次状态不能为空！");
        fileOriginalDataMonitorDao.batchUpdateStatusByBatchCode(param.getCompanyId(), param.getShopId(),param.getBatchCodeSet(), param.getUpdateStatus().getStatus());
    }

    @Override
    public void handleStuckTasks() {
        //查询在指定时间前状态为导入中的批次
        Date date = DateUtils.addHoursDate(new Date(), -1);
        List<FileAnalyzeRecordDO> fileAnalyzeRecordDOList = fileAnalyzeRecordDao.queryByStatusAndTime(FileAnalyzeStatusEnum.IMPORTING.getStatus(), date);
        //查询批次中文件记录的ID
        Set<Long> lastRecordIds = fileAnalyzeRecordDOList.stream().map(FileAnalyzeRecordDO::getId).collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(lastRecordIds)) {
            kmerpDatasourceConfiguration.doTransaction(Constant.ORIGINAL_DATA_BASE_CONNECTION_KEY, transactionStatus -> {
                try {
                    //将导入文件记录改为失败
                    fileAnalyzeRecordDao.batchUpdateStatusById(lastRecordIds, FileAnalyzeStatusEnum.TIMEOUT_IMPORT_FAIL.getStatus(), FileAnalyzeStatusEnum.TIMEOUT_IMPORT_FAIL.getDesc());
                    fileOriginalDataMonitorDao.batchUpdateStatusByLastRecordIds(lastRecordIds, MonitorStatusEnum.IMPORT_FAIL.getStatus());
                } catch (Exception e) {
                    log.error("更新批次状态异常", e);
                    transactionStatus.setRollbackOnly();
                }
                return transactionStatus;
            });
        }


    }


    /**
     * 构建批次信息
     *
     * @param context   上下文
     * @param batchNo   批次号
     * @param batchList 数据列表
     * @return OperateBatchDbInfo
     */
    private OperateBatchDbInfo buildOperateBatchDbInfo(RawDataStorageBatchInsertContext context, String batchNo, List<Map<String, Object>> batchList) {
        OperateBatchDbInfo operateBatchDbInfo = new OperateBatchDbInfo();
        //当前批次的时间
        Object batchDataTime = batchList.get(0).get(context.getTableSourceConfig().getDateField());
        if (!(batchDataTime instanceof Date)) {
            throw new BusinessException(ErrorCodeEnum.CONFIG_ERROR.getCode(), "数据时间字段类型为空或不是Date类型");
        }
        String shardTableName = HashTableShardingUtil.getShardTableName(context.getBasicTableName(), context.getCompanyId(), context.getTableSourceConfig());
        FileOriginalDataMonitorDO monitorDO = fileOriginalDataMonitorDao.getByShopAndSourceAndBatch(context.getCompanyId(), context.getShopId(),context.getDataSource(), shardTableName,batchNo);
        operateBatchDbInfo.setFileOriginalDataMonitorDO(monitorDO);
        operateBatchDbInfo.setBatchDataTime((Date) batchDataTime);
        operateBatchDbInfo.setTableName(shardTableName);
        operateBatchDbInfo.setTableFields(context.getTableFields());
        operateBatchDbInfo.setBatcDataList(batchList);
        operateBatchDbInfo.setBatchNo(batchNo);
        operateBatchDbInfo.setShopId(context.getShopId());
        operateBatchDbInfo.setFilterConfig(context.getTableSourceConfig().getFilterConfig());
        operateBatchDbInfo.setDataSourceType(context.getTableSourceConfig().getDataSourceType());
        operateBatchDbInfo.setDataSourceCode(context.getTableSourceConfig().getDataSourceCode());
        operateBatchDbInfo.setCompanyId(context.getCompanyId());
        operateBatchDbInfo.setDateType(context.getDateType());
        operateBatchDbInfo.setDataTimeField(context.getTableSourceConfig().getDateField());
        operateBatchDbInfo.setBatchField(context.getBatchField());
        operateBatchDbInfo.setFileSheetCountInfo(context.getFileSheetCountInfo());
        operateBatchDbInfo.setBasicTableName(context.getBasicTableName());
        operateBatchDbInfo.setSheetIndex(context.getSheetIndex());
        operateBatchDbInfo.setDataSource(context.getDataSource());
        return operateBatchDbInfo;
    }

    /**
     * 根据数据源配置根据数据源配置获取JdbcTemplate
     *
     * @param dataSourceCode 数据源配置
     * @return JdbcTemplate
     */
    private JdbcTemplate getJdbcTemplate(String dataSourceCode) {
        try {
            JdbcTemplate jdbcTemplate = kmerpDatasourceConfiguration.getJdbcTemplate(dataSourceCode);
            if (jdbcTemplate == null) {
                throw new BusinessException(ErrorCodeEnum.CONFIG_ERROR.getCode(), "数据源配置错误,请检查数据源配置");
            }
            return jdbcTemplate;
        } catch (Exception e) {
            log.error("|ExcelRawDataStorageHandleImpl.getJdbcTemplate error|获取JdbcTemplate异常", e);
            throw new BusinessException(ErrorCodeEnum.CONFIG_ERROR.getCode(), "获取JdbcTemplate异常");
        }

    }


    /**
     * 判断是否跳过当前批次
     *
     * @param startTime          入参筛选的数据开始
     * @param endTime            入参筛选的数据结束
     * @param handleBatchNoList  已处理批次
     * @param operateBatchDbInfo 当前批次信息
     * @return true表示跳过，false表示不跳过
     */
    private boolean isSkipBatch(Date startTime, Date endTime, Set<String> handleBatchNoList, OperateBatchDbInfo operateBatchDbInfo) {
        //如果当前批次之前已经成功导入或者正在导入，则跳过
        if (!handleBatchNoList.contains(operateBatchDbInfo.getBatchNo()) && operateBatchDbInfo.getFileOriginalDataMonitorDO() != null) {
            if (MonitorStatusEnum.IMPORTING.getStatus().equals(operateBatchDbInfo.getFileOriginalDataMonitorDO().getDataStatus())){
                log.info("该批次正在导入中，请勿重复导入!batchNo={}", operateBatchDbInfo.getBatchNo());
                return true;
            }
            if (MonitorStatusEnum.IMPORT_SUCCESS.getStatus().equals(operateBatchDbInfo.getFileOriginalDataMonitorDO().getDataStatus())) {
                log.info("该批次已经导入成功，跳过当前批次，batchNo={}", operateBatchDbInfo.getBatchNo());
                return true;
            }
        }
        //根据配置文件过滤
        Boolean filterFlag = Optional.ofNullable(operateBatchDbInfo.getFilterConfig()).map(FilterConfig::getFilterFlag).orElse(null);
        if (filterFlag == null || !filterFlag) {
            return false;
        }
        if (Objects.isNull(startTime) || Objects.isNull(endTime) || Objects.isNull(operateBatchDbInfo.getBatchDataTime())) {
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR.getCode(), "过滤的时间参数不能为空！");
        }
        TimeFilterParam timeFilterParam = new TimeFilterParam();
        timeFilterParam.setStartTime(startTime);
        timeFilterParam.setEndTime(endTime);
        timeFilterParam.setTargetTime(operateBatchDbInfo.getBatchDataTime());
        if (!FilterFactory.hasSupport(operateBatchDbInfo.getFilterConfig().getFilterTypeCode())) {
            log.error("filter type not support, type: {}", operateBatchDbInfo.getFilterConfig().getFilterTypeCode());
            throw new BusinessException(ErrorCodeEnum.CONFIG_ERROR.getCode(), "不支持的过滤类型！");
        }
        FilterHandler<TimeFilterParam> handler = FilterFactory.getHandler(operateBatchDbInfo.getFilterConfig().getFilterTypeCode());
        return handler.handle(timeFilterParam);
    }

    /**
     * 参数校验
     *
     * @param context 上下文
     */
    private void validateContext(RawDataStorageBatchInsertContext context) {
        AsserUtils.notNull(context, "参数不能为空！");
        AsserUtils.notEmpty(context.getDataList(), "数据不能为空！");
        AsserUtils.notEmpty(context.getBasicTableName(), "表名不能为空！");
        AsserUtils.notNull(context.getTableSourceConfig(), "表数据源配置不能为空！");
        AsserUtils.notEmpty(context.getTableSourceConfig().getDataSourceCode(), "数据源编码不能为空！");
        AsserUtils.notEmpty(context.getTableSourceConfig().getDataSourceType(), "数据源类型不能为空！");
        AsserUtils.notEmpty(context.getTableSourceConfig().getDateField(), "数据时间字段不能为空！");
        AsserUtils.notNull(context.getBatchField(), "批次号字段不能为空！");
        AsserUtils.notEmpty(context.getTableFields(), "表字段不能为空！");
        AsserUtils.notNull(context.getMonitorDataInfo(), "数据信息不能为空！");
        AsserUtils.notNull(context.getCompanyId(), "公司ID不能为空！");
        AsserUtils.notNull(context.getShopId(), "店铺ID不能为空！");
        AsserUtils.notNull(context.getFileSheetCountInfo(), "FileSheetCountInfo未初始化");
        TableCountInfo tableCountInfo = Optional.ofNullable(context.getFileSheetCountInfo().getTableCountInfoList())
                .map(map -> map.get(context.getBasicTableName()))
                .orElse(null);
        AsserUtils.notNull(tableCountInfo, "TableCountInfo未初始化");
        if (context.getTableSourceConfig().getFilterConfig() != null && context.getTableSourceConfig().getFilterConfig().getFilterFlag()) {
            AsserUtils.notEmpty(context.getTableSourceConfig().getFilterConfig().getFilterField(), "过滤字段未配置!");
            AsserUtils.notEmpty(context.getTableSourceConfig().getFilterConfig().getFilterTypeCode(), "过滤类型未配置!");
        }

        if (context.getTableSourceConfig().getTableSplitFlag() == 1) {
            AsserUtils.notNull(context.getTableSourceConfig().getTableSplitConfig(), "分表配置不能为空！");
            AsserUtils.notNull(context.getTableSourceConfig().getTableSplitConfig().getTableSplitNum(), "分表数配置不能为空！");
        }
    }


    /**
     * 根据批次号进行分组
     *
     * @param dataList   数据列表
     * @param tableName  表名
     * @param batchField 批次号字段
     * @return 分组后的数据列表
     */
    private Map<String, List<Map<String, Object>>> groupDataByBatchCode(List<Map<String, Object>> dataList, String tableName, String batchField) {
        return dataList.stream()
                .peek(map -> validateBatchCode(map, batchField, tableName))
                .collect(Collectors.groupingBy(
                        map -> map.get(batchField).toString(),
                        Collectors.toList()
                ));
    }


    /**
     * 验证批次号字段值不能为空
     *
     * @param map            数据
     * @param batchCodeField 批次号字段
     * @param tableName      表名
     */
    private void validateBatchCode(Map<String, Object> map, String batchCodeField, String tableName) {
        if (map.get(batchCodeField) == null) {
            log.error("批次号字段值为空,tableName={},batchCodeField={}", tableName, batchCodeField);
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR.getCode(),
                    String.format("批次号字段值为空!tableName=%s,batchCodeField=%s", tableName, batchCodeField));
        }
    }

    /**
     * 批量插入数据到数据库中
     *
     * @param operateBatchDbInfo 批次信息
     */
    private void insertData(OperateBatchDbInfo operateBatchDbInfo) {
        if (!SqlBuildFactory.hasSupport(operateBatchDbInfo.getDataSourceType())) {
            log.error("|ExcelRawDataStorageHandleImpl.insertData|不支持的数据源类型,dataSourceType={}", operateBatchDbInfo.getDataSourceType());
            throw new BusinessException(ErrorCodeEnum.CONFIG_ERROR.getCode(), "不支持的数据源类型");
        }
        //获取SQL构建处理器
        SqlBuildHandler handler = SqlBuildFactory.getHandler(operateBatchDbInfo.getDataSourceType());
        //构建SQL
        Pair<String, List<Object[]>> pair = handler.insertBatchSql(operateBatchDbInfo);
        //执行SQL
        JdbcTemplate jdbcTemplate = kmerpDatasourceConfiguration.getJdbcTemplate(operateBatchDbInfo.getDataSourceCode());
        jdbcTemplate.batchUpdate(pair.getFirst(), pair.getSecond());
        //记录入库数量
        FileSheetCountInfo fileSheetCountInfo = operateBatchDbInfo.getFileSheetCountInfo();
        TableCountInfo tableCountInfo = fileSheetCountInfo.getTableCountInfoList().get(operateBatchDbInfo.getBasicTableName());
        tableCountInfo.getInsertCount().addAndGet(pair.getSecond().size());
    }


    /**
     * 预处理
     *
     * @param operateBatchDbInfo 批次信息
     * @param monitorDataInfo    监控相关信息
     * @param handleBatchNoList  处理过的批次号列表
     */
    private void preHandle(OperateBatchDbInfo operateBatchDbInfo, MonitorDataInfo monitorDataInfo, Set<String> handleBatchNoList) {
        //如果批次号不存在，则创建批次监控记录
        if (!handleBatchNoList.contains(operateBatchDbInfo.getBatchNo())) {
            //修改或创建批次监控记录
            updateOrCreateMonitorRecord(operateBatchDbInfo, monitorDataInfo);
            //将批次号加入处理过的批次号列表
            handleBatchNoList.add(operateBatchDbInfo.getBatchNo());
            //根据批次号删除数据
            deleteDataByBatchCode( operateBatchDbInfo);

        }
    }


    /**
     * 修改或创建批次监控记录
     *
     * @param operateBatchDbInfo 批次信息
     * @param monitorDataInfo    监控相关信息
     */
    private void updateOrCreateMonitorRecord(OperateBatchDbInfo operateBatchDbInfo, MonitorDataInfo monitorDataInfo) {
        FileOriginalDataMonitorDO monitorDO = operateBatchDbInfo.getFileOriginalDataMonitorDO();
        //添加或修改批次号到批次监控中，初始化状态为导入中
        if (monitorDO == null) {
            monitorDO = buildNewMonitorRecord(operateBatchDbInfo, monitorDataInfo);
            fileOriginalDataMonitorDao.insert(monitorDO);
        } else {
            updateExistingMonitorRecord(monitorDO, operateBatchDbInfo, monitorDataInfo);
            fileOriginalDataMonitorDao.updateById(monitorDO);
        }
    }


    /**
     * 创建批次监控记录
     *
     * @param operateBatchDbInfo 批次信息
     * @param monitorDataInfo    监控相关信息
     * @return 批次监控记录
     */
    private FileOriginalDataMonitorDO buildNewMonitorRecord(OperateBatchDbInfo operateBatchDbInfo, MonitorDataInfo monitorDataInfo) {
        Date batchDataTime = operateBatchDbInfo.getBatchDataTime();
        DateTypeEnum dateTypeEnum = DateTypeEnum.of(operateBatchDbInfo.getDateType());
        if (Objects.isNull(batchDataTime) || Objects.isNull(dateTypeEnum)) {
            log.error("批次数据时间或时间类型为空,tableName={},batchDataTime={},dateType={}", operateBatchDbInfo.getTableName(), batchDataTime, dateTypeEnum);
            throw new BusinessException(ErrorCodeEnum.PARAM_ERROR.getCode(), "批次数据时间或时间类型为空");
        }
        return FileOriginalDataMonitorDO.builder()
                .companyId(operateBatchDbInfo.getCompanyId())
                .shopId(operateBatchDbInfo.getShopId())
                .batchCode(operateBatchDbInfo.getBatchNo())
                .dataRange(RawDataDateUtil.formatDate(batchDataTime, dateTypeEnum))
                .dataStatus(MonitorStatusEnum.IMPORTING.getStatus())
                .created(new Date())
                .updated(new Date())
                .dataSource(operateBatchDbInfo.getDataSource())
                .dateType(operateBatchDbInfo.getDateType())
                .tableName(operateBatchDbInfo.getTableName())
                .lastConfigId(monitorDataInfo.getLastConfigId())
                .lastRecordId(monitorDataInfo.getLastRecordId())
                .build();
    }

    /**
     * 生成修改批次监控记录DO
     *
     * @param monitorDO          批次监控记录
     * @param operateBatchDbInfo 批次信息
     * @param monitorDataInfo    监控相关信息
     */
    private void updateExistingMonitorRecord(FileOriginalDataMonitorDO monitorDO,
                                             OperateBatchDbInfo operateBatchDbInfo, MonitorDataInfo monitorDataInfo) {
        monitorDO.setLastConfigId(monitorDataInfo.getLastConfigId());
        monitorDO.setLastRecordId(monitorDataInfo.getLastRecordId());
        monitorDO.setDataStatus(MonitorStatusEnum.IMPORTING.getStatus());
        monitorDO.setUpdated(new Date());
        monitorDO.setDataRange(RawDataDateUtil.formatDate(operateBatchDbInfo.getBatchDataTime(), DateTypeEnum.of(operateBatchDbInfo.getDateType())));
        monitorDO.setDateType(operateBatchDbInfo.getDateType());
        monitorDO.setTableName(operateBatchDbInfo.getTableName());
    }

    /**
     * 根据批次号删除原始数据表数据
     *
     * @param operateBatchDbInfo 批次信息
     */
    private void deleteDataByBatchCode( OperateBatchDbInfo operateBatchDbInfo) {
        if (!SqlBuildFactory.hasSupport(operateBatchDbInfo.getDataSourceType())) {
            log.error("不支持的数据源类型,dataSourceType={}", operateBatchDbInfo.getDataSourceType());
            throw new BusinessException(ErrorCodeEnum.CONFIG_ERROR.getCode(), "不支持的数据源类型");
        }
        SqlBuildHandler handler = SqlBuildFactory.getHandler(operateBatchDbInfo.getDataSourceType());
        Pair<String, Object[]> pair = handler.deleteDataByBatchCode(operateBatchDbInfo);
        JdbcTemplate jdbcTemplate = kmerpDatasourceConfiguration.getJdbcTemplate(operateBatchDbInfo.getDataSourceCode());
        jdbcTemplate.update(pair.getFirst(), pair.getSecond());
    }
}