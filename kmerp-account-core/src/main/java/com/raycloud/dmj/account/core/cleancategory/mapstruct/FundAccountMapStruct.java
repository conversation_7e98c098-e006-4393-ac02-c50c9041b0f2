package com.raycloud.dmj.account.core.cleancategory.mapstruct;

import com.raycloud.dmj.account.core.base.domain.ShopInfoDO;
import com.raycloud.dmj.account.core.cleancategory.domain.object.FundAccountDO;
import com.raycloud.dmj.account.core.cleancategory.domain.vo.FundAccountVO;
import com.raycloud.dmj.account.core.cleancategory.domain.request.AddFundAccountReq;
import com.raycloud.dmj.account.core.cleancategory.domain.request.EditFundAccountReq;
import com.raycloud.dmj.account.core.common.TreeVO;
import com.raycloud.dmj.account.core.enums.AccountSourceEnum;
import com.raycloud.dmj.account.core.enums.AccountTypeEnum;
import com.raycloud.dmj.account.core.enums.YesOrNoEnum;
import com.raycloud.dmj.account.infra.session.AccountUser;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.Map;
import java.util.Optional;


/**
 * 账户映射
 *
 * <AUTHOR>
 */
@Mapper(imports = { AccountSourceEnum.class, YesOrNoEnum.class , AccountTypeEnum.class})
public interface FundAccountMapStruct {

    FundAccountMapStruct INSTANCE = Mappers.getMapper( FundAccountMapStruct.class );

    /**
     * 账户信息请求转账户信息DO
     *
     * @param addFundAccountReq 账户信息请求
     * @return 账户信息DO
     */
    @Mapping(target = "source",expression = "java(AccountSourceEnum.SYSTEM.getSourceCode())")
    @Mapping(target = "confirmStartPeriod",expression = "java(YesOrNoEnum.NO.getValue())")
    @Mapping(target = "authorize",expression = "java(YesOrNoEnum.NO.getValue())")
    @Mapping(target = "created", expression = "java(new java.util.Date())")
    @Mapping(target = "modified", expression = "java(new java.util.Date())")
    @Mapping(target = "companyId", source = "accountUser.companyId")
    @Mapping(target = "accountName", source = "addFundAccountReq.accountName")
    @Mapping(target = "id",ignore= true)
    FundAccountDO toDo(AccountUser accountUser, AddFundAccountReq addFundAccountReq);



    FundAccountDO toDo(EditFundAccountReq addFundAccountReq);



    @Mapping(target = "shopCode", expression = "java(FundAccountMapStruct.getShopCode(shopIdToObjMap,fundAccountDO.getShopId()))")
    @Mapping(target = "title", expression = "java(FundAccountMapStruct.getTitle(shopIdToObjMap,fundAccountDO.getShopId()))")
    @Mapping(target = "shortTitle", expression = "java(FundAccountMapStruct.getShortTitle(shopIdToObjMap,fundAccountDO.getShopId()))")
    @Mapping(target = "typeName", expression = "java(FundAccountMapStruct.getTypeName(fundAccountDO.getType()))")
    FundAccountVO toVO(Map<Long, ShopInfoDO> shopIdToObjMap,FundAccountDO fundAccountDO);



    @Mapping(target = "value" , source = "id")
    @Mapping(target = "label" , source = "accountName")
    TreeVO toTreeVO(FundAccountDO fundAccountDO);


    static String getShopCode(Map<Long, ShopInfoDO> shopIdToObjMap,Long shopId){
        if(shopIdToObjMap.isEmpty() || shopId == null){
            return null;
        }
        return Optional.ofNullable(shopIdToObjMap.get(shopId)).map(ShopInfoDO::getShopCode).orElse( null);
    }
    static String getTitle(Map<Long, ShopInfoDO> shopIdToObjMap,Long shopId){
        if(shopIdToObjMap.isEmpty() || shopId == null){
            return null;
        }
        return Optional.ofNullable(shopIdToObjMap.get(shopId)).map(ShopInfoDO::getTitle).orElse( null);
    }

    static String getShortTitle(Map<Long, ShopInfoDO> shopIdToObjMap,Long shopId){
        if(shopIdToObjMap.isEmpty() || shopId == null){
            return null;
        }
        return Optional.ofNullable(shopIdToObjMap.get(shopId)).map(ShopInfoDO::getShortTitle).orElse( null);
    }

    static String getTypeName(Integer type){
        if(type == null){
            return null;
        }
        return Optional.ofNullable(AccountTypeEnum.getByTypeCode(type)).map(AccountTypeEnum::getTypeDesc).orElse(null);

    }



}
