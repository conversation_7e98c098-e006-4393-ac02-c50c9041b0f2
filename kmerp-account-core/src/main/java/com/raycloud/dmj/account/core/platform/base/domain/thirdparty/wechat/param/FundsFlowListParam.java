package com.raycloud.dmj.account.core.platform.base.domain.thirdparty.wechat.param;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * @Description: 资金流水列表参数
 * <AUTHOR>
 * @Date 2023/10/24 5:43 下午
 */
@Data
public class FundsFlowListParam implements Serializable {

    private Integer page;

    private Integer pageSize;

    @JSONField(name = "start_time")
    private Long startTime;

    @JSONField(name = "end_time")
    private Long endTime;

    @JSONField(name = "next_key")
    private String nextKey;

    @JSONField(name = "transaction_id")
    private String transactionId;

}
