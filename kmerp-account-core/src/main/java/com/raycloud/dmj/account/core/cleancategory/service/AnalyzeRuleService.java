package com.raycloud.dmj.account.core.cleancategory.service;

import com.raycloud.dmj.account.core.cleancategory.domain.request.*;
import com.raycloud.dmj.account.core.cleancategory.domain.vo.BillAgainAnalyzeTaskVO;
import com.raycloud.dmj.account.core.cleancategory.domain.vo.CategoryAnalyzeRuleVO;
import com.raycloud.dmj.account.core.common.PageListBase;
import com.raycloud.dmj.account.core.common.TreeVO;
import com.raycloud.dmj.account.infra.session.AccountUser;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AnalyzeRuleService {

    /**
     * 添加解析规则
     * @param accountUser 用户信息，从登陆态获取
     * @param req 添加资金账户请求
     * @return 新增的ID
     */
    Long addAnalyzeRule(AccountUser accountUser, AddAnalyzeRuleReq req);



    /**
     * 编辑解析规则
     * @param accountUser 用户信息，从登陆态获取
     * @param req 编辑资金账户请求
     */
    void editAnalyzeRule(AccountUser accountUser, EditAnalyzeRuleReq req);



    /**
     * 删除解析规则
     * @param accountUser 用户信息，从登陆态获取
     * @param req 删除资金账户请求
     */
    void deleteAnalyzeRule(AccountUser accountUser, DeleteAnalyzeRuleReq req);


    /**
     * 分页查询解析规则
     * @param accountUser 用户信息，从登陆态获取
     * @param req 查询资金账户请求
     * @return 分页列表
     */
    PageListBase<CategoryAnalyzeRuleVO> pageQueryAnalyzeRule(AccountUser accountUser, QueryAnalyzeRuleReq req);

    /**
     * 重新解析账单
     * @param accountUser 用户信息，从登陆态获取
     * @param req 重新解析账单请求
     * @return 重新解析账单结果
     */
    Long againAnalyze(AccountUser accountUser, AgainAnalyzeReq req);



    /**
     * 查询账单重新解析任务列表
     * @param accountUser 用户信息，从登陆态获取
     * @param req 查询账单重新解析任务列表请求
     * @return 账单重新解析任务列表
     */
    List<BillAgainAnalyzeTaskVO> listBillAgainAnalyzeTask(AccountUser accountUser, QueryBillAgainAnalyzeTaskReq req);

    /**
     * 初始化解析规则
     */
    void initAnalyzeRule(Long companyId,Long fundAccountId);


    /**
     * 查询匹配字段树
     * @param accountUser 用户信息，从登陆态获取
     * @param req 查询匹配字段树请求
     * @return 匹配字段树
     */
    List<TreeVO> treeMatchFieldTree(AccountUser accountUser, TreeMatchFieldReq req);


    /**
     * 查询解析规则操作符树
     * @param accountUser 用户信息，从登陆态获取
     * @return 解析规则操作符树
     */
    List<TreeVO> treeAnalyzeRuleOperator(AccountUser accountUser);
}
