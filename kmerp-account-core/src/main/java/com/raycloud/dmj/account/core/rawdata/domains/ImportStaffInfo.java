package com.raycloud.dmj.account.core.rawdata.domains;

import lombok.Data;

@Data
public class ImportStaffInfo {

    /**
     * company_id
     */
    private Long companyId;
    /**
     * 当前用户id
     */
    private Long staffId;
    /**
     * user_id 当前店铺id
     */
    private Long shopUniId;
    /**
     * file_path
     */
    private String zipLocalFilePath;
    /**
     * zip_file_path
     */
    private String unzipFolderPath;
    /**
     * oss_url
     */
    private String ossUrl;
    /**
     * 是否是本地文件的标识，true是本地文件
     */
    private Boolean LocalFileFlag;
    /**
     * 文件类型
     */
    private String fileType;

    /**
     * 任务id
     */
    private Long recordId;


}
