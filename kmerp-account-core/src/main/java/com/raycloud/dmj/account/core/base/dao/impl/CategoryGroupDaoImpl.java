package com.raycloud.dmj.account.core.base.dao.impl;


import com.alibaba.fastjson2.JSON;
import com.raycloud.dmj.account.core.base.dao.CategoryGroupDao;
import com.raycloud.dmj.account.core.cleancategory.domain.object.CategoryGroupDO;
import com.raycloud.dmj.account.core.enums.field.CategoryGroupFieldEnum;
import com.raycloud.dmj.account.infra.common.BusinessException;
import com.raycloud.dmj.account.infra.common.ErrorCodeEnum;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import com.raycloud.dmj.table.api.plus.common.ColumnValues;
import com.raycloud.dmj.table.api.plus.common.base.SQL;
import com.raycloud.dmj.table.api.plus.common.enums.LinkMode;
import com.raycloud.dmj.table.api.plus.component.column.utils.Columns;
import com.raycloud.dmj.table.api.plus.component.condition.utils.Conditions;
import com.raycloud.dmj.table.api.plus.insert.Inserts;
import com.raycloud.dmj.table.api.plus.query.Queries;
import com.raycloud.dmj.table.api.plus.update.Updates;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */

@Repository
@Slf4j
public class CategoryGroupDaoImpl extends BaseDao implements CategoryGroupDao {


    private final String TABLE_NAME = "category_group";


    @Override
    public Long insert(CategoryGroupDO categoryGroupDO) {
        SQL sql = Inserts.insert()
                .into(TABLE_NAME)
                .columns(CategoryGroupFieldEnum.getInsertFields())
                .valueForEntity(categoryGroupDO)
                .columnNameCamelToUnderline()
                .toSql();
        return insertReturnPrimaryKey(sql);

    }

    @Override
    public List<CategoryGroupDO> queryByCodes(Long companyId, Set<String> codes) {

        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(CategoryGroupFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId),
                        Conditions.and(Columns.toColumn(CategoryGroupFieldEnum.CODE.getFieldCode()), LinkMode.IN, codes)
                )
                .select(
                )
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<CategoryGroupDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(CategoryGroupDO.class), args);
        return !query.isEmpty() ? query : null;
    }

    @Override
    public void updateByCode(CategoryGroupDO categoryGroupDO) {
        AsserUtils.notNull(categoryGroupDO, "参数不能为空！");
        AsserUtils.notNull(categoryGroupDO.getCompanyId(), "公司ID不能为空！");
        AsserUtils.notNull(categoryGroupDO.getCode(), "分类不能为空！");
        // 2. 生成SQL
        SQL sql = Updates.create()
                .table(TABLE_NAME)
                .where(
                        Conditions.and(CategoryGroupFieldEnum.CODE.getFieldCode(), LinkMode.EQUAL, categoryGroupDO.getCode()),
                        Conditions.and(CategoryGroupFieldEnum.COMPANY_ID.getFieldCode(), LinkMode.EQUAL, categoryGroupDO.getCompanyId())
                )
                .update(
                        ColumnValues.create(CategoryGroupFieldEnum.MODIFIED.getFieldCode(), new Date()),
                        ColumnValues.create(CategoryGroupFieldEnum.NAME.getFieldCode(), categoryGroupDO.getName())

                ).toSql();
        Object[] updateArgs = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        // 3. 执行更新操作
        int row = jdbcTemplate.update(sql.getSqlCode(), updateArgs);
        if (row <= 0){
            log.error("|CategoryGroupDaoImpl.updateByCode error|更新数据库失败！sql={},updateArgs={}",sql.getSqlCode(), JSON.toJSONString(sql.getArgs()));
            throw new BusinessException(ErrorCodeEnum.DB_ERROR.getCode(),"数据不存在！");
        }
    }

    @Override
    public List<CategoryGroupDO> list(Long companyId) {
        AsserUtils.notNull(companyId, "公司ID不能为空！");
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(CategoryGroupFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId)
                )
                .select(
                )
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<CategoryGroupDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(CategoryGroupDO.class), args);
        return !query.isEmpty() ? query : null;
    }
}
