package com.raycloud.dmj.account.core.common.utils;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.taobao.api.internal.util.NamedThreadFactory;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;

import java.util.concurrent.*;

/**
 * <AUTHOR>
 */
public class ThreadPoolUtil {


    /**
     * 分类清洗线程池
     * @return ThreadPoolExecutor
     */
    public static ThreadPoolExecutor getCategoryAnalyzeThreadPool(){

        return new ThreadPoolExecutor(5,
                10,
                5,
                TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(5,true),
                new CustomizableThreadFactory("CategoryAnalyzeThreadPool-"),
                new ThreadPoolExecutor.AbortPolicy());
    }
}