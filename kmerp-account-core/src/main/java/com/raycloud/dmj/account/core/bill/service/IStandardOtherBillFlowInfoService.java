package com.raycloud.dmj.account.core.bill.service;

import com.raycloud.dmj.account.core.bill.request.AddStandardOtherBillFlowInfoRequest;
import com.raycloud.dmj.account.core.bill.request.StandardOtherBillFlowRequest;
import com.raycloud.dmj.account.core.bill.vo.StandardOtherBillFlowInfoVO;
import com.raycloud.dmj.account.core.common.PageInfo;
import com.raycloud.dmj.account.infra.session.AccountUser;
import com.raycloud.dmj.data.chessboard.model.PushResult;
import com.raycloud.dmj.domain.account.Staff;

import java.util.List;

/**
 * 平台其他收支流水信息服务接口
 */
public interface IStandardOtherBillFlowInfoService {

    /**
     * 新增平台其他收支流水信息
     * @param accountUser 当前用户
     * @param request 流水信息
     * @return 新增的ID
     */
    Long addStandardOtherBillFlowInfo(AccountUser accountUser, AddStandardOtherBillFlowInfoRequest request);

    /**
     * 分页查询平台其他收支流水信息列表
     * @param accountUser 当前用户
     * @param request 查询条件
     * @return 分页结果
     */
    List<StandardOtherBillFlowInfoVO> getStandardOtherBillFlowInfoList(AccountUser accountUser, StandardOtherBillFlowRequest request);

    /**
     * 根据ID查询平台其他收支流水信息
     * @param accountUser 当前用户
     * @param id 主键ID
     * @return 流水信息
     */
    StandardOtherBillFlowInfoVO getStandardOtherBillFlowInfoById(AccountUser accountUser, Long id);

    /**
     * 获取分页汇总信息
     * @param accountUser 当前用户
     * @param request 查询条件
     * @return 分页汇总信息
     */
    PageInfo<Object> getPageInfo(AccountUser accountUser, StandardOtherBillFlowRequest request);

    /**
     * 导出平台其他收支流水信息
     * @param staff 当前用户
     * @param request 查询条件
     * @return 导出任务
     */
    PushResult export(Staff staff, StandardOtherBillFlowRequest request);
}
