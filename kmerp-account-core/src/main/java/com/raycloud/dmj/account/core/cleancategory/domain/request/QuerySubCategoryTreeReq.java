package com.raycloud.dmj.account.core.cleancategory.domain.request;

import lombok.Data;

import java.io.Serializable;


/**
 * 查询
 * <AUTHOR>
 */
@Data
public class QuerySubCategoryTreeReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 平台code
     */
    private String platformCode;

    /**
     * 分类组code
     */
    private String categoryGroupCode;

    /**
     * 类别id
     */
    private Long categoryId;




}
