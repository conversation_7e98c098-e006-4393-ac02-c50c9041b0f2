package com.raycloud.dmj.account.core.enums.field;

import lombok.Getter;

/**
 * 支付宝原始数据字段枚举
 * <AUTHOR>
 */
@Getter
public enum TmallAlipayRawBillFieldEnum {

    ID("id", "主键ID"),
    ACCOUNT_FLOW_NO("account_flow_no", "账务流水号"),
    BIZ_FLOW_NO("biz_flow_no", "业务流水号"),
    MERCHANT_ORDER_NO("merchant_order_no", "商户订单号"),
    PRODUCT_NAME("product_name", "商品名称"),
    OCCUR_TIME("occur_time", "发生时间"),
    COUNTERPARTY_ACCOUNT("counterparty_account", "对方账号"),
    INCOME_AMOUNT("income_amount", "收入金额(+元)"),
    EXPENSE_AMOUNT("expense_amount", "支出金额(-元)"),
    ACCOUNT_BALANCE("account_balance", "账户余额(元)"),
    TRANSACTION_CHANNEL("transaction_channel", "交易渠道"),
    BIZ_TYPE("biz_type", "业务类型"),
    REMARK("remark", "备注"),
    BIZ_DESCRIPTION("biz_description", "业务描述"),
    BIZ_BILL_SOURCE("biz_bill_source", "业务账单来源"),
    BIZ_BASE_ORDER_NO("biz_base_order_no", "业务基础订单号"),
    BIZ_ORDER_NO("biz_order_no", "业务订单号"),
    DOWNLOAD_TIME("download_time", "下载时间"),
    DOWNLOAD_ACCOUNT("download_account", "下载账户"),
    BIZ_KEY("biz_key", "唯一键"),
    BATCH_NO("batch_no", "批次号"),
    BATCH_TIME("batch_time", "批次时间"),
    COMPANY_ID("company_id", "公司ID"),
    SHOP_ID("shop_id", "店铺ID"),
    CREATED("created", "创建时间");

    /**
     * 字段编码
     */
    private final String fieldCode;

    /**
     * 字段描述
     */
    private final String fieldDesc;

    TmallAlipayRawBillFieldEnum(String fieldCode, String fieldDesc) {
        this.fieldCode = fieldCode;
        this.fieldDesc = fieldDesc;
    }
}    