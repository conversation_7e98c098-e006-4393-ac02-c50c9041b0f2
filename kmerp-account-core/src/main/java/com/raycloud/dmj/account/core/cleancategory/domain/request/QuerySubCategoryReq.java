package com.raycloud.dmj.account.core.cleancategory.domain.request;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class QuerySubCategoryReq implements Serializable {

    /**
     * 平台code
     */
    private List<String> platformCodeList;

    /**
     * 资金账户ID集合
     */
    private List<Long> accountIdList;

    /**
     * 分类ID集合
     */
    private List<Long> categoryIdList;
}
