package com.raycloud.dmj.account.core.base.dao.impl;


import com.alibaba.fastjson2.JSON;
import com.mysql.jdbc.Statement;
import com.raycloud.dmj.account.core.base.dao.FundAccountDao;
import com.raycloud.dmj.account.core.cleancategory.domain.object.FundAccountDO;
import com.raycloud.dmj.account.core.enums.field.FundAccountFieldEnum;
import com.raycloud.dmj.account.core.enums.field.ShopInfoFieldEnum;
import com.raycloud.dmj.account.infra.common.BusinessException;
import com.raycloud.dmj.account.infra.common.ErrorCodeEnum;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import com.raycloud.dmj.table.api.plus.common.ColumnValues;
import com.raycloud.dmj.table.api.plus.common.base.SQL;
import com.raycloud.dmj.table.api.plus.common.enums.LinkMode;
import com.raycloud.dmj.table.api.plus.component.column.utils.Columns;
import com.raycloud.dmj.table.api.plus.component.condition.utils.Conditions;
import com.raycloud.dmj.table.api.plus.insert.Inserts;
import com.raycloud.dmj.table.api.plus.query.Queries;
import com.raycloud.dmj.table.api.plus.update.Updates;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 资金账户Dao实现
 * <AUTHOR>
 */
@Repository
@Slf4j
public class FundAccountDaoImpl extends BaseDao implements FundAccountDao {


    private final String TABLE_NAME = "fund_account";
    private final String SHOP_TABLE_NAME = "shop_info";


    @Override
    public Long insert(FundAccountDO fundAccountDO) {
        AsserUtils.notNull(fundAccountDO, "参数不能为空！");
        SQL sql = Inserts.insert()
                .into(TABLE_NAME)
                .columns(FundAccountFieldEnum.getInsertFields())
                .valueForEntity(fundAccountDO)
                .columnNameCamelToUnderline()
                .toSql();

        // 创建KeyHolder用于获取生成的主键
        KeyHolder keyHolder = new GeneratedKeyHolder();

        // 使用带KeyHolder的update方法
        jdbcTemplate.update(
                connection -> {
                    PreparedStatement ps = connection.prepareStatement(
                            sql.getSqlCode(),
                            Statement.RETURN_GENERATED_KEYS
                    );
                    // 设置参数
                    Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
                    for (int i = 0; i < args.length; i++) {
                        ps.setObject(i + 1, args[i]);
                    }
                    return ps;
                },
                keyHolder
        );

        // 获取生成的主键值
        return keyHolder.getKey().longValue();
    }

    @Override
    public FundAccountDO queryByAccountCode(Long companyId,String accountCode) {
        AsserUtils.notNull(companyId, "公司ID不能为空！");
        AsserUtils.notEmpty(accountCode, "accountCode不能为空");
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(FundAccountFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId),
                        Conditions.and(Columns.toColumn(FundAccountFieldEnum.ACCOUNT_CODE.getFieldCode()), LinkMode.EQUAL, accountCode)
                )
                .select(
                )
                .limit(1)
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<FundAccountDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(FundAccountDO.class), args);
        return !query.isEmpty() ? query.get(0) : null;
    }

    @Override
    public void updateById(FundAccountDO fundAccountDO) {
        AsserUtils.notNull(fundAccountDO, "参数不能为空！");
        AsserUtils.notNull(fundAccountDO.getId(), "ID不能为空！");

        // 2. 生成SQL
        SQL sql = Updates.create()
                .table(TABLE_NAME)
                .where(
                        Conditions.and(FundAccountFieldEnum.ID.getFieldCode(), LinkMode.EQUAL, fundAccountDO.getId())
                )
                .update(
                        ColumnValues.create(FundAccountFieldEnum.MODIFIED.getFieldCode(), new Date()),
                        ColumnValues.create(FundAccountFieldEnum.SHOP_ID.getFieldCode()
                                , fundAccountDO.getShopId()),
                        ColumnValues.create(FundAccountFieldEnum.ACCOUNT_NAME.getFieldCode()
                                , fundAccountDO.getAccountName()),
                        ColumnValues.create(FundAccountFieldEnum.TYPE.getFieldCode()
                                , fundAccountDO.getType()),
                        ColumnValues.create(FundAccountFieldEnum.START_BALANCE.getFieldCode()
                                , fundAccountDO.getStartBalance()),
                        ColumnValues.create(FundAccountFieldEnum.START_DATE.getFieldCode()
                                , fundAccountDO.getStartDate())
                ).toSql();
        Object[] updateArgs = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        // 3. 执行更新操作
        int row = jdbcTemplate.update(sql.getSqlCode(), updateArgs);
        if (row <= 0){
            log.error("|FundAccountDaoImpl.updateById|更新数据库失败！sql={},updateArgs={}",sql.getSqlCode(), JSON.toJSONString(sql.getArgs()));
            throw new BusinessException(ErrorCodeEnum.DB_ERROR.getCode(),"该数据不存在！");
        }
    }

    @Override
    public List<FundAccountDO> queryByCompanyId(Long companyId) {
        AsserUtils.notNull(companyId, "公司ID不能为空！");
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(FundAccountFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId)
                )
                .select(

                )
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<FundAccountDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(FundAccountDO.class), args);
        return !query.isEmpty() ? query : null;
    }

    @Override
    public List<FundAccountDO> queryByIds(Long companyId, Set<Long> ids) {
        AsserUtils.notNull(companyId, "公司ID不能为空！");
        AsserUtils.notEmpty(ids, "ID不能为空！");
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(FundAccountFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId),
                        Conditions.and(Columns.toColumn(FundAccountFieldEnum.ID.getFieldCode()), LinkMode.IN, ids)
                )
                .select(
                )
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<FundAccountDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(FundAccountDO.class), args);
        return !query.isEmpty() ? query : null;
    }

    @Override
    public FundAccountDO queryById(Long companyId, Long id) {
        AsserUtils.notNull(companyId, "公司ID不能为空！");
        AsserUtils.notNull(id, "ID不能为空！");
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(FundAccountFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId),
                        Conditions.and(Columns.toColumn(FundAccountFieldEnum.ID.getFieldCode()), LinkMode.EQUAL, id)
                )
                .select(
                )
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<FundAccountDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(FundAccountDO.class), args);
        return !query.isEmpty() ? query.get(0) : null;
    }

    @Override
    public FundAccountDO getByShopIdAndType(Long companyId, Long shopId, Integer type) {
        AsserUtils.notNull(companyId, "公司ID不能为空！");
        AsserUtils.notNull(type, "类型不能为空！");
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(FundAccountFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId),
                        Conditions.and(Columns.toColumn(FundAccountFieldEnum.SHOP_ID.getFieldCode()), LinkMode.EQUAL, shopId),
                        Conditions.and(Columns.toColumn(FundAccountFieldEnum.TYPE.getFieldCode()), LinkMode.EQUAL, type)
                )
                .select(
                )
                .limit(1)
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<FundAccountDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(FundAccountDO.class), args);
        return !query.isEmpty() ? query.get(0) : null;

    }

    @Override
    public void updateAuthorizeById(Integer authorize, Long id) {
        AsserUtils.notNull(authorize, "授权状态不能为空！");
        AsserUtils.notNull(id, "ID不能为空！");

        // 2. 生成SQL
        SQL sql = Updates.create()
                .table(TABLE_NAME)
                .where(
                        Conditions.and(FundAccountFieldEnum.ID.getFieldCode(), LinkMode.EQUAL, id)
                )
                .update(
                        ColumnValues.create(FundAccountFieldEnum.MODIFIED.getFieldCode(), new Date()),
                        ColumnValues.create(FundAccountFieldEnum.AUTHORIZE.getFieldCode()
                                , authorize)
                ).toSql();
        Object[] updateArgs = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        // 3. 执行更新操作
        int row = jdbcTemplate.update(sql.getSqlCode(), updateArgs);
        if (row <= 0){
            log.error("|FundAccountDaoImpl.updateAuthorizeById|更新数据库失败！sql={},updateArgs={}",sql.getSqlCode(), JSON.toJSONString(sql.getArgs()));
            throw new BusinessException(ErrorCodeEnum.DB_ERROR.getCode(),"该数据不存在！");
        }

    }

    @Override
    public void updateConfirmStartPeriodById(Integer confirmStartPeriod, Long id) {

        AsserUtils.notNull(confirmStartPeriod, "确认状态不能为空！");
        AsserUtils.notNull(id, "ID不能为空！");

        // 2. 生成SQL
        SQL sql = Updates.create()
                .table(TABLE_NAME)
                .where(
                        Conditions.and(FundAccountFieldEnum.ID.getFieldCode(), LinkMode.EQUAL, id)
                )
                .update(
                        ColumnValues.create(FundAccountFieldEnum.MODIFIED.getFieldCode(), new Date()),
                        ColumnValues.create(FundAccountFieldEnum.CONFIRM_START_PERIOD.getFieldCode()
                                , confirmStartPeriod)
                ).toSql();
        Object[] updateArgs = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        // 3. 执行更新操作
        int row = jdbcTemplate.update(sql.getSqlCode(), updateArgs);
        if (row <= 0){
            log.error("|FundAccountDaoImpl.updateConfirmStartPeriodById|更新数据库失败！sql={},updateArgs={}",sql.getSqlCode(), JSON.toJSONString(sql.getArgs()));
            throw new BusinessException(ErrorCodeEnum.DB_ERROR.getCode(),"该数据不存在！");
        }

    }

    @Override
    public List<FundAccountDO> queryByShopIdList(List<Long> shopIdList, Long companyId) {
        AsserUtils.notNull(companyId, "公司ID不能为空！");
        AsserUtils.notEmpty(shopIdList, "店铺ID列表不能为空！");

        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(FundAccountFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId),
                        Conditions.and(Columns.toColumn(FundAccountFieldEnum.SHOP_ID.getFieldCode()), LinkMode.IN, shopIdList)
                )
                .select()
                .toSql();

        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<FundAccountDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(FundAccountDO.class), args);
        return !query.isEmpty() ? query : null;
    }

    @Override
    public List<FundAccountDO> queryByPlatformCode(String platformCode, Long companyId) {
        AsserUtils.notNull(companyId, "公司ID不能为空！");
        AsserUtils.notEmpty(platformCode, "平台code不能为空！");
        SQL sql = Queries.create()
                .from(TABLE_NAME,"f")
                .join(SHOP_TABLE_NAME,"s")
                .on(
                        Conditions.and(
                        Columns.create(FundAccountFieldEnum.SHOP_ID.getFieldCode()).referenceTableAlias("f"),
                        LinkMode.EQUAL,
                        Columns.create(ShopInfoFieldEnum.ID.getFieldCode()).referenceTableAlias("s")
                ))
                .where(
                        Conditions.and(Columns.toColumn(FundAccountFieldEnum.COMPANY_ID.getFieldCode()).referenceTableAlias("f"), LinkMode.EQUAL, companyId),
                        Conditions.and(Columns.toColumn(ShopInfoFieldEnum.PLATFORM_CODE.getFieldCode()).referenceTableAlias("s"), LinkMode.EQUAL, platformCode)
                )
                .select("f.*")
                .toSql();

        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<FundAccountDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(FundAccountDO.class), args);
        return !query.isEmpty() ? query : null;
    }

    @Override
    public FundAccountDO queryByAccountName(Long companyId, String accountName) {

        AsserUtils.notNull(companyId, "公司ID不能为空！");
        AsserUtils.notEmpty(accountName, "账户名称不能为空");
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(FundAccountFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId),
                        Conditions.and(Columns.toColumn(FundAccountFieldEnum.ACCOUNT_NAME.getFieldCode()), LinkMode.EQUAL, accountName)
                )
                .select(
                )
                .limit(1)
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<FundAccountDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(FundAccountDO.class), args);
        return !query.isEmpty() ? query.get(0) : null;

    }
}
