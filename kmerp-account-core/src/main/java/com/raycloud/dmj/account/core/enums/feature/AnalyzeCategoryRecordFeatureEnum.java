package com.raycloud.dmj.account.core.enums.feature;

import lombok.Getter;

/**
 * <AUTHOR>
 * Date:  2025/7/31
 */
@Getter
public enum AnalyzeCategoryRecordFeatureEnum {

    /**
     * 异常消息
     */
    ERROR_MESSAGE("errorMsg"),
    ;


    /**
     * 字段编码
     */
    private final String fieldCode;

    AnalyzeCategoryRecordFeatureEnum(String fileType) {
        this.fieldCode = fileType;
    }


}
