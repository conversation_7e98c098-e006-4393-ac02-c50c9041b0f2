package com.raycloud.dmj.account.core.base.domain;

import com.raycloud.dmj.account.core.common.BaseInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 平台其他收支流水信息表实体类
 * 对应表：erp_account_system.standard_other_bill_flow_info
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StandardOtherBillFlowInfoDO extends BaseInfo {

    private Long id;

    /**
     * 平台类型
     */
    private String platformCode;

    /**
     * 店铺ID
     */
    private Long shopId;


    /**
     * 分类code
     */
    private String categoryGroupCode;

    /**
     * 类别Code
     */
    private String categoryCode;

    /**
     * 流水子类别code
     */
    private String subCategoryCode;

    /**
     * 平台账期，格式：年月，例如202501
     */
    private Integer billingCycle;

    /**
     * 发生时间
     */
    private Date occurredAt;

    /**
     * 收支方向 1 收入 2 支出
     */
    private Integer incomeExpenseDirection;

    /**
     * 金额(元)（正数收入，负数支出）
     * 数据库字段类型：decimal(20,6)
     */
    private BigDecimal amount;

    /**
     * 关联订单号
     */
    private String orderNo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 收支对象ID
     */
    private Long counterpartyId;


    /**
     * 关联业务单据号，默认值-1
     */
    private String docNo;

    /**
     * 来源方式(1 账单,2 系统)，默认值1
     */
    private Integer source;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 唯一键（有唯一约束）
     */
    private String bizKey;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 账期
     */
    private Integer dataRange;

}
