package com.raycloud.dmj.account.core.base.dao.impl;

import com.alibaba.fastjson2.JSON;
import com.raycloud.dmj.account.core.base.dao.FileOriginalDataMonitorDao;
import com.raycloud.dmj.account.core.enums.MonitorStatusEnum;
import com.raycloud.dmj.account.core.enums.field.FileOriginalDataMonitorEnum;
import com.raycloud.dmj.account.core.rawdata.domains.FileOriginalDataMonitorDO;
import com.raycloud.dmj.account.infra.common.BusinessException;
import com.raycloud.dmj.account.infra.common.ErrorCodeEnum;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import com.raycloud.dmj.table.api.plus.common.ColumnValue;
import com.raycloud.dmj.table.api.plus.common.ColumnValues;
import com.raycloud.dmj.table.api.plus.common.base.SQL;
import com.raycloud.dmj.table.api.plus.common.enums.LinkMode;
import com.raycloud.dmj.table.api.plus.component.column.utils.Columns;
import com.raycloud.dmj.table.api.plus.component.condition.utils.Conditions;
import com.raycloud.dmj.table.api.plus.insert.Inserts;
import com.raycloud.dmj.table.api.plus.query.Queries;
import com.raycloud.dmj.table.api.plus.update.Updates;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.*;

/**
 * 文件原始数据监控表
 *
 * <AUTHOR>
 */

@Slf4j
@Repository
public class FileOriginalDataMonitorDaoImpl extends BaseDao implements FileOriginalDataMonitorDao {

    private final String TABLE_NAME = "file_original_data_monitor";


    @Override
    public FileOriginalDataMonitorDO getById(Long id) {
        AsserUtils.notNull(id, "ID不能为空！");
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(FileOriginalDataMonitorEnum.ID.getFieldCode()), LinkMode.EQUAL, id)
                )
                .select(
                        FileOriginalDataMonitorEnum.getSelectFields()
                )
                .limit(1)
                .toSql();
                Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
                List<FileOriginalDataMonitorDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(FileOriginalDataMonitorDO.class), args);
                return !query.isEmpty() ? query.get(0) : null;
    }

    @Override
    public FileOriginalDataMonitorDO getByShopAndSourceAndBatch(Long companyId, Long shopId, Integer dataSource,String tableName, String batchCode) {
        AsserUtils.notNull(companyId, "公司ID不能为空！");
        AsserUtils.notNull(batchCode, "批次号不能为空！");
        AsserUtils.notNull(shopId, "店铺ID不能为空！");
        AsserUtils.notNull(dataSource, "数据源不能为空！");
        AsserUtils.notEmpty(tableName, "表名不能为空！");
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(FileOriginalDataMonitorEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId),
                        Conditions.and(Columns.toColumn(FileOriginalDataMonitorEnum.SHOP_ID.getFieldCode()), LinkMode.EQUAL, shopId),
                        Conditions.and(Columns.toColumn(FileOriginalDataMonitorEnum.DATA_SOURCE.getFieldCode()), LinkMode.EQUAL, dataSource),
                        Conditions.and(Columns.toColumn(FileOriginalDataMonitorEnum.BATCH_CODE.getFieldCode()), LinkMode.EQUAL, batchCode),
                        Conditions.and(Columns.toColumn(FileOriginalDataMonitorEnum.TABLE_NAME.getFieldCode()), LinkMode.EQUAL, tableName)
                )
                .select(
                        FileOriginalDataMonitorEnum.getSelectFields()
                )
                .limit(1)
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<FileOriginalDataMonitorDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(FileOriginalDataMonitorDO.class), args);
        return !query.isEmpty() ? query.get(0) : null;
    }

    @Override
    public List<FileOriginalDataMonitorDO> getBySourceAndDataRange(Long companyId, Long shopId,Integer source, Integer dataRange) {
        AsserUtils.notNull(companyId, "公司ID不能为空！");
        AsserUtils.notNull(source, "数据源不能为空！");
        AsserUtils.notNull(dataRange, "数据范围不能为空！");
        AsserUtils.notNull(shopId, "店铺ID不能为空！");
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(FileOriginalDataMonitorEnum.DATA_SOURCE.getFieldCode()), LinkMode.EQUAL, source),
                        Conditions.and(Columns.toColumn(FileOriginalDataMonitorEnum.DATA_RANGE.getFieldCode()), LinkMode.EQUAL, dataRange),
                        Conditions.and(Columns.toColumn(FileOriginalDataMonitorEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId),
                        Conditions.and(Columns.toColumn(FileOriginalDataMonitorEnum.SHOP_ID.getFieldCode()), LinkMode.EQUAL, shopId)

                ).select(
                )
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<FileOriginalDataMonitorDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(FileOriginalDataMonitorDO.class), args);
        return !query.isEmpty() ? query : null;
    }


    @Override
    public void updateById(FileOriginalDataMonitorDO fileOriginalDataMonitorDO) {
        AsserUtils.notNull(fileOriginalDataMonitorDO, "参数不能为空！");
        AsserUtils.notNull(fileOriginalDataMonitorDO.getId(), "ID不能为空！");
        AsserUtils.notNull(fileOriginalDataMonitorDO.getVersion(), "版本号不能为空！");
        //生成动态修改列
        ArrayList<ColumnValue> columnValues = getColumnValues(fileOriginalDataMonitorDO);

        // 2. 生成SQL
        SQL sql = Updates.create()
                .table(TABLE_NAME)
                .where(
                        Conditions.and(FileOriginalDataMonitorEnum.VERSION.getFieldCode(), LinkMode.EQUAL, fileOriginalDataMonitorDO.getVersion()),
                        Conditions.and(FileOriginalDataMonitorEnum.ID.getFieldCode(), LinkMode.EQUAL, fileOriginalDataMonitorDO.getId())
                )
                .update(
                        columnValues
                ).toSql();
        Object[] updateArgs = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        // 3. 执行更新操作
        int row = jdbcTemplate.update(sql.getSqlCode(), updateArgs);
        if (row <= 0){
            log.error("|FileOriginalDataMonitorDao.updateById error|更新数据库失败！sql={},updateArgs={}",sql.getSqlCode(), JSON.toJSONString(sql.getArgs()));
            throw new BusinessException(ErrorCodeEnum.DB_ERROR);
        }

    }

    /**
     * 获取动态修改列
     * @param fileOriginalDataMonitorDO 文件原始数据监控表
     * @return 动态修改列
     */
    private static ArrayList<ColumnValue> getColumnValues(FileOriginalDataMonitorDO fileOriginalDataMonitorDO) {
        ArrayList<ColumnValue> columnValues = new ArrayList<>();
        // 添加版本号
        columnValues.add(ColumnValues.create(FileOriginalDataMonitorEnum.VERSION.getFieldCode(), Columns.create(FileOriginalDataMonitorEnum.VERSION.getFieldCode() + "+1")));
        //  添加更新时间
        columnValues.add(ColumnValues.create(FileOriginalDataMonitorEnum.UPDATED.getFieldCode(), new Date()));
        // 添加数据状态
        if (Objects.nonNull(fileOriginalDataMonitorDO.getDataStatus())){
            columnValues.add(ColumnValues.create(FileOriginalDataMonitorEnum.DATA_STATUS.getFieldCode()
                    , fileOriginalDataMonitorDO.getDataStatus()));
        }
        //添加批次号
        if (Objects.nonNull(fileOriginalDataMonitorDO.getBatchCode())){
            columnValues.add(ColumnValues.create(FileOriginalDataMonitorEnum.BATCH_CODE.getFieldCode()
                    , fileOriginalDataMonitorDO.getBatchCode()));
        }

        //添加店铺ID
        if (Objects.nonNull(fileOriginalDataMonitorDO.getShopId())){
            columnValues.add(ColumnValues.create(FileOriginalDataMonitorEnum.SHOP_ID.getFieldCode()
                    , fileOriginalDataMonitorDO.getShopId()));
        }

        //添加表名
        if (Objects.nonNull(fileOriginalDataMonitorDO.getTableName())){
            columnValues.add(ColumnValues.create(FileOriginalDataMonitorEnum.TABLE_NAME.getFieldCode()
                    , fileOriginalDataMonitorDO.getTableName()));
        }
        //时间类型
        if (Objects.nonNull(fileOriginalDataMonitorDO.getDateType())){
            columnValues.add(ColumnValues.create(FileOriginalDataMonitorEnum.DATE_TYPE.getFieldCode()
                    , fileOriginalDataMonitorDO.getDateType()));
        }

        //数据状态
        if (Objects.nonNull(fileOriginalDataMonitorDO.getDataStatus())){
            columnValues.add(ColumnValues.create(FileOriginalDataMonitorEnum.DATA_STATUS.getFieldCode()
            , fileOriginalDataMonitorDO.getDataStatus()));
        }

        // 最后配置ID
        if (Objects.nonNull(fileOriginalDataMonitorDO.getLastConfigId())){
            columnValues.add(ColumnValues.create(FileOriginalDataMonitorEnum.LAST_CONFIG_ID.getFieldCode()
                    , fileOriginalDataMonitorDO.getLastConfigId()));
        }
        //文件解析ID
        if (Objects.nonNull(fileOriginalDataMonitorDO.getLastRecordId())){
            columnValues.add(ColumnValues.create(FileOriginalDataMonitorEnum.LAST_RECORD_ID.getFieldCode()
                    , fileOriginalDataMonitorDO.getLastRecordId()));
        }
        //数据的日期
        if (Objects.nonNull(fileOriginalDataMonitorDO.getDataRange())){
            columnValues.add(ColumnValues.create(FileOriginalDataMonitorEnum.DATA_RANGE.getFieldCode()
                    , fileOriginalDataMonitorDO.getDataRange()));
        }
        return columnValues;
    }


    @Override
    public void insert(FileOriginalDataMonitorDO fileOriginalDataMonitorDO) {

        SQL sql = Inserts.insert()
                .into(TABLE_NAME)
                .columns(FileOriginalDataMonitorEnum.getInsertFields())
                .valueForEntity(fileOriginalDataMonitorDO)
                .columnNameCamelToUnderline()
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        jdbcTemplate.update(sql.getSqlCode(), args);
    }


    @Override
    public void batchUpdateStatusByBatchCode(Long companyId,Long shopId,Set<String> batchCodes, Integer monitorStatus) {
        AsserUtils.notEmpty(batchCodes, "batchCode不能为空");
        AsserUtils.notNull(monitorStatus, "batchCode不能为空");
        SQL sql = Updates.create()
                .table(TABLE_NAME)
                .where(
                        Conditions.and(FileOriginalDataMonitorEnum.COMPANY_ID.getFieldCode(), LinkMode.EQUAL, companyId),
                        Conditions.and(FileOriginalDataMonitorEnum.SHOP_ID.getFieldCode(), LinkMode.EQUAL, shopId),
                        Conditions.and(FileOriginalDataMonitorEnum.BATCH_CODE.getFieldCode(), LinkMode.IN, batchCodes)
                        , Conditions.and(FileOriginalDataMonitorEnum.DATA_STATUS.getFieldCode(), LinkMode.EQUAL, MonitorStatusEnum.IMPORTING.getStatus())
                ).update(
                        ColumnValues.create(FileOriginalDataMonitorEnum.DATA_STATUS.getFieldCode(), monitorStatus)
                        , ColumnValues.create(FileOriginalDataMonitorEnum.UPDATED.getFieldCode(), new Date())
                ).toSql();

        String sqlCode = sql.getSqlCode();

        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        // 执行批量更新
        jdbcTemplate.update(sqlCode, args);

    }


    @Override
    public void batchUpdateStatusByLastRecordIds(Set<Long> lastRecordIds, Integer monitorStatus) {
        AsserUtils.notEmpty(lastRecordIds, "lastRecordIds不能为空");
        AsserUtils.notNull(monitorStatus, "monitorStatus不能为空");
        SQL sql = Updates.create()
                .table(TABLE_NAME)
                .where(
                        Conditions.and(FileOriginalDataMonitorEnum.LAST_RECORD_ID.getFieldCode(), LinkMode.IN, lastRecordIds)
                        , Conditions.and(FileOriginalDataMonitorEnum.DATA_STATUS.getFieldCode(), LinkMode.EQUAL, MonitorStatusEnum.IMPORTING.getStatus())
                ).update(
                        ColumnValues.create(FileOriginalDataMonitorEnum.DATA_STATUS.getFieldCode(), monitorStatus)
                        , ColumnValues.create(FileOriginalDataMonitorEnum.UPDATED.getFieldCode(), new Date())
                ).toSql();

        String sqlCode = sql.getSqlCode();

        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        // 执行批量更新
        jdbcTemplate.update(sqlCode, args);

    }

    @Override
    public List<FileOriginalDataMonitorDO> listByLastRecordIds(Long companyId, Set<Long> lastRecordIds) {

        AsserUtils.notNull(companyId, "公司ID不能为空！");
        AsserUtils.notEmpty(lastRecordIds, "lastRecordIds不能为空");
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(FileOriginalDataMonitorEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId),
                        Conditions.and(Columns.toColumn(FileOriginalDataMonitorEnum.LAST_RECORD_ID.getFieldCode()), LinkMode.IN, lastRecordIds)
                )
                .select(
                )
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<FileOriginalDataMonitorDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(FileOriginalDataMonitorDO.class), args);
        return !query.isEmpty() ? query : null;

    }

    @Override
    public List<FileOriginalDataMonitorDO> listByShopAndSourceAndBatch(Long companyId, Long shopId, Integer dataSource, Set<String> batchCodes) {

        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(
                        Conditions.and(Columns.toColumn(FileOriginalDataMonitorEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId),
                        Conditions.and(Columns.toColumn(FileOriginalDataMonitorEnum.SHOP_ID.getFieldCode()), LinkMode.IN, shopId),
                        Conditions.and(Columns.toColumn(FileOriginalDataMonitorEnum.DATA_SOURCE.getFieldCode()), LinkMode.EQUAL, dataSource),
                        Conditions.and(Columns.toColumn(FileOriginalDataMonitorEnum.BATCH_CODE.getFieldCode()), LinkMode.IN, batchCodes)
                )
                .select(
                )
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<FileOriginalDataMonitorDO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(FileOriginalDataMonitorDO.class), args);
        return !query.isEmpty() ? query : null;
    }


}
