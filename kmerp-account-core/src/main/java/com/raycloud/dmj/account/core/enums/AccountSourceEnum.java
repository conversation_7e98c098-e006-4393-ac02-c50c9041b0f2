package com.raycloud.dmj.account.core.enums;

import lombok.Getter;

/**
 * 账户来源枚举
 * 1-系统，2-手动
 * <AUTHOR>
 */
@Getter
public enum AccountSourceEnum {

    SYSTEM(1, "系统"),
    MANUAL(2, "手动");

    /**
     * 来源编码
     */
    private final Integer sourceCode;

    /**
     * 来源描述
     */
    private final String sourceDesc;

    AccountSourceEnum(Integer sourceCode, String sourceDesc) {
        this.sourceCode = sourceCode;
        this.sourceDesc = sourceDesc;
    }

    /**
     * 根据来源编码获取枚举实例，方便从数据库值转换为枚举
     * @param sourceCode 来源编码
     * @return 对应的枚举实例，若未匹配到则返回 null
     */
    public static AccountSourceEnum getBySourceCode(Integer sourceCode) {
        for (AccountSourceEnum enumObj : values()) {
            if (enumObj.getSourceCode().equals(sourceCode)) {
                return enumObj;
            }
        }
        return null;
    }
}