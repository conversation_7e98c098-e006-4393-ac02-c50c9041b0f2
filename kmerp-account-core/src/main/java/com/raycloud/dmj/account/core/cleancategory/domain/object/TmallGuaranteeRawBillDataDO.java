package com.raycloud.dmj.account.core.cleancategory.domain.object;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 保证金原始数据表
 */
@Data
public class TmallGuaranteeRawBillDataDO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 自增主键
     */
    private Long id;

    /**
     * 店铺简称
     */
    private String storeShortName;

    /**
     * 完成时间
     */
    private Date completionTime;

    /**
     * 资金类型
     */
    private String fundType;

    /**
     * 操作类型
     */
    private String operationType;

    /**
     * 原因
     */
    private String reason;

    /**
     * 币种
     */
    private String currency;

    /**
     * 收支金额
     */
    private BigDecimal receiptAmount;

    /**
     * 现金总余额
     */
    private BigDecimal cashBalance;

    /**
     * 来源账户
     */
    private String sourceAccount;

    /**
     * 去向账户
     */
    private String destinationAccount;

    /**
     * 出资类型
     */
    private String contributionType;

    /**
     * 业务描述
     */
    private String businessDescription;

    /**
     * 业务编号
     */
    private String businessNumber;

    /**
     * 订单编号
     */
    private String orderNumber;

    /**
     * 备注
     */
    private String remark;

    /**
     * 下载时间
     */
    private Date downloadTime;

    /**
     * 下载账户
     */
    private String downloadAccount;

    /**
     * 唯一键
     */
    private String bizKey;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 批次时间
     */
    private Integer batchTime;

    /**
     * 公司ID
     */
    private Long companyId;

    /**
     * 店铺ID
     */
    private String shopId;

    /**
     * 创建时间
     */
    private Date created;
}    