package com.raycloud.dmj.account.core.base.dao.impl;

import com.raycloud.dmj.account.core.base.dao.StandardOtherBillFlowInfoDao;
import com.raycloud.dmj.account.core.base.domain.StandardOtherBillFlowInfoDO;
import com.raycloud.dmj.account.core.bill.parameter.StandardOtherBillFlowParameter;
import com.raycloud.dmj.account.core.bill.request.StandardOtherBillFlowRequest;
import com.raycloud.dmj.account.core.bill.vo.StandardOtherBillFlowInfoVO;
import com.raycloud.dmj.account.core.bill.vo.BillTotalInfo;
import com.raycloud.dmj.account.core.enums.DateTypeEnum;
import com.raycloud.dmj.account.core.enums.StandardFundBillSourceEnum;
import com.raycloud.dmj.account.core.enums.field.ShopInfoFieldEnum;
import com.raycloud.dmj.account.core.enums.field.StandardOtherBillFlowInfoFieldEnum;
import com.raycloud.dmj.account.core.rawdata.utils.RawDataDateUtil;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import com.raycloud.dmj.table.api.plus.common.base.SQL;
import com.raycloud.dmj.table.api.plus.common.enums.LinkMode;
import com.raycloud.dmj.table.api.plus.common.enums.OrderType;
import com.raycloud.dmj.table.api.plus.component.$;
import com.raycloud.dmj.table.api.plus.component.column.utils.Columns;
import com.raycloud.dmj.table.api.plus.component.condition.ConditionComponent;
import com.raycloud.dmj.table.api.plus.component.condition.utils.Conditions;
import com.raycloud.dmj.table.api.plus.delete.Deletes;
import com.raycloud.dmj.table.api.plus.insert.Inserts;
import com.raycloud.dmj.table.api.plus.insert.batch.InsertBatchSQL;
import com.raycloud.dmj.table.api.plus.insert.core.InsertMode;
import com.raycloud.dmj.table.api.plus.query.Queries;
import com.raycloud.dmj.table.api.plus.query.api.QueryFrom;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Repository
public class StandardOtherBillFlowInfoDaoImpl extends BaseDao implements StandardOtherBillFlowInfoDao {

    private final String tableName = "standard_other_bill_flow_info";
    private final static String shopTableName = "shop_info";
    private final String DOC_NO = "-1";

    @Override
    public Long addStandardOtherBillFlowInfo(StandardOtherBillFlowInfoDO flowInfo) {
        Date now = new Date();
        
        // 设置默认值
        if (flowInfo.getSource() == null) {
            flowInfo.setSource(StandardFundBillSourceEnum.BILL.getCode()); // 默认来源账单
        }
        if (flowInfo.getDocNo() == null) {
            flowInfo.setDocNo(DOC_NO); // 默认值
        }
        if (flowInfo.getCreated() == null) {
            flowInfo.setCreated(now);
        }
        if (flowInfo.getModified() == null) {
            flowInfo.setModified(now);
        }

        // 构建插入SQL
        SQL sql = Inserts.insert()
                .into(tableName)
                .columns(
                        StandardOtherBillFlowInfoFieldEnum.PLATFORM_CODE.getFieldCode(),
                        StandardOtherBillFlowInfoFieldEnum.SHOP_ID.getFieldCode(),
                        StandardOtherBillFlowInfoFieldEnum.CATEGORY_CODE.getFieldCode(),
                        StandardOtherBillFlowInfoFieldEnum.CATEGORY_GROUP_CODE.getFieldCode(),
                        StandardOtherBillFlowInfoFieldEnum.SUB_CATEGORY_CODE.getFieldCode(),
                        StandardOtherBillFlowInfoFieldEnum.BILLING_CYCLE.getFieldCode(),
                        StandardOtherBillFlowInfoFieldEnum.OCCURRED_AT.getFieldCode(),
                        StandardOtherBillFlowInfoFieldEnum.INCOME_EXPENSE_DIRECTION.getFieldCode(),
                        StandardOtherBillFlowInfoFieldEnum.AMOUNT.getFieldCode(),
                        StandardOtherBillFlowInfoFieldEnum.ORDER_NO.getFieldCode(),
                        StandardOtherBillFlowInfoFieldEnum.REMARK.getFieldCode(),
                        StandardOtherBillFlowInfoFieldEnum.COUNTERPARTY_ID.getFieldCode(),
                        StandardOtherBillFlowInfoFieldEnum.DOC_NO.getFieldCode(),
                        StandardOtherBillFlowInfoFieldEnum.SOURCE.getFieldCode(),
                        StandardOtherBillFlowInfoFieldEnum.BATCH_NO.getFieldCode(),
                        StandardOtherBillFlowInfoFieldEnum.BIZ_KEY.getFieldCode(),
                        StandardOtherBillFlowInfoFieldEnum.CREATOR.getFieldCode(),
                        StandardOtherBillFlowInfoFieldEnum.CREATED.getFieldCode(),
                        StandardOtherBillFlowInfoFieldEnum.MODIFIED.getFieldCode(),
                        StandardOtherBillFlowInfoFieldEnum.COMPANY_ID.getFieldCode()
                ).valueForEntity(flowInfo)
                .toSql();

        // 使用KeyHolder捕获自增主键
        KeyHolder keyHolder = new GeneratedKeyHolder();
        Object[] params = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();

        jdbcTemplate.update(connection -> {
            PreparedStatement ps = connection.prepareStatement(sql.getSqlCode(), Statement.RETURN_GENERATED_KEYS);
            for (int i = 0; i < params.length; i++) {
                ps.setObject(i + 1, params[i]);
            }
            return ps;
        }, keyHolder);

        // 返回生成的主键ID
        Number key = keyHolder.getKey();
        return key != null ? key.longValue() : null;
    }

    @Override
    public List<StandardOtherBillFlowInfoVO> getStandardOtherBillFlowInfoList(StandardOtherBillFlowRequest request, Long companyId) {
        // 构建基础查询
        QueryFrom queryBuilder = Queries.create().from(tableName, "bill")
                .join(shopTableName, "s")
                .on(
                        Conditions.and(
                                Columns.create(StandardOtherBillFlowInfoFieldEnum.SHOP_ID.getFieldCode()).referenceTableAlias("bill"),
                                LinkMode.EQUAL,
                                Columns.create(ShopInfoFieldEnum.ID.getFieldCode()).referenceTableAlias("s")
                        )
                );

        // 添加查询条件（多表查询，需要指定表别名）
        List<ConditionComponent<?>> conditions = getConditionComponentsWithAlias(companyId, request);

        // 构建SQL并执行查询
        SQL sql = queryBuilder.where(conditions)
                .orderBy($.create("bill." + StandardOtherBillFlowInfoFieldEnum.OCCURRED_AT.getFieldCode(), OrderType.find("desc")))
                .select("bill.*,s.short_title as shop_short_title,s.title as shop_title")
                .page(request.getPageNo(), request.getPageSize())
                .toSql();

        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        return jdbcTemplate.query(
                sql.getSqlCode(),
                new BeanPropertyRowMapper<>(StandardOtherBillFlowInfoVO.class),
                args
        );
    }

    @Override
    public Long getCount(StandardOtherBillFlowRequest request, Long companyId) {
        // 构建基础查询
        QueryFrom queryBuilder = Queries.create().from(tableName, "bill")
                .join(shopTableName, "s")
                .on(
                        Conditions.and(
                                Columns.create(StandardOtherBillFlowInfoFieldEnum.SHOP_ID.getFieldCode()).referenceTableAlias("bill"),
                                LinkMode.EQUAL,
                                Columns.create(ShopInfoFieldEnum.ID.getFieldCode()).referenceTableAlias("s")
                        )
                );

        // 添加查询条件
        List<ConditionComponent<?>> conditions = getConditionComponentsWithAlias(companyId, request);

        // 构建统计SQL
        SQL sql = queryBuilder.where(conditions)
                .select(Columns.create("COUNT(*)"))
                .toSql();

        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();

        // 执行统计查询
        return jdbcTemplate.queryForObject(sql.getSqlCode(), Long.class, args);
    }

    @Override
    public BillTotalInfo getTotalInfo(StandardOtherBillFlowRequest request, Long companyId) {
        // 构建基础查询
        QueryFrom queryBuilder = Queries.create().from(tableName, "bill")
                .join(shopTableName, "s")
                .on(
                        Conditions.and(
                                Columns.create(StandardOtherBillFlowInfoFieldEnum.SHOP_ID.getFieldCode()).referenceTableAlias("bill"),
                                LinkMode.EQUAL,
                                Columns.create(ShopInfoFieldEnum.ID.getFieldCode()).referenceTableAlias("s")
                        )
                );

        // 添加查询条件
        List<ConditionComponent<?>> conditions = getConditionComponentsWithAlias(companyId, request);

        // 构建统计SQL
        SQL sql = queryBuilder.where(conditions)
                .select(Columns.create("sum(bill.amount) as totalAmount,count(*) as total"))
                .toSql();

        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();

        // 执行统计查询，使用BeanPropertyRowMapper进行字段映射
        return jdbcTemplate.queryForObject(sql.getSqlCode(),
                new BeanPropertyRowMapper<>(BillTotalInfo.class), args);
    }

    @Override
    public StandardOtherBillFlowInfoDO getStandardOtherBillFlowInfoById(Long id) {
        SQL sql = Queries.create()
                .from(tableName)
                .where(Conditions.and(Columns.toColumn(StandardOtherBillFlowInfoFieldEnum.ID.getFieldCode()),
                                     LinkMode.EQUAL, id))
                .select()
                .limit(1)
                .toSql();

        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<StandardOtherBillFlowInfoDO> results = jdbcTemplate.query(
                sql.getSqlCode(),
                new BeanPropertyRowMapper<>(StandardOtherBillFlowInfoDO.class),
                args
        );

        return results.isEmpty() ? null : results.get(0);
    }

    @Override
    public Integer batchAddStandardOtherBillFlowInfo(List<StandardOtherBillFlowInfoDO> flowInfoList) {
        if (flowInfoList == null || flowInfoList.isEmpty()) {
            return 0;
        }

        // 构建批量插入SQL
        StringBuilder sqlBuilder = new StringBuilder();
        sqlBuilder.append("INSERT INTO ").append(tableName)
                .append(" (platform_type, shop_id, rule_id, classify_code, category_id, ")
                .append("sub_category_id, billing_cycle, occurred_at, income_expense_direction, ")
                .append("amount, order_no, remark, counterparty_id, doc_no, source, ")
                .append("batch_no, biz_key, creator, created, modified, company_id)")
                .append(" VALUES ");

        List<Object> params = new ArrayList<>();
        Date now = new Date();

        for (int i = 0; i < flowInfoList.size(); i++) {
            StandardOtherBillFlowInfoDO flowInfo = flowInfoList.get(i);
            
            // 设置默认值
            if (flowInfo.getSource() == null) {
                flowInfo.setSource(StandardFundBillSourceEnum.BILL.getCode());
            }
            if (flowInfo.getDocNo() == null) {
                flowInfo.setDocNo(DOC_NO);
            }
            if (flowInfo.getCreated() == null) {
                flowInfo.setCreated(now);
            }
            if (flowInfo.getModified() == null) {
                flowInfo.setModified(now);
            }

            if (i > 0) {
                sqlBuilder.append(", ");
            }
            sqlBuilder.append("(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

            params.add(flowInfo.getPlatformCode());
            params.add(flowInfo.getShopId());
            params.add(flowInfo.getCategoryGroupCode());
            params.add(flowInfo.getCategoryGroupCode());
            params.add(flowInfo.getSubCategoryCode());
            params.add(flowInfo.getBillingCycle());
            params.add(flowInfo.getOccurredAt());
            params.add(flowInfo.getIncomeExpenseDirection());
            params.add(flowInfo.getAmount());
            params.add(flowInfo.getOrderNo());
            params.add(flowInfo.getRemark());
            params.add(flowInfo.getCounterpartyId());
            params.add(flowInfo.getDocNo());
            params.add(flowInfo.getSource());
            params.add(flowInfo.getBatchNo());
            params.add(flowInfo.getBizKey());
            params.add(flowInfo.getCreator());
            params.add(flowInfo.getCreated());
            params.add(flowInfo.getModified());
            params.add(flowInfo.getCompanyId());
        }

        return jdbcTemplate.update(sqlBuilder.toString(), params.toArray());
    }

    @Override
    public Integer batchInsert(List<StandardOtherBillFlowInfoDO> flowInfoList) {
        AsserUtils.notEmpty(flowInfoList, "流水信息列表不能为空");
        List<Object> objectList = flowInfoList.stream()
                .map(item -> (Object) item)
                .collect(Collectors.toList());
        InsertBatchSQL sql = Inserts.create(InsertMode.DEFAULT)
                .into(tableName)
                .columns(StandardOtherBillFlowInfoFieldEnum.getInsertFields())
                .valueForEntities(objectList)
                .batch()
                .toSql();
        jdbcTemplate.batchUpdate(sql.getSqlPattern(), sql.getArguments());
        return flowInfoList.size();
    }

    @Override
    public void deleteByShopAndDataRangeAndCategory(Long companyId, Long shopId, Integer dataRange, String categoryCode) {
        AsserUtils.notNull(companyId, "公司ID不能为空！");
        AsserUtils.notNull(shopId, "店铺ID不能为空！");
        AsserUtils.notNull(dataRange, "账期不能为空！");
        AsserUtils.notNull(categoryCode, "类别编码不能为空！");

        SQL sql = Deletes.create()
                .from(tableName)
                .where(
                        Conditions.and(StandardOtherBillFlowInfoFieldEnum.COMPANY_ID.getFieldCode(), LinkMode.EQUAL, companyId),
                        Conditions.and(StandardOtherBillFlowInfoFieldEnum.SHOP_ID.getFieldCode(), LinkMode.EQUAL, shopId),
                        Conditions.and(StandardOtherBillFlowInfoFieldEnum.DATA_RANGE.getFieldCode(), LinkMode.EQUAL, dataRange),
                        Conditions.and(StandardOtherBillFlowInfoFieldEnum.CATEGORY_CODE.getFieldCode(), LinkMode.EQUAL, categoryCode)
                ).toSql();
        Object[] updateArgs = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        jdbcTemplate.update(sql.getSqlCode(),updateArgs);
    }

    @Override
    public SQL getExportSQL(StandardOtherBillFlowParameter parameter, Long companyId) {
        // 构建基础查询
        QueryFrom queryBuilder = Queries.create().from(tableName, "bill")
                .join(shopTableName, "s")
                .on(
                        Conditions.and(
                                Columns.create(StandardOtherBillFlowInfoFieldEnum.SHOP_ID.getFieldCode()).referenceTableAlias("bill"),
                                LinkMode.EQUAL,
                                Columns.create(ShopInfoFieldEnum.ID.getFieldCode()).referenceTableAlias("s")
                        )
                );

        // 添加查询条件（多表查询，需要指定表别名）
        List<ConditionComponent<?>> conditions = getConditionComponentsWithAlias(companyId, parameter);

        // 构建SQL并执行查询
        SQL sql = queryBuilder.where(conditions)
                .orderBy($.create("bill." + StandardOtherBillFlowInfoFieldEnum.OCCURRED_AT.getFieldCode(), OrderType.find("desc")))
                .select("bill.*,s.short_title as shop_short_title,s.title as shop_title")
                .toSql();

        return sql;
    }

    /**
     * 构建多表查询条件（带表别名）
     * @param companyId 公司ID
     * @param parameter 查询条件
     * @return 条件列表
     */
    private List<ConditionComponent<?>> getConditionComponentsWithAlias(Long companyId, StandardOtherBillFlowParameter parameter) {
        List<ConditionComponent<?>> conditions = new ArrayList<>();

        // 公司ID条件（必须）- 指定使用主表的company_id
        conditions.add(Conditions.and(
                Columns.create(StandardOtherBillFlowInfoFieldEnum.COMPANY_ID.getFieldCode()).referenceTableAlias("bill"),
                LinkMode.EQUAL,
                companyId));

        if (ObjectUtils.isEmpty(parameter)){
            return conditions;
        }

        //发生时间
        if (ObjectUtils.isNotEmpty(parameter.getStartTime()) && ObjectUtils.isNotEmpty(parameter.getEndTime())) {
            conditions.add(Conditions.and(
                    Columns.create(StandardOtherBillFlowInfoFieldEnum.OCCURRED_AT.getFieldCode()).referenceTableAlias("bill"),
                    LinkMode.GREATER_THAN_EQUAL,
                    parameter.getStartTime()));
            conditions.add(Conditions.and(
                    Columns.create(StandardOtherBillFlowInfoFieldEnum.OCCURRED_AT.getFieldCode()).referenceTableAlias("bill"),
                    LinkMode.LESS_THAN_EQUAL,
                    parameter.getEndTime()));
        }
        if (ObjectUtils.isNotEmpty(parameter.getCycleStartTime()) && ObjectUtils.isNotEmpty(parameter.getCycleEndTime())) {
            Integer startBillingCycle = RawDataDateUtil.formatDate(parameter.getCycleStartTime(), DateTypeEnum.MONTH);
            Integer endBillingCycle = RawDataDateUtil.formatDate(parameter.getCycleEndTime(), DateTypeEnum.MONTH);
            conditions.add(Conditions.and(
                    Columns.create(StandardOtherBillFlowInfoFieldEnum.BILLING_CYCLE.getFieldCode()).referenceTableAlias("bill"),
                    LinkMode.GREATER_THAN_EQUAL,
                    startBillingCycle));
            conditions.add(Conditions.and(
                    Columns.create(StandardOtherBillFlowInfoFieldEnum.BILLING_CYCLE.getFieldCode()).referenceTableAlias("bill"),
                    LinkMode.LESS_THAN_EQUAL,
                    endBillingCycle));
        }
        //平台
        if (ObjectUtils.isNotEmpty(parameter.getPlatformCodeList())) {
            conditions.add(Conditions.and(
                    Columns.create(StandardOtherBillFlowInfoFieldEnum.PLATFORM_CODE.getFieldCode()).referenceTableAlias("bill"),
                    LinkMode.IN,
                    parameter.getPlatformCodeList()));
        }
        // 店铺ID条件（可选）
        if (ObjectUtils.isNotEmpty(parameter.getShopIdList())) {
            conditions.add(Conditions.and(
                    Columns.create(StandardOtherBillFlowInfoFieldEnum.SHOP_ID.getFieldCode()).referenceTableAlias("bill"),
                    LinkMode.IN,
                    parameter.getShopIdList()));
        }
        // 子类别ID条件（可选）
        if (ObjectUtils.isNotEmpty(parameter.getSubCategoryCodeList())) {
            conditions.add(Conditions.and(
                    Columns.create(StandardOtherBillFlowInfoFieldEnum.SUB_CATEGORY_CODE.getFieldCode()).referenceTableAlias("bill"),
                    LinkMode.IN,
                    parameter.getSubCategoryCodeList()));
        }
        //收支方向
        if (ObjectUtils.isNotEmpty(parameter.getIncomeExpenseDirection())) {
            conditions.add(Conditions.and(
                    Columns.create(StandardOtherBillFlowInfoFieldEnum.INCOME_EXPENSE_DIRECTION.getFieldCode()).referenceTableAlias("bill"),
                    LinkMode.EQUAL,
                    parameter.getIncomeExpenseDirection()));
        }
        //关联账户订单号
        if (ObjectUtils.isNotEmpty(parameter.getOrderNoList())) {
            conditions.add(Conditions.and(
                    Columns.create(StandardOtherBillFlowInfoFieldEnum.ORDER_NO.getFieldCode()).referenceTableAlias("bill"),
                    LinkMode.IN,
                    parameter.getOrderNoList()));
        }
        //关联业务单据号
        if (StringUtils.isNotEmpty(parameter.getDocNo())) {
            conditions.add(Conditions.and(
                    Columns.create(StandardOtherBillFlowInfoFieldEnum.DOC_NO.getFieldCode()).referenceTableAlias("bill"),
                    LinkMode.EQUAL,
                    parameter.getDocNo()));
        }
        //备注
        if (StringUtils.isNotEmpty(parameter.getRemark())) {
            conditions.add(Conditions.and(
                    Columns.create(StandardOtherBillFlowInfoFieldEnum.REMARK.getFieldCode()).referenceTableAlias("bill"),
                    LinkMode.LIKE_FULL_MATCH,
                    parameter.getRemark()));
        }
        //是否关联业务单据
        if (ObjectUtils.isNotEmpty(parameter.getIsRelatedDoc())) {
            if (parameter.getIsRelatedDoc() == 1) {
                conditions.add(Conditions.and(
                        Columns.create(StandardOtherBillFlowInfoFieldEnum.DOC_NO.getFieldCode()).referenceTableAlias("bill"),
                        LinkMode.NOT_EQUAL,
                        DOC_NO));
            }else {
                conditions.add(Conditions.and(
                        Columns.create(StandardOtherBillFlowInfoFieldEnum.DOC_NO.getFieldCode()).referenceTableAlias("bill"),
                        LinkMode.EQUAL,
                        DOC_NO));
            }
        }
        //来源方式
        if (ObjectUtils.isNotEmpty(parameter.getSource())) {
            conditions.add(Conditions.and(
                    Columns.create(StandardOtherBillFlowInfoFieldEnum.SOURCE.getFieldCode()).referenceTableAlias("bill"),
                    LinkMode.EQUAL,
                    parameter.getSource()));
        }
        return conditions;
    }

    /**
     * 构建多表查询条件（带表别名）
     * @param companyId 公司ID
     * @param request 查询条件
     * @return 条件列表
     */
    private List<ConditionComponent<?>> getConditionComponentsWithAlias(Long companyId, StandardOtherBillFlowRequest request) {
        List<ConditionComponent<?>> conditions = new ArrayList<>();

        // 公司ID条件（必须）- 指定使用主表的company_id
        conditions.add(Conditions.and(
                Columns.create(StandardOtherBillFlowInfoFieldEnum.COMPANY_ID.getFieldCode()).referenceTableAlias("bill"),
                LinkMode.EQUAL,
                companyId));

        if (ObjectUtils.isEmpty(request)){
            return conditions;
        }

        //发生时间
        if (ObjectUtils.isNotEmpty(request.getStartTime()) && ObjectUtils.isNotEmpty(request.getEndTime())) {
            conditions.add(Conditions.and(
                    Columns.create(StandardOtherBillFlowInfoFieldEnum.OCCURRED_AT.getFieldCode()).referenceTableAlias("bill"),
                    LinkMode.GREATER_THAN_EQUAL,
                    request.getStartTime()));
            conditions.add(Conditions.and(
                    Columns.create(StandardOtherBillFlowInfoFieldEnum.OCCURRED_AT.getFieldCode()).referenceTableAlias("bill"),
                    LinkMode.LESS_THAN_EQUAL,
                    request.getEndTime()));
        }
        if (ObjectUtils.isNotEmpty(request.getCycleStartTime()) && ObjectUtils.isNotEmpty(request.getCycleEndTime())) {
            Integer startBillingCycle = RawDataDateUtil.formatDate(request.getCycleStartTime(), DateTypeEnum.MONTH);
            Integer endBillingCycle = RawDataDateUtil.formatDate(request.getCycleEndTime(), DateTypeEnum.MONTH);
            conditions.add(Conditions.and(
                    Columns.create(StandardOtherBillFlowInfoFieldEnum.BILLING_CYCLE.getFieldCode()).referenceTableAlias("bill"),
                    LinkMode.GREATER_THAN_EQUAL,
                    startBillingCycle));
            conditions.add(Conditions.and(
                    Columns.create(StandardOtherBillFlowInfoFieldEnum.BILLING_CYCLE.getFieldCode()).referenceTableAlias("bill"),
                    LinkMode.LESS_THAN_EQUAL,
                    endBillingCycle));
        }
        //平台
        if (ObjectUtils.isNotEmpty(request.getPlatformCodeList())) {
            conditions.add(Conditions.and(
                    Columns.create(StandardOtherBillFlowInfoFieldEnum.PLATFORM_CODE.getFieldCode()).referenceTableAlias("bill"),
                    LinkMode.IN,
                    request.getPlatformCodeList()));
        }
        // 店铺ID条件（可选）
        if (ObjectUtils.isNotEmpty(request.getShopIdList())) {
            conditions.add(Conditions.and(
                    Columns.create(StandardOtherBillFlowInfoFieldEnum.SHOP_ID.getFieldCode()).referenceTableAlias("bill"),
                    LinkMode.IN,
                    request.getShopIdList()));
        }
        // 子类别Code条件（可选）
        if (ObjectUtils.isNotEmpty(request.getSubCategoryCodeList())) {
            conditions.add(Conditions.and(
                    Columns.create(StandardOtherBillFlowInfoFieldEnum.SUB_CATEGORY_CODE.getFieldCode()).referenceTableAlias("bill"),
                    LinkMode.IN,
                    request.getSubCategoryCodeList()));
        }
        //收支方向
        if (ObjectUtils.isNotEmpty(request.getIncomeExpenseDirection())) {
            conditions.add(Conditions.and(
                    Columns.create(StandardOtherBillFlowInfoFieldEnum.INCOME_EXPENSE_DIRECTION.getFieldCode()).referenceTableAlias("bill"),
                    LinkMode.EQUAL,
                    request.getIncomeExpenseDirection()));
        }
        //关联账户订单号
        if (ObjectUtils.isNotEmpty(request.getOrderNoList())) {
            conditions.add(Conditions.and(
                    Columns.create(StandardOtherBillFlowInfoFieldEnum.ORDER_NO.getFieldCode()).referenceTableAlias("bill"),
                    LinkMode.IN,
                    request.getOrderNoList()));
        }
        //关联业务单据号
        if (StringUtils.isNotEmpty(request.getDocNo())) {
            conditions.add(Conditions.and(
                    Columns.create(StandardOtherBillFlowInfoFieldEnum.DOC_NO.getFieldCode()).referenceTableAlias("bill"),
                    LinkMode.EQUAL,
                    request.getDocNo()));
        }
        //备注
        if (StringUtils.isNotEmpty(request.getRemark())) {
            conditions.add(Conditions.and(
                    Columns.create(StandardOtherBillFlowInfoFieldEnum.REMARK.getFieldCode()).referenceTableAlias("bill"),
                    LinkMode.LIKE_FULL_MATCH,
                    request.getRemark()));
        }
        //是否关联业务单据
        if (ObjectUtils.isNotEmpty(request.getIsRelatedDoc())) {
            if (request.getIsRelatedDoc() == 1) {
                conditions.add(Conditions.and(
                        Columns.create(StandardOtherBillFlowInfoFieldEnum.DOC_NO.getFieldCode()).referenceTableAlias("bill"),
                        LinkMode.NOT_EQUAL,
                        DOC_NO));
            }else {
                conditions.add(Conditions.and(
                        Columns.create(StandardOtherBillFlowInfoFieldEnum.DOC_NO.getFieldCode()).referenceTableAlias("bill"),
                        LinkMode.EQUAL,
                        DOC_NO));
            }
        }
        //来源方式
        if (ObjectUtils.isNotEmpty(request.getSource())) {
            conditions.add(Conditions.and(
                    Columns.create(StandardOtherBillFlowInfoFieldEnum.SOURCE.getFieldCode()).referenceTableAlias("bill"),
                    LinkMode.EQUAL,
                    request.getSource()));
        }
        return conditions;
    }
}
