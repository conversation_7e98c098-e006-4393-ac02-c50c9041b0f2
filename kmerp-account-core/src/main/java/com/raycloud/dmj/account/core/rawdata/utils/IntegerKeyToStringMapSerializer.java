package com.raycloud.dmj.account.core.rawdata.utils;

import com.alibaba.fastjson2.JSONWriter;
import com.alibaba.fastjson2.writer.ObjectWriter;

import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;

public class IntegerKeyToStringMapSerializer implements ObjectWriter<Object> {

    @Override
    public void write(JSONWriter jsonWriter, Object object, Object fieldName, Type fieldType, long features) {
        if (object == null) {
            jsonWriter.writeNull();
            return;
        }

        Map<?, ?> originalMap = (Map<?, ?>) object;
        Map<String, Object> convertedMap = new HashMap<>();

        for (Map.Entry<?, ?> entry : originalMap.entrySet()) {
            convertedMap.put(String.valueOf(entry.getKey()), entry.getValue());
        }

        jsonWriter.write(convertedMap);
    }
}