package com.raycloud.dmj.account.core.base.dao;

import com.raycloud.dmj.account.core.cleancategory.domain.object.CategoryAnalyzeRuleDO;
import com.raycloud.dmj.account.core.common.Page;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CategoryAnalyzeRuleDao {

    /**
     * 新增类别解析规则
     * @param analyzeRuleDO 类别解析规则
     * @return 新增类别解析规则ID
     */
    Long insert(CategoryAnalyzeRuleDO analyzeRuleDO);


    /**
     * 根据ID查询类别解析规则
     * @param companyId 公司ID
     * @param id 类别解析规则ID
     * @return 类别解析规则
     */
    CategoryAnalyzeRuleDO queryById(Long companyId, Long id);

    /**
     * 逻辑删除
     * @param companyId 公司ID
     * @param ids ID集合
     */
    void logicDelete(Long companyId,List<Long> ids);

    /**
     * 逻辑删除
     * @param companyId 公司ID
     * @param subCategoryId 子类别ID
     */
    void logicDeleteBySubCategoryId(Long companyId,Long subCategoryId);

    /**
     * 根据子类别ID集合查询类别解析规则数量
     * @param companyId 公司ID
     * @param subCategoryIds 子类别ID集合
     * @return 类别解析规则数量
     */
    Long countBySubCategoryIds(Long companyId,List<Long> subCategoryIds);

    /**
     * 根据子类别ID集合查询类别解析规则数量
     * @param companyId 公司ID
     * @param subCategoryIds 子类别ID集合
     * @param page 分页参数
     * @return 类别解析规则列表
     */
    List<CategoryAnalyzeRuleDO> pageQueryBySubCategoryIds(Long companyId, List<Long> subCategoryIds, Page page);

    /**
     * 根据子类别ID集合查询类别解析规则数量
     * @param companyId 公司ID
     * @param subCategoryIds 子类别ID集合
     * @return 类别解析规则列表
     */
    List<CategoryAnalyzeRuleDO> listBySubCategoryIds(Long companyId, List<Long> subCategoryIds);
}
