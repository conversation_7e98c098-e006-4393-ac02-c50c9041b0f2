package com.raycloud.dmj.account.core.cleancategory.service.impl;

import com.raycloud.dmj.account.core.cleancategory.domain.request.CategoryAnalyzeReq;
import com.raycloud.dmj.account.core.cleancategory.service.CategoryAnalyzeService;
import com.raycloud.dmj.account.core.base.dao.FileOriginalDataMonitorDao;
import com.raycloud.dmj.account.core.cleancategory.strategy.CategoryAnalyzeSelect;
import com.raycloud.dmj.account.core.cleancategory.strategy.param.CategoryAnalyzeParam;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * 类目分析服务实现
 * <AUTHOR>
 */
@Service
public class CategoryAnalyzeServiceImpl implements CategoryAnalyzeService {


    private static final Logger log = LoggerFactory.getLogger(CategoryAnalyzeServiceImpl.class);
    @Resource
    private FileOriginalDataMonitorDao fileOriginalDataMonitorDao;

    @Resource
    private CategoryAnalyzeSelect categoryAnalyzeSelect;



    @Override
    public void categoryAnalyze(CategoryAnalyzeReq categoryAnalyzeReq) {
        AsserUtils.notNull(categoryAnalyzeReq, "参数不能为空！");
        //进行类目解析
        CategoryAnalyzeParam categoryAnalyzeParam = new CategoryAnalyzeParam();
        categoryAnalyzeParam.setCompanyId(categoryAnalyzeReq.getCompanyId());
        categoryAnalyzeParam.setShopId(categoryAnalyzeReq.getShopId());
        categoryAnalyzeParam.setSource(categoryAnalyzeReq.getSource());
        categoryAnalyzeParam.setDataRange(categoryAnalyzeReq.getDataRange());
        categoryAnalyzeParam.setDateType(categoryAnalyzeReq.getDateType());
        categoryAnalyzeSelect.execute(categoryAnalyzeParam);

    }
}
