package com.raycloud.dmj.account.core.common.response;

import com.raycloud.dmj.account.core.common.constant.SystemConstants;
import lombok.Getter;
import lombok.Setter;
import org.apache.log4j.MDC;
import org.springframework.http.HttpStatus;

/**
 * 请求响应的基本结构
 * @param <T>
 */
@Getter
@Setter
public class BaseResponse<T> {
    public BaseResponse() {

    }

    public BaseResponse(T data) {
        this.data = data;
    }

    /**
     * 用于跟踪请求的线索ID
     */
    private String clueId = String.valueOf(MDC.get(SystemConstants.CLUE_ID));

    /**
     * 返回的数据
     */
    private T data;

    /**
     * 状态码
     */
    private Integer result = HttpStatus.OK.value();

}
