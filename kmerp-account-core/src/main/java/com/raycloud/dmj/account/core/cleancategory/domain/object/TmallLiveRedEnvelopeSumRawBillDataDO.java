package com.raycloud.dmj.account.core.cleancategory.domain.object;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 直播红包汇总数据
 * <AUTHOR>
 */
@Data
public class TmallLiveRedEnvelopeSumRawBillDataDO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 账单月份
     */
    private String billingMonth;

    /**
     * 红包类型
     */
    private String redEnvelopeType;

    /**
     * 发票类型
     */
    private String invoiceType;

    /**
     * 发票抬头
     */
    private String invoiceTitle;

    /**
     * 当期开票金额（元）
     */
    private BigDecimal currentInvoiceAmount;

    /**
     * 当期退款金额（元）
     */
    private BigDecimal currentRefundAmount;

    /**
     * 应开票金额（元）
     */
    private BigDecimal dueInvoiceAmount;

    /**
     * 已开发票金额（元）
     */
    private BigDecimal invoicedAmount;

    /**
     * 主播名称
     */
    private String anchorName;

    /**
     * 购方税号
     */
    private String buyerTaxId;

    /**
     * 开户银行
     */
    private String bankName;

    /**
     * 银行账户
     */
    private String bankAccount;

    /**
     * 企业电话
     */
    private String companyPhone;

    /**
     * 企业地址
     */
    private String companyAddress;

    /**
     * 收票人
     */
    private String invoiceRecipient;

    /**
     * 收票人电话
     */
    private String recipientPhone;

    /**
     * 收票人地址
     */
    private String recipientAddress;

    /**
     * 主播备注
     */
    private String anchorRemark;

    /**
     * 与明细表关联字段
     */
    private String serialNo;

    /**
     * 唯一键
     */
    private String bizKey;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 批次时间 格式如********
     */
    private Integer batchTime;

    /**
     * 租户ID
     */
    private Long companyId;

    /**
     * 店铺ID
     */
    private String shopId;

    /**
     * 创建时间
     */
    private Date created;
}