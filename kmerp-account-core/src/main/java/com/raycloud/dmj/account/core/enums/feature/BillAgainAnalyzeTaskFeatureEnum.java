package com.raycloud.dmj.account.core.enums.feature;

import com.raycloud.dmj.account.core.enums.FileTypeEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 * Date:  2025/7/31
 */
@Getter
public enum BillAgainAnalyzeTaskFeatureEnum {

    /**
     * 异常消息
     */
    ERROR_MESSAGE("errorMsg"),
    ;


    /**
     * 字段编码
     */
    private final String fieldCode;

    BillAgainAnalyzeTaskFeatureEnum(String fileType) {
        this.fieldCode = fileType;
    }


}
