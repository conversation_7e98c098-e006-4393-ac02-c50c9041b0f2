package com.raycloud.dmj.account.core.enums;

import com.google.common.base.CaseFormat;
import com.raycloud.dmj.account.core.common.TreeVO;
import com.raycloud.dmj.account.core.enums.field.TmallAlipayRawBillFieldEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 解析规则中满足条件字段
 * <AUTHOR>
 */

@Getter
public enum AlipayMatchFieldEnum{

    REMARK(TmallAlipayRawBillFieldEnum.REMARK, "备注"),
    BIZ_DESCRIPTION(TmallAlipayRawBillFieldEnum.BIZ_DESCRIPTION, "业务描述"),
    COUNTERPARTY_ACCOUNT(TmallAlipayRawBillFieldEnum.COUNTERPARTY_ACCOUNT, "对方账号"),
    ;


    private final String fieldCode;

    private final TmallAlipayRawBillFieldEnum matchField;

    private final String fieldDesc;

    AlipayMatchFieldEnum(TmallAlipayRawBillFieldEnum matchField, String fieldDesc) {
        //改为小驼峰命名，用于配置解析规则
        this.fieldCode= CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL,matchField.getFieldCode());
        this.matchField = matchField;
        this.fieldDesc = fieldDesc;
    }

    public static List<TreeVO> listTreeVO() {
        return Arrays.stream(AlipayMatchFieldEnum.values()).map(
                x -> {
                    TreeVO treeVO = new TreeVO();
                    treeVO.setLabel(x.getFieldDesc());
                    treeVO.setValue(x.getFieldCode());
                    return treeVO;
                }
        ).collect(Collectors.toList());
    }
}
