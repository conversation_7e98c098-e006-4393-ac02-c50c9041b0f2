package com.raycloud.dmj.account.core.tj.vo;

import lombok.Data;

/**
 * 表规则Vo类
 */
@Data
public class FileSheetTableConfigVo {

    /**
     * 【字段名】自定义字段编码
     */
    private String tableField;

    /**
     * 【字段取值】字段取值公式（&{xx}当前行 #{}用户入参 ${}表格的Head取）
     */
    private String formula;

    /**
     * 【校验器】校验器的code
     */
    private String checker;

    /**
     * 【校验器的入参】自定义输入，自定义参数
     */
    private String value;

    /**
     * 【取值类型】取值范围   3 ：自定义公式
     */
    private String sourceType;

    /**
     * 【转换器】转换器的code
     */
    private String translator;

    /**
     * 【转换器的入参】自定义输入，自定义参数
     */
    private String transValue;

    /**
     * 【是否存储】取值范围  true: 存储，false: 不存储； 默认为true
     */
    private String bePutStorage;

}
