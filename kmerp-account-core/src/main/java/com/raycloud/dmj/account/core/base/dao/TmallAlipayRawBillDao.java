package com.raycloud.dmj.account.core.base.dao;

import com.raycloud.dmj.account.core.common.Page;
import com.raycloud.dmj.account.core.cleancategory.domain.object.TmallAlipayRawBillDataDO;

import java.util.List;


/**
 * 天猫支付宝账单原始数据表
 * <AUTHOR>
 */
public interface TmallAlipayRawBillDao {
    /**
     * 根据批次号分页查询天猫支付宝账单
     *
     * @param dataRange 账期
     * @return 天猫支付宝账单
     */
    List<TmallAlipayRawBillDataDO> listPageByDataRange(Long companyId, Long shopId, Integer dataRange, Page page);



    /**
     * 根据批次号查询该批次最早的一条数据
     * @param companyId 公司 ID
     * @param shopId 店铺 ID
     * @param dataRange 账期
     * @return 天猫支付宝账单
     */
    TmallAlipayRawBillDataDO getEarliestByDataRange(Long companyId, Long shopId, Integer dataRange);



    /**
     * 根据批次号查询该批次最晚的一条数据
     * @param companyId 公司 ID
     * @param shopId 店铺 ID
     * @param dataRange 账期
     * @return 最晚的一条数据
     */
    TmallAlipayRawBillDataDO getLatestByDataRange(Long companyId, Long shopId, Integer dataRange);


}
