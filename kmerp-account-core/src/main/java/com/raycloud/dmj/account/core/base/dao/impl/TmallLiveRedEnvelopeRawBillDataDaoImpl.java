package com.raycloud.dmj.account.core.base.dao.impl;

import com.raycloud.dmj.account.core.base.dao.TmallLiveRedEnvelopeRawBillDataDao;
import com.raycloud.dmj.account.core.cleancategory.domain.TmallLiveRedEnvelopeRawBillDataDTO;
import com.raycloud.dmj.account.core.common.Page;
import com.raycloud.dmj.account.core.enums.field.TmallLiveRedEnvelopeRawBillFieldEnum;
import com.raycloud.dmj.account.core.enums.field.TmallLiveRedEnvelopeSumRawBillFieldEnum;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import com.raycloud.dmj.table.api.plus.common.base.SQL;
import com.raycloud.dmj.table.api.plus.common.enums.LinkMode;
import com.raycloud.dmj.table.api.plus.component.column.utils.Columns;
import com.raycloud.dmj.table.api.plus.component.condition.utils.Conditions;
import com.raycloud.dmj.table.api.plus.query.Queries;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class TmallLiveRedEnvelopeRawBillDataDaoImpl extends BaseDao implements TmallLiveRedEnvelopeRawBillDataDao {


    private final String TABLE_NAME = "tmall_live_red_envelope_raw_bill_data";


    private final String SUM_TABLE_NAME = "tmall_live_red_envelope_sum_raw_bill_data";



    @Override
    public List<TmallLiveRedEnvelopeRawBillDataDTO> listPageByBatchTime(Long companyId, Long shopId, Integer batchTime, Page page) {
        AsserUtils.notNull(companyId, "公司ID不能为空！");
        AsserUtils.notNull(shopId, "店铺ID不能为空！");
        AsserUtils.notNull(batchTime, "批次时间不能为空！");
        SQL sql = Queries.create()
                .from(TABLE_NAME, "a")
                .join(SUM_TABLE_NAME, "b")
                .on(
                        Conditions.and(
                                Columns.create(TmallLiveRedEnvelopeRawBillFieldEnum.SERIAL_NO.getFieldCode()).referenceTableAlias("a"),
                                LinkMode.EQUAL,
                                Columns.create(TmallLiveRedEnvelopeSumRawBillFieldEnum.SERIAL_NO.getFieldCode()).referenceTableAlias("b")
                                ),
                        Conditions.and(
                                Columns.create(TmallLiveRedEnvelopeRawBillFieldEnum.COMPANY_ID.getFieldCode()).referenceTableAlias("a"),
                                LinkMode.EQUAL,
                                Columns.create(TmallLiveRedEnvelopeSumRawBillFieldEnum.COMPANY_ID.getFieldCode()).referenceTableAlias("b")
                        )
                )
                .where(
                        Conditions.and(Columns.toColumn(TmallLiveRedEnvelopeRawBillFieldEnum.COMPANY_ID.getFieldCode()).referenceTableAlias("a"), LinkMode.EQUAL, companyId),
                        Conditions.and(Columns.toColumn(TmallLiveRedEnvelopeRawBillFieldEnum.SHOP_ID.getFieldCode()).referenceTableAlias("a"), LinkMode.EQUAL, shopId),
                        Conditions.and(Columns.toColumn(TmallLiveRedEnvelopeRawBillFieldEnum.BATCH_TIME.getFieldCode()).referenceTableAlias("a"), LinkMode.EQUAL, batchTime)
                )
                .select(
                        Columns.toColumn(TmallLiveRedEnvelopeRawBillFieldEnum.TAOBAO_MAIN_ORDER_NO.getFieldCode()).referenceTableAlias("a").alias(TmallLiveRedEnvelopeRawBillFieldEnum.TAOBAO_MAIN_ORDER_NO.getFieldCode()),
                        Columns.toColumn(TmallLiveRedEnvelopeRawBillFieldEnum.ORDER_CONFIRM_RECEIVE_TIME.getFieldCode()).referenceTableAlias("a").alias(TmallLiveRedEnvelopeRawBillFieldEnum.ORDER_CONFIRM_RECEIVE_TIME.getFieldCode()),
                        Columns.toColumn(TmallLiveRedEnvelopeRawBillFieldEnum.RED_ENVELOPE_TYPE.getFieldCode()).referenceTableAlias("a").alias(TmallLiveRedEnvelopeRawBillFieldEnum.RED_ENVELOPE_TYPE.getFieldCode()),
                        Columns.toColumn(TmallLiveRedEnvelopeRawBillFieldEnum.RED_ENVELOPE_FLOW_AMOUNT.getFieldCode()).referenceTableAlias("a").alias(TmallLiveRedEnvelopeRawBillFieldEnum.RED_ENVELOPE_FLOW_AMOUNT.getFieldCode()),
                        Columns.toColumn(TmallLiveRedEnvelopeRawBillFieldEnum.REMARK.getFieldCode()).referenceTableAlias("a").alias(TmallLiveRedEnvelopeRawBillFieldEnum.REMARK.getFieldCode()),
                        Columns.toColumn(TmallLiveRedEnvelopeRawBillFieldEnum.BIZ_KEY.getFieldCode()).referenceTableAlias("a").alias(TmallLiveRedEnvelopeRawBillFieldEnum.BIZ_KEY.getFieldCode()),
                        Columns.toColumn(TmallLiveRedEnvelopeRawBillFieldEnum.BATCH_NO.getFieldCode()).referenceTableAlias("a").alias(TmallLiveRedEnvelopeRawBillFieldEnum.BATCH_NO.getFieldCode()),
                        Columns.toColumn(TmallLiveRedEnvelopeSumRawBillFieldEnum.BILLING_MONTH.getFieldCode()).referenceTableAlias("b").alias(TmallLiveRedEnvelopeSumRawBillFieldEnum.BILLING_MONTH.getFieldCode())
                )
                .page(page.getPageNo(), page.getPageSize())
                .toSql();
        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
        List<TmallLiveRedEnvelopeRawBillDataDTO> query = jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(TmallLiveRedEnvelopeRawBillDataDTO.class), args);
        return !query.isEmpty() ? query : null;

    }
}
