package com.raycloud.dmj.account.core.rawdata.handle.param;

import com.raycloud.dmj.account.core.enums.RawDataSourceEnum;
import com.raycloud.dmj.account.core.rawdata.domains.FileOriginalDataMonitorDO;
import com.raycloud.dmj.account.core.rawdata.domains.FileSheetCountInfo;
import com.raycloud.dmj.account.core.rawdata.domains.FilterConfig;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;


/**
 * <AUTHOR>
 * 操作数据库相关的类
 */
@Data
public class OperateBatchDbInfo {


    /**
     * 公司ID
     */
    private Long companyId;

    /**
     * 店铺ID
     */
    private Long shopId;
    /**
     * 同一批文件一组
     */
    private String groupCode;
    /**
     * 分库分表后的表名
     */
    private String tableName;

    /**
     * 数据源类型
     */
    private String dataSourceType;

    /**
     * 过滤配置
     */
    private FilterConfig filterConfig;


    /**
     * 数据源标识
     */
    private String dataSourceCode;

    /**
     * 表字段
     */
    Set<String> tableFields;

    /**
     * 当前批次的数据
     */
    private List<Map<String, Object>> batcDataList;


    /**
     * 当前批次数据对应的时间
     */
    private Date batchDataTime;

    /**
     * 时间字段
     */
    private String dataTimeField;


    /**
     * 时间类型，默认为1，1为日，2为月，3为年
     */
    private Integer dateType;


    /**
     * sheet索引
     */
    private Integer sheetIndex;


    /**
     * 基础表名
     */
    private String basicTableName;

    /**
     * 当前批次监控表信息
     */
    private FileOriginalDataMonitorDO fileOriginalDataMonitorDO;



    /**
     * 文件解析统计数据
     */
    private FileSheetCountInfo fileSheetCountInfo;


    /**
     * 批次号字段
     */
    private String batchField;

    /**
     * 批次号
     */
    private String batchNo;

    /**
     * 数据来源
     * @see RawDataSourceEnum
     */
    private Integer dataSource;
}
