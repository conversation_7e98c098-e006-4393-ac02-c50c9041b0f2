package com.raycloud.dmj.account.core.enums;

import lombok.Getter;

/**
 * 时间类型枚举
 * 对应数值：1=日，2=月，3=年
 * <AUTHOR>
 */
@Getter
public enum DateTypeEnum {
    DAY(1, "日","yyyy-MM-dd"),
    MONTH(2, "月","yyyy-MM"),
    YEAR(3, "年","yyyy");

    private final Integer code;
    private final String description;
    private final String patter;

    DateTypeEnum(Integer code, String description,String patter) {
        this.code = code;
        this.description = description;
        this.patter = patter;
    }


    /**
     * 根据数值查找对应的枚举
     * @param code 数值
     * @return 对应的枚举，若未找到则返回null
     */
    public static DateTypeEnum of(Integer code) {
        for (DateTypeEnum type : values()) {
            if (type.getCode().equals( code)) {
                return type;
            }
        }
        return null;
    }

}