package com.raycloud.dmj.account.core.platform.dao.impl;

import com.mysql.jdbc.Statement;
import com.raycloud.dmj.account.core.base.dao.impl.BaseDao;
import com.raycloud.dmj.account.core.enums.field.ShopInfoFieldEnum;
import com.raycloud.dmj.account.core.platform.base.domain.ShopAuthRecordDO;
import com.raycloud.dmj.account.core.platform.base.domain.enums.ShopAuthRecordFieldEnum;
import com.raycloud.dmj.account.core.platform.dao.ShopAuthRecordDao;
import com.raycloud.dmj.account.infra.common.BusinessException;
import com.raycloud.dmj.account.infra.common.ErrorCodeEnum;
import com.raycloud.dmj.table.api.plus.common.ColumnValue;
import com.raycloud.dmj.table.api.plus.common.base.SQL;
import com.raycloud.dmj.table.api.plus.common.enums.LinkMode;
import com.raycloud.dmj.table.api.plus.component.$;
import com.raycloud.dmj.table.api.plus.component.column.utils.Columns;
import com.raycloud.dmj.table.api.plus.component.condition.ConditionComponent;
import com.raycloud.dmj.table.api.plus.component.condition.utils.Conditions;
import com.raycloud.dmj.table.api.plus.insert.Inserts;
import com.raycloud.dmj.table.api.plus.query.Queries;
import com.raycloud.dmj.table.api.plus.update.Updates;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Service;

import java.sql.PreparedStatement;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class ShopAuthRecordDaoImpl extends BaseDao implements ShopAuthRecordDao {

    private static final String TABLE_NAME = "shop_auth_record";

    @Override
    public Long insert(ShopAuthRecordDO record) {
        SQL sql = Inserts.insert()
                .into(TABLE_NAME)
                .columns(ShopAuthRecordFieldEnum.getInsertFields())
                .valueForEntity(record)
                .columnNameCamelToUnderline()
                .toSql();

        KeyHolder keyHolder = new GeneratedKeyHolder();
        jdbcTemplate.update(
                connection -> {
                    PreparedStatement ps = connection.prepareStatement(
                            sql.getSqlCode(),
                            Statement.RETURN_GENERATED_KEYS
                    );
                    Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();
                    for (int i = 0; i < args.length; i++) {
                        ps.setObject(i + 1, args[i]);
                    }
                    return ps;
                },
                keyHolder
        );
        return Objects.requireNonNull(keyHolder.getKey()).longValue();
    }

    @Override
    public ShopAuthRecordDO getById(Long id) {
        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(getByIdCondition(id))
                .select()
                .toSql();

        List<ShopAuthRecordDO> records = jdbcTemplate.query(
                sql.getSqlCode(),
                new BeanPropertyRowMapper<>(ShopAuthRecordDO.class),
                sql.getArgs().toArray()
        );
        return records.isEmpty() ? null : records.get(0);
    }

    @Override
    public int updateById(ShopAuthRecordDO record) {
        // 先查询现有记录
        ShopAuthRecordDO existingRecord = getById(record.getId());
        if (existingRecord == null) {
            throw new BusinessException(ErrorCodeEnum.DB_ERROR, "shop_auth_record update error");
        }

        List<ColumnValue> columnValues = new ArrayList<>();
        if (record.getShopId() != null) {
            columnValues.add($.updateKeyValue(ShopAuthRecordFieldEnum.SHOP_ID.getFieldCode(), record.getShopId()));
        }
        if (record.getCompanyId() != null) {
            columnValues.add($.updateKeyValue(ShopAuthRecordFieldEnum.COMPANY_ID.getFieldCode(), record.getCompanyId()));
        }
        if (record.getPlatformCode() != null) {
            columnValues.add($.updateKeyValue(ShopAuthRecordFieldEnum.PLATFORM_CODE.getFieldCode(), record.getPlatformCode()));
        }
        if (record.getCallBackUrl() != null) {
            columnValues.add($.updateKeyValue(ShopAuthRecordFieldEnum.CALL_BACK_URL.getFieldCode(), record.getCallBackUrl()));
        }
        if (record.getAuthStatus() != null) {
            columnValues.add($.updateKeyValue(ShopAuthRecordFieldEnum.AUTH_STATUS.getFieldCode(), record.getAuthStatus()));
        }
        if (record.getModified() != null) {
            columnValues.add($.updateKeyValue(ShopAuthRecordFieldEnum.MODIFIED.getFieldCode(), record.getModified()));
        }
        SQL sql = Updates.create()
                .table(TABLE_NAME)
                .where(Conditions.and(Columns.toColumn(ShopInfoFieldEnum.ID.getFieldCode()), LinkMode.EQUAL, record.getId()))
                .update(
                       columnValues
                ).toSql();
        return jdbcTemplate.update(sql.getSqlCode(), sql.getArgs().toArray());
    }

    private List<ConditionComponent<?>> getByIdCondition(Long id) {
        List<ConditionComponent<?>> conditions = new ArrayList<>();
        conditions.add(Conditions.and(
                Columns.toColumn(ShopAuthRecordFieldEnum.ID.getFieldCode()),
                LinkMode.EQUAL,
                id
        ));
        return conditions;
    }
}
