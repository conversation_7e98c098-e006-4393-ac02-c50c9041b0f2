package com.raycloud.dmj.account.core.cleancategory.domain.request;

import lombok.Data;

import java.io.Serializable;


/**
 * 添加资金账户
 * <AUTHOR>
 */
@Data
public class EditCategoryGroupReq implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 分类编码
     */
    private String categoryGroupCode;

    /**
     * 分类名称
     */
    private String categoryGroupName;

}
