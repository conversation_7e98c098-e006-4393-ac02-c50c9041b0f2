package com.raycloud.dmj.account.core.bill.params;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class RuleVerifyResultRecordParam {

    /**
     * 平台code
     */
    private String  platformCode;

    /**
     * 店铺ID
     */
    private List<Long> shopIdList;

    /**
     * 帐期开始时间
     */
    private Date startTime;

    /**
     * 帐期结束时间
     */
    private Date endTime;

    /**
     * 帐期类型 1:日账单 2:月账单
     */
    private Integer billingCycleType;

}
