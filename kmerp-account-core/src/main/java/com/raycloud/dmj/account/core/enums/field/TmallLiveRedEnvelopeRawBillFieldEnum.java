package com.raycloud.dmj.account.core.enums.field;

import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Getter
public enum TmallLiveRedEnvelopeRawBillFieldEnum {
    ID("id", "主键ID"),
    TAOBAO_MAIN_ORDER_NO("taobao_main_order_no", "淘宝主订单号"),
    REFUND_OPERATION_NO("refund_operation_no", "退款操作单号"),
    SHOP_NAME("shop_name", "店铺名称"),
    ANCHOR_NICKNAME("anchor_nickname", "主播昵称"),
    RECHARGE_ACCOUNT_NICKNAME("recharge_account_nickname", "充值账号昵称"),
    ORDER_CREATE_TIME("order_create_time", "订单创建时间"),
    ORDER_PAY_TIME("order_pay_time", "订单支付时间"),
    ORDER_CONFIRM_RECEIVE_TIME("order_confirm_receive_time", "订单确认收货时间"),
    ORDER_REFUND_TIME("order_refund_time", "订单退款时间"),
    ORDER_FLOW_TYPE("order_flow_type", "订单流水类型"),
    ORDER_REFUND_TYPE("order_refund_type", "订单退款类型"),
    RED_ENVELOPE_TYPE("red_envelope_type", "红包类型"),
    RED_ENVELOPE_FLOW_AMOUNT("red_envelope_flow_amount", "红包流水金额"),
    INCOME_EXPENSE_TYPE("income_expense_type", "收支类型"),
    REMARK("remark", "备注"),
    BIZ_KEY("biz_key", "唯一键"),
    BATCH_NO("batch_no", "批次号"),
    BATCH_TIME("batch_time", "批次时间 格式如********"),
    COMPANY_ID("company_id", "租户ID"),
    SHOP_ID("shop_id", "店铺ID"),
    CREATED("created", "创建时间"),
    SERIAL_NO("serial_no", "与汇总表关联字段");

    /**
     * 字段编码
     */
    private final String fieldCode;

    /**
     * 字段描述
     */
    private final String fieldDesc;

    TmallLiveRedEnvelopeRawBillFieldEnum(String fieldCode, String fieldDesc) {
        this.fieldCode = fieldCode;
        this.fieldDesc = fieldDesc;
    }

    /**
     * 获取插入字段
     * @return 插入字段
     */
    public static Set<String> getInsertFields() {
        Set<TmallLiveRedEnvelopeRawBillFieldEnum> filterSet = Collections.singleton(TmallLiveRedEnvelopeRawBillFieldEnum.ID);
        return Arrays.stream(TmallLiveRedEnvelopeRawBillFieldEnum.values())
                .filter(x ->!filterSet.contains(x))
                .map(x -> x.fieldCode)
                .collect(Collectors.toSet());
    }
}