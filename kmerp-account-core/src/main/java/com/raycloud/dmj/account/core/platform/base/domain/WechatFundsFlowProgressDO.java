package com.raycloud.dmj.account.core.platform.base.domain;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
public class WechatFundsFlowProgressDO {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 公司ID
     */
    private Long companyId;

    /**
     * 分页参数，为空则表示overTime的账单已完成
     */
    private String nextKey;


    private Integer pageNo;

    /**
     * 最近一次结束时间，获取时间为当前日期+1
     */
    private LocalDate overTime;

    /**
     * 账单暂存文件名
     */
    private String objectName;

    /**
     * 创建时间
     */
    private LocalDateTime created;

    /**
     * 修改时间
     */
    private LocalDateTime modified;
}
