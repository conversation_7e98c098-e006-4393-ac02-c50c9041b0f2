package com.raycloud.dmj.account.core.bill.service.impl;

import com.alibaba.fastjson2.JSON;
import com.raycloud.dmj.account.core.base.dao.*;
import com.raycloud.dmj.account.core.base.domain.BillSummaryRecordDO;
import com.raycloud.dmj.account.core.base.domain.BillVerifyRuleDO;
import com.raycloud.dmj.account.core.base.domain.RuleVerifyResultRecordDO;
import com.raycloud.dmj.account.core.base.domain.ShopInfoDO;
import com.raycloud.dmj.account.core.bill.dto.VerifyRuleDTO;
import com.raycloud.dmj.account.core.bill.params.RuleVerifyResultRecordParam;
import com.raycloud.dmj.account.core.bill.request.BillVerifyRuleRequest;
import com.raycloud.dmj.account.core.bill.request.RuleVerifyResultRecordRequest;
import com.raycloud.dmj.account.core.bill.service.IRuleVerifyResultRecordService;
import com.raycloud.dmj.account.core.bill.utils.VerifyRuleUtils;
import com.raycloud.dmj.account.core.bill.vo.RuleVerifyResultDetailVO;
import com.raycloud.dmj.account.core.bill.vo.RuleVerifyResultVo;
import com.raycloud.dmj.account.core.cleancategory.domain.object.FundAccountDO;
import com.raycloud.dmj.account.core.enums.CommonStatusEnum;
import com.raycloud.dmj.account.core.enums.DateTypeEnum;
import com.raycloud.dmj.account.core.enums.OperatorTypeEnum;
import com.raycloud.dmj.account.core.rawdata.utils.RawDataDateUtil;
import com.raycloud.dmj.account.core.shop.req.ShopInfoRequest;
import com.raycloud.dmj.account.infra.common.BusinessException;
import com.raycloud.dmj.account.infra.common.ErrorCodeEnum;
import com.raycloud.dmj.account.infra.session.AccountUser;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import com.raycloud.dmj.account.infra.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 规则校验结果记录服务实现类
 * <AUTHOR>
 */
@Slf4j
@Service
public class RuleVerifyResultRecordServiceImpl implements IRuleVerifyResultRecordService {

    protected final Logger logger = Logger.getLogger(this.getClass());

    @Resource
    private RuleVerifyResultRecordDao ruleVerifyResultRecordDao;
    @Resource
    private BillVerifyRuleDao billVerifyRuleDao;
    @Resource
    private FundAccountDao accountDao;
    @Resource
    private ShopInfoDao shopInfoDao;
    @Resource
    private BillSummaryRecordDao billSummaryRecordDao;

    @Override
    public Long addRuleVerifyResultRecord(RuleVerifyResultRecordDO record, AccountUser accountUser) {
        AsserUtils.notNull(accountUser, "用户不能为空");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空");
        AsserUtils.notNull(record, "校验结果记录不能为空");
        AsserUtils.notNull(record.getRuleId(), "校验规则ID不能为空");
        AsserUtils.notNull(record.getAmount(), "校验结果金额不能为空");
        AsserUtils.notNull(record.getStatus(), "校验结果状态不能为空");
        AsserUtils.notNull(record.getVerifyTime(), "校验日期不能为空");

        record.setCompanyId(accountUser.getCompanyId());

        return ruleVerifyResultRecordDao.insert(record);
    }

    @Override
    public List<RuleVerifyResultDetailVO> getRuleVerifyResultRecordList(RuleVerifyResultRecordRequest request, AccountUser accountUser) {
        // 参数校验
        AsserUtils.notNull(accountUser, "用户不能为空");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空");
        AsserUtils.notNull(request, "查询参数不能为空");
        AsserUtils.hasText(request.getPlatformCode(),"请选择平台");

        RuleVerifyResultRecordParam param = convertRequestParams(request);

        //根据平台查询店铺信息
        ShopInfoRequest shopInfoRequest = ShopInfoRequest.builder()
                .idList(param.getShopIdList())
                .platformCode(param.getPlatformCode())
                .build();
        List<ShopInfoDO> shopInfoDOList = shopInfoDao.getShopList(shopInfoRequest, accountUser.getCompanyId());
        AsserUtils.notEmpty(shopInfoDOList, "该平台下不存在店铺信息");
        //获取店铺ID
        List<Long> shopIdList = shopInfoDOList.stream().map(ShopInfoDO::getId).collect(Collectors.toList());

        List<RuleVerifyResultDetailVO> voList = new ArrayList<>();
        // 查询对应店铺下的规则ID信息
        BillVerifyRuleRequest ruleRequest = new BillVerifyRuleRequest();
        ruleRequest.setShopIdList(shopIdList);
        List<BillVerifyRuleDO> ruleDOList = billVerifyRuleDao.queryByParam(ruleRequest, accountUser.getCompanyId());
        if (CollectionUtils.isEmpty(ruleDOList)) {
            return voList;
        }

        // 获取规则ID集合
        List<Long> ruleIdList = ruleDOList.stream().map(BillVerifyRuleDO::getId).collect(Collectors.toList());

        // 查询校验结果记录
        List<RuleVerifyResultRecordDO> recordDOList = ruleVerifyResultRecordDao.queryByParam(ruleIdList, param.getStartTime(), param.getEndTime(), accountUser.getCompanyId());

        // 按规则和时间维度生成结果
        voList = generateTimeBasedResults(ruleDOList, recordDOList, param);

        return voList;
    }

    private RuleVerifyResultRecordParam convertRequestParams(RuleVerifyResultRecordRequest request) {
        RuleVerifyResultRecordParam param = new RuleVerifyResultRecordParam();
        param.setShopIdList(request.getShopIdList());
        param.setPlatformCode(request.getPlatformCode());

        if (request.getStartTime() != null && request.getEndTime() != null) {
            param.setBillingCycleType(request.getBillingCycleType());

            DateTypeEnum dateTypeEnum = DateTypeEnum.of(request.getBillingCycleType());
            AsserUtils.notNull(dateTypeEnum,"异常时间类型");
            param.setStartTime(DateUtils.parse(request.getStartTime(),dateTypeEnum.getPatter()));
            param.setEndTime(DateUtils.parseEndTime(request.getEndTime(),dateTypeEnum.getPatter(),dateTypeEnum.getCode()));
        }else {
            //设置默认值
            param.setBillingCycleType(DateTypeEnum.DAY.getCode());
            //设置默认查询时间区间为30天
            param.setStartTime(DateUtils.getDayStartTime(DateUtils.getOffsetDay(new Date(), -30)));
            param.setEndTime(DateUtils.getDayEndTime(new Date()));
        }

        return param;

    }

    @Override
    public List<RuleVerifyResultRecordDO> getRuleVerifyResultRecordByRuleId(Long ruleId, AccountUser accountUser) {
        AsserUtils.notNull(ruleId, "规则ID不能为空");
        AsserUtils.notNull(accountUser, "用户不能为空");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空");

        return ruleVerifyResultRecordDao.queryByRuleId(ruleId, accountUser.getCompanyId());
    }

    @Override
    public int batchAddRuleVerifyResultRecord(List<RuleVerifyResultRecordDO> recordList, AccountUser accountUser) {
        AsserUtils.notNull(accountUser, "用户不能为空");
        AsserUtils.notNull(accountUser.getCompanyId(), "公司ID不能为空");
        
        if (CollectionUtils.isEmpty(recordList)) {
            return 0;
        }

        Date now = new Date();
        for (RuleVerifyResultRecordDO record : recordList) {
            record.setCompanyId(accountUser.getCompanyId());
            record.setCreated(now);
            record.setModified(now);
            
            if (record.getEnableStatus() == null) {
                record.setEnableStatus(1);
            }
        }

        return ruleVerifyResultRecordDao.batchInsert(recordList);
    }

    @Override
    public void verifyRule(Long shopId, Integer billingCycle, Integer billingCycleType, Long companyId) {
        AsserUtils.notNull(shopId, "资金账户ID不能为空");
        AsserUtils.notNull(billingCycle,"帐期时间不能为空");
        AsserUtils.notNull(billingCycleType,"帐期类型不能为空");
        AsserUtils.notNull(companyId,"公司ID不能为空");

        List<BillVerifyRuleDO> ruleList = billVerifyRuleDao.queryListByAccountId(shopId, companyId);
        if (CollectionUtils.isEmpty(ruleList)) {
            logger.error(String.format("店铺:%s没有配置校验规则)",shopId));
            return;
        }

        //过滤掉当前时间没生效的规则
        ruleList = ruleList.stream().filter(rule -> DateUtils.isInRange(new Date(),rule.getEffectiveTime(),rule.getInvalidTime()) && rule.getStatus().equals(CommonStatusEnum.ON.getType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ruleList)) {
            logger.error(String.format("店铺:%s没有配置有效的校验规则)",shopId));
            return;
        }

        //帐期
        Date date = RawDataDateUtil.parseDate(String.valueOf(billingCycle), DateTypeEnum.of(billingCycleType));
        //查询对应帐期下是否存在校验数据，如果存在则不在重复校验
        List<Long> rlueIdList = ruleList.stream().map(BillVerifyRuleDO::getId).collect(Collectors.toList());
        List<RuleVerifyResultRecordDO> recordDOList = ruleVerifyResultRecordDao.queryRuleList(rlueIdList, date, companyId);
        if (CollectionUtils.isNotEmpty(recordDOList)) {
            logger.info(String.format("店铺:%s,已存在规则校验记录，不重复校验,账期:%s",shopId,billingCycle));
            return;
        }

        //获取对应帐内的分类统计数据
        List<BillSummaryRecordDO> billSummaryRecordDOList = billSummaryRecordDao.queryByShopAndCycle(shopId,date, companyId);
        if (CollectionUtils.isEmpty(billSummaryRecordDOList)) {
            logger.error(String.format("店铺:%s没有分类统计数据)",shopId));
            return;
        }
        //获取合计数据中的分类以及合计金额
        Map<String, BigDecimal> subCategoryAmountMap = billSummaryRecordDOList.stream()
                .collect(Collectors.toMap(
                        // 将 subCategoryId 转换为 String 类型
                        record -> String.valueOf(record.getSubCategoryId()),
                        BillSummaryRecordDO::getAmount,
                        // 处理键冲突的合并函数（这里选择保留后出现的值）
                        (existing, replacement) -> replacement
                ));

        //校验结果记录
        List<RuleVerifyResultRecordDO> resultRecordList = new ArrayList<>();
        ruleList.forEach(rule -> {
            RuleVerifyResultRecordDO record = new RuleVerifyResultRecordDO();
            //获取规则内容
            List<VerifyRuleDTO> ruleDOList = JSON.parseArray(rule.getRuleContent(), VerifyRuleDTO.class);

            //判断校验对应账户下的分类解析数据是否已入库
            if (!isDataReadyForVerification(ruleDOList,billSummaryRecordDOList)) {
                logger.error(String.format("店铺:%s,校验规则:%s,对应账户下的分类解析数据未到帐,不进行校验",shopId,rule.getRuleName()));
                return;
            }
            //获取表达式计算结果
            BigDecimal result = VerifyRuleUtils.evaluateExpression(ruleDOList, subCategoryAmountMap);
            //获取校验状态
            Integer verifyStatus = verifyAmountWithRule(result, rule);

            record.setRuleId(rule.getId());
            record.setAmount(result);
            record.setStatus(verifyStatus);
            record.setVerifyTime(date);
            record.setCompanyId(companyId);
            resultRecordList.add(record);
        });

        ruleVerifyResultRecordDao.batchInsert(resultRecordList);
        logger.info(String.format("店铺：%s,批量保存校验结果保存成功，保存数量条数：%s",shopId,resultRecordList.size()));
    }

    private boolean isDataReadyForVerification(List<VerifyRuleDTO> ruleDOList, List<BillSummaryRecordDO> billSummaryRecordDOList) {
        //过滤得到变量
        List<String> subCategoryIdList = ruleDOList.stream().filter(rule -> rule.getType().equals(OperatorTypeEnum.VARIABLE.getType())).map(VerifyRuleDTO::getValue).collect(Collectors.toList());
        //表达式中没有变量，则直接返回true
        if (CollectionUtils.isEmpty(subCategoryIdList)) {
            return true;
        }

        for (String subCategoryId : subCategoryIdList) {
            if (billSummaryRecordDOList.stream().noneMatch(record -> record.getSubCategoryId().equals(Long.valueOf(subCategoryId)))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 按时间维度生成校验结果
     * 日账单类型：取每天最新的一条记录
     * 月账单类型：计算当月所有记录的总和
     */
    private List<RuleVerifyResultDetailVO> generateTimeBasedResults(List<BillVerifyRuleDO> ruleDOList,
                                                                   List<RuleVerifyResultRecordDO> recordDOList,
                                                                   RuleVerifyResultRecordParam request) {
        List<RuleVerifyResultDetailVO> voList = new ArrayList<>();
        // 生成时间范围内的所有时间点
        List<String> timeKeys = generateTimeKeys(request.getStartTime(), request.getEndTime(), request.getBillingCycleType());
        // 按规则ID分组记录
        Map<Long, List<RuleVerifyResultRecordDO>> ruleRecordMap = recordDOList.stream()
                .collect(Collectors.groupingBy(RuleVerifyResultRecordDO::getRuleId));
        // 为每个规则生成时间维度的结果
        for (BillVerifyRuleDO rule : ruleDOList) {
            RuleVerifyResultDetailVO detailVO = new RuleVerifyResultDetailVO();
            detailVO.setRuleId(rule.getId());
            detailVO.setRuleName(rule.getRuleName());
            detailVO.setShopId(rule.getShopId());
            // 获取该规则的所有记录
            List<RuleVerifyResultRecordDO> ruleRecords = ruleRecordMap.getOrDefault(rule.getId(), new ArrayList<>());
            // 按时间分组该规则的记录
            Map<String, List<RuleVerifyResultRecordDO>> timeRecordMap = ruleRecords.stream()
                    .collect(Collectors.groupingBy(record -> formatTimeKey(record.getVerifyTime(), request.getBillingCycleType())));

            // 为每个时间点生成结果
            List<RuleVerifyResultVo> verifyResultList = new ArrayList<>();
            for (String timeKey : timeKeys) {
                RuleVerifyResultVo vo = new RuleVerifyResultVo();
                vo.setBillingCycleStr(timeKey);
                List<RuleVerifyResultRecordDO> timeRecords = timeRecordMap.get(timeKey);
                if (CollectionUtils.isNotEmpty(timeRecords)) {
                    if (DateTypeEnum.MONTH.getCode().equals(request.getBillingCycleType())) {
                        // 月账单类型：计算当月所有日账单记录的总和
                        // 例如：2024-01月的所有日账单记录（2024-01-01, 2024-01-02, ... 2024-01-31）进行求和
                        BigDecimal totalAmount = timeRecords.stream()
                                .map(RuleVerifyResultRecordDO::getAmount)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);

                        // 根据规则校验金额和判断方式来确定状态
                        Integer verifyStatus = verifyAmountWithRule(totalAmount, rule);
                        vo.setStatus(verifyStatus);
                        vo.setAmount(totalAmount.stripTrailingZeros());
                    } else {
                        // 日账单类型：取当天最新的一条记录
                        RuleVerifyResultRecordDO latestRecord = timeRecords.stream()
                                .max(Comparator.comparing(RuleVerifyResultRecordDO::getVerifyTime))
                                .orElse(null);

                        if (latestRecord != null) {
                            vo.setAmount(latestRecord.getAmount().stripTrailingZeros());
                            vo.setStatus(latestRecord.getStatus());
                        } else {
                            vo.setAmount(BigDecimal.ZERO);
                            vo.setStatus(1); // 通过
                        }
                    }
                } else {
                    // 无记录，设置默认值
                    BigDecimal zeroAmount = BigDecimal.ZERO;
                    if (DateTypeEnum.MONTH.getCode().equals(request.getBillingCycleType())) {
                        // 月账单类型：即使无记录也要根据规则判断
                        Integer verifyStatus = verifyAmountWithRule(zeroAmount, rule);
                        vo.setAmount(zeroAmount);
                        vo.setStatus(verifyStatus);
                    } else {
                        // 日账单类型：无记录默认通过
                        vo.setAmount(zeroAmount);
                        vo.setStatus(1); // 通过
                    }
                }

                verifyResultList.add(vo);
            }

            detailVO.setVerifyResultList(verifyResultList);
            voList.add(detailVO);
        }

        return voList;
    }

    /**
     * 生成时间范围内的所有时间点
     */
    private List<String> generateTimeKeys(Date startTime, Date endTime, Integer dateType) {
        List<String> timeKeys = new ArrayList<>();
        SimpleDateFormat formatter;
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startTime);

        if (DateTypeEnum.DAY.getCode().equals(dateType)) {
            formatter = new SimpleDateFormat("yyyy-MM-dd");
            while (!calendar.getTime().after(endTime)) {
                timeKeys.add(formatter.format(calendar.getTime()));
                calendar.add(Calendar.DAY_OF_MONTH, 1);
            }
        } else if (DateTypeEnum.MONTH.getCode().equals(dateType)) {
            formatter = new SimpleDateFormat("yyyy-MM");
            while (!calendar.getTime().after(endTime)) {
                timeKeys.add(formatter.format(calendar.getTime()));
                calendar.add(Calendar.MONTH, 1);
            }
        }

        return timeKeys;
    }

    /**
     * 格式化时间为时间键
     */
    private String formatTimeKey(Date date, Integer dateType) {
        SimpleDateFormat formatter;
        if (DateTypeEnum.DAY.getCode().equals(dateType)) {
            formatter = new SimpleDateFormat(DateTypeEnum.DAY.getPatter());
        } else {
            formatter = new SimpleDateFormat(DateTypeEnum.MONTH.getPatter());
        }
        return formatter.format(date);
    }

    /**
     * 根据规则校验金额
     * @param actualAmount 实际金额
     * @param rule 校验规则
     * @return 1:通过 -1:异常
     */
    private Integer verifyAmountWithRule(BigDecimal actualAmount, BillVerifyRuleDO rule) {
        if (rule.getAmount() == null || rule.getJudgmentType() == null) {
            return 1; // 规则不完整，默认通过
        }

        int compareResult = rule.getAmount().compareTo(actualAmount);

        // 判断方式：1大于，2等于，3小于
        switch (rule.getJudgmentType()) {
            case 1: // 大于：规则金额 > 实际金额 才通过
                return compareResult > 0 ? 1 : -1;
            case 2: // 等于：规则金额 = 实际金额 才通过
                return compareResult == 0 ? 1 : -1;
            case 3: // 小于：规则金额 < 实际金额 才通过
                return compareResult < 0 ? 1 : -1;
            default:
                return -1; // 未知判断方式，默认通过
        }
    }
}
