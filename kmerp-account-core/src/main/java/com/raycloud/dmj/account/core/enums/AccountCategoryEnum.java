package com.raycloud.dmj.account.core.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * Date:  2025/8/4
 */

@Getter
public enum AccountCategoryEnum {

    /**
     * 线下账户
     */
    OFFLINE(1, "线下账户"),

    /**
     * 店铺账户
     */
    SHOP(2, "店铺账户"),

    /**
     * 自定义支付宝
     */
    CUSTOM_ALIPAY(3, "自定义支付宝"),
    ;

    private final Integer code;

    private final String desc;

    AccountCategoryEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
