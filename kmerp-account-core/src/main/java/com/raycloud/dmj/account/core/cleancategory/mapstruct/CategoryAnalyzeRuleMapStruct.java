package com.raycloud.dmj.account.core.cleancategory.mapstruct;

import com.raycloud.dmj.account.core.cleancategory.domain.object.BillAgainAnalyzeTaskDO;
import com.raycloud.dmj.account.core.cleancategory.domain.object.CategoryAnalyzeRuleDO;
import com.raycloud.dmj.account.core.cleancategory.domain.request.AddAnalyzeRuleReq;
import com.raycloud.dmj.account.core.cleancategory.domain.request.AgainAnalyzeReq;
import com.raycloud.dmj.account.core.cleancategory.domain.request.EditAnalyzeRuleReq;
import com.raycloud.dmj.account.core.cleancategory.domain.vo.BillAgainAnalyzeTaskVO;
import com.raycloud.dmj.account.core.enums.AgainAnalyzeStatusEnum;
import com.raycloud.dmj.account.core.enums.YesOrNoEnum;
import com.raycloud.dmj.account.infra.session.AccountUser;
import com.raycloud.dmj.account.infra.utils.DateUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.Date;
import java.util.HashMap;
import java.util.Optional;


/**
 *
 * 类目解析规则映射
 * <AUTHOR>
 */
@Mapper(imports = {YesOrNoEnum.class, Date.class, BaseConverter.class, AgainAnalyzeStatusEnum.class})
public interface CategoryAnalyzeRuleMapStruct {

    CategoryAnalyzeRuleMapStruct INSTANCE = Mappers.getMapper( CategoryAnalyzeRuleMapStruct.class );


    @Mapping(target ="created" , expression = "java(new Date())" )
    @Mapping(target ="modified" , expression = "java(new Date())" )
    @Mapping(target ="ruleCondition" , expression = "java(BaseConverter.convertToString(req.getAnalyzeRuleContent()))" )
    @Mapping(target = "enableStatus" , expression = "java( YesOrNoEnum.YES.getBoolean())")
    @Mapping(target = "deleted" , expression = "java(YesOrNoEnum.NO.getBoolean())")
    @Mapping(target = "companyId" , source = "accountUser.companyId")
    CategoryAnalyzeRuleDO toAnalyzeRuleDO(AccountUser accountUser, AddAnalyzeRuleReq req);


    @Mapping(target = "id" ,ignore = true)
    @Mapping(target ="created" , expression = "java(new Date())" )
    @Mapping(target ="modified" , expression = "java(new Date())" )
    @Mapping(target = "companyId" , source = "user.companyId")
    @Mapping(target ="ruleCondition" , expression = "java(BaseConverter.convertToString(req.getAnalyzeRuleContent()))" )
    @Mapping(target = "enableStatus" , expression = "java( YesOrNoEnum.YES.getBoolean())")
    @Mapping(target = "deleted" , expression = "java(YesOrNoEnum.NO.getBoolean())")
    CategoryAnalyzeRuleDO toAnalyzeRuleDO(AccountUser user, EditAnalyzeRuleReq req);


    @Mapping(target = "id" ,ignore = true)
    @Mapping(target ="created" , expression = "java(new Date())" )
    @Mapping(target ="modified" , expression = "java(new Date())" )
    @Mapping(target = "companyId" , source = "user.companyId")
    @Mapping(target ="status" , expression = "java(AgainAnalyzeStatusEnum.ANALYSIS.getStatus())")
    @Mapping(target = "reprocessStartTime" , source = "startTime")
    @Mapping(target = "reprocessEndTime" , source = "endTime")
    @Mapping(target = "version" , constant = "0")
    BillAgainAnalyzeTaskDO toBillAgainAnalyzeTaskDO(AccountUser user, AgainAnalyzeReq req,Date startTime,Date endTime);



    @Mapping(target = "fundAccountName" , expression = "java( fundAccountIdToNameMap.get(billAgainAnalyzeTaskDO.getFundAccountId()) )")
    @Mapping(target = "reprocessStartTime" , source = "billAgainAnalyzeTaskDO.reprocessStartTime")
    @Mapping(target = "reprocessEndTime" , source = "billAgainAnalyzeTaskDO.reprocessEndTime")
    @Mapping(target = "created" , source = "billAgainAnalyzeTaskDO.created")
    @Mapping(target = "completionDate" , source = "billAgainAnalyzeTaskDO.completionDate")
    @Mapping(target = "statusDesc" , expression = "java(CategoryAnalyzeRuleMapStruct.getStatusDesc(billAgainAnalyzeTaskDO.getStatus()))")
    BillAgainAnalyzeTaskVO toBillAgainAnalyzeTaskVO(HashMap<Long, String> fundAccountIdToNameMap,BillAgainAnalyzeTaskDO billAgainAnalyzeTaskDO);

    static String getStatusDesc(Integer status){
        return Optional.ofNullable(AgainAnalyzeStatusEnum.of(status))
                .map(AgainAnalyzeStatusEnum::getDesc).orElse(null);
    }

}
