package com.raycloud.dmj.account.core.bill.parameter;

import com.raycloud.dmj.account.core.common.Page;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
public class StandardOtherBillFlowParameter extends Page {

    /**
     * 帐期开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date cycleStartTime;

    /**
     * 帐期结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date cycleEndTime;

    /**
     * 发生开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 发生结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 平台类型集合
     */
    private List<String> platformCodeList;

    /**
     * 店铺ID集合
     */
    private List<String> shopIdList;

    /**
     * 流水子类别Code集合
     */
    private List<String> subCategoryCodeList;

    /**
     * 收支方向 1 收入 2 支出
     */
    private Integer incomeExpenseDirection;

    /**
     * 关联订单号集合
     */
    private List<String> orderNoList;

    /**
     * 备注（模糊查询）
     */
    private String remark;

    /**
     * 关联业务单据号
     */
    private String docNo;

    /**
     * 来源方式(1 账单,2 系统)
     */
    private Integer source;

    /**
     * 是否关联业务单据（0否 1是）
     */
    private Integer isRelatedDoc;

    /**
     * 页面ID
     */
    private Long pageId;
}
