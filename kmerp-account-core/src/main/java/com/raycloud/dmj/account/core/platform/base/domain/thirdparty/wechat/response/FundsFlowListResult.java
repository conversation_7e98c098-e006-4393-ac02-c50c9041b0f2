package com.raycloud.dmj.account.core.platform.base.domain.thirdparty.wechat.response;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * @Description: 资金流水列表响应结果
 * <AUTHOR>
 * @Date 2023/10/24 3:54 下午
 */
@Data
public class FundsFlowListResult extends WxBaseResult {

    @JSONField(name = "has_more")
    private Boolean hasMore;

    @JSONField(name = "next_key")
    private String nextKey;

    @JSONField(name = "flow_ids")
    private List<String> flowIds;

}
