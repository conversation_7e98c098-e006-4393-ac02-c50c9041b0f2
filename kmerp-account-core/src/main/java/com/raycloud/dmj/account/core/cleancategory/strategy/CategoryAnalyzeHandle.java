package com.raycloud.dmj.account.core.cleancategory.strategy;


import com.raycloud.dmj.account.core.base.dao.AnalyzeCategoryRecordDao;
import com.raycloud.dmj.account.core.cleancategory.domain.object.AnalyzeCategoryRecordDO;
import com.raycloud.dmj.account.core.cleancategory.domain.object.FundAccountDO;
import com.raycloud.dmj.account.core.cleancategory.strategy.param.CategoryAnalyzeParam;
import com.raycloud.dmj.account.core.enums.DateTypeEnum;
import com.raycloud.dmj.account.core.enums.RawDataSourceEnum;
import com.raycloud.dmj.account.core.enums.field.CategoryAnalyzeStatusEnum;
import com.raycloud.dmj.account.core.rawdata.manage.RawDataStorageManage;
import com.raycloud.dmj.account.core.rawdata.utils.RawDataDateUtil;
import com.raycloud.dmj.account.infra.common.BusinessException;
import com.raycloud.dmj.account.infra.common.ErrorCodeEnum;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Component
public interface CategoryAnalyzeHandle {


    /**
     * 未识别的分类Code
     */
     String DEFAULT_UNKNOWN_CATEGORY_CODE = "UNKNOWN";



    /**
     * 默认每次的解析数量
     */
    int DEFAULT_ANALYZE_SIZE = 5000;


    /**
     * 处理
     * @param param 参数
     */
    void handle(CategoryAnalyzeParam param);


    /**
     * 是否满足策略
     * @param source 数据源
     * @return 是否满足 true-满足 false-不满足
     */
    boolean accord(RawDataSourceEnum source);




    /**
     * 前置处理
     *
     * @param param 参数
     */
   default void paramVerify(CategoryAnalyzeParam param) {
        AsserUtils.notNull(param, "参数不能为空！");
        AsserUtils.notNull(param.getCompanyId(), "公司ID不能为空！");
        AsserUtils.notNull(param.getShopId(), "店铺ID不能为空！");
        AsserUtils.notNull(param.getDataRange(), "数据范围不能为空！");
        AsserUtils.notNull(param.getSource(), "数据源不能为空！");
        AsserUtils.notNull(param.getDateType(), "时间类型不能为空！");
    }

    /**
     * 幂等校验
     * @param analyzeCategoryRecordDO  解析记录
     * @return true:幂等校验通过
     */
    default  boolean idempotentVerify(AnalyzeCategoryRecordDO analyzeCategoryRecordDO) {
        if (Objects.isNull(analyzeCategoryRecordDO)){
            return false;
        }
        //解析
        if (CategoryAnalyzeStatusEnum .ANALYZING.getStatus().equals(analyzeCategoryRecordDO.getAnalyzeStatus())) {
            throw new BusinessException(ErrorCodeEnum.ANALYZE_ING, "账期【%S】分类解析中，请勿重复解析！", analyzeCategoryRecordDO.getDataRange());
        }
        //幂等判断
        return CategoryAnalyzeStatusEnum.ANALYZE_SUCCESS.getStatus().equals(analyzeCategoryRecordDO.getAnalyzeStatus());
    }

    /**
     * 校验数据是否导入成功
     */
    default void verifyDataIsImport(RawDataStorageManage rawDataStorageManage, CategoryAnalyzeParam param) {
        boolean isImport = rawDataStorageManage.checkDataIsImport(param.getCompanyId(), param.getShopId(), param.getSource(), param.getDataRange());
        if (!isImport){
            throw new BusinessException(ErrorCodeEnum.FILE_NOT_IMPORT_FINISH, "账期【%S】的原始数据未导入完成，无法解析！",param.getDataRange());
        }
    }

    /**
     * 校验上个账期是否解析成功
     * @param analyzeCategoryRecordDao  DAO
     * @param param 参数
     * @param fundAccountDO 资金账户信息
     */
    default void verifyPreviousDataRange(AnalyzeCategoryRecordDao analyzeCategoryRecordDao,CategoryAnalyzeParam param, FundAccountDO fundAccountDO) {
        Date startDate = fundAccountDO.getStartDate();
        Integer startDataRange = RawDataDateUtil.formatDate(startDate, param.getDateType());
        //如果不是期初的数据，则校验上一期数据是否到达 前一期的数据未解析完成，禁止解析
        if (!Objects.equals(startDataRange, param.getDataRange())) {
            //获取上一个账期
            Integer dataRange = param.getDataRange();
            Integer dateType = param.getDateType().getCode();
            Integer previousDataRange = RawDataDateUtil.getPreviousDataRange(dataRange, DateTypeEnum.of(dateType));
            //前一期的数据未解析完成，禁止解析
            AnalyzeCategoryRecordDO previousAnalyzeCategoryRecordDO = analyzeCategoryRecordDao.getByShopAndSourceAndDataRange(param.getCompanyId(), param.getShopId(), param.getSource().getCode(), previousDataRange);
            if (Objects.isNull(previousAnalyzeCategoryRecordDO) ||
                    !CategoryAnalyzeStatusEnum.ANALYZE_SUCCESS.getStatus().equals(previousAnalyzeCategoryRecordDO.getAnalyzeStatus())) {
                throw new BusinessException(ErrorCodeEnum.PARAM_ERROR.getCode(), String.format("【%S】的【%S】上个账期：【%S】未解析成功，无法继续解析！", param.getDataRange(), param.getSource().getDesc(), previousDataRange));
            }
        }
    }


    /**
     * 新增或修改解析记录
     * @param analyzeCategoryRecordDao 解析记录DAO
     * @param param 解析参数
     * @param analyzeCategoryRecordDO 解析记录
     * @return 新增或修改的记录ID 和 数据库当前的版本号
     */
    default AnalyzeCategoryRecordDO insertOrUpdateAnalyzeCategoryRecord(AnalyzeCategoryRecordDao analyzeCategoryRecordDao, CategoryAnalyzeParam param, AnalyzeCategoryRecordDO analyzeCategoryRecordDO){

        if (Objects.nonNull(analyzeCategoryRecordDO)){
            analyzeCategoryRecordDO.setAnalyzeStatus(CategoryAnalyzeStatusEnum.ANALYZING.getStatus());
            analyzeCategoryRecordDao.updateAnalyzeStatusById(analyzeCategoryRecordDO);
            //版本号加1 因为上面执行了update
            analyzeCategoryRecordDO.setVersion(analyzeCategoryRecordDO.getVersion() + 1);
            return analyzeCategoryRecordDO;
        }else {
            analyzeCategoryRecordDO = new AnalyzeCategoryRecordDO();
            analyzeCategoryRecordDO.setCompanyId(param.getCompanyId());
            analyzeCategoryRecordDO.setShopId(param.getShopId());
            analyzeCategoryRecordDO.setDataRange(param.getDataRange());
            analyzeCategoryRecordDO.setDataSource(param.getSource().getCode());
            analyzeCategoryRecordDO.setAnalyzeStatus(CategoryAnalyzeStatusEnum.ANALYZING.getStatus());
            analyzeCategoryRecordDO.setVersion(0);
            analyzeCategoryRecordDO.setDateType(param.getDateType().getCode());
            analyzeCategoryRecordDO.setCreated(new Date());
            analyzeCategoryRecordDO.setModified(new Date());
            Long id = analyzeCategoryRecordDao.insert(analyzeCategoryRecordDO);
            analyzeCategoryRecordDO.setId(id);
            return analyzeCategoryRecordDO;
        }

    }






}
