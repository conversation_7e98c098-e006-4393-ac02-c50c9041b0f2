package com.raycloud.dmj.account.export.core;


import com.raycloud.dmj.account.core.pageconfig.dto.PageColumnConfigDto;
import com.raycloud.dmj.account.core.pageconfig.service.IPageColumnConfigService;
import com.raycloud.dmj.domain.account.Staff;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@RequiredArgsConstructor
@Slf4j
@Service
public class QueryContextFactory {

    @Resource
    private final IPageColumnConfigService pageColumnConfigService;

    /**
     * 获取页面列配置
     *
     * @param staff
     * @param pageId
     * @return
     */
    public List<PageColumnConfigDto> getColumns(Staff staff, Long pageId) {
        return pageColumnConfigService.queryDtoByPageId(staff.getCompanyId(), pageId);
    }


}
