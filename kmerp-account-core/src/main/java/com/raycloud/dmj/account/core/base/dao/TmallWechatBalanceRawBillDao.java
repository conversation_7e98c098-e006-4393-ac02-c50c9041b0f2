package com.raycloud.dmj.account.core.base.dao;

import com.raycloud.dmj.account.core.cleancategory.domain.object.TmallWechatBalanceRawBillDO;

/**
 * <AUTHOR>
 */
public interface TmallWechatBalanceRawBillDao {



    /**
     * 查询账期余额
     * @param companyId 公司ID
     * @param shopId 店铺ID
     * @param dataRange 账期
     * @return 账期余额
     */
    TmallWechatBalanceRawBillDO getByDataRange(Long companyId, Long shopId, Integer dataRange);
}
