package com.raycloud.dmj.account.core.enums.field;

import lombok.Getter;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 账单重洗任务表字段枚举
 * <AUTHOR>
 */
@Getter
public enum BillAgainAnalyzeTaskFieldEnum {

    ID("id", "主键ID"),
    CREATED("created", "创建时间"),
    MODIFIED("modified", "修改时间"),
    FUND_ACCOUNT_ID("fund_account_id", "资金账户ID"),
    PLATFORM_CODE("platform_code", "平台code"),
    REPROCESS_START_TIME("reprocess_start_time", "重洗开始时间"),
    REPROCESS_END_TIME("reprocess_end_time", "重洗结束时间"),
    STATUS("status", "任务状态，0-清洗中，30-清洗失败，50-清洗成功"),
    COMPLETION_DATE("completion_date", "完成日期"),
    COMPANY_ID("company_id", "公司ID"),
    FEATURE("feature", "扩展字段") ,
    VERSION("version", "版本号")

    ;


    /**
     * 字段编码
     */
    private final String fieldCode;

    /**
     * 字段描述
     */
    private final String fieldDesc;

    BillAgainAnalyzeTaskFieldEnum(String fieldCode, String fieldDesc) {
        this.fieldCode = fieldCode;
        this.fieldDesc = fieldDesc;
    }

    /**
     * 获取插入字段（排除自增主键）
     */
    public static Set<String> getInsertFields() {
        List<BillAgainAnalyzeTaskFieldEnum> filterFields = Arrays.asList(ID);
        return Arrays.stream(values())
                .filter(f -> !filterFields.contains(f))
                .map(f -> f.fieldCode)
                .collect(Collectors.toSet());
    }
}