package com.raycloud.dmj.account.core.common;

import java.io.Serializable;

/**
 * 页码信息，startRow必须从1开始,page必须大于或者等于0
 *
 * <AUTHOR>
 */
public class Page implements Serializable {

    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	public final static int DEFAULT_PAGE_SIZE = 20;
	public final static int MAX_PAGE_SIZE = 500;
    public final static int DEFAULT_PAGE_NUM = 1;
    protected Integer pageSize = DEFAULT_PAGE_SIZE;
    //起始行
    protected Integer startRow;
    //结束行(闭合)
    protected Integer offsetRow;
    protected Integer pageNo = DEFAULT_PAGE_NUM;

    public Integer getStartRow() {
    	if(startRow == null){
    		setPageNo(pageNo);
    	}
        return startRow >= 0 ? startRow : 0;
    }

    public Page setStartRow(Integer startRow) {
        this.startRow = startRow;
        return this;
    }

    public Integer getOffsetRow() {
    	if(null == offsetRow){
    		setPageNo(pageNo);
    	}
        return offsetRow;
    }

    public Page setOffsetRow(Integer offsetRow) {
        this.offsetRow = offsetRow;
        return this;
    }

    public Page setPageNo(Integer page) {
        if (page == null || page < 0) page = DEFAULT_PAGE_NUM;
        this.pageNo = page;
        this.startRow = (page - 1) * this.pageSize;
        this.offsetRow = this.pageSize;
        return this;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public Page setPageSize(Integer pageSize) {
        if (pageSize == null || pageSize < 1) pageSize = 1;
        this.pageSize = pageSize;
        this.startRow = (pageNo - 1) * this.pageSize;
        this.offsetRow = this.pageSize;
        return this;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public Page(Integer pageNo, Integer pageSize) {
        this.setPageNo(pageNo);
        this.setPageSize(pageSize);
    }

    public Page() {
    }
}
