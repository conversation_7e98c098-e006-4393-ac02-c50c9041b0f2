package com.raycloud.dmj.account.core.enums;

import lombok.Getter;

/**
 * 表示是或否的枚举类，1 代表 YES，0 代表 NO
 * <AUTHOR>
 */
@Getter
public enum YesOrNoEnum {
    YES(1, "是"),
    NO(0, "否");

    /**
     * 枚举对应的数值
     */
    private final Integer value;

    /**
     * 枚举的描述信息
     */
    private final String desc;

    YesOrNoEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public boolean getBoolean() {
        return this.value==YES.getValue() ;
    }

}