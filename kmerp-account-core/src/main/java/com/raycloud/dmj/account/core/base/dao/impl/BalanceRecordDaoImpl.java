package com.raycloud.dmj.account.core.base.dao.impl;

import com.raycloud.dmj.account.core.base.dao.BalanceRecordDao;
import com.raycloud.dmj.account.core.cleancategory.domain.object.BalanceRecordDO;
import com.raycloud.dmj.account.core.enums.field.BalanceRecordFieldEnum;
import com.raycloud.dmj.account.infra.utils.AsserUtils;
import com.raycloud.dmj.table.api.plus.common.ColumnValues;
import com.raycloud.dmj.table.api.plus.common.base.SQL;
import com.raycloud.dmj.table.api.plus.common.enums.LinkMode;
import com.raycloud.dmj.table.api.plus.component.column.utils.Columns;
import com.raycloud.dmj.table.api.plus.component.condition.ConditionComponent;
import com.raycloud.dmj.table.api.plus.component.condition.utils.Conditions;
import com.raycloud.dmj.table.api.plus.insert.Inserts;
import com.raycloud.dmj.table.api.plus.query.Queries;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;


/**
 * <AUTHOR>
 */
@Repository
public class BalanceRecordDaoImpl extends BaseDao implements BalanceRecordDao {

    private final String TABLE_NAME = "balance_record";

    @Override
    public void insertOnDuplicateKey(BalanceRecordDO categoryDO) {
        SQL sql = Inserts.insert()
                .into(TABLE_NAME)
                .columns(BalanceRecordFieldEnum.getInsertFields())
                .valueForEntity(categoryDO)
                .onDuplicateKey()
                .update(
                        ColumnValues.create(BalanceRecordFieldEnum.START_BALANCE.getFieldCode(), categoryDO.getStartBalance()),
                        ColumnValues.create(BalanceRecordFieldEnum.END_BALANCE.getFieldCode(),categoryDO.getEndBalance()),
                        ColumnValues.create(BalanceRecordFieldEnum.CHANGE_AMOUNT.getFieldCode(),categoryDO.getChangeAmount()),
                        ColumnValues.create(BalanceRecordFieldEnum.MODIFIED.getFieldCode(),new Date())
                )
                .columnNameCamelToUnderline()
                .toSql();
         jdbcTemplate.update(
                sql.getSqlCode(),
                sql.getArgs().toArray(new Object[0]));

    }

    @Override
    public List<BalanceRecordDO> listByFundAccountIds(List<Long> fundAccountIds, Long companyId, Date startTime, Date endTime) {
        AsserUtils.notNull(companyId, "公司ID不能为空！");
        if(fundAccountIds.isEmpty()){
            return Collections.emptyList();
        }

        List<ConditionComponent<?>> conditionsList = new ArrayList<>();
        if(startTime!=null){
            conditionsList.add(Conditions.and(Columns.toColumn(BalanceRecordFieldEnum.DATE.getFieldCode()), LinkMode.GREATER_THAN_EQUAL, startTime));
        }
        if(endTime!=null){
            conditionsList.add(Conditions.and(Columns.toColumn(BalanceRecordFieldEnum.DATE.getFieldCode()), LinkMode.LESS_THAN, endTime));
        }
        conditionsList.add(Conditions.and(Columns.toColumn(BalanceRecordFieldEnum.COMPANY_ID.getFieldCode()), LinkMode.EQUAL, companyId));
        conditionsList.add(Conditions.and(Columns.toColumn(BalanceRecordFieldEnum.FUND_ACCOUNT_ID.getFieldCode()), LinkMode.IN, fundAccountIds));


        SQL sql = Queries.create()
                .from(TABLE_NAME)
                .where(conditionsList)
                .select()
                .toSql();

        Object[] args = sql.getArgs() == null ? new Object[]{} : sql.getArgs().toArray();

        return jdbcTemplate.query(sql.getSqlCode(), new BeanPropertyRowMapper<>(BalanceRecordDO.class), args);
    }
}
