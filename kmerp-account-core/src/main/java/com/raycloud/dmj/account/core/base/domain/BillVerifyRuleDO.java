package com.raycloud.dmj.account.core.base.domain;

import com.raycloud.dmj.account.core.common.BaseInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 账单核验规则表实体类，对应数据库表 bill_verify_rule
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BillVerifyRuleDO extends BaseInfo {

    /**
     * 主键ID，自增
     */
    private Long id;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 核验规则名称
     */
    private String ruleName;

    /**
     * 生效时间
     */
    private Date effectiveTime;

    /**
     * 失效时间
     */
    private Date invalidTime;

    /**
     * 时间类型
     */
    private Integer dateType;

    /**
     * 核验规则内容
     */
    private String ruleContent;

    /**
     * 异常是否提示(1是,0否)
     */
    private Integer isExceptionTip;

    /**
     * 是否启用(1开启,0关闭)
     */
    private Integer status;

    /**
     * 判断方式(1大于,2等于,3小于)
     */
    private Integer judgmentType;

    /**
     * 核验金额(元)
     */
    private BigDecimal amount;

    /**
     * 启用状态：0弃用，1正常
     */
    private Integer enableStatus;
}
