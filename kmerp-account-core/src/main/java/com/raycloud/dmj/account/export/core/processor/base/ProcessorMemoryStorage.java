package com.raycloud.dmj.account.export.core.processor.base;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.raycloud.dmj.account.infra.common.BizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.springframework.cache.Cache;
import org.springframework.cache.caffeine.CaffeineCache;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;

@Slf4j
public class ProcessorMemoryStorage {

    private final CaffeineCacheManager manager = new CaffeineCacheManager();

    public static ProcessorMemoryStorage create() {
        return new ProcessorMemoryStorage();
    }

    public void registerCache(String cacheName, Caffeine<Object, Object> caffeine) {
        manager.registerCustomCache(cacheName, caffeine.build());
    }

    public Cache getCache(String cacheName){
        return manager.getCache(cacheName);
    }


    /**
     * 获取不在缓存中的KEY
     *
     * @param cacheName
     * @param keyClass
     * @param keys
     * @param <K>
     * @return
     */
    public <K> List<K> getNotContainCacheKeyList(String cacheName, Class<K> keyClass, List<K> keys) {
        List<K> cacheKeyList = getCacheKeyList(cacheName, keyClass);
        if (ObjectUtils.isEmpty(cacheKeyList)) {
            // 说明所有列都不在缓存中
            return keys;
        }
        Collection<K> subtracted = CollectionUtils.subtract(keys, cacheKeyList);
        if (ObjectUtils.isEmpty(subtracted)){
            // 说明所有列都在缓存中
            return new ArrayList<>();
        }
        return new ArrayList<>(subtracted);
    }

    /**
     * 获取缓存中的KEY
     * @param cacheName
     * @param keyClass
     * @return
     * @param <K>
     */
    public <K> List<K> getCacheKeyList(String cacheName, Class<K> keyClass) {
        List<K> list = new ArrayList<>();
        Cache cache = manager.getCache(cacheName);
        if (cache instanceof CaffeineCache) {
            com.github.benmanes.caffeine.cache.Cache<Object, Object> nativeCache = ((CaffeineCache) cache).getNativeCache();
            ConcurrentMap<@NonNull Object, @NonNull Object> map = nativeCache.asMap();
            for (Map.Entry<@NonNull Object, @NonNull Object> entry : map.entrySet()) {
                Object key = entry.getKey();
                list.add(keyClass.cast(key));
            }
            return list;
        }
        throw new BizException("不支持的缓存类型");
    }

    /**
     * 获取缓存中的数据
     * @param cacheName
     * @param uniqueKey
     * @param clazz
     * @return
     * @param <K>
     * @param <T>
     */
    public <K, T> T get(String cacheName, K uniqueKey, Class<T> clazz) {
        Cache cache = manager.getCache(cacheName);
        if (cache != null) {
            return cache.get(uniqueKey, clazz);
        }
        return null;
    }

    /**
     * 获取缓存中的数据
     * @param cacheName
     * @param uniqueKey
     * @param clazz
     * @param creator
     * @return
     * @param <K>
     * @param <T>
     */
    public <K, T> T get(String cacheName, K uniqueKey, Class<T> clazz, Function<K, T> creator) {
        T catalog = get(cacheName, uniqueKey, clazz);
        if (catalog == null) {
            synchronized (uniqueKey) {
                catalog = get(cacheName, uniqueKey, clazz);
                if (catalog != null) {
                    return catalog;
                }
                catalog = creator.apply(uniqueKey);
                save(cacheName, uniqueKey, catalog);
                return catalog;
            }
        }
        return catalog;
    }


    /**
     * 保存缓存数据
     * @param cacheName
     * @param uniqueKey
     * @param catalog
     * @param <T>
     */
    public <T> void save(String cacheName, Object uniqueKey, T catalog) {
        Cache cache = manager.getCache(cacheName);
        if (cache != null) {
            cache.put(uniqueKey, catalog);
        }
    }


    /**
     * 批量保存缓存数据
     * @param cacheName
     * @param values
     * @param <T>
     */
    public <K,T> void saveBatch(String cacheName, Map<K, T> values) {
        Cache cache = manager.getCache(cacheName);
        if (cache != null) {
            for (Map.Entry<K, T> entry : values.entrySet()) {
                K key = entry.getKey();
                T value = entry.getValue();
                cache.put(key, value);
            }
        }
    }

    /**
     * 删除缓存数据
     * @param cacheName
     * @param uniqueKey
     */
    public void delete(String cacheName, Object uniqueKey) {
        Cache cache = manager.getCache(cacheName);
        if (cache != null) {
            cache.evict(uniqueKey);
        }
    }

    /**
     * 关闭缓存
     */
    public void close() {
        Collection<String> cacheNames = manager.getCacheNames();
        if (CollectionUtils.isEmpty(cacheNames)) {
            return;
        }
        for (String cacheName : cacheNames) {
            Cache cache = manager.getCache(cacheName);
            if (cache != null) {
                cache.clear();
            }
        }
    }
}
