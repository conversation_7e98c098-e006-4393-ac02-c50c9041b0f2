package com.raycloud.dmj.account.core.base.domain;

import com.raycloud.dmj.account.core.common.BaseInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 规则校验结果记录表实体类，对应数据库表 rule_verify_result_record
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RuleVerifyResultRecordDO extends BaseInfo {

    /**
     * 主键ID，自增
     */
    private Long id;

    /**
     * 校验规则ID
     */
    private Long ruleId;

    /**
     * 校验结果金额
     */
    private BigDecimal amount;

    /**
     * 校验结果状态：-1异常 1通过
     */
    private Integer status;

    /**
     * 校验日期
     */
    private Date verifyTime;

    /**
     * 启用状态：0弃用，1正常
     */
    private Integer enableStatus;
}
