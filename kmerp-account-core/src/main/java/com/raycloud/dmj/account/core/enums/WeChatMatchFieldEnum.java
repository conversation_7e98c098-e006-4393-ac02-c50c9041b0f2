package com.raycloud.dmj.account.core.enums;

import com.google.common.base.CaseFormat;
import com.raycloud.dmj.account.core.common.TreeVO;
import com.raycloud.dmj.account.core.enums.field.TmallWechatRawBillFieldEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


/**
 * 解析规则中满足条件字段
 * <AUTHOR>
 */

@Getter
public enum WeChatMatchFieldEnum {

    REMARK(TmallWechatRawBillFieldEnum.REMARK, "备注"),
    BIZ_DESCRIPTION(TmallWechatRawBillFieldEnum.BIZ_DESCRIPTION, "业务描述"),
    ENTRY_TYPE(TmallWechatRawBillFieldEnum.ENTRY_TYPE, "入账类型")
    ;

    private final String fieldCode;

    private final TmallWechatRawBillFieldEnum matchField;

    private final String fieldDesc;

    WeChatMatchFieldEnum(TmallWechatRawBillFieldEnum matchField, String fieldDesc) {
        //改为小驼峰命名，用于配置解析规则
        this.fieldCode= CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL,matchField.getFieldCode());
        this.matchField = matchField;
        this.fieldDesc = fieldDesc;
    }

    public static List<TreeVO> listTreeVO() {
        return Arrays.stream(WeChatMatchFieldEnum.values()).map(
                x -> {
                    TreeVO treeVO = new TreeVO();
                    treeVO.setLabel(x.getFieldDesc());
                    treeVO.setValue(x.getFieldCode());
                    return treeVO;
                }
        ).collect(Collectors.toList());
    }

}
