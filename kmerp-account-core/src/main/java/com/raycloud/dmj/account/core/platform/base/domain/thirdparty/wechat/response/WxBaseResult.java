package com.raycloud.dmj.account.core.platform.base.domain.thirdparty.wechat.response;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

/**
 * @Description:
 * <AUTHOR>
 * @Date 2023/10/24 3:44 下午
 */
@Data
public class WxBaseResult implements Serializable {

    @JSONField(name = "errcode")
    private Long errcode;

    @JSONField(name = "errmsg")
    private String errmsg;

    public boolean isSuccess() {
        return Objects.isNull(errcode) || errcode == 0L;
    }

}
