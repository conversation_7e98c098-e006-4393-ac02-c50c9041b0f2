package com.raycloud.dmj.account.core.enums;

import lombok.Getter;

/**
 * 监控状态枚举
 * <AUTHOR>
 */
@Getter
public enum FileAnalyzeStatusEnum {

    //数据状态，0-导入中， 30-系统报错，导入失败，35-业务报错，导入失败  50-导入成功

    IMPORTING(0, "导入中"),
    SYSTEM_ERROR_IMPORT_FAIL(30, "系统报错，导入失败"),
    BIZ_ERROR_IMPORT_FAIL(35, "业务报错，导入失败"),
    TIMEOUT_IMPORT_FAIL(40, "超时自动停止导入"),
    IMPORT_SUCCESS(50, "导入成功");


    private final Integer status;

    private final String desc;

    FileAnalyzeStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
