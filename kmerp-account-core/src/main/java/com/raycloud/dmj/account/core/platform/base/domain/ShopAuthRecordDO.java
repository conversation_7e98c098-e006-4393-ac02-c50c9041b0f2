package com.raycloud.dmj.account.core.platform.base.domain;

import com.raycloud.dmj.account.core.common.BaseInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 商家授权记录表实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShopAuthRecordDO {

    /**
     * 自增主键
     */
    private Long id;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 公司ID
     */
    private Long companyId;

    /**
     * 授权平台id
     */
    private String platformCode;

    /**
     * 调用的平台名称
     */
    private String serverName;

    /**
     * 回调url
     */
    private String callBackUrl;

    /**
     * 授权状态
     */
    private String authStatus;

    /**
     * 创建时间
     */
    private LocalDateTime created;

    /**
     * 修改时间
     */
    private LocalDateTime modified;
}