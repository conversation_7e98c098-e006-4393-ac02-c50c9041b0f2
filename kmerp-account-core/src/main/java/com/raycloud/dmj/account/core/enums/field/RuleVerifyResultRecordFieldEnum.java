package com.raycloud.dmj.account.core.enums.field;

import lombok.Getter;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 规则校验结果记录表字段枚举
 * <AUTHOR>
 */
@Getter
public enum RuleVerifyResultRecordFieldEnum {
    ID("id", "主键ID"),
    RULE_ID("rule_id", "校验规则ID"),
    AMOUNT("amount", "校验结果金额"),
    STATUS("status", "校验结果状态：-1异常 1通过"),
    VERIFY_TIME("verify_time", "校验日期"),
    ENABLE_STATUS("enable_status", "启用状态：0弃用，1正常"),
    CREATED("created", "创建时间"),
    MODIFIED("modified", "修改时间"),
    COMPANY_ID("company_id", "公司ID");

    /**
     * 字段编码
     */
    private final String fieldCode;

    /**
     * 字段描述
     */
    private final String fieldDesc;

    RuleVerifyResultRecordFieldEnum(String fieldCode, String fieldDesc) {
        this.fieldCode = fieldCode;
        this.fieldDesc = fieldDesc;
    }

    /**
     * 获取插入字段
     * @return 插入字段
     */
    public static Set<String> getInsertFields() {
        List<RuleVerifyResultRecordFieldEnum> filterField = Arrays.asList(ID, ENABLE_STATUS, CREATED, MODIFIED);
        return Arrays.stream(values()).filter(x -> !filterField.contains(x)).map(x -> x.fieldCode).collect(Collectors.toSet());
    }

    /**
     * 获取所有查询字段
     * @return 查询字段数组
     */
    public static String[] getSelectFields() {
        return Arrays.stream(values()).map(RuleVerifyResultRecordFieldEnum::getFieldCode).toArray(String[]::new);
    }
}
