package com.raycloud.dmj.account.core.cleancategory.domain.object;

import com.raycloud.dmj.account.core.enums.CategorySourceEnum;
import com.raycloud.dmj.account.core.enums.InitCategoryGroupEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 分类组表DO
 * <AUTHOR>
 */

@Data
public class CategoryGroupDO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 修改时间
     */
    private Date modified;

    /**
     * 来源（1-系统，2-手动）
     * @see CategorySourceEnum
     */
    private Integer source;

    /**
     * 类型编码
     * @see InitCategoryGroupEnum
     */
    private String code;

    /**
     * 分类名称
     */
    private String name;


    /**
     * 公司ID
     */
    private Long companyId;

}