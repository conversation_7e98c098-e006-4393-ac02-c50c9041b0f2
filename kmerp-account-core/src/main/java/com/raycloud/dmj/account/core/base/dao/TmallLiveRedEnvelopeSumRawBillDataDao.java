package com.raycloud.dmj.account.core.base.dao;

import com.raycloud.dmj.account.core.cleancategory.domain.object.TmallLiveRedEnvelopeSumRawBillDataDO;
import com.raycloud.dmj.account.core.common.Page;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface TmallLiveRedEnvelopeSumRawBillDataDao {

    /**
     * 根据批次时间分页查询账单
     * @param batchTime 批次时间
     * @param companyId 公司ID
     * @param shopId 店铺ID
     * @return 微信支付宝账单
     */
    List<TmallLiveRedEnvelopeSumRawBillDataDO> listPageByBatchCode(Long companyId, Long shopId, Integer batchTime, Page page);


}
