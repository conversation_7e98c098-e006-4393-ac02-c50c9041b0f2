<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.raycloud.dmj</groupId>
        <artifactId>kmerp-account-system</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>kmerp-account-core</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <raycloud.middle.gateway.version>1.1.0-SNAPSHOT</raycloud.middle.gateway.version>
        <jd.sdk.version>********</jd.sdk.version>
        <erpcore.version>1.0.0-SNAPSHOT</erpcore.version>
        <jackson-mapper-asl.version>1.9.13</jackson-mapper-asl.version>

    </properties>

    <dependencies>

        <dependency>
            <groupId>com.raycloud.dmj</groupId>
            <artifactId>chessboard-client-adapter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.raycloud.dmj</groupId>
            <artifactId>kmerp-account-configuration</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.raycloud.analyzeExcel</groupId>
            <artifactId>AnalyzeExcel</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>


        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <!-- MyBatis 依赖 -->
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
        </dependency>
        <!-- 平台授权服务-->
        <dependency>
            <groupId>com.raycloud.dmj</groupId>
            <artifactId>kmerp-account-dubbo-auth</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <!--京东账单服务-->
        <dependency>
            <groupId>com.raycloud</groupId>
            <artifactId>jd-sdk</artifactId>
            <version>${jd.sdk.version}</version>
        </dependency>
        <!-- Jackson Mapper -->
        <dependency>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-mapper-asl</artifactId>
            <version>${jackson-mapper-asl.version}</version>
        </dependency>

        <dependency>
            <groupId>com.raycloud.dmj</groupId>
            <artifactId>dmj-services-api</artifactId>
            <version>${erpcore.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.taobao.sdk</groupId>
                    <artifactId>sdk</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>mysql</groupId>
                    <artifactId>mysql-connector-java</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-web</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>guava</artifactId>
                    <groupId>com.google.guava</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-lang</artifactId>
                    <groupId>commons-lang</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jackson-mapper-asl</artifactId>
                    <groupId>org.codehaus.jackson</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jackson-core-asl</artifactId>
                    <groupId>org.codehaus.jackson</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.raycloud.dmj.kmbi</groupId>
            <artifactId>kmbi-dubbo-dependency</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-reload4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.raycloud.middle.gateway</groupId>
            <artifactId>middle-gateway-dubbo-api</artifactId>
            <version>${raycloud.middle.gateway.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.raycloud</groupId>
                    <artifactId>bizlogger</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.kdzs</groupId>
                    <artifactId>kdzs-common-utils</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.raycloud.dmj</groupId>
            <artifactId>chessboard-client-adapter</artifactId>
        </dependency>

    </dependencies>
</project>