package com.raycloud.dmj.data.export;

import com.alibaba.fastjson.JSON;
import com.raycloud.dmj.data.chessboard.flow.ChessboardDefaultMultiExportFlow;
import com.raycloud.dmj.data.chessboard.listener.ExportContext;
import com.raycloud.dmj.data.chessboard.listener.NormalExportListener;
import com.raycloud.dmj.data.chessboard.model.PushResult;
import com.raycloud.dmj.data.chessboard.model.TaskMessage;
import com.raycloud.dmj.data.chessboard.model.UnconditionalFinishedException;
import com.raycloud.dmj.data.chessboard.utils.ExportContextHelper;
import com.raycloud.dmj.data.export.core.DownloadParameter;
import com.raycloud.dmj.data.export.core.IMultiDataLoader;
import com.raycloud.dmj.data.export.core.MultiDataWriter;
import com.raycloud.dmj.data.export.core.head.DynamicHeadDefine;
import com.raycloud.dmj.data.export.imp.easyexcel.ExcelHeadInfo;
import com.raycloud.dmj.domain.account.Staff;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;

/**
 * 这是一个用于开发测试的监听器，实际开发中，需要继承 NormalExportListener
 */
@Component
public class DemoExportListener extends NormalExportListener {

    @Override
    public String taskCode() {
        return "demo.query";
    }

    @Override
    protected void consume0(ExportContext context, TaskMessage taskMessage) throws Exception {

        String jsonString = JSON.toJSONString(taskMessage);
        System.out.println("> " + taskCode() + " parameters : " + jsonString);

        // 业务参数，类型需要自己把控，并且在执行前可以自行调整加工
        Object parameter = taskMessage.getParameter();
        Map<String, Object> extendParameter = taskMessage.getExtendParameter();
        Integer sleepTime = extendParameter.get("sleepTime") == null ? 5000 : (Integer) extendParameter.get("sleepTime");

        // 执行导出
        context.run(new ChessboardDefaultMultiExportFlow<Map<String, Object>>() {
            @Override
            protected DynamicHeadDefine buildHeadDefine(Staff staff, DownloadParameter downloadParameter, Map<String, Object> parameter) {
                return DynamicHeadDefine.builder()
                        .title(downloadParameter.getTitle())
                        .infos(Arrays.asList(
                                        new ExcelHeadInfo(0, "string", "字符串类型", 200),
                                        new ExcelHeadInfo(0, "long", "长整形类型", 200),
                                        new ExcelHeadInfo(0, "time", "时间类型", 200)
                                )
                        )
                        .build();
            }

            @Override
            protected IMultiDataLoader<Map<String, Object>> buildDataLoader(Staff staff, DownloadParameter downloadParameter, Map<String, Object> taskParam) {
                return new IMultiDataLoader<Map<String, Object>>() {
                    @Override
                    public void execute(Staff staff, Map<String, Object> param, MultiDataWriter writer) throws Exception {
                        ExportContext exportContext = ExportContextHelper.getExportContext();

                        AtomicBoolean ret = new AtomicBoolean(false);
                        List<Map<String, Object>> list = new ArrayList<>();
                        for (int i = 0; i < 10; i++) {
                            // 这里是模拟中断标记判断
                            boolean interrupt = exportContext.isInterrupt();
                            if (interrupt) {
                                throw new RuntimeException("接收远程命令，导出中断");
                            }
                            // 无条件结束，不导出文件
                            if (ret.get()){
                                throw new UnconditionalFinishedException();
                            }

                            // 模拟数据
                            Map<String, Object> map = new HashMap<>();
                            map.put("string", "name" + i);
                            map.put("long", System.currentTimeMillis());
                            map.put("time", new Date());
                            list.add(map);

                            // 进度条是自定义的，如果不需要进度条则可以忽略
                            double process = ((i * 1.0) / 10) * 100;
                            exportContext.updateProcess("已下载：" + process + "%", new Consumer<PushResult>() {
                                @Override
                                public void accept(PushResult pushResult) {
                                    // 说明已经有其他服务在运行了，不用继续跑了
                                    if ("400".equals(pushResult.getCode())){
                                        ret.set(true);
                                    }
                                }
                            });
                            Thread.sleep(sleepTime);
                        }
                        writer.write(list);
                    }
                };
            }
        }, parameter);

        System.out.println("> " + taskCode() + " : flow run finish");
    }

}
