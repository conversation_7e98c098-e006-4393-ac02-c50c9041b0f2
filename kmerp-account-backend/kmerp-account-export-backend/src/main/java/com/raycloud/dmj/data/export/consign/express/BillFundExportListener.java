package com.raycloud.dmj.data.export.consign.express;

import com.raycloud.dmj.account.core.bill.parameter.StandardFundBillFlowParameter;
import com.raycloud.dmj.data.chessboard.flow.ChessboardDefaultMultiExportFlow;
import com.raycloud.dmj.data.chessboard.listener.ExportContext;
import com.raycloud.dmj.data.chessboard.listener.NormalExportListener;
import com.raycloud.dmj.data.chessboard.model.TaskMessage;
import com.raycloud.dmj.data.export.KmerpExportHelper;
import com.raycloud.dmj.data.export.core.DownloadParameter;
import com.raycloud.dmj.data.export.core.IMultiDataLoader;
import com.raycloud.dmj.data.export.core.head.DynamicHeadDefine;
import com.raycloud.dmj.domain.account.Staff;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * 标准资金账单流水导出监听器
 */
@Component
@RequiredArgsConstructor
public class BillFundExportListener extends NormalExportListener {

    private final KmerpExportHelper kmerpExportHelper;

    @Override
    protected void consume0(ExportContext context, TaskMessage taskMessage) throws Exception {
        // 业务参数，类型需要自己把控，并且在执行前可以自行调整加工
        Object parameter = taskMessage.getParameter();

        // 执行导出
        context.run(
                new ChessboardDefaultMultiExportFlow<StandardFundBillFlowParameter>() {
                    @Override
                    protected DynamicHeadDefine buildHeadDefine(Staff staff, DownloadParameter downloadParameter, StandardFundBillFlowParameter parameter) {
                        // 表头定义
                        return DynamicHeadDefine.builder()
                                .title(downloadParameter.getTitle())
                                .infos(kmerpExportHelper.getExcelHeads(taskMessage))
                                .build();
                    }

                    @Override
                    protected IMultiDataLoader<StandardFundBillFlowParameter> buildDataLoader(Staff staff, DownloadParameter downloadParameter, StandardFundBillFlowParameter taskParam) {
                        return kmerpExportHelper.getExport(taskMessage);
                    }
                }, parameter);
    }

    @Override
    public String taskCode() {
        return "bill-fund-export";
    }
}
