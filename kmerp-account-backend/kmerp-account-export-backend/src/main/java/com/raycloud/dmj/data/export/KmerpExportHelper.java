package com.raycloud.dmj.data.export;


import com.raycloud.dmj.account.core.pageconfig.dto.PageColumnConfigDto;
import com.raycloud.dmj.account.export.core.helper.StaffHelper;
import com.raycloud.dmj.account.export.core.statistics.interf.IExport;
import com.raycloud.dmj.account.export.core.QueryContextFactory;
import com.raycloud.dmj.account.export.utils.ExportUtils;
import com.raycloud.dmj.account.infra.common.AppContextHolder;
import com.raycloud.dmj.account.infra.common.BizException;
import com.raycloud.dmj.data.chessboard.model.TaskMessage;
import com.raycloud.dmj.data.export.core.head.HeadInfo;
import com.raycloud.dmj.data.export.imp.easyexcel.ExcelHeadInfo;
import com.raycloud.dmj.domain.account.Staff;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;

@Component
@RequiredArgsConstructor
public class KmerpExportHelper {

    private final StaffHelper staffHelper;

    private final QueryContextFactory factory;

    public <T> T getParameter(TaskMessage taskMessage, Class<T> clz) {
        return ExportUtils.extractParameter(
                taskMessage.getParameter(),
                clz
        );
    }

    public List<HeadInfo> getExcelHeads(TaskMessage taskMessage) {
        Long staffId = taskMessage.getStaffId();
        Staff staff = staffHelper.getStaff(staffId);
        Long pageId = ExportUtils.extractPageId(taskMessage.getExtendParameter());

        //获取列配置，用于导出
        List<PageColumnConfigDto> columns = factory.getColumns(staff, pageId);
        if (ObjectUtils.isEmpty(columns)) {
            return new ArrayList<>();
        }

        int index = 0;
        List<HeadInfo> headList = new ArrayList<>();
        for (PageColumnConfigDto column : columns) {
            if (column.getVisible().equals(0)) {
                continue;
            }
            headList.add(
                    new ExcelHeadInfo(
                            index++,
                            column.getColCode(),
                            column.getColTitle(),
                            column.getWidth()
                    )
            );
        }
        return headList;
    }


    public IExport getExport(TaskMessage taskMessage) {
        Class<?> reportBeanClass = ExportUtils.extractReportBeanClass(taskMessage.getExtendParameter());
        if (ObjectUtils.isEmpty(reportBeanClass)) {
            throw new BizException("未能发现IExport Class:" + taskMessage.getTaskName());
        }
        if (!AppContextHolder.hasBean(reportBeanClass)){
            throw new BizException("未能发现IExport Bean:" + taskMessage.getTaskName());
        }
        return (IExport) AppContextHolder.getBean(reportBeanClass);
    }
}
