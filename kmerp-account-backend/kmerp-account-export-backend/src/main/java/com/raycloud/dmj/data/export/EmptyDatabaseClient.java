package com.raycloud.dmj.data.export;

import com.raycloud.dmj.account.infra.common.BizException;
import com.raycloud.dmj.account.infra.datasource.KmerpDatasourceConfiguration;
import com.raycloud.dmj.account.infra.repository.Connectors;
import com.raycloud.dmj.data.export.adapter.DatabaseClient;
import com.raycloud.dmj.domain.account.Staff;
import lombok.RequiredArgsConstructor;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.orm.ibatis.SqlMapClientTemplate;
import org.springframework.stereotype.Component;

/**
 * 为了兼容export框架，实际不需要用这个。
 */
@Deprecated
@RequiredArgsConstructor
@Component
public class EmptyDatabaseClient implements DatabaseClient {

    private final KmerpDatasourceConfiguration kmerpDatasourceConfiguration;

    @Override
    public SqlMapClientTemplate getSqlMapClientTemplate(Staff staff, Object... args) {
        throw new BizException("不支持ibatis");
    }

    @Override
    public JdbcTemplate getJdbcTemplate(Staff staff, Object... args) {
        String connectorId = Connectors.getReportPg(staff);
        return kmerpDatasourceConfiguration.getJdbcTemplate(connectorId);
    }
}
