package com.raycloud.dmj.account.listener;

import com.alibaba.fastjson2.JSON;
import com.raycloud.dmj.account.core.base.dao.FileOriginalDataMonitorDao;
import com.raycloud.dmj.account.core.cleancategory.domain.request.CategoryAnalyzeReq;
import com.raycloud.dmj.account.core.cleancategory.service.CategoryAnalyzeService;
import com.raycloud.dmj.account.core.cleancategory.strategy.param.CategoryAnalyzeParam;
import com.raycloud.dmj.account.core.enums.DateTypeEnum;
import com.raycloud.dmj.account.core.enums.MonitorStatusEnum;
import com.raycloud.dmj.account.core.enums.RawDataSourceEnum;
import com.raycloud.dmj.account.core.rawdata.domains.FileOriginalDataMonitorDO;
import com.raycloud.dmj.account.core.rawdata.service.RawDataStorageService;
import com.raycloud.dmj.account.core.rocketmq.TopicConstant;
import com.raycloud.dmj.account.core.rocketmq.dto.RawDataStatusChangeMsg;
import com.raycloud.dmj.account.core.rocketmq.dto.RawDataStorageMsg;
import com.raycloud.dmj.account.infra.common.BusinessException;
import com.raycloud.dmj.account.infra.common.ErrorCodeEnum;
import com.raycloud.dmj.account.infra.mq.rocket.KmerpRocketMQConsumerProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

/**
 * 原始数据导入任务监听器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RawDataStatusChangeListener implements MessageListenerConcurrently {


    @Resource
    private KmerpRocketMQConsumerProperties kmerpRocketMQConsumerProperties;

    @Resource
    private CategoryAnalyzeService categoryAnalyzeService;



    @Resource
    private FileOriginalDataMonitorDao fileOriginalDataMonitorDao;

    @Value("${spring.profiles.active}")
    private String env;

    private static final String CONSUMER_GROUP_NAME = "RAW_DATA_STATUS_CHANGE_GROUP";


    @PostConstruct
    public void init() throws MQClientException {
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(CONSUMER_GROUP_NAME + env);
        consumer.setNamesrvAddr(kmerpRocketMQConsumerProperties.getNameServer());
        consumer.subscribe(TopicConstant.BATCH_RAW_DATA_STATUS_CHANGE_TOPIC, "50");
        consumer.registerMessageListener(this);
        consumer.start();
    }

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        try {
            doConsumeMessage(msgs);
        } catch (Throwable e) {
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    private void doConsumeMessage(List<MessageExt> msgs) {

        for (MessageExt msg : msgs) {
            System.out.println(" RawDataStatusChangeListener Received message: " + new String(msg.getBody()));
            RawDataStatusChangeMsg rawDataStorageMsg = JSON.parseObject(msg.getBody(), RawDataStatusChangeMsg.class);
            try {
                FileOriginalDataMonitorDO fileOriginalDataMonitorDO = fileOriginalDataMonitorDao.getById(rawDataStorageMsg.getRawDataBatchMonitorId());
                if (fileOriginalDataMonitorDO == null){
                    throw new BusinessException(ErrorCodeEnum.PARAM_ERROR.getCode(),"没有查询到该批次的信息！");
                }
                //进行类目解析
                CategoryAnalyzeReq categoryAnalyzeReq = new CategoryAnalyzeReq();
                categoryAnalyzeReq.setCompanyId(fileOriginalDataMonitorDO.getCompanyId());
                categoryAnalyzeReq.setShopId(fileOriginalDataMonitorDO.getShopId());
                categoryAnalyzeReq.setSource(RawDataSourceEnum.getByCode(fileOriginalDataMonitorDO.getDataSource()));
                categoryAnalyzeReq.setDataRange(fileOriginalDataMonitorDO.getDataRange());
                categoryAnalyzeReq.setDateType(DateTypeEnum.of(fileOriginalDataMonitorDO.getDateType()));
                categoryAnalyzeService.categoryAnalyze(categoryAnalyzeReq);
            } catch (BusinessException e) {
                //不是系统错误，不进行重试
                log.error("|RawDataStorageService.rpaRawDataStorage BusinessException|RawDataStorageMsg:{},error:{}", JSON.toJSONString(rawDataStorageMsg),
                        e.getErrorMessage());
                if (e.getErrorCode().equals(ErrorCodeEnum.SYSTEM_ERROR.getCode())) {
                    throw e;
                }
            } catch (Exception e) {
                log.error("|RawDataStorageService.rpaRawDataStorage error|RawDataStorageMsg:{},error:{}", JSON.toJSONString(rawDataStorageMsg),
                        e.getMessage());
                throw new BusinessException(ErrorCodeEnum.ROCKETMQ_ERROR.getCode(), e.getMessage());
            }

        }


    }

}