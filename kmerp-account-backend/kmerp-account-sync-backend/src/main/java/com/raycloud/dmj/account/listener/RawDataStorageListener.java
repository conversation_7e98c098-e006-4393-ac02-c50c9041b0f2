package com.raycloud.dmj.account.listener;

import com.alibaba.fastjson2.JSON;
import com.raycloud.dmj.account.core.rawdata.service.RawDataStorageService;
import com.raycloud.dmj.account.core.rocketmq.TopicConstant;
import com.raycloud.dmj.account.core.rocketmq.dto.RawDataStorageMsg;
import com.raycloud.dmj.account.infra.common.BusinessException;
import com.raycloud.dmj.account.infra.common.ErrorCodeEnum;
import com.raycloud.dmj.account.infra.mq.rocket.KmerpRocketMQConsumerProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

/**
 * 原始数据导入任务监听器
 * <AUTHOR>
 */
@Slf4j
@Component
public class RawDataStorageListener implements MessageListenerConcurrently{


    @Resource
    private KmerpRocketMQConsumerProperties kmerpRocketMQConsumerProperties;

    @Resource
    private RawDataStorageService rawDataStorageService;


    @PostConstruct
    public void init( ) throws MQClientException {
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(kmerpRocketMQConsumerProperties.getConsumerGroupName());
        consumer.setNamesrvAddr(kmerpRocketMQConsumerProperties.getNameServer());
        consumer.subscribe(TopicConstant.RAW_DATA_STORAGE_TOPIC, "*");
        consumer.registerMessageListener(this);
        consumer.start();
    }

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext consumeConcurrentlyContext) {
        try {
            doConsumeMessage(msgs);
        }catch (Throwable e){
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    private void doConsumeMessage(List<MessageExt> msgs) throws IOException, ClassNotFoundException {

        for (MessageExt msg : msgs) {
            System.out.println("Received message: " + new String(msg.getBody()));
            RawDataStorageMsg rawDataStorageMsg = JSON.parseObject(msg.getBody(), RawDataStorageMsg.class);
            try {
                rawDataStorageService.rpaRawDataStorage(rawDataStorageMsg.getRecordId());
            }catch (BusinessException  e){
                //不是系统错误，不进行重试
                log.error("|RawDataStorageService.rpaRawDataStorage BusinessException|RawDataStorageMsg:{},error:{}",JSON.toJSONString(rawDataStorageMsg),
                        e.getErrorMessage());
                if (e.getErrorCode().equals(ErrorCodeEnum.SYSTEM_ERROR.getCode())){
                    throw e;
                }

            } catch (Exception e) {
                log.error("|RawDataStorageService.rpaRawDataStorage error|RawDataStorageMsg:{},error:{}",JSON.toJSONString(rawDataStorageMsg),
                        e.getMessage());
                throw new BusinessException(ErrorCodeEnum.ROCKETMQ_ERROR.getCode(),e.getMessage());
            }

        }


    }

}