/*
package com.raycloud.dmj.account.listener;

import com.alibaba.fastjson2.JSON;
import com.raycloud.dmj.account.core.bill.service.IRuleVerifyResultRecordService;
import com.raycloud.dmj.account.core.rocketmq.TopicConstant;
import com.raycloud.dmj.account.core.rocketmq.dto.RuleVerifyMsg;
import com.raycloud.dmj.account.infra.common.BusinessException;
import com.raycloud.dmj.account.infra.common.ErrorCodeEnum;
import com.raycloud.dmj.account.infra.mq.rocket.KmerpRocketMQConsumerProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.client.consumer.listener.MessageListenerConcurrently;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

*/
/**
 * 规则校验监听器
 *//*

@Slf4j
@Component
public class RuleVerifyListener implements MessageListenerConcurrently {

    @Resource
    private KmerpRocketMQConsumerProperties kmerpRocketMQConsumerProperties;
    @Resource
    private IRuleVerifyResultRecordService verifyResultRecordService;

    @PostConstruct
    public void init( ) throws MQClientException {
        DefaultMQPushConsumer consumer = new DefaultMQPushConsumer(kmerpRocketMQConsumerProperties.getConsumerGroupName());
        consumer.setNamesrvAddr(kmerpRocketMQConsumerProperties.getNameServer());
        consumer.subscribe(TopicConstant.RULE_VERIFY_TOPIC, "*");
        consumer.registerMessageListener(this);
        consumer.start();
    }

    @Override
    public ConsumeConcurrentlyStatus consumeMessage(List<MessageExt> msgs, ConsumeConcurrentlyContext context) {
        try {
            doConsumeMessage(msgs);
        }catch (Throwable e){
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    private void doConsumeMessage(List<MessageExt> msgs) throws IOException, ClassNotFoundException {

        for (MessageExt msg : msgs) {
            System.out.println("Received message: " + new String(msg.getBody()));
            RuleVerifyMsg ruleVerifyMsg = JSON.parseObject(msg.getBody(), RuleVerifyMsg.class);
            try {
                verifyResultRecordService.verifyRule(ruleVerifyMsg.getAccountId(),ruleVerifyMsg.getBillingCycle(), ruleVerifyMsg.getBillingCycleType(), ruleVerifyMsg.getCompanyId());
            }catch (BusinessException e){
                //不是系统错误，不进行重试
                log.error("|RawDataStorageService.rpaRawDataStorage BusinessException|RawDataStorageMsg:{},error:{}",JSON.toJSONString(ruleVerifyMsg),
                        e.getErrorMessage());
                if (e.getErrorCode().equals(ErrorCodeEnum.SYSTEM_ERROR.getCode())){
                    throw e;
                }

            } catch (Exception e) {
                log.error("|RawDataStorageService.rpaRawDataStorage error|RawDataStorageMsg:{},error:{}",JSON.toJSONString(ruleVerifyMsg),
                        e.getMessage());
                throw new BusinessException(ErrorCodeEnum.ROCKETMQ_ERROR.getCode(),e.getMessage());
            }

        }


    }
}
*/
