package com.raycloud.dmj.account.schedule;

import com.raycloud.dmj.account.core.rawdata.handle.RawDataStorageHandle;
import com.raycloud.dmj.account.infra.utils.DateUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Component
public class TaskUtils {

    @Resource
    private RawDataStorageHandle rawDataStorageHandle;
    // 添加定时任务    
    @Scheduled(cron = "*/5 * * * * ?")
    public void doTask(){
        System.out.println("执行静态定时任务时间: " + DateUtils.formatDate(new Date()));
        rawDataStorageHandle.handleStuckTasks();
    }
}