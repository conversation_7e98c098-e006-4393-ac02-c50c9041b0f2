<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.raycloud.dmj</groupId>
        <artifactId>kmerp-account-system</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>kmerp-account-profiles</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <profiles>
        <profile>
            <id>test</id>
            <build>
                <resources>
                    <resource>
                        <directory>projects/config-cache</directory>
                        <includes>
                            <include>dynamic-redis-gray3.properties</include>
                        </includes>
                        <filtering>true</filtering>
                    </resource>
                </resources>
            </build>
        </profile>
        <profile>
            <id>gray3</id>
            <build>
                <resources>
                    <resource>
                        <directory>projects/config-cache</directory>
                        <includes>
                            <include>dynamic-redis-dev.properties</include>
                        </includes>
                        <filtering>true</filtering>
                    </resource>
                </resources>
            </build>
        </profile>
        <profile>
            <id>prod</id>
            <build>
                <resources>
                    <resource>
                        <directory>projects/config-cache</directory>
                        <includes>
                            <include>dynamic-redis-prod.properties</include>
                        </includes>
                        <filtering>true</filtering>
                    </resource>
                </resources>
            </build>
        </profile>
    </profiles>


</project>