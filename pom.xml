<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.18</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <groupId>com.raycloud.dmj</groupId>
    <artifactId>kmerp-account-system</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <modules>
        <module>kmerp-account-profiles</module>
        <module>kmerp-account-boots</module>
        <module>kmerp-account-frontend</module>
        <module>kmerp-account-backend</module>
        <module>kmerp-account-core</module>
        <module>kmerp-account-configuration</module>
        <module>kmerp-account-api</module>
    </modules>

    <properties>
        <java.version>1.8</java.version>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <kmbi.dependency.version>1.0.0-SNAPSHOT</kmbi.dependency.version>
        <kmbi.connector.version>1.0.0-SNAPSHOT</kmbi.connector.version>
        <kmbi.uid.version>1.0.0-SNAPSHOT</kmbi.uid.version>
        <kmbi.io.version>1.0.0-SNAPSHOT</kmbi.io.version>
        <table.api.version>3.0.0-SNAPSHOT</table.api.version>
        <raycloud.excel.read.version>1.0.0-SNAPSHOT</raycloud.excel.read.version>
        <kmerp.data.export.version>3.0.0-SNAPSHOT</kmerp.data.export.version>
        <kmerp.export.chessboard.version>1.1.1-SNAPSHOT</kmerp.export.chessboard.version>
        <lombok.version>1.16.16</lombok.version>
        <kmerp.core.version>1.0.0-SNAPSHOT</kmerp.core.version>
        <kmerp.pt.version>1.0.0-SNAPSHOT</kmerp.pt.version>
        <kmerp.caigou.version>1.0.0-SNAPSHOT</kmerp.caigou.version>
        <kmerp.pda.version>1.0.0-SNAPSHOT</kmerp.pda.version>
        <kmerp.aftersale.version>1.0.0-SNAPSHOT</kmerp.aftersale.version>
        <kmerp.fms.version>1.0.0-SNAPSHOT</kmerp.fms.version>
        <kmerp.sparrow.version>1.0.0-SNAPSHOT</kmerp.sparrow.version>
        <kmerp.storage.version>erp-1.0.0-SNAPSHOT</kmerp.storage.version>

        <kmerp.db.version>1.2.3</kmerp.db.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <mybatis.version>3.5.13</mybatis.version>
        <mybatis-spring-boot.version>2.3.1</mybatis-spring-boot.version>

        <kmerp.session.version>1.9.6-SNAPSHOT</kmerp.session.version>
        <kmerp.cache.version>1.6.9</kmerp.cache.version>
        <kmerp.secret.version>1.1-SNAPSHOT</kmerp.secret.version>
        <version.xmemcached>1.4.2</version.xmemcached>
        <version.redisson>3.18.1</version.redisson>
        <version.commons.lang3>3.12.0</version.commons.lang3>
        <version.commons.lang>2.6</version.commons.lang>
        <version.spring>5.3.31</version.spring>
        <kmerp.xserial.version>2.0-SNAPSHOT</kmerp.xserial.version>



        <xmemcache.version>1.4.2</xmemcache.version>
        <asm.version>9.3</asm.version>
    </properties>

    <dependencyManagement>
        <dependencies>

            <!-- KMERP导出依赖 START-->
            <dependency>
                <groupId>com.raycloud.dmj</groupId>
                <artifactId>chessboard-client-adapter</artifactId>
                <version>${kmerp.export.chessboard.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>fastjson</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>guava</artifactId>
                        <groupId>com.google.guava</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>hessian</artifactId>
                        <groupId>com.caucho</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>redisson</artifactId>
                        <groupId>org.redisson</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.raycloud.dmj</groupId>
                <artifactId>chessboard-dubbo-api</artifactId>
                <version>${kmerp.export.chessboard.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>fastjson</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>guava</artifactId>
                        <groupId>com.google.guava</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>hessian</artifactId>
                        <groupId>com.caucho</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>redisson</artifactId>
                        <groupId>org.redisson</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.raycloud.dmj</groupId>
                <artifactId>erp-data-export-configuration</artifactId>
                <version>${kmerp.data.export.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-compress</artifactId>
                        <groupId>org.apache.commons</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-io</artifactId>
                        <groupId>commons-io</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>fastjson</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>guava</artifactId>
                        <groupId>com.google.guava</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>javassist</artifactId>
                        <groupId>org.javassist</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>kryo</artifactId>
                        <groupId>com.esotericsoftware</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>objenesis</artifactId>
                        <groupId>org.objenesis</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.raycloud.dmj</groupId>
                <artifactId>erp-data-export-adapter</artifactId>
                <version>${kmerp.data.export.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-compress</artifactId>
                        <groupId>org.apache.commons</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-io</artifactId>
                        <groupId>commons-io</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>fastjson</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>guava</artifactId>
                        <groupId>com.google.guava</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>javassist</artifactId>
                        <groupId>org.javassist</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>kryo</artifactId>
                        <groupId>com.esotericsoftware</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>objenesis</artifactId>
                        <groupId>org.objenesis</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.raycloud.dmj</groupId>
                <artifactId>erp-data-export-plugin</artifactId>
                <version>${kmerp.data.export.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-compress</artifactId>
                        <groupId>org.apache.commons</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-io</artifactId>
                        <groupId>commons-io</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>fastjson</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>guava</artifactId>
                        <groupId>com.google.guava</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>javassist</artifactId>
                        <groupId>org.javassist</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>kryo</artifactId>
                        <groupId>com.esotericsoftware</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>objenesis</artifactId>
                        <groupId>org.objenesis</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-core</artifactId>
                <version>4.5.22</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.raycloud</groupId>
                <artifactId>secret-api-interface</artifactId>
                <version>${kmerp.secret.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-io</artifactId>
                        <groupId>commons-io</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-lang</artifactId>
                        <groupId>commons-lang</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-codec</artifactId>
                        <groupId>commons-codec</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.google.guava</groupId>
                        <artifactId>guava</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-mapper-asl</artifactId>
                        <groupId>org.codehaus.jackson</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-core-asl</artifactId>
                        <groupId>org.codehaus.jackson</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-core-lgpl</artifactId>
                        <groupId>org.codehaus.jackson</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-mapper-lgpl</artifactId>
                        <groupId>org.codehaus.jackson</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.raycloud.erp</groupId>
                <artifactId>erp-db-model</artifactId>
                <version>${kmerp.db.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
                <scope>provided</scope>
            </dependency>

            <!-- MyBatis 依赖 -->
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis-spring-boot.version}</version>
            </dependency>

            <dependency>
                <groupId>com.raycloud.analyzeExcel</groupId>
                <artifactId>AnalyzeExcel</artifactId>
                <version>${raycloud.excel.read.version}</version>
            </dependency>
            <!-- KMERP导出依赖 END-->

            <dependency>
                <groupId>com.raycloud.dmj</groupId>
                <artifactId>kmerp-account-configuration</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.raycloud.dmj</groupId>
                <artifactId>kmerp-account-export-backend</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.raycloud.dmj</groupId>
                <artifactId>kmerp-account-sync-backend</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.raycloud.dmj</groupId>
                <artifactId>kmerp-account-admin-frontend</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.raycloud.dmj</groupId>
                <artifactId>kmerp-account-dubbo-frontend</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.raycloud.dmj</groupId>
                <artifactId>kmerp-account-web-frontend</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.raycloud.dmj</groupId>
                <artifactId>kmerp-account-core</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.raycloud.dmj</groupId>
                <artifactId>kmerp-account-profiles</artifactId>
                <version>${project.version}</version>
            </dependency>

            <dependency>
                <groupId>com.raycloud.dmj</groupId>
                <artifactId>erp-table-api-plus</artifactId>
                <version>${table.api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.raycloud.dmj.kmbi</groupId>
                <artifactId>kmbi-boot-starter</artifactId>
                <version>${kmbi.dependency.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>reload4j</artifactId>
                        <groupId>ch.qos.reload4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>slf4j-reload4j</artifactId>
                        <groupId>org.slf4j</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.raycloud.dmj.kmbi</groupId>
                <artifactId>kmbi-boot-starter-webflux</artifactId>
                <version>${kmbi.dependency.version}</version>
            </dependency>

            <dependency>
                <groupId>com.raycloud.dmj.kmbi</groupId>
                <artifactId>kmbi-logging-dependency</artifactId>
                <version>${kmbi.dependency.version}</version>
            </dependency>

            <dependency>
                <groupId>com.raycloud.dmj.kmbi</groupId>
                <artifactId>kmbi-dubbo-dependency</artifactId>
                <version>${kmbi.dependency.version}</version>
            </dependency>

            <dependency>
                <groupId>com.raycloud.dmj.kmbi</groupId>
                <artifactId>kmbi-rocketmq-dependency</artifactId>
                <version>${kmbi.dependency.version}</version>
            </dependency>

            <dependency>
                <groupId>com.raycloud.dmj.kmbi</groupId>
                <artifactId>kmbi-redission-dependency</artifactId>
                <version>${kmbi.dependency.version}</version>
            </dependency>

            <dependency>
                <groupId>com.raycloud.dmj.kmbi</groupId>
                <artifactId>kmbi-jdbc-dependency</artifactId>
                <version>${kmbi.dependency.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>asm</artifactId>
                        <groupId>org.ow2.asm</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.raycloud.dmj.kmbi</groupId>
                <artifactId>kmbi-tool-dependency</artifactId>
                <version>${kmbi.dependency.version}</version>
            </dependency>

            <dependency>
                <groupId>com.raycloud.dmj.kmbi</groupId>
                <artifactId>kmbi-connector-adapter</artifactId>
                <version>${kmbi.connector.version}</version>
            </dependency>

            <dependency>
                <groupId>com.raycloud.dmj.kmbi</groupId>
                <artifactId>kmbi-connector-jdbc</artifactId>
                <version>${kmbi.connector.version}</version>
            </dependency>

            <dependency>
                <groupId>com.raycloud.dmj.kmbi</groupId>
                <artifactId>kmbi-connector-redis</artifactId>
                <version>${kmbi.connector.version}</version>
            </dependency>

            <dependency>
                <groupId>com.raycloud.dmj.kmbi</groupId>
                <artifactId>kmbi-leaf-adpter</artifactId>
                <version>${kmbi.uid.version}</version>
            </dependency>

            <dependency>
                <groupId>com.raycloud.dmj.kmbi</groupId>
                <artifactId>kmbi-data-io-api</artifactId>
                <version>${kmbi.io.version}</version>
            </dependency>

            <dependency>
                <groupId>com.raycloud.dmj.kmbi</groupId>
                <artifactId>kmbi-data-io-core</artifactId>
                <version>${kmbi.io.version}</version>
            </dependency>

            <dependency>
                <groupId>com.raycloud.cache</groupId>
                <artifactId>cache-ocs-direct</artifactId>
                <version>${kmerp.cache.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>commons-io</artifactId>
                        <groupId>commons-io</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-io</artifactId>
                        <groupId>commons-io</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-mapper-asl</artifactId>
                        <groupId>org.codehaus.jackson</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-core-asl</artifactId>
                        <groupId>org.codehaus.jackson</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-core-lgpl</artifactId>
                        <groupId>org.codehaus.jackson</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-mapper-lgpl</artifactId>
                        <groupId>org.codehaus.jackson</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>fastjson</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>servlet-api</artifactId>
                        <groupId>javax.servlet</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.raycloud.cache</groupId>
                <artifactId>cache-api</artifactId>
                <version>${kmerp.cache.version}</version>
            </dependency>

            <dependency>
                <groupId>com.raycloud.cache</groupId>
                <artifactId>cache-spring</artifactId>
                <version>${kmerp.cache.version}</version>
            </dependency>

            <dependency>
                <groupId>com.googlecode.xmemcached</groupId>
                <artifactId>xmemcached</artifactId>
                <version>${xmemcache.version}</version>
            </dependency>

            <dependency>
                <groupId>com.raycloud.dmj</groupId>
                <artifactId>erp-tb-common</artifactId>
                <version>${kmerp.core.version}</version>
                <exclusions>
                        <exclusion>
                            <artifactId>reload4j</artifactId>
                            <groupId>ch.qos.reload4j</groupId>
                        </exclusion>
                        <exclusion>
                            <artifactId>slf4j-reload4j</artifactId>
                            <groupId>org.slf4j</groupId>
                        </exclusion>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-io</artifactId>
                        <groupId>commons-io</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-logging</artifactId>
                        <groupId>commons-logging</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-lang</artifactId>
                        <groupId>commons-lang</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-codec</artifactId>
                        <groupId>commons-codec</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.google.guava</groupId>
                        <artifactId>guava</artifactId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-mapper-asl</artifactId>
                        <groupId>org.codehaus.jackson</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-core-asl</artifactId>
                        <groupId>org.codehaus.jackson</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-core-lgpl</artifactId>
                        <groupId>org.codehaus.jackson</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>jackson-mapper-lgpl</artifactId>
                        <groupId>org.codehaus.jackson</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.zookeeper</groupId>
                        <artifactId>zookeeper</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.raycloud</groupId>
                        <artifactId>bizlogger</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 引入快麦ERP的会话管理 END -->

            <!-- ERP基础依赖 -->
            <dependency>
                <groupId>com.raycloud.dmj</groupId>
                <artifactId>dmj-services-api</artifactId>
                <version>${kmerp.core.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>${project.groupId}</groupId>
                        <artifactId>erp-tb-common</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>${project.groupId}</groupId>
                        <artifactId>dmj-domain-basis</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.codehaus.jackson</groupId>
                        <artifactId>jackson-mapper-asl</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.codehaus.jackson</groupId>
                        <artifactId>jackson-core-asl</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-web</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.raycloud.dmj</groupId>
                        <artifactId>dmj-domain-trades</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.raycloud.ec</groupId>
                        <artifactId>ec-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.raycloud.dmj</groupId>
                <artifactId>dmj-domain-basis</artifactId>
                <version>${kmerp.core.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-beanutils</groupId>
                        <artifactId>commons-beanutils</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.apache.commons</groupId>
                        <artifactId>commons-lang3</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.google.guava</groupId>
                        <artifactId>guava</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.raycloud.erp</groupId>
                        <artifactId>erp-db-model</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.raycloud.erp</groupId>
                        <artifactId>erp-buffer-model</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.google.guava</groupId>
                        <artifactId>guava</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.alibaba</groupId>
                        <artifactId>fastjson</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.raycloud.erp</groupId>
                <artifactId>erp-db-model</artifactId>
                <version>${kmerp.db.version}</version>
            </dependency>

            <dependency>
                <groupId>org.ow2.asm</groupId>
                <artifactId>asm</artifactId>
                <version>${asm.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>${version.commons.lang}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${version.commons.lang3}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                <version>${version.spring}</version>
            </dependency>

            <dependency>
                <groupId>com.raycloud.commons</groupId>
                <artifactId>xserial-number-api</artifactId>
                <version>${kmerp.xserial.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>javassist</artifactId>
                        <groupId>org.javassist</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>dubbo</artifactId>
                        <groupId>com.alibaba</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- 排除JSONObject冲突 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>2.7.18</version>
                <scope>test</scope>
                <exclusions>
                    <exclusion>
                        <groupId>com.vaadin.external.google</groupId>
                        <artifactId>android-json</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
        </dependencies>
    </dependencyManagement>


    <distributionManagement>
        <repository>
            <id>raycloud2</id>
            <name>Nexus Release Repository</name>
            <url>http://maven.superboss.cc:8082/repository/maven-releases/</url>
        </repository>
        <snapshotRepository>
            <id>raycloud2</id>
            <name>Nexus Snapshot Repository</name>
            <url>http://maven.superboss.cc:8082/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <executions>
                    <execution>
                        <!-- 绑定source插件到Maven的生命周期 -->
                        <phase>compile</phase>
                        <!--在生命周期后执行绑定的source插件的goals -->
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>versions-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>gray1</id>
            <properties>
                <newVersion>gray-1.0-SNAPSHOT</newVersion>
                <kmerp.core.version>gray-1.0-SNAPSHOT</kmerp.core.version>
                <kmerp.pt.version>gray-1.0-SNAPSHOT</kmerp.pt.version>
                <kmerp.caigou.version>gray-1.0-SNAPSHOT</kmerp.caigou.version>
                <kmerp.pda.version>gray-1.0-SNAPSHOT</kmerp.pda.version>
                <kmerp.aftersale.version>gray-1.0-SNAPSHOT</kmerp.aftersale.version>
                <kmerp.fms.version>gray-1.0-SNAPSHOT</kmerp.fms.version>
                <kmerp.sparrow.version>gray-1.0-SNAPSHOT</kmerp.sparrow.version>
                <kmerp.storage.version>erp-gray-1.0-SNAPSHOT</kmerp.storage.version>
            </properties>
        </profile>
        <profile>
            <id>gray2</id>
            <properties>
                <newVersion>gray2-1.0-SNAPSHOT</newVersion>
                <kmerp.core.version>gray2-1.0-SNAPSHOT</kmerp.core.version>
                <kmerp.pt.version>gray2-1.0-SNAPSHOT</kmerp.pt.version>
                <kmerp.caigou.version>gray2-1.0-SNAPSHOT</kmerp.caigou.version>
                <kmerp.pda.version>gray2-1.0-SNAPSHOT</kmerp.pda.version>
                <kmerp.aftersale.version>gray2-1.0-SNAPSHOT</kmerp.aftersale.version>
                <kmerp.fms.version>gray2-1.0-SNAPSHOT</kmerp.fms.version>
                <kmerp.sparrow.version>gray2-1.0-SNAPSHOT</kmerp.sparrow.version>
                <kmerp.storage.version>erp-gray2-1.0-SNAPSHOT</kmerp.storage.version>
            </properties>
        </profile>
        <profile>
            <id>gray3</id>
            <properties>
                <newVersion>gray3-1.0-SNAPSHOT</newVersion>
                <kmerp.core.version>gray3-1.0-SNAPSHOT</kmerp.core.version>
                <kmerp.pt.version>gray3-1.0-SNAPSHOT</kmerp.pt.version>
                <kmerp.caigou.version>gray3-1.0-SNAPSHOT</kmerp.caigou.version>
                <kmerp.pda.version>gray3-1.0-SNAPSHOT</kmerp.pda.version>
                <kmerp.aftersale.version>gray3-1.0-SNAPSHOT</kmerp.aftersale.version>
                <kmerp.fms.version>gray3-1.0-SNAPSHOT</kmerp.fms.version>
                <kmerp.sparrow.version>gray3-1.0-SNAPSHOT</kmerp.sparrow.version>
                <kmerp.storage.version>erp-gray3-1.0-SNAPSHOT</kmerp.storage.version>
            </properties>
        </profile>
        <profile>
            <id>gray4</id>
            <properties>
                <newVersion>gray4-1.0-SNAPSHOT</newVersion>
                <kmerp.core.version>gray4-1.0-SNAPSHOT</kmerp.core.version>
                <kmerp.pt.version>gray4-1.0-SNAPSHOT</kmerp.pt.version>
                <kmerp.caigou.version>gray4-1.0-SNAPSHOT</kmerp.caigou.version>
                <kmerp.pda.version>gray4-1.0-SNAPSHOT</kmerp.pda.version>
                <kmerp.aftersale.version>gray4-1.0-SNAPSHOT</kmerp.aftersale.version>
                <kmerp.fms.version>gray4-1.0-SNAPSHOT</kmerp.fms.version>
                <kmerp.sparrow.version>gray4-1.0-SNAPSHOT</kmerp.sparrow.version>
                <kmerp.storage.version>erp-gray4-1.0-SNAPSHOT</kmerp.storage.version>
            </properties>
        </profile>
        <profile>
            <id>gray5</id>
            <properties>
                <newVersion>gray5-1.0-SNAPSHOT</newVersion>
                <kmerp.core.version>gray5-1.0-SNAPSHOT</kmerp.core.version>
                <kmerp.pt.version>gray5-1.0-SNAPSHOT</kmerp.pt.version>
                <kmerp.caigou.version>gray5-1.0-SNAPSHOT</kmerp.caigou.version>
                <kmerp.pda.version>gray5-1.0-SNAPSHOT</kmerp.pda.version>
                <kmerp.aftersale.version>gray5-1.0-SNAPSHOT</kmerp.aftersale.version>
                <kmerp.fms.version>gray5-1.0-SNAPSHOT</kmerp.fms.version>
                <kmerp.sparrow.version>gray5-1.0-SNAPSHOT</kmerp.sparrow.version>
                <kmerp.storage.version>erp-gray5-1.0-SNAPSHOT</kmerp.storage.version>
            </properties>
        </profile>
        <profile>
            <id>gray6</id>
            <properties>
                <newVersion>gray6-1.0-SNAPSHOT</newVersion>
                <kmerp.core.version>gray6-1.0-SNAPSHOT</kmerp.core.version>
                <kmerp.pt.version>gray6-1.0-SNAPSHOT</kmerp.pt.version>
                <kmerp.caigou.version>gray6-1.0-SNAPSHOT</kmerp.caigou.version>
                <kmerp.pda.version>gray6-1.0-SNAPSHOT</kmerp.pda.version>
                <kmerp.aftersale.version>gray6-1.0-SNAPSHOT</kmerp.aftersale.version>
                <kmerp.fms.version>gray6-1.0-SNAPSHOT</kmerp.fms.version>
                <kmerp.sparrow.version>gray6-1.0-SNAPSHOT</kmerp.sparrow.version>
                <kmerp.storage.version>erp-gray6-1.0-SNAPSHOT</kmerp.storage.version>
            </properties>
        </profile>
        <profile>
            <id>vipprod</id>
            <properties>
                <newVersion>vipprod-1.0-SNAPSHOT</newVersion>
                <kmerp.core.version>vipprod-1.0-SNAPSHOT</kmerp.core.version>
                <kmerp.pt.version>vipprod-1.0-SNAPSHOT</kmerp.pt.version>
                <kmerp.caigou.version>vipprod-1.0-SNAPSHOT</kmerp.caigou.version>
                <kmerp.pda.version>vipprod-1.0-SNAPSHOT</kmerp.pda.version>
                <kmerp.aftersale.version>vipprod-1.0-SNAPSHOT</kmerp.aftersale.version>
                <kmerp.fms.version>vipprod-1.0-SNAPSHOT</kmerp.fms.version>
                <kmerp.sparrow.version>vipprod-1.0-SNAPSHOT</kmerp.sparrow.version>
                <kmerp.storage.version>erp-vipprod-1.0-SNAPSHOT</kmerp.storage.version>
            </properties>
        </profile>
        <profile>
            <id>preissue</id>
            <properties>
                <newVersion>preissue-1.0-SNAPSHOT</newVersion>
                <kmerp.core.version>preissue-1.0-SNAPSHOT</kmerp.core.version>
                <kmerp.pt.version>preissue-1.0-SNAPSHOT</kmerp.pt.version>
                <kmerp.caigou.version>preissue-1.0-SNAPSHOT</kmerp.caigou.version>
                <kmerp.pda.version>preissue-1.0-SNAPSHOT</kmerp.pda.version>
                <kmerp.aftersale.version>preissue-1.0-SNAPSHOT</kmerp.aftersale.version>
                <kmerp.fms.version>preissue-1.0-SNAPSHOT</kmerp.fms.version>
                <kmerp.sparrow.version>preissue-1.0-SNAPSHOT</kmerp.sparrow.version>
                <kmerp.storage.version>erp-preissue-1.0-SNAPSHOT</kmerp.storage.version>
            </properties>
        </profile>
        <profile>
            <id>preissue2</id>
            <properties>
                <newVersion>1.137.preissue2-1.0-SNAPSHOT</newVersion>
                <kmerp.core.version>1.137.preissue2-1.0-SNAPSHOT</kmerp.core.version>
                <kmerp.pt.version>1.137.preissue2-1.0-SNAPSHOT</kmerp.pt.version>
                <kmerp.caigou.version>1.137.preissue2-1.0-SNAPSHOT</kmerp.caigou.version>
                <kmerp.pda.version>preissue2-1.0-SNAPSHOT</kmerp.pda.version>
                <kmerp.aftersale.version>1.137.preissue2-1.0-SNAPSHOT</kmerp.aftersale.version>
                <kmerp.fms.version>preissue2-1.0-SNAPSHOT</kmerp.fms.version>
                <kmerp.sparrow.version>preissue2-1.0-SNAPSHOT</kmerp.sparrow.version>
                <kmerp.storage.version>erp-preissue2-1.0-SNAPSHOT</kmerp.storage.version>
            </properties>
        </profile>

        <profile>
            <id>prod</id>
            <properties>
                <newVersion>1.137.2</newVersion>
                <kmerp.core.version>1.137.2</kmerp.core.version>
                <kmerp.pt.version>1.137.2</kmerp.pt.version>
                <kmerp.caigou.version>1.137.2</kmerp.caigou.version>
                <kmerp.pda.version>1.137.2</kmerp.pda.version>
                <kmerp.aftersale.version>1.137.2</kmerp.aftersale.version>
                <kmerp.fms.version>prod-1.0-SNAPSHOT</kmerp.fms.version>
                <kmerp.sparrow.version>prod-1.0-SNAPSHOT</kmerp.sparrow.version>
                <kmerp.storage.version>1.137.2</kmerp.storage.version>
            </properties>
        </profile>

    </profiles>
</project>