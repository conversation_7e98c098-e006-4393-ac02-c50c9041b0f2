package com.raycloud.dmj.account.web.configuration;

import com.raycloud.dmj.account.core.common.constant.SystemConstants;
import com.raycloud.dmj.account.core.session.SessionContext;
import com.raycloud.dmj.account.core.tj.vo.TenantEmployeeVo;
import com.raycloud.dmj.services.utils.ClueIdUtil;
import org.apache.log4j.Logger;
import org.apache.log4j.MDC;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 这个用于统计各个请求花费时间的统计
 */
@Configuration
public class ControllerTookInterceptor extends HandlerInterceptorAdapter {

    private final Logger logger = Logger.getLogger(this.getClass());

    private final ThreadLocal<Long> local = new ThreadLocal<>();

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (logger.isDebugEnabled()) {
            local.set(System.currentTimeMillis());
        }
        ClueIdUtil.generateClueId();
        return super.preHandle(request, response, handler);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        try {
            if (logger.isDebugEnabled()) {
                Long start = local.get();
                if (null != start) {
                    //获取会话用户
                    TenantEmployeeVo employeeVo =  SessionContext.get();

                    //持续时间
                    long duration = System.currentTimeMillis() - start;

                    String requestURI = request.getRequestURI();
                    logger.debug("["+ requestURI + "】[ "+(employeeVo!=null?employeeVo.getEmployeeName():"无")+" ]["+MDC.get(SystemConstants.CLUE_ID)+"]请求耗时：" + duration);
                }
            }
        }finally {
            // 资源回收
            local.remove();
            ClueIdUtil.removeClueId();
        }

        super.afterCompletion(request, response, handler, ex);
    }

}
