package com.raycloud.dmj.account.web.controller;

import com.raycloud.dmj.account.core.bill.request.RuleVerifyResultRecordRequest;
import com.raycloud.dmj.account.core.bill.service.IRuleVerifyResultRecordService;
import com.raycloud.dmj.account.core.bill.vo.RuleVerifyResultDetailVO;
import com.raycloud.dmj.account.core.common.response.Response;
import com.raycloud.dmj.account.infra.session.AccountUser;
import com.raycloud.dmj.account.infra.session.SessionController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 规则校验结果记录控制器
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/${web.dynamic.path}/ruleVerifyResultRecord")
public class RuleVerifyResultRecordController extends SessionController {

    @Resource
    private IRuleVerifyResultRecordService ruleVerifyResultRecordService;

    /**
     * 查询规则校验结果记录列表（按规则和时间维度）
     *
     * @param request 查询参数
     * @return 每个规则的校验结果详细信息列表
     */
    @PostMapping("/list")
    public Response<List<RuleVerifyResultDetailVO>> getRuleVerifyResultRecordList(@RequestBody RuleVerifyResultRecordRequest request) {
        AccountUser accountUser = getAccountUser();
        List<RuleVerifyResultDetailVO> voList = ruleVerifyResultRecordService.getRuleVerifyResultRecordList(request, accountUser);
        return Response.success(voList);
    }

}
