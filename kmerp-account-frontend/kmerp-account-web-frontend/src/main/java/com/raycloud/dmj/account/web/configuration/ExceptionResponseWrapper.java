package com.raycloud.dmj.account.web.configuration;

import com.raycloud.dmj.account.core.common.response.Response;
import com.raycloud.dmj.account.infra.common.ErrorCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

/**
 * 异常响应包装器
 * 用于统一处理异常信息的封装和日志记录
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Component
public class ExceptionResponseWrapper {

    /**
     * 包装业务异常响应
     * 
     * @param errorCode 错误码
     * @param errorMessage 错误信息
     * @param request HTTP请求对象
     * @param ex 异常对象
     * @return 封装后的响应对象
     */
    public Response<Object> wrapBusinessException(String errorCode, String errorMessage, 
                                                 HttpServletRequest request, Exception ex) {
        logException("业务异常", request, errorCode, errorMessage, ex);
        return Response.error(errorCode, errorMessage);
    }

    /**
     * 包装系统异常响应
     * 
     * @param errorMessage 错误信息
     * @param request HTTP请求对象
     * @param ex 异常对象
     * @return 封装后的响应对象
     */
    public Response<Object> wrapSystemException(String errorMessage, HttpServletRequest request, Exception ex) {
        logException("系统异常", request, ErrorCodeEnum.SYSTEM_ERROR.getCode(), errorMessage, ex);
        return Response.error(ErrorCodeEnum.SYSTEM_ERROR.getCode(), errorMessage);
    }

    /**
     * 包装参数异常响应
     * 
     * @param errorMessage 错误信息
     * @param request HTTP请求对象
     * @param ex 异常对象
     * @return 封装后的响应对象
     */
    public Response<Object> wrapParameterException(String errorMessage, HttpServletRequest request, Exception ex) {
        logException("参数异常", request, ErrorCodeEnum.PARAM_ERROR.getCode(), errorMessage, ex);
        return Response.error(ErrorCodeEnum.PARAM_ERROR.getCode(), errorMessage);
    }

    /**
     * 包装数据库异常响应
     * 
     * @param request HTTP请求对象
     * @param ex 异常对象
     * @return 封装后的响应对象
     */
    public Response<Object> wrapDatabaseException(HttpServletRequest request, Exception ex) {
        String errorMessage = "数据库操作异常，请稍后重试";
        logException("数据库异常", request, ErrorCodeEnum.DB_ERROR.getCode(), errorMessage, ex);
        return Response.error(ErrorCodeEnum.DB_ERROR.getCode(), errorMessage);
    }

    /**
     * 记录异常日志
     * 
     * @param exceptionType 异常类型
     * @param request HTTP请求对象
     * @param errorCode 错误码
     * @param errorMessage 错误信息
     * @param ex 异常对象
     */
    private void logException(String exceptionType, HttpServletRequest request, 
                             String errorCode, String errorMessage, Exception ex) {
        String requestInfo = buildRequestInfo(request);
        
        if (ex instanceof RuntimeException && !(ex.getCause() instanceof Exception)) {
            // 对于一般的运行时异常，使用warn级别
            log.warn("{} - {} - 错误码: {}, 错误信息: {}", 
                    exceptionType, requestInfo, errorCode, errorMessage);
        } else {
            // 对于系统级异常，使用error级别并打印堆栈
            log.error("{} - {} - 错误码: {}, 错误信息: {}", 
                    exceptionType, requestInfo, errorCode, errorMessage, ex);
        }
    }

    /**
     * 构建请求信息字符串
     * 
     * @param request HTTP请求对象
     * @return 请求信息字符串
     */
    private String buildRequestInfo(HttpServletRequest request) {
        if (request == null) {
            return "Unknown Request";
        }
        
        StringBuilder requestInfo = new StringBuilder();
        requestInfo.append("URL: ").append(request.getRequestURI());
        
        if (request.getQueryString() != null) {
            requestInfo.append("?").append(request.getQueryString());
        }
        
        requestInfo.append(", Method: ").append(request.getMethod());
        
        String userAgent = request.getHeader("User-Agent");
        if (userAgent != null && userAgent.length() > 100) {
            userAgent = userAgent.substring(0, 100) + "...";
        }
        
        String clientIp = getClientIpAddress(request);
        if (clientIp != null) {
            requestInfo.append(", IP: ").append(clientIp);
        }
        
        return requestInfo.toString();
    }

    /**
     * 获取客户端真实IP地址
     * 
     * @param request HTTP请求对象
     * @return 客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String[] headerNames = {
            "X-Forwarded-For",
            "X-Real-IP", 
            "Proxy-Client-IP",
            "WL-Proxy-Client-IP",
            "HTTP_CLIENT_IP",
            "HTTP_X_FORWARDED_FOR"
        };
        
        for (String headerName : headerNames) {
            String ip = request.getHeader(headerName);
            if (ip != null && ip.length() != 0 && !"unknown".equalsIgnoreCase(ip)) {
                // 多级代理的情况下，第一个IP为客户端真实IP
                if (ip.contains(",")) {
                    ip = ip.split(",")[0].trim();
                }
                return ip;
            }
        }
        
        return request.getRemoteAddr();
    }

    /**
     * 安全地获取异常消息
     * 避免敏感信息泄露
     * 
     * @param ex 异常对象
     * @return 安全的错误消息
     */
    public String getSafeErrorMessage(Exception ex) {
        if (ex == null) {
            return "未知错误";
        }
        
        String message = ex.getMessage();
        if (message == null || message.trim().isEmpty()) {
            return "系统内部错误";
        }
        
        // 过滤敏感信息
        message = message.replaceAll("(?i)password", "***")
                        .replaceAll("(?i)token", "***")
                        .replaceAll("(?i)secret", "***");
        
        // 限制错误消息长度
        if (message.length() > 200) {
            message = message.substring(0, 200) + "...";
        }
        
        return message;
    }
}
