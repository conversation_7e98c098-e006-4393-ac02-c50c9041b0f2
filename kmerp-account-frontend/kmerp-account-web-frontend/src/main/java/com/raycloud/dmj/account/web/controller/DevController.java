package com.raycloud.dmj.account.web.controller;

import com.raycloud.dmj.account.infra.session.SessionController;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.session.controller.ERPControllerBase;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/kmas/dev")
public class DevController extends SessionController {

    @GetMapping("hello")
    public Object hello() throws SessionException {
        Staff staff = getStaff();
        return "hello:" + staff.getAccountName();
    }
}
