package com.raycloud.dmj.account.web.controller;

import com.raycloud.dmj.account.export.core.ExportApplicationService;
import com.raycloud.dmj.account.infra.session.SessionController;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.session.SessionException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/kmrp")
public class TempOpsController extends SessionController {

    private final ExportApplicationService exportApplicationService;

    private final RedissonClient client;

    @GetMapping(value = "demo", produces = MediaType.APPLICATION_JSON_VALUE)
    public Object hello() throws SessionException {
        Staff staff = getStaff();
        return "hello:" + staff.getName();
    }

    @GetMapping(value = "ping", produces = MediaType.APPLICATION_JSON_VALUE)
    public Object ping() {
        return "pong";
    }


    @GetMapping(value = "export", produces = MediaType.APPLICATION_JSON_VALUE)
    public Object export(Integer sleeptime) throws SessionException {
        Staff staff = getLightStaff();
        return exportApplicationService.mockExportTask(staff, sleeptime);
    }

}
