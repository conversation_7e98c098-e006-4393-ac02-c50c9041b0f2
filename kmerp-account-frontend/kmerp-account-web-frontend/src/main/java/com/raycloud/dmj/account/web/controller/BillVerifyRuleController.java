package com.raycloud.dmj.account.web.controller;

import com.raycloud.dmj.account.core.bill.request.AddBillVerifyRuleRequest;
import com.raycloud.dmj.account.core.bill.request.BillVerifyRuleRequest;
import com.raycloud.dmj.account.core.bill.service.IBillVerifyRuleService;
import com.raycloud.dmj.account.core.bill.vo.BillVerifyRuleVo;
import com.raycloud.dmj.account.core.common.response.Response;
import com.raycloud.dmj.account.infra.session.AccountUser;
import com.raycloud.dmj.account.infra.session.SessionController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 账单核验规则控制器
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/${web.dynamic.path}/billVerifyRule")
public class BillVerifyRuleController extends SessionController {

    @Resource
    private IBillVerifyRuleService billVerifyRuleService;

    /**
     * 新增账单核验规则
     *
     * @param request 账单核验规则对象
     * @return 新增结果
     */
    @PostMapping("/save")
    public Response<Map<String, Object>> addBillVerifyRule(@RequestBody AddBillVerifyRuleRequest request) {
        AccountUser accountUser = getAccountUser();
        Long id = billVerifyRuleService.addBillVerifyRule(request, accountUser);
        Map<String, Object> result = new HashMap<>();
        result.put("id", id);
        return Response.success(result);
    }

    /**
     * 根据ID查询账单核验规则
     *
     * @param id 主键ID
     * @return 账单核验规则信息
     */
    @RequestMapping("/getDetail")
    public Response<BillVerifyRuleVo> getBillVerifyRuleById(Long id) {
        AccountUser accountUser = getAccountUser();
        BillVerifyRuleVo billVerifyRuleVO = billVerifyRuleService.getBillVerifyRuleById(id, accountUser);
        return Response.success(billVerifyRuleVO);
    }

    /**
     * 更新账单核验规则
     *
     * @param request 账单核验规则对象
     * @return 更新结果
     */
    @PostMapping("/update")
    public Response<Object> updateBillVerifyRule(@RequestBody AddBillVerifyRuleRequest request) {
        AccountUser accountUser = getAccountUser();
        billVerifyRuleService.updateBillVerifyRule(request, accountUser);
        return Response.success();
    }

    /**
     * 根据ID删除账单核验规则
     *
     * @param id 主键ID
     * @return 删除结果
     */
    @PostMapping("/delete")
    public Response<Object> deleteBillVerifyRule(@RequestParam Long id) {
        AccountUser accountUser = getAccountUser();
        billVerifyRuleService.deleteBillVerifyRule(id, accountUser);
        return Response.success();
    }

    /**
     * 根据ID集合批量删除账单核验规则
     *
     * @param idList 主键ID
     * @return 删除结果
     */
    @PostMapping("/bulkDeletion")
    public Response<Object> bulkDeletion(@RequestBody List<Long> idList) {
        AccountUser accountUser = getAccountUser();
        billVerifyRuleService.bulkDeletionBillVerifyRule(idList, accountUser);
        return Response.success();
    }

    /**
     * 查询账单核验规则列表
     *
     * @param request 查询参数
     * @return 账单核验规则列表
     */
    @RequestMapping("/list")
    public Response<List<BillVerifyRuleVo>> getBillVerifyRuleList(@RequestBody BillVerifyRuleRequest request) {
        AccountUser accountUser = getAccountUser();
        List<BillVerifyRuleVo> pageResult = billVerifyRuleService.getBillVerifyRuleList(request, accountUser);
        return Response.success(pageResult);
    }


    /**
     * 启用核验规则
     * @param idList
     * @return
     */
    @RequestMapping("/enableRule")
    public Response<Object> enableRule(@RequestBody List<Long> idList) {
        AccountUser accountUser = getAccountUser();
        billVerifyRuleService.enableRule(idList, accountUser);
        return Response.success();
    }

    /**
     * 关闭核验规则
     * @param idList
     * @return
     */
    @RequestMapping ("/shutDownRule")
    public Response<Object> shutDownRule(@RequestBody List<Long> idList) {
        AccountUser accountUser = getAccountUser();
        billVerifyRuleService.shutDownRule(idList, accountUser);
        return Response.success();
    }

}
