package com.raycloud.dmj.account.web.configuration;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Web MVC 配置类
 * 用于注册拦截器、配置静态资源等
 * 
 * <AUTHOR>
 */
@Configuration
public class WebMvcConfiguration implements WebMvcConfigurer {

    @Autowired
    private ControllerTookInterceptor controllerTookInterceptor;

    /**
     * 注册拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(controllerTookInterceptor)
                .addPathPatterns("/**")  // 拦截所有请求
                .excludePathPatterns(
                        "/ping",                           // 健康检查接口
                        "/project_auto_check_monitor.jsp", // 监控检查页面
                        "/error",                          // 错误页面
                        "/static/**",                      // 静态资源
                        "/css/**",                         // CSS文件
                        "/js/**",                          // JS文件
                        "/images/**",                      // 图片文件
                        "/favicon.ico",                    // 网站图标
                        "/actuator/**"                     // Spring Boot Actuator端点
                );
    }
}
