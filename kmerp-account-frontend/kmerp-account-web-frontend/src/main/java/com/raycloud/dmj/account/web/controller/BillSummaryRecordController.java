package com.raycloud.dmj.account.web.controller;

import com.raycloud.dmj.account.core.bill.request.BillSummaryRecordRequest;
import com.raycloud.dmj.account.core.bill.service.IBillSummaryRecordService;
import com.raycloud.dmj.account.core.bill.vo.BillSummaryRecordVO;
import com.raycloud.dmj.account.core.common.response.Response;
import com.raycloud.dmj.account.infra.session.AccountUser;
import com.raycloud.dmj.account.infra.session.SessionController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 资金流水合计记录控制器
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/${web.dynamic.path}/billSummaryRecord")
public class BillSummaryRecordController extends SessionController {

    @Resource
    private IBillSummaryRecordService billSummaryRecordService;

    /**
     * 根据参数查询资金流水合计记录列表
     *
     * @param request 查询参数
     * @return 资金流水合计记录VO列表
     */
    @PostMapping("/list")
    public Response<List<BillSummaryRecordVO>> getBillSummaryRecordList(@RequestBody BillSummaryRecordRequest request) {
        AccountUser accountUser = getAccountUser();
        List<BillSummaryRecordVO> voList = billSummaryRecordService.getBillSummaryRecordList(request, accountUser);
        return Response.success(voList);
    }
}
