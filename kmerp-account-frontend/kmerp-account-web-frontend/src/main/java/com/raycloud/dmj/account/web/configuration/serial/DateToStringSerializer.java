package com.raycloud.dmj.account.web.configuration.serial;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class DateToStringSerializer extends JsonSerializer<Date> {

    // 线程安全的 DateTimeFormatter
//    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").withZone(ZoneId.systemDefault()); // 设置时区
    private static final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    public Class<Date> handledType() {
        return Date.class;
    }

    @Override
    public void serialize(Date value, JsonGenerator gen, SerializerProvider serializers)
            throws IOException {
        // 将 Date 值转为 String 并写入 JSON
        if (value != null) {
            gen.writeString(formatDate(value));
        }
    }

    public static String formatDate(Date date) {
        if (date instanceof java.sql.Date) {
            return ((java.sql.Date) date).toLocalDate().toString();
        }
        return sdf.format(date);
    }

}
