package com.raycloud.dmj.account.web.controller;

import com.alibaba.fastjson2.JSON;
import com.raycloud.dmj.account.core.common.response.Response;
import com.raycloud.dmj.account.core.rawdata.req.BillUploadRequest;
import com.raycloud.dmj.account.core.rawdata.req.OriginalDataCallbackReq;
import com.raycloud.dmj.account.core.rawdata.service.RawDataStorageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 原始数据
 *
 * <AUTHOR> Raw data
 */
@RestController
@RequestMapping("/${web.dynamic.path}/rawData")
@Slf4j
public class RawDataController {


    @Resource
    private RawDataStorageService rawDataStorageService;

    @PostMapping("/upload")
    public Response<Void> upload(BillUploadRequest request) {
        rawDataStorageService.acceptRawDataStorageRequest(request);
        return Response.success();
    }

    @PostMapping("/rpaOriginalDataCallback")
    public Response<Void> rpaOriginalDataCallback(@RequestBody OriginalDataCallbackReq request) {
        log.info("|RawDataController.rpaOriginalDataCallback#|request#|{}|",JSON.toJSONString( request));
        String date = getDate(request);
        rawDataStorageService.rpaOriginalDataCallback(request);
        return Response.success();
    }

    public  String getDate(OriginalDataCallbackReq request) {
        return  JSON.toJSONString(request);
    }


}