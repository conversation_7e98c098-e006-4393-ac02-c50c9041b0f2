package com.raycloud.dmj.account.web.configuration.serial;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;

public class LongToStringSerializer extends JsonSerializer<Long> {

    @Override
    public Class<Long> handledType() {
        return Long.class;
    }

    @Override
    public void serialize(Long value, JsonGenerator gen, SerializerProvider serializers)
            throws IOException {
        // 将 Long 值转为 String 并写入 JSON
        if (value != null) {
            gen.writeString(value.toString());
        }
    }
}
