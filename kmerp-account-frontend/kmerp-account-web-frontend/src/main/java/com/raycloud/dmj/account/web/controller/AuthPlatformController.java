package com.raycloud.dmj.account.web.controller;

import com.raycloud.dmj.account.IPlatformAuthDubbo;
import com.raycloud.dmj.account.ISharedDataDubbo;
import com.raycloud.dmj.account.common.*;
import com.raycloud.dmj.account.core.common.response.Response;
import com.raycloud.dmj.account.core.platform.auth.handler.AliPayPlatformHandler;
import com.raycloud.dmj.account.core.rawdata.utils.DownloadFileByUrlUtil;
import com.raycloud.dmj.account.core.rawdata.utils.OutputFilePathUtil;
import com.raycloud.dmj.account.core.rawdata.utils.UnzipProcessor;
import com.raycloud.dmj.account.enums.DataType;
import com.raycloud.dmj.account.enums.PlatformType;
import com.raycloud.dmj.account.infra.utils.RandomUtils;
import com.raycloud.dmj.services.user.IUserService;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.view.RedirectView;

import javax.annotation.Resource;
import java.io.*;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

@RequestMapping("${web.dynamic.path}/auth-platform")
@RestController
public class AuthPlatformController {

    @DubboReference(check = false)
    private IPlatformAuthDubbo platformAuthDubbo;

    @DubboReference(check = false)
    private ISharedDataDubbo sharedDataDubbo;

    @Resource
    private AliPayPlatformHandler aliPayPlatformHandler;


    @DubboReference(check = false, registry = "erpZk", version = "erp-prod-0.0.1")
    private IUserService UserService;

    @GetMapping("/zfb-callback")
    public RedirectView zfbAuthCallBack(    @RequestParam("isAuth") Boolean isAuth,
                                    @RequestParam("callbackObj") String callbackObj,
                                    @RequestParam("sign") String sign,
                                    @RequestParam("message") String message) {
      return new RedirectView(aliPayPlatformHandler.callback(isAuth, callbackObj, sign, message));
    }

    @GetMapping("/test-zfb-callback")
    public Response<?> testCallback( @RequestParam("isAuth") Boolean isAuth,
                                     @RequestParam("callbackObj") String callbackObj,
                                     @RequestParam("sign") String sign,
                                     @RequestParam("message") String message) {

       return Response.success( "callback message" + "isAuth:" + isAuth + "callbackObj:" + callbackObj + "sign:" + sign + "message" + message);
    }

    @GetMapping("/create-zfb-auth")
    public Response<?> createZfbAuth(
                                @RequestParam("platform") String platform,
                                @RequestParam("companyId") Long companyId,
                                @RequestParam("shopId") Long shopId,
                                @RequestParam("callbackUrl") String callbackUrl) {
        CreateAuthUrlReq createAuthUrlReq = new CreateAuthUrlReq();
        createAuthUrlReq.setPlatformType(PlatformType.valueOf(platform));
        createAuthUrlReq.setCompanyId(companyId);
        createAuthUrlReq.setShopId(shopId);
        createAuthUrlReq.setCallbackUrl(callbackUrl);
        DubboResponse<CreateAuthResponse> authUrl = platformAuthDubbo.createAuthUrl(createAuthUrlReq);
        return Response.success(authUrl.getData().getAuthUrl());
    }

    @GetMapping("/summary2")
    public Response<?> summary2() {
        ShopBillUrlRequest shopBillUrlRequest = new ShopBillUrlRequest();
        shopBillUrlRequest.setPlatformType(PlatformType.ALIPAY);
        shopBillUrlRequest.setCompanyId(21401L);
        shopBillUrlRequest.setShopId(900178460L);
        shopBillUrlRequest.setDataType(DataType.BILL);
        shopBillUrlRequest.setBillDate(LocalDate.of(2025, 7, 1));
        return Response.success(sharedDataDubbo.getDataUrl(shopBillUrlRequest));
    }

    @GetMapping("/getAuthInfo")
    public Response<?> getAuthInfo() {
//        ShopBillUrlRequest shopBillUrlRequest = new ShopBillUrlRequest();
//        shopBillUrlRequest.setPlatform(PlatformType.ALIPAY);
//        shopBillUrlRequest.setCompanyId(21401L);
//        shopBillUrlRequest.setShopId(900178460L);
//        shopBillUrlRequest.setDataType(DataType.BILL);
//        shopBillUrlRequest.setBillDate(LocalDate.of(2025, 7, 1));

        AuthInfoReq authInfoReq = new AuthInfoReq();
        authInfoReq.setPlatformType(PlatformType.ALIPAY);
        authInfoReq.setCompanyId(21401L);
        authInfoReq.setShopId(900178460L);
        return Response.success(platformAuthDubbo.getAuthInfo(authInfoReq));
    }

    @GetMapping("/getUserInfo/{shopId}")
    public Response<?> getUserInfo(@PathVariable("shopId") Long shopId) {
        return Response.success(UserService.queryById(shopId));
    }

    @GetMapping("/getJdBill")
    public Response<?> getJdBill() {
        ShopBillUrlRequest shopBillUrlRequest = new ShopBillUrlRequest();
        shopBillUrlRequest.setPlatformType(PlatformType.JD_POP);
        shopBillUrlRequest.setCompanyId(10438L);
        shopBillUrlRequest.setShopId(79938L);
        shopBillUrlRequest.setDataType(DataType.BILL);
        shopBillUrlRequest.setBillDate(LocalDate.of(2025, 7, 28));
        return Response.success(sharedDataDubbo.getDataUrl(shopBillUrlRequest));
    }


}
