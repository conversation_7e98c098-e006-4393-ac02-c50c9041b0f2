package com.raycloud.dmj.account.web.controller;

import com.raycloud.dmj.account.core.bill.request.AddStandardOtherBillFlowInfoRequest;
import com.raycloud.dmj.account.core.bill.request.StandardFundBillFlowRequest;
import com.raycloud.dmj.account.core.bill.request.StandardOtherBillFlowRequest;
import com.raycloud.dmj.account.core.bill.service.IStandardOtherBillFlowInfoService;
import com.raycloud.dmj.account.core.bill.vo.StandardOtherBillFlowInfoVO;
import com.raycloud.dmj.account.core.common.PageInfo;
import com.raycloud.dmj.account.core.common.response.Response;
import com.raycloud.dmj.account.core.enums.OtherSubCategoryEnum;
import com.raycloud.dmj.account.infra.session.AccountUser;
import com.raycloud.dmj.account.infra.session.SessionController;
import com.raycloud.dmj.data.chessboard.model.PushResult;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.session.SessionException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 平台其他收支流水信息控制器
 */
@Slf4j
@RestController
@RequestMapping("/${web.dynamic.path}/otherBill")
public class StandardOtherBillFlowInfoController extends SessionController {

    @Resource
    private IStandardOtherBillFlowInfoService standardOtherBillFlowInfoService;

    /**
     * 新增平台其他收支流水信息
     *
     * @param request 流水信息
     * @return 新增结果
     */
    @PostMapping("/save")
    public Response<Object> addStandardOtherBillFlowInfo(@RequestBody AddStandardOtherBillFlowInfoRequest request) {
        AccountUser accountUser = getAccountUser();
        Long id = standardOtherBillFlowInfoService.addStandardOtherBillFlowInfo(accountUser, request);
        Map<String, Object> result = new HashMap<>();
        result.put("id", id);
        return Response.success(result);
    }

    /**
     * 分页查询平台其他收支流水信息列表
     *
     * @param request 查询条件
     * @return 分页结果
     */
    @PostMapping("/list")
    public Response<Object> getStandardOtherBillFlowInfoList(@RequestBody StandardOtherBillFlowRequest request) {
        AccountUser accountUser = getAccountUser();
        List<StandardOtherBillFlowInfoVO> voList = standardOtherBillFlowInfoService.getStandardOtherBillFlowInfoList(accountUser, request);
        return Response.success(voList);
    }

    /**
     * 根据条件查询分页统计信息
     *
     * @param request 查询条件
     * @return 分页统计信息
     */
    @PostMapping("/getPageInfo")
    public Response<Object> getPageInfo(@RequestBody StandardOtherBillFlowRequest request) {
        AccountUser accountUser = getAccountUser();
        PageInfo<Object> pageInfo = standardOtherBillFlowInfoService.getPageInfo(accountUser, request);
        return Response.success(pageInfo);
    }

    /**
     * 根据ID查询平台其他收支流水信息
     *
     * @param id 主键ID
     * @return 流水信息
     */
    @GetMapping("/getDetail")
    public Response<Object> getStandardOtherBillFlowInfoById(Long id) {
        AccountUser accountUser = getAccountUser();
        StandardOtherBillFlowInfoVO flowInfo = standardOtherBillFlowInfoService.getStandardOtherBillFlowInfoById(accountUser, id);
        return Response.success(flowInfo);
    }

    @PostMapping ("/export")
    public Response<Object> export(@RequestBody StandardOtherBillFlowRequest request) throws SessionException {
        Staff staff = getLightStaff();
        PushResult export = standardOtherBillFlowInfoService.export(staff, request);
        return Response.success("导出任务已创建", export.getClueId());
    }

    /**
     * 获取其他收支子分类code集合
     *
     * @return 子分类code集合
     */
    @GetMapping("/getSubCategoryList")
    public Response<List<Map<String,String>>> getSubCategoryList() {
        AccountUser accountUser = getAccountUser();
        return Response.success(OtherSubCategoryEnum.getSubCategoryList());
    }
}
