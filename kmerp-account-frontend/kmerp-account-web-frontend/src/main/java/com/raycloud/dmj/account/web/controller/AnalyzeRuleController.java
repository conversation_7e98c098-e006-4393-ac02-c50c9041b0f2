package com.raycloud.dmj.account.web.controller;

import com.raycloud.dmj.account.core.cleancategory.domain.request.*;
import com.raycloud.dmj.account.core.cleancategory.domain.vo.BillAgainAnalyzeTaskVO;
import com.raycloud.dmj.account.core.cleancategory.domain.vo.CategoryAnalyzeRuleVO;
import com.raycloud.dmj.account.core.cleancategory.service.AnalyzeRuleService;
import com.raycloud.dmj.account.core.common.PageListBase;
import com.raycloud.dmj.account.core.common.TreeVO;
import com.raycloud.dmj.account.core.common.response.Response;
import com.raycloud.dmj.account.infra.session.AccountUser;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 解析规则管理
 * <AUTHOR>
 */
@RequestMapping("/${web.dynamic.path}/categoryAnalyzeRule")
@RestController
public class AnalyzeRuleController {


    @Resource
    private AnalyzeRuleService analyzeRuleService;


    /**
     * 编辑解析规则
     * @param req 编辑解析规则请求
     * @return 编辑结果
     */
    @PostMapping("/editAnalyzeRule")
    public Response<Void> editAnalyzeRule(@RequestBody EditAnalyzeRuleReq req) {
        //暂时先写死 后面从登陆态获取
        AccountUser accountUser = new AccountUser();
        accountUser.setCompanyId(10438L);
        //修改类目
        analyzeRuleService.editAnalyzeRule(accountUser, req);
        return Response.success();
    }


    /**
     * 添加解析规则
     * @param req 添加解析规则
     * @return 添加ID
     */
    @PostMapping("/addAnalyzeRule")
    public Response<Long> addAnalyzeRule(@RequestBody AddAnalyzeRuleReq req) {
        //暂时先写死 后面从登陆态获取
        AccountUser accountUser = new AccountUser();
        accountUser.setCompanyId(10438L);
        return Response.success(analyzeRuleService.addAnalyzeRule(accountUser, req));
    }


    /**
     * 删除规则
     * @param req 删除规则请求
     * @return 删除结果
     */
    @PostMapping("/deleteAnalyzeRule")
    public Response<Void> deleteAnalyzeRule(@RequestBody DeleteAnalyzeRuleReq req) {
        //暂时先写死 后面从登陆态获取
        AccountUser accountUser = new AccountUser();
        accountUser.setCompanyId(10438L);
        analyzeRuleService.deleteAnalyzeRule(accountUser, req);
        return Response.success();
    }

    /**
     * 分页查询规则列表
     * @param req 查询参数
     * @return 规则列表
     */
    @RequestMapping("/pageQueryAnalyzeRule")
    public Response<PageListBase<CategoryAnalyzeRuleVO>> pageQueryAnalyzeRule(@RequestBody QueryAnalyzeRuleReq  req) {
        //暂时先写死 后面从登陆态获取
        AccountUser accountUser = new AccountUser();
        accountUser.setCompanyId(10438L);
        PageListBase<CategoryAnalyzeRuleVO> pageVO = analyzeRuleService.pageQueryAnalyzeRule(accountUser, req);
        //查询资金账户
        return Response.success(pageVO);
    }


    /**
     * 重新洗数
     * @param req 重新洗数请求
     * @return 重新洗数结果
     */
    @PostMapping("/againAnalyze")
    public Response<Long> againAnalyze(@RequestBody AgainAnalyzeReq  req) {
        //暂时先写死 后面从登陆态获取
        AccountUser accountUser = new AccountUser();
        accountUser.setCompanyId(10438L);
        //重新洗数
        return Response.success(analyzeRuleService.againAnalyze(accountUser, req));
    }

    /**
     * 查询重新洗数任务列表
     * @param req 查询参数
     * @return 重新洗数任务列表
     */
    @GetMapping(value = "listBillAgainAnalyzeTask")
    public Response<List<BillAgainAnalyzeTaskVO>> listBillAgainAnalyzeTask(QueryBillAgainAnalyzeTaskReq req) {
        //暂时先写死 后面从登陆态获取
        AccountUser accountUser = new AccountUser();
        accountUser.setCompanyId(10438L);
        //查询重新洗数列表
        return Response.success(analyzeRuleService.listBillAgainAnalyzeTask(accountUser, req));
    }

    /**
     * 查询满足条件字段树形结构
     * @return 字段
     */
    @GetMapping("/treeMatchField")
    public Response<List<TreeVO>> treeMatchField(TreeMatchFieldReq req) {
        //暂时先写死 后面从登陆态获取
        AccountUser accountUser = new AccountUser();
        accountUser.setCompanyId(10438L);
        //查询资金账户类型
        return Response.success(analyzeRuleService.treeMatchFieldTree(accountUser,req));
    }



    /**
     * 查询分析规则运算符树形结构
     * @return 运算符树形结构
     */
    @GetMapping("/treeAnalyzeRuleOperator")
    public Response<List<TreeVO>> treeAnalyzeRuleOperator() {
        //暂时先写死 后面从登陆态获取
        AccountUser accountUser = new AccountUser();
        accountUser.setCompanyId(10438L);
        //查询资金账户类型
        return Response.success(analyzeRuleService.treeAnalyzeRuleOperator(accountUser));
    }


}
