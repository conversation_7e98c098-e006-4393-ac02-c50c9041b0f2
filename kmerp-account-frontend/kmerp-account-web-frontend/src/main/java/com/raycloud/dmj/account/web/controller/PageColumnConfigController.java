package com.raycloud.dmj.account.web.controller;

import com.raycloud.dmj.account.core.common.response.Response;
import com.raycloud.dmj.account.core.pageconfig.request.BatchAddPageColumnConfigRequest;
import com.raycloud.dmj.account.core.pageconfig.request.BatchDeletePageColumnConfigRequest;
import com.raycloud.dmj.account.core.pageconfig.request.BatchUpdatePageColumnConfigRequest;
import com.raycloud.dmj.account.core.pageconfig.service.IPageColumnConfigService;
import com.raycloud.dmj.account.core.pageconfig.vo.PageColumnConfigVO;
import com.raycloud.dmj.account.infra.session.AccountUser;
import com.raycloud.dmj.account.infra.session.SessionController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 页面列配置控制器
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/${web.dynamic.path}/pageColumnConfig")
public class PageColumnConfigController extends SessionController {

    @Resource
    private IPageColumnConfigService pageColumnConfigService;

    /**
     * 批量新增页面列配置
     * @param request 新增请求
     * @return 新增结果
     */
    @PostMapping("/batchAdd")
    public Response<Map<String, Object>> batchAddPageColumnConfig(@RequestBody BatchAddPageColumnConfigRequest request) {
        AccountUser accountUser = getAccountUser();
        Integer count = pageColumnConfigService.batchAddPageColumnConfig(accountUser, request);
        
        Map<String, Object> result = new HashMap<>();
        result.put("count", count);
        return Response.success(result);
    }

    /**
     * 批量更新页面列配置
     * @param request 更新请求
     * @return 更新结果
     */
    @PostMapping("/batchUpdate")
    public Response<Map<String, Object>> batchUpdatePageColumnConfig(@RequestBody BatchUpdatePageColumnConfigRequest request) {
        AccountUser accountUser = getAccountUser();
        Integer count = pageColumnConfigService.batchUpdatePageColumnConfig(accountUser, request);
        
        Map<String, Object> result = new HashMap<>();
        result.put("count", count);
        return Response.success(result);
    }

    /**
     * 批量删除页面列配置
     * @param request 删除请求
     * @return 删除结果
     */
    @PostMapping("/batchDelete")
    public Response<Map<String, Object>> batchDeletePageColumnConfig(@RequestBody BatchDeletePageColumnConfigRequest request) {
        AccountUser accountUser = getAccountUser();
        Integer count = pageColumnConfigService.batchDeletePageColumnConfig(accountUser, request);
        
        Map<String, Object> result = new HashMap<>();
        result.put("count", count);
        return Response.success(result);
    }

    /**
     * 根据页面ID删除配置
     * @param pageId 页面ID
     * @return 删除结果
     */
    @PostMapping("/deleteByPageId")
    public Response<Map<String, Object>> deleteByPageId(@RequestParam Long pageId) {
        AccountUser accountUser = getAccountUser();
        Integer count = pageColumnConfigService.deleteByPageId(accountUser, pageId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("count", count);
        return Response.success(result);
    }

    /**
     * 根据页面ID查询配置列表
     * @param pageId 页面ID
     * @return 配置列表
     */
    @GetMapping("/queryByPageId")
    public Response<List<PageColumnConfigVO>> queryByPageId(@RequestParam Long pageId) {
        AccountUser accountUser = getAccountUser();
        List<PageColumnConfigVO> configList = pageColumnConfigService.queryByPageId(accountUser, pageId);
        return Response.success(configList);
    }

    /**
     * 根据ID查询单个配置
     * @param id 配置ID
     * @return 配置信息
     */
    @GetMapping("/queryById")
    public Response<PageColumnConfigVO> queryById(@RequestParam Long id) {
        AccountUser accountUser = getAccountUser();
        PageColumnConfigVO config = pageColumnConfigService.queryById(accountUser, id);
        return Response.success(config);
    }
}
