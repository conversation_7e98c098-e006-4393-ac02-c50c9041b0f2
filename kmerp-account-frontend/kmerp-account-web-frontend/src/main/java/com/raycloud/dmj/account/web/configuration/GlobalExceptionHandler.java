package com.raycloud.dmj.account.web.configuration;

import com.raycloud.dmj.account.core.common.response.Response;
import com.raycloud.dmj.account.infra.common.BusinessException;
import com.raycloud.dmj.account.infra.common.ErrorCodeEnum;
import com.raycloud.dmj.session.SessionException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import java.sql.SQLException;

/**
 * 全局异常处理器
 * 统一拦截和处理系统中的所有异常，并封装为统一的响应格式
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    @Autowired
    private ExceptionResponseWrapper responseWrapper;

    /**
     * 处理会话异常
     */
    @ExceptionHandler(SessionException.class)
    public Response<Object> handleSessionException(SessionException ex, HttpServletRequest request) {
        return responseWrapper.wrapSystemException("会话已过期，请重新登录", request, ex);
    }

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    public Response<Object> handleBusinessException(BusinessException ex, HttpServletRequest request) {
        return responseWrapper.wrapBusinessException(ex.getErrorCode(), ex.getErrorMessage(), request, ex);
    }

    /**
     * 处理HTTP请求方法不支持异常
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public Response<Object> handleMethodNotSupportedException(HttpRequestMethodNotSupportedException ex, HttpServletRequest request) {
        String errorMsg = String.format("不支持的请求方法: %s", ex.getMethod());
        log.warn("请求方法不支持异常 - URL: {}, 错误信息: {}", request.getRequestURI(), errorMsg);
        return Response.error(ErrorCodeEnum.PARAM_ERROR.getCode(), errorMsg);
    }

    /**
     * 处理数据库相关异常
     */
    @ExceptionHandler({SQLException.class, DataAccessException.class})
    public Response<Object> handleDatabaseException(Exception ex, HttpServletRequest request) {
        log.error("数据库异常 - URL: {}", request.getRequestURI(), ex);
        return Response.error(ErrorCodeEnum.DB_ERROR.getCode(), "数据库操作异常," + ex.getMessage());
    }

    /**
     * 处理数字格式异常
     */
    @ExceptionHandler(NumberFormatException.class)
    public Response<Object> handleNumberFormatException(NumberFormatException ex, HttpServletRequest request) {
        log.warn("数字格式异常 - URL: {}, 错误信息: {}", request.getRequestURI(), ex.getMessage());
        return Response.error(ErrorCodeEnum.PARAM_ERROR.getCode(), "数字格式错误，请检查输入参数");
    }

    /**
     * 处理运行时异常（RuntimeException的通用处理）
     */
    @ExceptionHandler(RuntimeException.class)
    public Response<Object> handleRuntimeException(RuntimeException ex, HttpServletRequest request) {
        log.error("运行时异常 - URL: {}", request.getRequestURI(), ex);
        return Response.error(ErrorCodeEnum.SYSTEM_ERROR.getCode(), ex.getMessage());
    }

    /**
     * 处理所有未捕获的异常（兜底处理）
     */
    @ExceptionHandler(Throwable.class)
    public Response<Object> handleThrowable(Throwable ex, HttpServletRequest request) {
        log.error("未知异常 - URL: {}", request.getRequestURI(), ex);
        return Response.error(ErrorCodeEnum.SYSTEM_ERROR.getCode(), "系统繁忙，请稍后重试");
    }
}
