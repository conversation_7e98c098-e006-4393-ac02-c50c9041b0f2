package com.raycloud.dmj.account.web;

import com.alibaba.fastjson2.JSON;
import com.raycloud.dmj.account.core.common.response.Response;
import com.raycloud.dmj.account.infra.common.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

/**
 * 优化后的数据包装拦截器
 *
 * <AUTHOR>
 */
@Component
@Aspect
@Order(0)
@Slf4j
public class ResponseDataWrapperAspect {

    @Pointcut("@annotation(com.raycloud.dmj.account.core.common.Log)")
    public void aspect() {
    }

    @Around("aspect()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        String methodName = signature.getName();
        String className = joinPoint.getTarget().getClass().getSimpleName();
        Object[] args = joinPoint.getArgs();
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Object response = null;
        Boolean result = null;
        Exception error = null;
        try {
            response = joinPoint.proceed();
            result = true;
            return response;
        } catch (Exception e) {
            error=e;
            result = false;
            throw e;
        } finally {
            stopWatch.stop();
            // 记录日志
            log.info("|ResponseDataWrapper|method={}.{}|result={}|request={}|response={}|cost={}ms|",
                    className, methodName, result, JSON.toJSONString(args), JSON.toJSONString(response), stopWatch.getTotalTimeMillis(), error);
        }
    }
}