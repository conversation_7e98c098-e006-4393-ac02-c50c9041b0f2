package com.raycloud.dmj.account.web.controller;


import com.raycloud.dmj.account.core.cleancategory.domain.request.*;
import com.raycloud.dmj.account.core.cleancategory.domain.vo.CategoryGroupVO;
import com.raycloud.dmj.account.core.cleancategory.domain.vo.SimpleSubCategoryVo;
import com.raycloud.dmj.account.core.cleancategory.domain.vo.SubCategoryVO;
import com.raycloud.dmj.account.core.cleancategory.service.CategoryManageService;
import com.raycloud.dmj.account.core.common.PageListBase;
import com.raycloud.dmj.account.core.common.TreeVO;
import com.raycloud.dmj.account.core.common.response.Response;
import com.raycloud.dmj.account.infra.session.AccountUser;
import com.raycloud.dmj.account.infra.session.SessionController;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 类别管理
 *
 * <AUTHOR>
 */
@RequestMapping("/${web.dynamic.path}/category")
@RestController
public class CategoryController extends SessionController {

    @Resource
    private CategoryManageService categoryManageService;


    /**
     * 编辑分组
     * @param req 编辑分组参数
     * @return  void
     */
    @PostMapping("/editCategoryGroup")
    public Response<Void> editCategoryGroup(@RequestBody EditCategoryGroupReq req) {
        //暂时先写死 后面从登陆态获取
        AccountUser accountUser = new AccountUser();
        accountUser.setCompanyId(10438L);
        //修改类目
        categoryManageService.editCategoryGroup(accountUser, req);
        return Response.success();
    }


    /**
     * 新增类别
     * @param addCategoryReq 新增类别参数
     * @return 子类别ID
     */
    @PostMapping("/addCategory")
    public Response<Long> addCategory(@RequestBody AddCategoryReq addCategoryReq) {
        //暂时先写死 后面从登陆态获取
        AccountUser accountUser = new AccountUser();
        accountUser.setCompanyId(10438L);
        return Response.success(categoryManageService.addCategory(accountUser, addCategoryReq));
    }


    /**
     * 删除类别
     * @param req 删除类别参数
     * @return  void
     */
    @PostMapping("/deleteCategory")
    public Response<Void> deleteCategory(@RequestBody DeleteCategoryReq req) {
        //暂时先写死 后面从登陆态获取
        AccountUser accountUser = new AccountUser();
        accountUser.setCompanyId(10438L);
        categoryManageService.deleteCategory(accountUser, req);
        return Response.success();
    }


    /**
     * 编辑类别
     * @param req 编辑类别参数
     * @return  void
     */
    @PostMapping("/editCategory")
    public Response<Void> editCategory(@RequestBody EditCategoryReq req) {
        //暂时先写死 后面从登陆态获取
        AccountUser accountUser = new AccountUser();
        accountUser.setCompanyId(10438L);
        //修改类目
        categoryManageService.editCategory(accountUser, req);
        return Response.success();
    }

    /**
     * 分页查询子类别
     * @param req 查询子类别参数
     * @return 子类别列表
     */
    @RequestMapping("/pageQueryCategoryList")
    public Response<PageListBase<SubCategoryVO>> pageQueryCategoryList(@RequestBody QueryCategoryReq req) {
        //暂时先写死 后面从登陆态获取
        AccountUser accountUser = new AccountUser();
        accountUser.setCompanyId(10438L);
        PageListBase<SubCategoryVO> pageVO = categoryManageService.pageQueryCategoryList(accountUser, req);
        //查询资金账户
        return Response.success(pageVO);
    }

    /**
     * 查询分类
     * @param req 查询分组参数
     * @return 分组列表
     */
    @GetMapping("/listCategoryGroup")
    public Response<List<CategoryGroupVO>> listCategoryGroup(QueryCategoryGroupReq req) {
        //暂时先写死 后面从登陆态获取
        AccountUser accountUser = new AccountUser();
        accountUser.setCompanyId(10438L);
        return Response.success(categoryManageService.listCategoryGroup(accountUser, req));
    }


    /**
     * 查询分类组树
     * @param req 查询分组参数
     * @return 分类组树
     */
    @RequestMapping("/queryCategoryGroupTree")
    public Response<List<TreeVO>> queryCategoryGroupTree(QueryCategoryGroupTreeReq req) {
        //暂时先写死 后面从登陆态获取
        AccountUser accountUser = new AccountUser();
        accountUser.setCompanyId(10438L);
        return Response.success(categoryManageService.queryCategoryGroupTree(accountUser, req));
    }


    /**
     * 查询类别树
     * @param req 查询类别参数
     * @return 类别树
     */
    @PostMapping("/queryCategoryTree")
    public Response<List<TreeVO>> queryCategoryTree(@RequestBody QueryCategoryTreeReq req) {
        //暂时先写死 后面从登陆态获取
        AccountUser accountUser = new AccountUser();
        accountUser.setCompanyId(10438L);
        return Response.success(categoryManageService.queryCategoryTree(accountUser, req));
    }

    /**
     * 根据资金账户ID集合查询子类别简单信息
     * @param req 查询子类别参数
     * @return 子类别列表
     */
    @PostMapping("/getSimpleSubCategoryVoByAccountId")
    public Response<List<SimpleSubCategoryVo>> getSimpleSubCategoryVoByAccountId(@RequestBody QuerySubCategoryReq req) {
        AccountUser accountUser = getAccountUser();
        return Response.success(categoryManageService.getSimpleSubCategoryVoByAccountId(accountUser, req));
    }

/*    @GetMapping("/querySubCategoryTree")
    public Response<List<TreeVO>> querySubCategoryTree(QuerySubCategoryTreeReq req) {
        //暂时先写死 后面从登陆态获取
        AccountUser accountUser = new AccountUser();
        accountUser.setCompanyId(10438L);
        return Response.success(categoryManageService.querySubCategoryTree(accountUser, req));
    }*/







}
