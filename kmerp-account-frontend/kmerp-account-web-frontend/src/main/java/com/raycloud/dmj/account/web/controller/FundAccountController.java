package com.raycloud.dmj.account.web.controller;


import com.raycloud.dmj.account.core.cleancategory.domain.request.*;
import com.raycloud.dmj.account.core.cleancategory.domain.vo.FundAccountVO;
import com.raycloud.dmj.account.core.cleancategory.service.FundAccountService;
import com.raycloud.dmj.account.core.common.TreeVO;
import com.raycloud.dmj.account.core.common.response.Response;
import com.raycloud.dmj.account.infra.session.AccountUser;
import com.raycloud.dmj.account.infra.session.SessionController;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 账户管理
 *
 * <AUTHOR>
 */
@RequestMapping("/${web.dynamic.path}/fundAccount")
@RestController
public class FundAccountController extends SessionController {

    @Resource
    private FundAccountService fundAccountService;


    @PostMapping("/addFundAccount")
    public Response<Long> addFundAccount(@RequestBody AddFundAccountReq addFundAccountReq) {
        //暂时先写死 后面从登陆态获取
        AccountUser accountUser = new AccountUser();
        accountUser.setCompanyId(10438L);
        return Response.success(fundAccountService.addFundAccount(accountUser, addFundAccountReq));
    }

    @PostMapping("/editFundAccount")
    public Response<Boolean> editFundAccount(@RequestBody EditFundAccountReq req) {
        //暂时先写死 后面从登陆态获取
        AccountUser accountUser = new AccountUser();
        accountUser.setCompanyId(10438L);
        //修改资金账户
        fundAccountService.editFundAccount(accountUser, req);
        return Response.success(Boolean.TRUE);
    }

    @GetMapping("/queryFundAccount")
    public Response<List<FundAccountVO>> queryFundAccount() {
        //暂时先写死 后面从登陆态获取
        AccountUser accountUser = new AccountUser();
        accountUser.setCompanyId(10438L);
        //查询资金账户
        return Response.success(fundAccountService.queryFundAccountList(accountUser));
    }

    @PostMapping("/authorizeFundAccount")
    public Response<Boolean> authorizeFundAccount(@RequestBody AuthorizeFundAccountReq req) {
        //暂时先写死 后面从登陆态获取
        AccountUser accountUser = new AccountUser();
        accountUser.setCompanyId(10438L);
        //查询资金账户
        fundAccountService.authorizeFundAccount(accountUser, req.getId());
        return Response.success(Boolean.TRUE);
    }

    @PostMapping("/confirmStartPeriod")
    public Response<Boolean> confirmStartPeriod(@RequestBody ConfirmStartPeriodReq req) {
        //暂时先写死 后面从登陆态获取
        AccountUser accountUser = new AccountUser();
        accountUser.setCompanyId(10438L);
        //查询资金账户
        fundAccountService.confirmStartPeriod(accountUser, req.getId());
        return Response.success(Boolean.TRUE);
    }


    @GetMapping("/queryFundTypeTree")
    public Response<List<TreeVO>> queryFundTypeTree() {
        //暂时先写死 后面从登陆态获取
        AccountUser accountUser = new AccountUser();
        accountUser.setCompanyId(10438L);
        //查询资金账户类型
        return Response.success(fundAccountService.queryFundTypeTree(accountUser));
    }

    @GetMapping("/queryFundAccountTree")
    public Response<List<TreeVO>> queryFundAccountTree() {
        //暂时先写死 后面从登陆态获取
        AccountUser accountUser = new AccountUser();
        accountUser.setCompanyId(10438L);
        //查询资金账户类型
        return Response.success(fundAccountService.queryFundAccountTree(accountUser));
    }

    /**
     * 根据店铺ID集合查询资金账户简单信息
     * @param req 请求参数
     * @return 资金账户简单信息列表
     */
    @RequestMapping ("/getSimpleFundAccountList")
    public Response<Object> getSimpleFundAccountList(@RequestBody QueryFundAccountReq req) {
        //暂时先写死 后面从登陆态获取
//        AccountUser accountUser = getAccountUser();
        AccountUser accountUser = new AccountUser();
        accountUser.setCompanyId(10438L);
        //查询资金账户类型
        return Response.success(fundAccountService.getSimpleFundAccountList(accountUser,req));
    }
}
