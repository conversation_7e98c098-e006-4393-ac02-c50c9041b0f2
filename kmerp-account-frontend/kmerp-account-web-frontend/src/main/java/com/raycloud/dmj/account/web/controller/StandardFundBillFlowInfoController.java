package com.raycloud.dmj.account.web.controller;

import com.raycloud.dmj.account.core.bill.request.AddStandardFundBillFlowInfoRequest;
import com.raycloud.dmj.account.core.bill.request.StandardFundBillFlowRequest;
import com.raycloud.dmj.account.core.bill.request.StandardFundBillSplitFlowRequest;
import com.raycloud.dmj.account.core.bill.service.IStandardFundBillFlowInfoService;
import com.raycloud.dmj.account.core.bill.vo.StandardFundBillFlowInfoVO;
import com.raycloud.dmj.account.core.common.PageInfo;
import com.raycloud.dmj.account.core.common.response.Response;
import com.raycloud.dmj.account.infra.session.AccountUser;
import com.raycloud.dmj.account.infra.session.SessionController;
import com.raycloud.dmj.data.chessboard.model.PushResult;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.session.SessionException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 标准资金账单流水信息控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/${web.dynamic.path}/fundBill")
public class StandardFundBillFlowInfoController extends SessionController {

    @Resource
    private IStandardFundBillFlowInfoService standardFundBillFlowInfoService;

    /**
     * 新增标准资金账单流水信息
     *
     * @param request 流水信息
     * @return 新增结果
     */
    @PostMapping("/save")
    public Response<Object> addStandardFundBillFlowInfo(@RequestBody AddStandardFundBillFlowInfoRequest request) {
        AccountUser accountUser = getAccountUser();
        Long id = standardFundBillFlowInfoService.addStandardFundBillFlowInfo(accountUser, request);
        Map<String, Object> result = new HashMap<>();
        result.put("id", id);
        return Response.success(result);
    }

    /**
     * 批量新增标准资金账单流水信息
     *
     * @param requestList 流水信息列表
     * @return 批量新增结果
     */
    @PostMapping("/batchAdd")
    public Response<String> batchAddStandardFundBillFlowInfo(@RequestBody List<AddStandardFundBillFlowInfoRequest> requestList) {
        AccountUser accountUser = getAccountUser();
        standardFundBillFlowInfoService.batchAddStandardFundBillFlowInfo(accountUser, requestList);
        return Response.success("批量新增成功，共处理 " + requestList.size() + " 条记录");
    }

    /**
     * 根据条件查询标准资金账单流水信息列表
     *
     * @param request 查询条件
     * @return 流水信息列表
     */
    @PostMapping("/list")
    public Response<Object> getStandardFundBillFlowInfoList(@RequestBody StandardFundBillFlowRequest request) {
        AccountUser accountUser = getAccountUser();
        List<StandardFundBillFlowInfoVO> voList = standardFundBillFlowInfoService.getStandardFundBillFlowInfoList(accountUser, request);
        return Response.success(voList);
    }

    /**
     * 根据条件查询分页统计信息
     *
     * @param request 查询条件
     * @return 分页统计信息
     */
    @PostMapping ("/getPageInfo")
    public Response<Object> getPageInfo(@RequestBody StandardFundBillFlowRequest request) {
        AccountUser accountUser = getAccountUser();
        PageInfo<Object> pageInfo = standardFundBillFlowInfoService.getPageInfo(accountUser, request);
        return Response.success(pageInfo);
    }

    /**
     * 根据ID查询标准资金账单流水信息
     *
     * @param id 主键ID
     * @return 流水信息
     */
    @RequestMapping("/getDetail")
    public Response<StandardFundBillFlowInfoVO> getStandardFundBillFlowInfoById(Long id) {
        AccountUser accountUser = getAccountUser();
        StandardFundBillFlowInfoVO flowInfo = standardFundBillFlowInfoService.getStandardFundBillFlowInfoById(accountUser,id);
        return Response.success(flowInfo);
    }

    /**
     * 拆分流水
     *
     * @param splitFlowRequest 流水信息列表
     * @return 拆分结果
     */
    @PostMapping("/splitFlow")
    public Response<Object> splitFlow(@RequestBody StandardFundBillSplitFlowRequest splitFlowRequest) {
        AccountUser accountUser = getAccountUser();
        standardFundBillFlowInfoService.splitFlow(accountUser, splitFlowRequest);
        return Response.success();
    }

    @PostMapping ("/export")
    public Response<Object> export(@RequestBody StandardFundBillFlowRequest request) throws SessionException {
        Staff staff = getLightStaff();
        PushResult export = standardFundBillFlowInfoService.export(staff, request);
        return Response.success("导出任务已创建", export.getClueId());
    }

}
