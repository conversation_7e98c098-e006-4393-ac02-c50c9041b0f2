package com.raycloud.dmj.account.web.controller;
import com.raycloud.dmj.account.core.common.PageInfo;
import com.raycloud.dmj.account.core.common.response.Response;
import com.raycloud.dmj.account.core.monitor.service.IMonitorService;
import com.raycloud.dmj.account.core.monitor.request.QueryShopMonitorReq;
import com.raycloud.dmj.account.core.monitor.vo.MonitorSummaryVO;
import com.raycloud.dmj.account.infra.session.AccountUser;
import com.raycloud.dmj.account.infra.session.SessionController;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 数据监控
 */
@RestController
@RequestMapping("/${web.dynamic.path}/monitor")
public class MonitorController extends SessionController {

    @Resource
    private IMonitorService monitorService;

    /**
     *  数据监控数据列表
     * @param platformCode
     * @param shopId
     * @param pageNo
     * @param pageSize
     * @return
     */
    @RequestMapping("/pageDataShopMonitor")
    public Response<List<MonitorSummaryVO>> pageDataShopMonitor(
                                                                  @RequestParam String platformCode,
                                                                  @RequestParam (required = false) Long shopId,
                                                                  @RequestParam Integer pageNo,
                                                                  @RequestParam Integer pageSize) {
        AccountUser accountUser = getAccountUser();
        QueryShopMonitorReq req = new QueryShopMonitorReq();
        req.setCompanyId(accountUser.getCompanyId());
        req.setPlatformCode(platformCode);
        req.setShopId(shopId);
        req.setPageNo(pageNo);
        req.setPageSize(pageSize);
        //查询资金账户
        return Response.success(monitorService.queryShopDataMonitor(req));
    }

    /**
     *  数据监控分页信息
     * @param platformCode
     * @param shopId
     * @param pageNo
     * @param pageSize
     * @return
     */
    @RequestMapping("/pageInfoShopMonitor")
    public Response<PageInfo<Void>> pageInfoShopMonitor(
                                                         @RequestParam String platformCode,
                                                         @RequestParam (required = false) Long shopId,
                                                         @RequestParam Integer pageNo,
                                                         @RequestParam Integer pageSize) {
        AccountUser accountUser = getAccountUser();
        QueryShopMonitorReq req = new QueryShopMonitorReq();
        req.setCompanyId(accountUser.getCompanyId());
        req.setPlatformCode(platformCode);
        req.setShopId(shopId);
        req.setPageNo(pageNo);
        req.setPageSize(pageSize);
        req.setCompanyId(accountUser.getCompanyId());
        //查询资金账户
        return Response.success(monitorService.queryShopDataMonitorPageInfo(req));
    }

}
