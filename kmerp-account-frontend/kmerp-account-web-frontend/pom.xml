<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.raycloud.dmj</groupId>
        <artifactId>kmerp-account-frontend</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>kmerp-account-web-frontend</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <version.erpsession>1.9.6-SNAPSHOT</version.erpsession>
        <version.rccache>1.6.0</version.rccache>
        <version.xmemcached>1.4.2</version.xmemcached>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.raycloud.dmj</groupId>
            <artifactId>kmerp-account-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- https://mvnrepository.com/artifact/javax.servlet/javax.servlet-api -->
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
        </dependency>

<!--    授权 dubbo api    -->
        <dependency>
            <groupId>com.raycloud.dmj</groupId>
            <artifactId>kmerp-account-dubbo-auth</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

    </dependencies>

</project>