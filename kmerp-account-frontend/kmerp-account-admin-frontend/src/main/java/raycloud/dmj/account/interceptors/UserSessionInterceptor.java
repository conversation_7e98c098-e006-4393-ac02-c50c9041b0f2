package raycloud.dmj.account.interceptors;

import com.alibaba.fastjson2.JSON;
import com.raycloud.dmj.account.core.common.exception.SessionException;
import com.raycloud.dmj.account.core.tj.req.TenantEmployeeGetRequest;
import com.raycloud.dmj.account.core.common.response.FailResponse;
import com.raycloud.dmj.account.core.session.SessionContext;
import com.raycloud.dmj.account.core.tj.vo.TenantEmployeeVo;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;
import raycloud.dmj.account.session.ocs.SessionService;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 基础的会话拦截处理器
 */
public abstract class UserSessionInterceptor extends HandlerInterceptorAdapter {

    private final Logger logger = Logger.getLogger(this.getClass());

    @Resource
    private SessionService sessionService;


    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String requestURI = request.getRequestURI();
        logger.info("拦截器被调用 - 请求路径: " + requestURI);

        try {
            checkLoginEmployee(request, response);
        }catch (SessionException e){
            generatorResponse(request, response, e.getMessage(), 901);
            logger.error("用户会话异常", e);
            return false;
        }catch (Exception e){
            generatorResponse(request, response, e.getMessage(), 500);
            logger.error("用户访问异常", e);
            return false;
        }

        return super.preHandle(request, response, handler);
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {

        SessionContext.remove();

        super.afterCompletion(request, response, handler, ex);
    }

    /**
     * 通过请求获取用户会话信息
     * @param request 请求
     * @param response 响应
     * @return 登陆的员工信息
     */
    protected abstract TenantEmployeeGetRequest getLoginEmployee(HttpServletRequest request, HttpServletResponse response) throws SessionException;

    /**
     * 获取用户的sessionId,有各系统实现，比如从cookie、header、request等途径获取
     * 为空表示用户没有登录
     * @return sessionId
     */
    protected abstract String getSessionId(HttpServletRequest request);

    /**
     * 获取当前系统的code
     * @return code
     */
    protected abstract String getSysCode();

    private void checkLoginEmployee(HttpServletRequest request, HttpServletResponse response){

        TenantEmployeeVo employeeVo = SessionContext.get();
        if (employeeVo != null) {
            //已登录的用户
            SessionContext.put(employeeVo);
            return;
        }

        String sessionId = getSessionId(request);
        if(StringUtils.isEmpty(sessionId)){
            throw new SessionException("用户未登录，请先登录再使用");
        }else{
            sessionId = getCacheSessionId(sessionId);
        }

        //从会话缓存中获取
        employeeVo = sessionService.getSessionUser(request, sessionId);
        if(employeeVo != null){
            //已登录的用户
            SessionContext.put(employeeVo);
            return;
        }

        //通过业务的逻辑，获取登录用户
        //同一个SessionId 只需要获取一次
        synchronized (sessionId.intern()){
            employeeVo = SessionContext.get();
            if (employeeVo != null) {
                //已登录的用户
                SessionContext.put(employeeVo);
                return;
            }

            TenantEmployeeGetRequest getRequest = getLoginEmployee(request, response);
            if(getRequest==null){
                throw new SessionException("用户未登录，请先登录再使用");
            }
            if(StringUtils.isEmpty(getRequest.getSysCode()) || StringUtils.isEmpty(getRequest.getTenantOuterCode()) || StringUtils.isEmpty(getRequest.getEmployeeOuterCode())){
                throw new SessionException("登录用户，信息数据异常，请重新登陆");
            }

//            employeeVo = tenantEmployeeService.getTenantEmployeeFullInfo(getRequest);
//            if(employeeVo==null){
//                throw new SessionException("登录用户，无法获取授权，请联系管理员处理");
//            }

            //添加到会话缓存
            sessionService.setSessionUser(request, sessionId, employeeVo);

            SessionContext.put(employeeVo);
        }

    }

    /**
     * 拼接缓存用户的cache的key
     * @return cacheKey
     */
    private String getCacheSessionId(String sessionId){

        return getSysCode() + ":" + "session:" + sessionId;
    }

    /**
     * 前端输出校验码。
     * @param request
     * @param response
     * @param msg
     * @param errorCode
     * @throws IOException
     */
    protected void generatorResponse(HttpServletRequest request, HttpServletResponse response, String msg, int errorCode) throws IOException {
        FailResponse<?> errorInfo = new FailResponse<>(errorCode, msg);
        String text = JSON.toJSONString(errorInfo);
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().write(text);
        response.setStatus(HttpStatus.OK.value());
    }

}
