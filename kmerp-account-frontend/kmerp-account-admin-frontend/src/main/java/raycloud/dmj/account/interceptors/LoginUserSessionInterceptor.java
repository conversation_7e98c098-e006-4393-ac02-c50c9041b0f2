package raycloud.dmj.account.interceptors;

import com.raycloud.dmj.account.core.common.exception.SessionException;
import com.raycloud.dmj.account.core.tj.req.TenantEmployeeGetRequest;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.session.ISession;
import com.raycloud.dmj.session.controller.StaffSessionContext;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;
import com.raycloud.dmj.account.core.tj.utils.CookieUtils;
import raycloud.dmj.account.session.ocs.TJOcsSessionManager;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 登录用户会话拦截器
 * 用于验证用户登录状态和权限
 */
@Component
public class LoginUserSessionInterceptor extends UserSessionInterceptor{

    private final Logger logger = Logger.getLogger(this.getClass());

    @Resource
    private TJOcsSessionManager sessionManager;

    @Override
    protected TenantEmployeeGetRequest getLoginEmployee(HttpServletRequest request, HttpServletResponse response) throws SessionException {
        try{
            //获取用户的方法
            ISession session = sessionManager.getSession(request, response);
            Staff staff = session.getStaff();

            TenantEmployeeGetRequest getRequest = new TenantEmployeeGetRequest();
            getRequest.setSysCode(getSysCode());
            getRequest.setTenantOuterCode(String.valueOf(staff.getCompanyId()));
            getRequest.setEmployeeOuterCode(String.valueOf(staff.getId()));

            return getRequest;
        }catch (SessionException e){
            logger.error("ERP TJ 会话异常", e);
            throw new SessionException(e.getMessage());
        }catch(Exception e){
            logger.error("ERP TJ 会话获取异常", e);
            throw new RuntimeException(e);
        }finally {
            StaffSessionContext.remove();
        }
    }

    @Override
    protected String getSessionId(HttpServletRequest request) {

        return CookieUtils.read(request, "_new_tj_censeid");
    }

    @Override
    protected String getSysCode() {

        return "ERP_TJ";
    }

}
