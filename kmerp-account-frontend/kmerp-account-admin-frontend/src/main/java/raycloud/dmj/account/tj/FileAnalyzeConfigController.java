package raycloud.dmj.account.tj;

import com.raycloud.dmj.account.core.tj.req.FileAnalyzeConfigRequest;
import com.raycloud.dmj.account.core.tj.req.FileSheetConfigRequest;
import com.raycloud.dmj.account.core.common.response.SuccessResponse;
import com.raycloud.dmj.account.core.tj.vo.CheckerVo;
import com.raycloud.dmj.account.core.tj.vo.FileAnalyzeConfigVo;
import com.raycloud.dmj.account.core.tj.vo.FileSheetConfigVo;
import org.apache.log4j.Logger;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.*;
import com.raycloud.dmj.account.core.tj.serveice.IFileAnalyzeConfigService;

import javax.annotation.Resource;
import java.util.List;

@RequestMapping("/${web.dynamic.path}/")
@RestController
@Scope("prototype")
public class FileAnalyzeConfigController {

    private Logger logger = Logger.getLogger(this.getClass());

    @Resource
    private IFileAnalyzeConfigService fileAnalyzeConfigService;

    //获取校验器列表
    @GetMapping(value = "/getCheckerList")
    @ResponseBody
    public SuccessResponse<Object> getCheckerList() {
        List<CheckerVo> checkerVoList = fileAnalyzeConfigService.getCheckerList();
        return new SuccessResponse<>(checkerVoList);
    }

    //获取转换器列表
    @GetMapping(value = "/getTranslatorList")
    @ResponseBody
    public SuccessResponse<Object> getTranslatorList() {
        List<CheckerVo> checkerVoList = fileAnalyzeConfigService.getTranslatorList();
        return new SuccessResponse<>(checkerVoList);
    }

    //获取预处理器列表
    @GetMapping(value = "/getPreprocessorList")
    @ResponseBody
    public SuccessResponse<Object> getPreprocessorList() {
        List<CheckerVo> checkerVoList = fileAnalyzeConfigService.getPreprocessorList();
        return new SuccessResponse<>(checkerVoList);
    }

    //获取文件解析配置列表
    @GetMapping(value = "/getFileAnalyzeConfigList")
    @ResponseBody
    public SuccessResponse<Object> getFileAnalyzeConfigList(String dataType) {
        logger.info("获取文件解析配置列表");
        List<FileAnalyzeConfigVo> fileAnalyzeConfigVoList = fileAnalyzeConfigService.getFileAnalyzeConfigList(dataType);
        return new SuccessResponse<>(fileAnalyzeConfigVoList);
    }

    //获取单个文件解析配置
    @GetMapping(value = "/getFileAnalyzeConfig")
    @ResponseBody
    public SuccessResponse<FileAnalyzeConfigVo> getFileAnalyzeConfig(@RequestParam(required = true) Long id) {
        FileAnalyzeConfigVo fileAnalyzeConfigVo = fileAnalyzeConfigService.getFileAnalyzeConfig(id);
        return new SuccessResponse<>(fileAnalyzeConfigVo);
    }

    //新增文件解析配置
    @PostMapping(value = "/addFileAnalyzeConfig")
    @ResponseBody
    public SuccessResponse<Object> addFileAnalyzeConfig(@RequestBody FileAnalyzeConfigRequest addFileAnalyzeConfig) {
        Long id = fileAnalyzeConfigService.addFileAnalyzeConfig(addFileAnalyzeConfig);
        return new SuccessResponse<>(id);
    }

    //修改文件解析配置
    @PostMapping(value = "/updateFileAnalyzeConfig")
    @ResponseBody
    public SuccessResponse<Object> updateFileAnalyzeConfig(@RequestBody FileAnalyzeConfigRequest fileAnalyzeConfig) {
        Long id = fileAnalyzeConfigService.updateFileAnalyzeConfig(fileAnalyzeConfig);
        return new SuccessResponse<>(id);
    }

    //获取文件Sheet的数据配置
    @GetMapping(value = "/getFileAnalyzeTableConfigList")
    @ResponseBody
    public SuccessResponse<Object> getFileAnalyzeTableConfigList(@RequestParam(required = true) Long configId) {
        List<FileSheetConfigVo> fileAnalyzeConfigVoList = fileAnalyzeConfigService.getFileAnalyzeTableConfigList(configId);
        return new SuccessResponse<>(fileAnalyzeConfigVoList);
    }

    //添加文件Sheet的数据配置
    @PostMapping(value = "/addFileAnalyzeTableConfig")
    @ResponseBody
    public SuccessResponse<Object> addFileAnalyzeTableConfig(@RequestBody FileSheetConfigRequest fileSheetConfigRequest) {
        Long id = fileAnalyzeConfigService.addFileAnalyzeTableConfig(fileSheetConfigRequest);
        return new SuccessResponse<>(id);
    }

    //修改文件Sheet的数据配置
    @PostMapping(value = "/updateFileAnalyzeTableConfig")
    @ResponseBody
    public SuccessResponse<Object> updateFileAnalyzeTableConfig(@RequestBody FileSheetConfigRequest fileSheetConfigRequest) {
        Long id = fileAnalyzeConfigService.updateFileAnalyzeTableConfig(fileSheetConfigRequest);
        return new SuccessResponse<>(id);
    }
}
