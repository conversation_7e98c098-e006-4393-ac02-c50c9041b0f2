package raycloud.dmj.account.session.ocs;

import com.raycloud.dmj.account.core.tj.vo.TenantEmployeeVo;
import com.raycloud.dmj.account.infra.redis.RayRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.Duration;

@Service
public class SessionService {

    @Resource
    RayRedisTemplate rayRedisTemplate;

    public TenantEmployeeVo getSessionUser(HttpServletRequest request, String sessionId){

        return rayRedisTemplate.getObject(sessionId, TenantEmployeeVo.class);
    }

    public void setSessionUser(HttpServletRequest request, String sessionId, TenantEmployeeVo employeeVo){

        //默认只给10分钟
        rayRedisTemplate.setObject(sessionId, employeeVo, Duration.ofMinutes(10));
    }


}
