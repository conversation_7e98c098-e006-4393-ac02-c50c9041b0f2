package raycloud.dmj.account.session.ocs;

import com.raycloud.cache.CacheException;
import com.raycloud.cache.ICache;
import com.raycloud.dmj.domain.account.Staff;
import com.raycloud.dmj.session.ISession;
import com.raycloud.dmj.session.ISessionManager;
import com.raycloud.dmj.session.SessionConfig;
import com.raycloud.dmj.session.SessionException;
import com.raycloud.dmj.session.ocs.OcsSession;
import com.raycloud.dmj.session.ocs.OcsSessionBusiness;
import com.raycloud.dmj.session.ocs.SessionConfigUtils;
import com.raycloud.dmj.session.utils.SessionIdGenerator;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;

/**
 * 会话管理器
 * 
 * <AUTHOR>
 *
 */
@Component
public class TJOcsSessionManager implements ISessionManager {

	public static final Integer DEFAULT_EXPIRY_TIME = 12 * 3600;

	public final static String CENTRAL_VAL_COOKIE = "_new_tj_censeid";

	@Resource
	private OcsSessionBusiness sessionBusiness;

	private SessionConfig sessionConfig;

	private ICache cache;

	private final Logger logger = Logger.getLogger(this.getClass());

	@PostConstruct
	public void _start(){
		cache = sessionBusiness.getCache();
		System.out.println("OcsSessionManager类覆盖");
	}

	/**
	 * 获取会话
	 */
	@Override
	public ISession getSession(HttpServletRequest request,
                               HttpServletResponse response) throws SessionException {
		try {
			String sessionId = findCentralSessionId(request);
			if(logger.isTraceEnabled() && StringUtils.isEmpty(sessionId)){
				logger.trace("can't find sessionId from cookie:" + CENTRAL_VAL_COOKIE + ",path:" + request.getPathInfo());
			}
			System.out.println("sessionId: " + sessionId);
			Long staffId = validateMasterSession(sessionId);
			if(logger.isTraceEnabled() && staffId == null){
				logger.trace("can't find staffId from sessionId:" + sessionId);
			}
			return new OcsSession(request, response, cache, sessionId, staffId);
		} catch(SessionException e){
			throw e;
		} catch (Exception e) {
			throw new SessionException(e);
		}
	}

	@Override
	public ISession createStaffSession(Staff staff, HttpServletRequest request, HttpServletResponse response, String domain) throws SessionException {
		try {
			OcsSession session = new OcsSession(request, response, cache, SessionIdGenerator.generate(), staff.getId());
			// 通过反射绕过访问控制限制
			Method setStaffMethod = session.getClass().getDeclaredMethod("setStaff", ISession.class, Staff.class);
			setStaffMethod.setAccessible(true);
			setStaffMethod.invoke(session, session, staff);
			// 通过构造函数传入Staff对象，避免后续设置
			initCentralSession(request, response, session, staff, domain);
			return session;
		} catch(SessionException e){
			throw e;
		} catch (Exception e) {
			throw new SessionException(e);
		}
	}

	@Override
	public SessionConfig getSessionConfig() throws SessionException {
		if (null == sessionConfig) {
			sessionConfig = new SessionConfig();
		}
		return sessionConfig;
	}

	public void setSessionConfig(SessionConfig config){
		sessionConfig = config;
	}
	
	@Override
	public boolean isCentralSession() {
		return true;
	}
	
	@Override
	public ISession simulateStaffSession(String sessionId,
                                         HttpServletRequest request, HttpServletResponse response)
			throws SessionException {
		return null;
	}

	/**
	 * 得到用于集中式会话中Cookie的sessionId
	 *
	 * @return
	 */
	protected String findCentralSessionId(HttpServletRequest request) {
		Cookie[] cookies = request.getCookies();
		String sessionId = null;
		if (cookies == null) {
			return null;
		}
		for (Cookie cookie : cookies) {
			/*if(!defaultDomain.equals(cookie.getDomain())){
				continue;
			}*/
			if (CENTRAL_VAL_COOKIE.equals(cookie.getName())) {
				//logger.trace("session cookie:" + cookie.getDomain() + "," + cookie.getName() + "," + cookie.getValue());
				sessionId = cookie.getValue();
				break;
			}
		}
		return sessionId;
	}

	/**
	 * 当从主站跳转到子模块的Web服务时，需要校验主站的会话用户是否和跳转过来的参数的staffId一致，
	 * 否则将是不安全的访问，此方法只用于集中式会话
	 *
	 * @return
	 * @throws IllegalAccessException
	 * @throws CacheException
	 */
	protected Long validateMasterSession(String sessionId) throws SessionException {
		if (StringUtils.isEmpty(sessionId)) {
			return null;
		}

		try {
			//每次读取延长12小时会话时间
			Long sessionStaffId = cache.getAndTouch(sessionId, SessionConfigUtils.getExpiryTime(sessionConfig));
			if (null == sessionStaffId) {
				return null;
			}
			System.out.println("sessionStaffId: " + sessionStaffId);
			return sessionStaffId;
		} catch (Exception e) {
			return null;
		}
	}

	/**
	 * 初始化集中式会话的缓存
	 *
	 * @param session
	 * @param staff
	 * @throws CacheException
	 * @throws SessionException
	 */
	private void initCentralSession(HttpServletRequest request, HttpServletResponse response, ISession session, Staff staff, String domain) throws CacheException, SessionException {
		String sessionId = session.getSessionId();
		if (null == sessionId) {
			throw new SessionException("会话失效，请重新登录");
		}
		Cookie cookie = new Cookie(CENTRAL_VAL_COOKIE, sessionId);
		cookie.setDomain("." + domain);
		cookie.setPath("/");
		response.addCookie(cookie);
		cache.set(sessionId, staff.getId(), SessionConfigUtils.getExpiryTime(sessionConfig));
		// TODO 这个KEY的设计需要重新考虑下，比如对单点登录的支持是否有效，并且要考虑会话的touch的功能
		cache.set(buildCentralSessionKey(staff), sessionId, SessionConfigUtils.getExpiryTime(sessionConfig));
	}

	protected String buildCentralSessionKey(Staff staff){
		return new StringBuilder("").append(staff.getId()).append(CENTRAL_VAL_COOKIE).toString();
	}



}
