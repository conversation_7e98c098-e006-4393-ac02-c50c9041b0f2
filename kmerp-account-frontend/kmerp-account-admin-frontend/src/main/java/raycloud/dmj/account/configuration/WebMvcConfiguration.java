package raycloud.dmj.account.configuration;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import raycloud.dmj.account.interceptors.LoginUserSessionInterceptor;

/**
 * Web MVC 配置类
 * 用于注册拦截器、配置静态资源等
 */
@Configuration
public class WebMvcConfiguration implements WebMvcConfigurer {

    @Autowired
    private LoginUserSessionInterceptor loginUserSessionInterceptor;

    /**
     * 注册拦截器
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(loginUserSessionInterceptor)
                .addPathPatterns("/**")  // 拦截所有请求
                .excludePathPatterns(
//                        "/erpaccount/**",
                        "/ping",                           // 健康检查接口
                        "/project_auto_check_monitor.jsp", // 监控检查页面
                        "/account/notFound",               // 404错误页面
                        "/error",                          // 错误页面
                        "/resources/**",                      // 静态资源
                        "/css/**",                         // CSS文件
                        "/js/**",                          // JS文件
                        "/images/**",                      // 图片文件
                        "/favicon.ico"                     // 网站图标
                );
    }

    //解决跨域问题
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOrigins("*")
                .allowedMethods("GET", "POST")
                .allowedHeaders("*")
                .maxAge(3600);
    }
}
