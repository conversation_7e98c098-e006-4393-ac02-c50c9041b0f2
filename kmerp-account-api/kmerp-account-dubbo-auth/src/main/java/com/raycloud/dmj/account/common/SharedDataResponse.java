package com.raycloud.dmj.account.common;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
public class SharedDataResponse extends BaseResponse implements Serializable {
    private static final long serialVersionUID = 104466666L;

    private String url;

    public static int AuthDisableCode = 10001;

    public static int DataNotExistCode = 10002;

    public static int BizError = 500;

    public SharedDataResponse(boolean success, Integer code, String errorMsg, String url) {
      super(success, code, errorMsg);
      this.url = url;
    }

    public SharedDataResponse() {
    }

    public static SharedDataResponse success(String url) {
        return new SharedDataResponse(true, 200, null, url);
    }

    public static SharedDataResponse error(int code, String errorMsg) {
        return new SharedDataResponse(false, code, errorMsg, null);
    }

    public static SharedDataResponse error() {
        return new SharedDataResponse(false, BizError, "系统异常", null);
    }

}
