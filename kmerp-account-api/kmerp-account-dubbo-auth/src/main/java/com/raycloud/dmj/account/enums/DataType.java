package com.raycloud.dmj.account.enums;

import lombok.Getter;

@Getter
public enum DataType {

    BILL("bill", "账单");

    private final String value;

    private final String desc;


    DataType(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static DataType of(String value) {
        for (DataType dataType : DataType.values()) {
            if (dataType.getValue().equals(value)) {
                return dataType;
            }
        }
        throw new IllegalArgumentException("Invalid value: " + value);
    }
}
