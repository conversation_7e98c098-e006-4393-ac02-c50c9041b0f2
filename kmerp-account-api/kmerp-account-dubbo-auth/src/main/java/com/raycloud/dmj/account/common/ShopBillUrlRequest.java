package com.raycloud.dmj.account.common;

import com.raycloud.dmj.account.enums.DataType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;

@EqualsAndHashCode(callSuper = true)
@Data
public class ShopBillUrlRequest extends BaseRequest implements Serializable {

    private static final long serialVersionUID = 29345734234L;

    private DataType dataType;
    /**
     *  账单时间时间
     */
    private LocalDate billDate;


}
