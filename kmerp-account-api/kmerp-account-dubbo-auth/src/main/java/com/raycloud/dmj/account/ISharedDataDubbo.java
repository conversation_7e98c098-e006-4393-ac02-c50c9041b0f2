package com.raycloud.dmj.account;

import com.raycloud.dmj.account.common.DubboResponse;
import com.raycloud.dmj.account.common.SharedDataResponse;
import com.raycloud.dmj.account.common.ShopBillUrlRequest;

public interface ISharedDataDubbo {

    /**
     * 获取一定时间范围内的数据
     * 支付宝 （T+1），当日数据一般于次日 9 点前生成
     *
     * @param request
     * @return  SharedDataResponse 中
     */
    DubboResponse<SharedDataResponse> getDataUrl(ShopBillUrlRequest request);

}
