package com.raycloud.dmj.account.utils;

public class ShopIdUtil {

    public static Long getShopId(String shopUniId) {
        String[] splitArr = shopUniId.split("_");
        if (splitArr.length != 2) {
            throw new RuntimeException("店铺ID格式错误");
        }
        Long userId = Long.parseLong(splitArr[0]);
        Long shopId = Long.parseLong(splitArr[1]);
        if (userId > Integer.MAX_VALUE || shopId > Integer.MAX_VALUE) {
            throw new RuntimeException("该店铺超出计算上限!");
        }
        userId = userId << 32;
        return userId | shopId;
    }
}
