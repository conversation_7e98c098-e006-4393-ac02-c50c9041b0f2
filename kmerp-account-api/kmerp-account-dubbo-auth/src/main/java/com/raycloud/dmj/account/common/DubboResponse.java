package com.raycloud.dmj.account.common;

import java.io.Serializable;

public class DubboResponse<T> implements Serializable {
    private static final long serialVersionUID = 1L;

    private Boolean isSuccess = true;

    private int code = 200;

    private String errorMsg;

    private T data;

    public Boolean getSuccess() {
        return isSuccess;
    }

    public void setSuccess(Boolean success) {
        isSuccess = success;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public static <T> DubboResponse<T> successOf(T data) {
        return new DubboResponse<T>(data);
    }

    public static <T> DubboResponse<T> errorOf(T data, String errorMsg) {
        return new DubboResponse<>(false, data, errorMsg);
    }

    public DubboResponse(T data) {
        this.data = data;
    }

    public DubboResponse(Boolean isSuccess, T data, String errorMsg) {
        this.isSuccess = isSuccess;
        this.data = data;
        this.errorMsg = errorMsg;
    }
}
